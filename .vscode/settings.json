{"eslint.enable": true, "cSpell.words": ["<PERSON><PERSON><PERSON>"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.insertSpaces": true, "editor.tabSize": 2, "prettier.singleQuote": true, "prettier.semi": true, "prettier.trailingComma": "es5", "prettier.printWidth": 100, "prettier.jsxSingleQuote": false, "javascript.preferences.quoteStyle": "single", "typescript.preferences.quoteStyle": "single", "prettier.endOfLine": "lf", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.experimental.useTsgo": false}