## Build & Development Commands
- Development: `npm run dev`
- Build: `npm run build`
- Production: `npm run start`
- Lint: `npm run lint`
- Fix linting issues: `npm run lint:fix`
- Format check: `npm run format:check`
- Format code: `npm run format:write`
- Run tests: `npm run test`
- i18n download: `npm run i18n:download`
- i18n upload: `npm run i18n:upload`

## Code Style Guidelines
- TypeScript with strict typing (`tsconfig.json`)
- Use Zod for validation schemas
- Next.js app router structure with server components
- Use React Server Components where possible
- Keep file sizes small, extract components when needed
- Use barrel exports pattern where appropriate
- Prefer named exports over default exports
- UI components use shadcnui
- Follow kebab-case for file names
- Use jotai for client state management. You don't need Redux or Context
- Components should be properly typed with TypeScript
- Use server actions for data mutations