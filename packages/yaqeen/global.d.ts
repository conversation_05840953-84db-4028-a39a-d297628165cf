import type en from "./i18n/messages/locale/en.json";
import "@tanstack/react-table";

type Messages = typeof en;

declare global {
  // Use type safe message keys with `next-intl`
  type IntlMessages = Messages;
}

type EditedRows = Record<string, boolean>;

declare module "@tanstack/react-table" {
  interface ColumnMeta<TData, TValue> {
    type?: "number" | "text" | "select";
    selectOptions?: { label: string; value: string }[];
    max?: number;
  }

  interface TableMeta {
    localData: TData[];
    editedRows: EditedRows;
    addRow: () => void;
    setEditedRows: React.Dispatch<React.SetStateAction<EditedRows>>;
    toggleEdit: (rowIndex: number) => void;
    updateData: (rowIndex: number) => void;
    removeRow: (rowIndex: number) => void;
    updateTempData: (rowIndex: number, columnId: string, formattedValue: TData) => void;
    revertData: (rowIndex: number) => void;
  }
}
