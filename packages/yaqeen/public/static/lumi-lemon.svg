<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="d104655a-c837-438d-8363-9714f67cf465"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="5aab032a-887c-46d4-af6f-9050c05f9a48"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(32.41 0 0 32.41 675.75 798.77)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(147,213,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-67.56, -32.92)" d="M 69.3143 25.6298 L 69.4311 37.4091 C 69.4311 38.198 68.9641 38.9274 68.2649 39.1692 C 68.0314 39.2908 67.3322 39.2908 67.0987 39.1692 C 66.3995 38.9261 65.9325 38.2588 65.8741 37.4685 L 65.8158 25.5676 L 64.5328 26.9036 L 56.603 35.3437 C 56.2528 35.7084 56.3112 36.3757 56.7198 36.6796 C 59.8681 39.0477 63.6585 40.3229 67.5644 40.2621 C 69.7216 40.2621 71.8789 39.8366 73.861 39.0477 C 75.4942 38.4398 77.0093 37.5293 78.409 36.4365 C 78.8176 36.1326 78.876 35.4652 78.5257 35.1005 L 69.313 25.6284 L 69.3143 25.6298 Z" stroke-linecap="round" />
</g>
<g transform="matrix(32.41 0 0 32.41 304.39 419.81)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(147,213,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-56.11, -21.23)" d="M 61.8517 24.4153 L 63.1346 23.0793 L 51.7646 23.2009 C 50.9487 23.2009 50.2482 22.6551 50.016 21.8649 C 49.9576 21.6217 49.8993 21.1976 50.016 20.772 C 50.2495 19.9831 50.8903 19.4361 51.7063 19.4361 L 63.0762 19.3145 L 53.8635 9.84373 C 53.4549 9.47901 52.8725 9.5398 52.5222 9.96531 C 51.5311 11.4215 50.7139 13.0007 50.1315 14.7014 C 49.4323 16.8262 49.082 19.0119 49.082 21.2583 C 49.082 25.3879 50.365 29.2729 52.6973 32.4906 C 53.0476 32.9161 53.63 32.9769 54.0386 32.6122 L 61.8517 24.4153 Z" stroke-linecap="round" />
</g>
</svg>