import clsx from "clsx";
import { LumiLemon } from "./LumiLemon";
import { type Platform } from "../app-sidebar/content.mock";

export default function PlatformIcon({ variant, className }: { variant: Platform["id"]; className?: string }) {
  const fill = {
    rental: "#3E5B0C", // text-lumi-900
    admin: "#7C3612", // text-orange-900
    fleet: "#1E3A8A", // text-blue-900
    lease: "#7C3612", // text-red-900
  };
  return (
    <div
      className={clsx(
        "flex items-center justify-center rounded-lg border p-2",
        "h-10 w-10",
        {
          "border-lumi-200 bg-lumi-100 text-lumi-700": variant === "rental",
          "border-orange-200 bg-orange-100 text-orange-700": variant === "admin",
          "border-blue-200 bg-blue-100 text-blue-700": variant === "fleet",
          "border-red-200 bg-red-100 text-red-700": variant === "lease",
        },
        className,
      )}
    >
      <LumiLemon fill={fill[variant]} />
    </div>
  );
}
