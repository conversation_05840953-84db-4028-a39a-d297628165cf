"use client";

import React, { useEffect, useState } from "react";

import { Select as ShadCnSelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import InfiniteScroll from "../ui/InfiniteScroll";
import { LoadingSpinner } from "../ui/loading-spinner";
import { Label } from "../ui/label";

type paginationProps =
  | {
      getNextPage: (page: number) => Promise<void>;
      hasMoreOptions: boolean;
    }
  | {
      getNextPage?: null;
      hasMoreOptions?: never;
    };

export type SelectProps<T> = paginationProps & {
  options: { value: T; label: string }[];
  name?: string;
  required?: boolean;
  disabled?: boolean;
  defaultValue?: T;
  label?: string;
  hideSearch?: boolean;
  placeholder?: string;
  onSelect?: (value: T) => void;
  customClasses?: {
    trigger?: string;
  };
};

export default function Select<T extends string | number | null>({
  options,
  hasMoreOptions = false,
  getNextPage,
  required,
  disabled,
  defaultValue,
  label,
  placeholder = "Select...",
  name,
  hideSearch,
  onSelect,
  customClasses,
}: SelectProps<T>) {
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [localOptions, setLocalOptions] = useState(options);
  const [hasMore, setHasMore] = useState(hasMoreOptions);
  const [page, setPage] = useState(1);
  const [value, setValue] = useState<T | undefined>(defaultValue);

  const filteredOptions = localOptions.filter((option) =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  useEffect(() => {
    if (options.length > localOptions.length) {
      setLocalOptions(options);
      setLoading(false);
      setHasMore(hasMoreOptions);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);

  const next = async () => {
    if (!getNextPage) return;

    setLoading(true);
    await getNextPage(page);
    setLoading(false);
    setPage(page + 1);
  };
  return (
    <div>
      {label && <Label>{label}</Label>}

      <ShadCnSelect
        disabled={disabled}
        name={name}
        required={required}
        defaultValue={defaultValue && defaultValue !== "null" ? `${defaultValue}` : undefined}
        onValueChange={(value) => {
          setValue(value as T);
          onSelect?.((value && value !== "null" ? `${value}` : null) as T);
        }}
        value={value && value !== "null" ? `${value}` : ""}
      >
        <SelectTrigger
          className={cn(
            "flex min-w-28 items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0",
            customClasses?.trigger,
          )}
          aria-label="Select"
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {!hideSearch && (
            <div className="p-2">
              <Input
                placeholder="Search..."
                value={searchQuery}
                onClick={(e) => e.stopPropagation()}
                onKeyDown={(e) => e.stopPropagation()}
                onChange={(e) => {
                  e.stopPropagation();
                  setSearchQuery(e.target.value);
                }}
                className="h-8"
              />
            </div>
          )}
          <div className="grid max-h-64 gap-2 overflow-auto py-4">
            {filteredOptions.map((option) => {
              return (
                <SelectItem key={option.value} value={`${option.value}`}>
                  <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                    {option.label}
                  </div>
                </SelectItem>
              );
            })}
          </div>
          {getNextPage && (
            <>
              <div className="mb-4"></div>
              <InfiniteScroll isLoading={loading} hasMore={hasMore} next={next}>
                <div className="flex w-full justify-center">
                  {hasMore && <LoadingSpinner className="my-4 h-8 w-8 animate-spin" />}
                </div>
              </InfiniteScroll>
            </>
          )}
        </SelectContent>
      </ShadCnSelect>
    </div>
  );
}
