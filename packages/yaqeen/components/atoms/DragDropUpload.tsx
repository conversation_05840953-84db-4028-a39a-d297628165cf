import React, { useCallback } from "react";

interface DragDropUploadProps {
  onDrop: (files: FileList) => void;
  onDragOver?: () => void;
  onDragLeave?: () => void;
}

const DragDropUpload: React.FC<DragDropUploadProps> = ({ onDrop, onDragOver, onDragLeave }) => {
  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      const files = event.dataTransfer.files;
      if (files.length > 0) {
        onDrop(files);
      }
    },
    [onDrop]
  );

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (onDragOver) onDragOver();
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (onDragLeave) onDragLeave();
  };

  return (
    <div
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      className="rounded-md border-2 border-dashed border-gray-400 p-4 text-center"
    >
      <p>Drag and drop your files here, or click to select files</p>
    </div>
  );
};

export default DragDropUpload;
