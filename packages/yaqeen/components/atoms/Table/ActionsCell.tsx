"use client";

import { type CellContext } from "@tanstack/react-table";

const ActionsCell = <T,>({ row, table }: CellContext<T, unknown>) => {
  const meta = table.options.meta;

  const setEditedRows = () => {
    meta?.toggleEdit(row.index);
  };
  const revert = () => {
    meta?.revertData?.(row.index);
  };
  const doneEdit = () => {
    meta?.updateData?.(row.index);
  };
  // const deleteRow = () => {
  //   meta?.removeRow?.(row.index);
  // };

  return meta?.editedRows[row.id] ? (
    <div className="flex gap-2">
      <button type="button" onClick={revert} className="me-2" name="cancel">
        X
      </button>
      <button type="button" onClick={doneEdit} name="done">
        ✔
      </button>
    </div>
  ) : (
    <div className="flex gap-2">
      <button type="button" onClick={setEditedRows} name="edit">
        ✐
      </button>
      {/* <button type="button" onClick={deleteRow} name="delete">
        <Trash />
      </button> */}
    </div>
  );
};

export default ActionsCell;
