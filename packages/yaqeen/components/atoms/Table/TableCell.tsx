"use client";

import { type JSX, useEffect, useState } from "react";
import { type CellContext } from "@tanstack/react-table";
import { SelectTrigger } from "@radix-ui/react-select";

import { Select, SelectContent, SelectGroup, SelectItem, SelectValue } from "@/components/ui/select";

const TableCell = <T,>({ getValue, row, column, table }: CellContext<T, string | number>): JSX.Element => {
  const columnMeta = column.columnDef.meta;
  const tableMeta = table.options.meta;
  const col = column.id as keyof T;
  const rowIdx = row.id as unknown as number;
  const initialValue = ((tableMeta?.localData?.[rowIdx] as T)?.[col] ?? getValue()) as string | number;
  const [value, setValue] = useState<string | number>(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue, tableMeta?.editedRows?.[row.id]]);

  const onBlur = () => {
    let formattedValue = value;
    if (columnMeta?.type === "number") {
      formattedValue = +value;
    }

    table.options.meta?.updateTempData?.(row.index, column.id, formattedValue);
  };

  if (columnMeta?.type && tableMeta?.editedRows[row.id]) {
    if (columnMeta?.type === "select") {
      const options = columnMeta?.selectOptions;

      return (
        <div className="max-w-18 border p-1" onBlur={onBlur}>
          <Select
            defaultValue={`${value}`}
            onValueChange={(value) => {
              setValue(value);
            }}
          >
            <SelectTrigger className="min-h-6 w-full min-w-6 text-start focus:outline-none">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {options?.map((option) => {
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  );
                })}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      );
    }

    return (
      <input
        className="max-w-14 border p-1"
        value={value || ""}
        max={columnMeta?.max}
        onChange={(e) => setValue(e.target.value)}
        onBlur={onBlur}
        type={columnMeta?.type ?? "text"}
      />
    );
  }
  return <span>{getValue() || "N/A"}</span>;
};

export default TableCell;
