import { cn } from "@/lib/utils";
import { Input, type InputProps } from "../ui/input";
import { Label } from "../ui/label";

type InputFieldProps = InputProps & {
  label?: string;
  helper?: string;
};

const InputField = ({ label, helper, ...props }: InputFieldProps) => {
  return (
    <div className={cn("mb-6 flex w-[418px] flex-col gap-2 ", props.className)}>
      {label && <Label>{label}</Label>}
      <Input {...props} />
      {helper && <p className="text-sm text-gray-500">{helper}</p>}
    </div>
  );
};

export default InputField;
