import { cn } from "@/lib/utils";
import { Textarea, type TextAreaProps } from "../ui/textarea";
import { Label } from "../ui/label";

type TextAreaFieldProps = TextAreaProps & {
  label?: string;
};

const TextAreaField = ({ label, ...props }: TextAreaFieldProps) => {
  return (
    <div className={cn("mb-6 flex w-[418px] flex-col gap-2 ", props.className)}>
      {label && <Label>{label}</Label>}
      <Textarea {...props} />
    </div>
  );
};

export default TextAreaField;
