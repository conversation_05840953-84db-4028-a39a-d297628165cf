import React from "react";
import { Too<PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { TooltipArrow } from "@radix-ui/react-tooltip";

interface TooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  side?: "top" | "bottom" | "left" | "right";
  withArrow?: boolean;
}

export default function TooltipComponent({ children, content, side = "top", withArrow = true }: TooltipProps) {
  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent side={side} sideOffset={5}>
          {content}
          {withArrow && <TooltipArrow className=" TooltipArrow fill-white" />}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
