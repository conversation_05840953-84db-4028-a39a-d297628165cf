/**
 * Permission Gate Component
 * Server component that conditionally renders children based on permission
 * Following the Layer Separation Principle from Java Spring Boot Architecture
 */
import { type ReactNode } from "react";
import { checkPermission } from "@/lib/server-permission";

interface PermissionGateProps {
  /**
   * Single permission ID to check
   * Format: "permission:group:subGroup:scope"
   */
  permission?: string;
  
  /**
   * List of permission IDs to check (any one must match)
   * Format: ["permission:group1:subGroup1:scope1", "permission:group2:subGroup2:scope2"]
   */
  permissions?: string[];
  
  /**
   * The content to render if the user has the permission
   */
  children: ReactNode;
  
  /**
   * Optional content to render if the user doesn't have the permission
   */
  fallback?: ReactNode;
  
  /**
   * If true, user must have ALL permissions in the list
   * If false (default), user must have ANY permission in the list
   */
  requireAll?: boolean;
}

/**
 * Server component that conditionally renders children based on permission
 * 
 * Usage with single permission:
 * <PermissionGate permission="permission:agreement:agreement-extension:read">
 *   <ProtectedComponent />
 * </PermissionGate>
 * 
 * Usage with multiple permissions (any match):
 * <PermissionGate permissions={["permission:agreement:view", "permission:agreement:edit"]}>
 *   <ProtectedComponent />
 * </PermissionGate>
 * 
 * Usage requiring all permissions:
 * <PermissionGate 
 *   permissions={["permission:agreement:view", "permission:agreement:edit"]} 
 *   requireAll={true}
 * >
 *   <ProtectedComponent />
 * </PermissionGate>
 */
export async function PermissionGate({ 
  permission,
  permissions,
  requireAll = false,
  children, 
  fallback = null 
}: PermissionGateProps) {
  // Check if valid permissions were provided
  if (!permission && (!permissions || permissions.length === 0)) {
    console.warn("PermissionGate: No valid permissions specified.");
    return <>{fallback}</>;
  }
  
  // Handle single permission case
  if (permission) {
    const hasPermission = await checkPermission(permission);
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  }
  
  // Handle multiple permissions case
  if (permissions && permissions.length > 0) {
    // Check each permission
    const permissionResults = await Promise.all(
      permissions.map(perm => checkPermission(perm))
    );
    
    // If requireAll is true, all permissions must be true
    // Otherwise, at least one permission must be true
    const hasPermission = requireAll 
      ? permissionResults.every(result => result) 
      : permissionResults.some(result => result);
    
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  }
  
  // Fallback case (should never reach here due to initial check)
  return <>{fallback}</>;
}
