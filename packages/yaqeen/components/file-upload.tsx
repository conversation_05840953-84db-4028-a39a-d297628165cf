"use client";

import { UploadSimple } from "@phosphor-icons/react";
import { Button } from "./ui/button";
import { useRef, useState } from "react";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";

interface FileUploadProps {
  onSuccess?: (url: string) => void;
  onError?: (error: string) => void;
  className?: string;
  accept?: string;
  buttonText?: string;
  uploadFunction?: (formData: FormData) => Promise<{
    url: string;
    success?: boolean;
    message?: string;
    error?: string;
  }>;
  hideFileName?: boolean;
}

const defaultUploadFile = async (formData: FormData) => {
  const response = await fetch("/next-api/upload", {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    throw new Error("Failed to upload file");
  }

  return response.json();
};

export function FileUpload({
  onSuccess,
  onError,
  className,
  accept = "image/*,.pdf",
  buttonText = "Upload File",
  uploadFunction = defaultUploadFile,
  hideFileName = false,
}: FileUploadProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileName, setFileName] = useState<string>("");

  const { mutate: upload, isPending } = useMutation({
    mutationFn: uploadFunction,
    onSuccess: (data) => {
      if (data.success !== false && data.url) {
        onSuccess?.(data.url);
      } else {
        throw new Error(data.message || data.error || "Failed to upload file");
      }
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : "Failed to upload file";
      console.error("File upload error:", error);
      toast({
        title: "Upload Failed",
        description: errorMessage,
        variant: "destructive",
      });
      onError?.(errorMessage);
      setFileName("");
    },
  });

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setFileName(file.name);
    const formData = new FormData();
    formData.append("file", file);
    upload(formData);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" accept={accept} />
      <div className="flex items-center gap-2">
        <Button
          type="button"
          variant="outline"
          className="flex items-center gap-2"
          onClick={handleUploadClick}
          disabled={isPending}
        >
          <UploadSimple size={16} />
          <span>{isPending ? "Uploading..." : buttonText}</span>
        </Button>
        {!hideFileName && fileName && <span className="text-sm text-slate-600">{fileName}</span>}
      </div>
    </div>
  );
}
