"use client";

import * as React from "react";

import { cn } from "@/lib/utils";

export type InputProps = React.InputHTMLAttributes<HTMLInputElement>;

interface InputWithIconProps extends InputProps {
  iconButton?: React.ReactNode;
  hasError?: boolean;
  inputClassName?: string;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputWithIconProps>(
  ({ className, type, iconButton, inputClassName, startAdornment, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);

    const handleFocus = () => {
      setIsFocused(true);
    };

    const handleBlur = () => {
      setIsFocused(false);
    };

    return (
      <div
        className={cn(
          "flex h-10 w-full items-center overflow-hidden rounded-md border border-input bg-background text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground read-only:cursor-not-allowed disabled:cursor-not-allowed disabled:opacity-50",
          {
            "": isFocused,
            "border-red-500 ring-red-500": props.hasError,
          },
          className
        )}
      >
        {startAdornment}
        <input
          type={type}
          className={cn(
            "w-full px-3 py-2 read-only:cursor-not-allowed read-only:bg-gray-100 focus:outline-none",
            inputClassName
          )}
          ref={ref}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        <div className="ms-auto text-slate-900">{iconButton}</div>
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };
