"use client";

import * as React from "react";
import {
  format,
  addMonths,
  subMonths,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameDay,
  isToday,
} from "date-fns";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useLocale } from "next-intl";

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  onRangeChange: (startDate: Date | null, endDate: Date | null) => void;
  className?: string;
  minDate?: Date;
  maxDate?: Date;
  title?: string;
}
export function DateRangePicker({
  startDate,
  endDate,
  onRangeChange,
  className,
  minDate,
  maxDate,
  title,
}: DateRangePickerProps) {
  const locale = useLocale();
  const direction = locale === "ar" ? "rtl" : "ltr";
  const [isOpen, setIsOpen] = React.useState(false);
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [hoverDate, setHoverDate] = React.useState<Date | null>(null);

  // Selection state
  const [selecting, setSelecting] = React.useState<"start" | "end" | null>(null);

  // Format the display date range
  const formatDisplayDate = (title?: string) => {
    if (!startDate && !endDate) return title || "Select date range";

    if (startDate && !endDate) {
      return `${title || "Date Range"}: ${format(startDate, "MM/dd/yyyy")}`;
    }

    if (startDate && endDate) {
      return `${title || "Date Range"}: ${format(startDate, "MM/dd/yyyy")} - ${format(endDate, "MM/dd/yyyy")}`;
    }

    return title || "Select date range";
  };

  // Add a function to check if a date is within the allowed range
  const isDateInAllowedRange = (date: Date): boolean => {
    if (minDate && date < minDate) return false;
    if (maxDate && date > maxDate) return false;
    return true;
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    if (!isDateInAllowedRange(date)) return;

    if (!selecting || selecting === "start") {
      onRangeChange(date, null);
      setSelecting("end");
    } else {
      // Ensure end date is after start date
      if (startDate && date < startDate) {
        onRangeChange(date, startDate);
      } else {
        onRangeChange(startDate, date);
      }
      setSelecting("start");
      setIsOpen(false);
    }
  };

  // Handle date hover for preview
  const handleDateHover = (date: Date) => {
    setHoverDate(date);
  };

  // Check if a date is in the selected range
  const isInRange = (date: Date) => {
    if (!startDate || !endDate) {
      if (selecting === "end" && startDate && hoverDate && date >= startDate && date <= hoverDate) {
        return true;
      }
      return false;
    }
    return date >= startDate && date <= endDate;
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };

  const nextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1));
  };

  // Render a month calendar
  const renderMonth = (monthDate: Date) => {
    const monthStart = startOfMonth(monthDate);
    const monthEnd = endOfMonth(monthDate);
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // Get days from previous and next month to fill the calendar grid
    const firstDayOfMonth = monthStart.getDay();
    const lastDayOfMonth = monthEnd.getDay();

    // Create array of days for the month
    const daysArray: (Date | null)[] = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      daysArray.push(null);
    }

    // Add days of the month
    days.forEach((day) => {
      daysArray.push(day);
    });

    // Add empty cells for days after the last day of the month
    for (let i = lastDayOfMonth; i < 6; i++) {
      daysArray.push(null);
    }

    return (
      <div className="space-y-4">
        <div className="text-center font-medium">{format(monthDate, "MMMM yyyy")}</div>
        <div className="grid grid-cols-7 gap-1 text-center">
          {["Su", "Mo", "Tue", "Wed", "Thu", "Fri", "Sa"].map((day) => (
            <div key={day} className="text-sm text-muted-foreground">
              {day}
            </div>
          ))}
          {daysArray.map((day, index) => {
            if (!day) {
              return <div key={`empty-${index}`} className="h-9 w-9" />;
            }

            const isSelected =
              startDate && endDate
                ? isSameDay(day, startDate) || isSameDay(day, endDate)
                : startDate
                  ? isSameDay(day, startDate)
                  : false;

            const isInSelectedRange = isInRange(day);

            const isDisabled = !isDateInAllowedRange(day);
            return (
              <div key={day.toString()} className="relative h-9 w-9 p-0">
                <button
                  type="button"
                  onClick={() => handleDateSelect(day)}
                  onMouseEnter={() => handleDateHover(day)}
                  disabled={isDisabled}
                  className={cn(
                    "flex h-9 w-9 items-center justify-center rounded-md p-0 font-normal aria-selected:opacity-100",
                    isToday(day) && "bg-accent text-accent-foreground",
                    isSelected &&
                      "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                    isInSelectedRange && !isSelected && "bg-primary/10",
                    !isInSelectedRange && !isSelected && "hover:bg-accent hover:text-accent-foreground",
                    isDisabled && "cursor-not-allowed opacity-50 hover:bg-transparent hover:text-muted-foreground"
                  )}
                >
                  {format(day, "d")}
                </button>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !startDate && !endDate && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          <span className="truncate">{formatDisplayDate(title)}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className={cn("flex items-center justify-between p-3", direction === "rtl" && "flex-row-reverse")}>
          <Button
            variant="outline"
            size="icon"
            onClick={direction === "rtl" ? nextMonth : prevMonth}
            className="h-7 w-7"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={direction === "rtl" ? prevMonth : nextMonth}
            className="h-7 w-7"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex gap-2 p-2">
          {renderMonth(currentDate)}
          {renderMonth(addMonths(currentDate, 1))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
