"use client";

import { cn, toUnderscoreCap } from "@/lib/utils";
import { CaretDown } from "@phosphor-icons/react/dist/ssr";
import { CheckIcon } from "lucide-react";
import { Badge } from "./badge";
import { But<PERSON> } from "./button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "./command";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import { Separator } from "./separator";
import { useState } from "react";
import { useTranslations } from 'next-intl';

interface DataFilterProps {
  filterKey?: string;
  options: Array<{ label: string; value: string | number }>;
  disabled?: boolean;
  title?: string;
  isMultiSelect?: boolean;
  value?: string[];
  onFilterChange?: (values: string[]) => void;
}

export function DataFilter({
  filterKey,
  title,
  options,
  disabled,
  isMultiSelect,
  value = [],
  onFilterChange,
}: DataFilterProps) {
  const t = useTranslations('common.filters');
  const [selectedValues, setSelectedValues] = useState<Set<string>>(new Set(value));

  const handleFilterChange = (optionValue: string | number) => {
    const opt = toUnderscoreCap(String(optionValue));
    const newSelectedValues = new Set(selectedValues);

    if (isMultiSelect) {
      if (newSelectedValues.has(opt)) {
        newSelectedValues.delete(opt);
      } else {
        newSelectedValues.add(opt);
      }
    } else {
      newSelectedValues.clear();
      newSelectedValues.add(opt);
    }

    setSelectedValues(newSelectedValues);
    onFilterChange?.(Array.from(newSelectedValues));
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex h-8 min-w-36 justify-between border border-slate-300"
          disabled={disabled}
        >
          {title}
          {selectedValues.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge variant="secondary" className="rounded-sm px-1 font-normal lg:hidden">
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                    {selectedValues.size} {t("selected")}
                  </Badge>
                ) : (
                  options
                    .filter((option) => selectedValues.has(toUnderscoreCap(String(option.value))))
                    .map((option) => (
                      <Badge key={option.value} variant="secondary" className="me-1 rounded-sm px-1 font-normal">
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
          <CaretDown className="shadcn-ml-2" size={16} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[210px] p-0" align="start">
        <Command>
          <CommandInput placeholder={title} />
          <CommandList>
            <CommandEmpty>{t("noResultsFound")}</CommandEmpty>
            <CommandGroup key={filterKey} className="gap-x-2">
              {options.map((option) => {
                const isSelected = selectedValues.has(toUnderscoreCap(String(option.value)));
                return (
                  <CommandItem key={option.value} onSelect={() => handleFilterChange(option.value)} className="gap-x-2">
                    {isMultiSelect ? (
                      <div
                        className={cn(
                          "me-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                          isSelected ? "bg-primary text-primary-foreground" : "opacity-50 [&_svg]:invisible"
                        )}
                      >
                        <CheckIcon className={cn("h-4 w-4")} />
                      </div>
                    ) : (
                      <div className={cn("me-2 flex h-4 w-4 items-center justify-center ")}>
                        {isSelected && <CheckIcon className={cn("h-4 w-4")} />}
                      </div>
                    )}
                    <span>{option.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setSelectedValues(new Set());
                      onFilterChange?.([]);
                    }}
                    className="justify-center text-center "
                  >
                   {t("clearFilters")}
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
