import React, { useState, useEffect } from "react";

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface Props {
  timestamp: string;
}

const CountdownTimer: React.FC<Props> = ({ timestamp }) => {
  const calculateTimeLeft = (): TimeLeft => {
    const difference = +Number(timestamp) * 1000 - +Date.now();
    let timeLeft: TimeLeft = {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
    };

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }
    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState<TimeLeft>(calculateTimeLeft());

  useEffect(() => {
    const timer = setTimeout(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearTimeout(timer);
  });

  const timerComponents: React.JSX.Element[] = [];

  Object.keys(timeLeft).forEach((interval) => {
    if (!timeLeft[interval as keyof TimeLeft]) {
      return;
    }

    timerComponents.push(
      <span key={interval}>
        {timeLeft[interval as keyof TimeLeft]} {interval}{" "}
      </span>,
    );
  });

  return (
    <div>
      {timerComponents.length ? (
        <span className="font-sm text-gray-600">OTP expires in {timerComponents}</span>
      ) : (
        <span>
          OTP has expired.{" "}
          <button className="text-blue-500 underline" onClick={() => history.back()}>
            Go back and try again.
          </button>
        </span>
      )}
    </div>
  );
};

export default CountdownTimer;
