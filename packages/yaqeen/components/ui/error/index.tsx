"use client";

import { useEffect } from "react";
import { useTranslations } from "next-intl";
import { useToast } from "@/lib/hooks/use-toast";

export default function Error({ error }: { error: Error & { digest?: string } }) {
  const t = useTranslations("common");
  const { toast } = useToast();

  useEffect(() => {
    if (window.location.origin !== "https://yaqeen.lumirental.com") {
      if (error.message.includes("Forbidden")) {
        toast({
          title: "Forbidden",
          description: `You don't have permission to access this resource. ${error.message}`,
          variant: "destructive",
        });
      }
    }
  }, [error]);

  return (
    <div className="flex h-[calc(100vh-140px)] flex-col items-center justify-center">
      <div className="space-y-2 text-center">
        <h2 className="text-xl font-semibold">{t("errors.oops")}</h2>
        <p className="text-gray-600">{t("errors.dontWorry")}</p>
      </div>
      <button
        className="mt-4 rounded-md bg-lumi-500 px-4 py-2 text-sm text-white transition-colors hover:bg-lumi-400"
        onClick={() => window.location.reload()}
      >
        {t("tryAgain")}
      </button>
    </div>
  );
}
