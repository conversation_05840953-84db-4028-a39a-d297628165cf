"use client";

import { useLocale } from "next-intl";
import { type ReactNode } from "react";

interface DirectionalIconProps {
  /**
   * The icon to display. Will be automatically flipped for RTL languages
   */
  children: ReactNode;
  /**
   * Additional class name to apply
   */
  className?: string;
  /**
   * Force LTR/RTL direction regardless of locale
   */
  forcedDirection?: "ltr" | "rtl";
}

/**
 * A component that automatically flips icons based on the current locale direction
 * For Arabic (RTL), it will flip left/right icons, so CaretLeft becomes CaretRight and vice versa
 */
export function DirectionalIcon({ children, className, forcedDirection }: DirectionalIconProps) {
  const locale = useLocale();

  // Determine if the current locale is RTL
  const isRtl = forcedDirection === "rtl" || (!forcedDirection && locale === "ar");

  // Apply the appropriate transform based on the direction
  const transform = isRtl ? "scale(-1, 1)" : undefined;

  return (
    <div className={className} style={{ display: "inline-flex", transform }}>
      {children}
    </div>
  );
}
