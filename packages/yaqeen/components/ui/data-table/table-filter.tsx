import { cn, toUnderscoreCap } from "@/lib/utils";
import { CaretDown } from "@phosphor-icons/react/dist/ssr";
import { type Column, type Table } from "@tanstack/react-table";
import { CheckIcon } from "lucide-react";
import { type Route } from "next";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useQueryState } from "nuqs";
import { Badge } from "../badge";
import { Button } from "../button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "../command";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";
import { Separator } from "../separator";
import { DateRangePicker } from "../date-range-picker";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

interface DataTableFilterProps<TData> {
  filterKey: string;
  column?: Column<TData>;
  options?: Array<{
    label: string;
    value: string;
    translationKey?: string;
  }>;
  disabled?: boolean;
  title?: string;
  table: Table<TData>;
  isMultiSelect?: boolean;
  filterType?: string;
  dateRangeTitle?: string;
  translationNamespace?: string;
}

export function DataTableFilter<TData>({
  filterKey,
  column,
  title,
  options,
  table,
  disabled,
  isMultiSelect,
  filterType,
  dateRangeTitle,
  translationNamespace = "Filters",
}: DataTableFilterProps<TData>) {
  const [, setFilterValue] = useQueryState(filterKey, {
    shallow: false,
  });
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams.toString());
  const selectedValues = new Set(column?.getFilterValue() as string[]);
  const tCommon = useTranslations("common.filters");

  const resetPagination = () => {
    table.resetPagination();
    router.push(`${pathname}?${params.toString()}` as Route);
  };

  if (filterType === "daterange") {
    return (
      <DateRangePicker
        className="mr-1 flex h-8 min-w-36 justify-between border border-slate-300"
        startDate={
          column?.getFilterValue() && (column?.getFilterValue() as string[])[0]
            ? new Date((column?.getFilterValue() as string[])[0] ?? "")
            : null
        }
        endDate={
          column?.getFilterValue() && (column?.getFilterValue() as string[])[1]
            ? new Date((column?.getFilterValue() as string[])[1] ?? "")
            : null
        }
        maxDate={new Date()}
        onRangeChange={(start: Date | null, end: Date | null) => {
          const column = table.getColumn(filterKey);
          if (column) {
            const formattedRange = [start ? format(start, "yyyy-MM-dd") : null, end ? format(end, "yyyy-MM-dd") : null];
            column?.setFilterValue(formattedRange);
            void setFilterValue(formattedRange.filter(Boolean).join(","));
          }
        }}
        title={dateRangeTitle}
      />
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="min-w-auto mr-1 flex h-8 justify-between border border-slate-300"
          disabled={disabled}
        >
          {title}
          {selectedValues?.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge variant="secondary" className="rounded-sm px-1 font-normal lg:hidden">
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                    {selectedValues.size} {tCommon("selected")}
                  </Badge>
                ) : (
                  options
                    ?.filter((option) => {
                      return selectedValues.has(toUnderscoreCap(String(option.value)));
                    })
                    .map((option) => (
                      <Badge variant="secondary" key={option.value} className={"mx-1 rounded-sm px-1 font-normal"}>
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
          <CaretDown className="shadcn-ml-2" size={16} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit p-0" align="start">
        <div className="block h-full w-full">
          <Command>
            <CommandInput placeholder={title} />
            <CommandList>
              <CommandEmpty>{tCommon("noResultsFound")}</CommandEmpty>
              <CommandGroup>
                {(options ?? []).map((option) => {
                  const isSelected = selectedValues.has(toUnderscoreCap(String(option.value)));
                  return (
                    <CommandItem
                      key={option.value}
                      onSelect={() => {
                        resetPagination();
                        const opt = toUnderscoreCap(String(option.value));
                        if (isMultiSelect) {
                          if (isSelected) {
                            selectedValues.delete(opt);
                          } else {
                            selectedValues.add(opt);
                          }
                          const filterValues = Array.from(selectedValues);
                          column?.setFilterValue(filterValues.length ? filterValues : undefined);
                          if (filterValues.length) {
                            void setFilterValue(filterValues.join(","));
                          } else {
                            void setFilterValue(null);
                          }
                        } else {
                          // clear all other filters and only select this one
                          column?.setFilterValue([opt]);
                          void setFilterValue(opt);
                        }
                      }}
                    >
                      {isMultiSelect ? (
                        <>
                          <div
                            className={cn(
                              "me-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                              isSelected ? "bg-primary text-primary-foreground" : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <CheckIcon className={cn("h-4 w-4")} />
                          </div>
                        </>
                      ) : !isMultiSelect ? (
                        <>
                          {
                            <div className={cn("mr-2 flex h-4 w-4 items-center justify-center ")}>
                              {isSelected && <CheckIcon className={cn("h-4 w-4")} />}
                            </div>
                          }
                        </>
                      ) : (
                        <></>
                      )}
                      <span>{String(option.label)}</span>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
              {selectedValues.size > 0 && (
                <>
                  <CommandSeparator />
                  <CommandGroup>
                    <CommandItem
                      onSelect={() => {
                        resetPagination();
                        column?.setFilterValue(undefined);
                        void setFilterValue(null);
                      }}
                      className="justify-center text-center "
                    >
                      {tCommon("clearFilters")}
                    </CommandItem>
                  </CommandGroup>
                </>
              )}
            </CommandList>
          </Command>
        </div>
      </PopoverContent>
    </Popover>
  );
}
