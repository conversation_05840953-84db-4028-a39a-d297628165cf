import React from "react";

interface NoDataProps {
  title: string | React.JSX.Element;
  icon: () => React.JSX.Element;
  callback?: () => React.JSX.Element;
}

const NoData: React.FC<NoDataProps> = ({ title, icon, callback }) => {
  return (
    <section className="flex h-[344px] flex-col items-center justify-center rounded-md bg-slate-50 text-center text-2xl font-medium text-slate-500">
      {icon()}
      <h2 className="mt-2 max-md:max-w-full">{title}</h2>
      {callback?.()}
    </section>
  );
};

export default NoData;
