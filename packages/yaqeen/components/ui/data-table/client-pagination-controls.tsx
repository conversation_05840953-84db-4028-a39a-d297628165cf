"use client";


import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CaretDoubleLeft, CaretDoubleRight, CaretDown, CaretLeft, CaretRight } from "@phosphor-icons/react/dist/ssr";
import { Button } from "../button";
import { type Table } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";

interface PaginationControlsProps<TData> {
  table: Table<TData>;
  start: number;
  end: number;
  total: number;
}
export default function ClientPaginationControls<TData>({ table, start, end, total }: PaginationControlsProps<TData>) {
  const t = useTranslations("common.pagination");
  const locale = useLocale();
  const isRTL = locale === "ar";

  return (
    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <div>
          {t('showing', { start, end, total: new Intl.NumberFormat(locale).format(total) })}
        </div>
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8">
              <CaretDown size={16} />
            </DropdownMenuTrigger>
            <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
              {[10, 50, 500].map((size: number) => (
                <DropdownMenuItem
                  key={`size-${size}`}
                  onClick={() => {
                    table.setPageSize(size)
                  }}
                >
                  {size}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
        <Button
          aria-label={t('firstPage')}
          variant="outline"
          className={`hidden size-8 w-10 p-0 lg:flex ${isRTL ? 'rotate-180' : ''}`}
          onClick={() => table.setPageIndex(0)}
          disabled={!table.getCanPreviousPage()}
        >
          <CaretDoubleLeft className="size-4" aria-hidden="true" />
        </Button>
        <Button
          aria-label={t('previousPage')}
          variant="outline"
          size="icon"
          className={`size-8 ${isRTL ? 'rotate-180' : ''}`}
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          <CaretLeft className="size-4" aria-hidden="true" />
        </Button>
        <div className="flex w-16 items-center justify-center text-sm font-medium">
          {table.getState().pagination.pageIndex + 1} {t('of')} {table.getPageCount()}
        </div>
        <Button
          aria-label={t('nextPage')}
          variant="outline"
          size="icon"
          className={`size-8 ${isRTL ? 'rotate-180' : ''}`}
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          <CaretRight className="size-4" aria-hidden="true" />
        </Button>
        <Button
          aria-label={t('lastPage')}
          variant="outline"
          size="icon"
          className={`hidden size-8 lg:flex ${isRTL ? 'rotate-180' : ''}`}
          onClick={() => table.setPageIndex(table.getPageCount() - 1)}
          disabled={!table.getCanNextPage()}
        >
          <CaretDoubleRight className="size-4" aria-hidden="true" />
        </Button>
      </div>
    </div>
  );
}
