"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CaretDoubleLeft, CaretDoubleRight, CaretDown, CaretLeft, CaretRight } from "@phosphor-icons/react/dist/ssr";
import { Button } from "../button";
import { usePagination } from "@/lib/hooks/usePagination";
import { type Table } from "@tanstack/react-table";
import { cn } from "@/lib/utils";
import { useLocale, useTranslations } from "next-intl";

export interface PaginationControlsProps<TData> {
  table: Table<TData>;
  start: number;
  end: number;
  total: number;
  className?: string;
  legacyPage?: boolean;
  onPageSizeChange: (size: number) => void;
}

export function PaginationControls<TData>({
  table,
  start,
  end,
  total,
  className,
  legacyPage = false,
  onPageSizeChange,
}: PaginationControlsProps<TData>) {
  const t = useTranslations("common.pagination");
  const locale = useLocale();
  const isRTL = locale === "ar";
  
  return (
    <div className={cn("flex justify-between", className, isRTL ? 'flex-row-reverse' : '')} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <div>
          {t('showing', { start, end, total: new Intl.NumberFormat(locale).format(total) })}
        </div>
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8">
              <CaretDown size={16} />
            </DropdownMenuTrigger>
            <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
              {[10, 50, 500].map((size: number) => (
                <DropdownMenuItem
                  key={`size-${size}`}
                  onClick={() => {
                    onPageSizeChange(size);
                  }}
                >
                  {size}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className={cn("flex items-center", isRTL ? 'space-x-reverse space-x-2' : 'space-x-2')}>
        <Button
          aria-label={t('firstPage')}
          variant="outline"
          className={cn("hidden size-8 w-10 p-0 lg:flex", isRTL ? 'rotate-180' : '')}
          onClick={() => table.setPageIndex(0)}
          disabled={!table.getCanPreviousPage()}
        >
          <CaretDoubleLeft className="size-4" aria-hidden="true" />
        </Button>
        <Button
          aria-label={t('previousPage')}
          variant="outline"
          size="icon"
          className={cn("size-8", isRTL ? 'rotate-180' : '')}
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          <CaretLeft className="size-4" aria-hidden="true" />
        </Button>
        <div className="flex w-16 items-center justify-center text-sm font-medium">
          {table.getState().pagination.pageIndex + (legacyPage ? 0 : 1)} {t('of')} {table.getPageCount()}
        </div>
        <Button
          aria-label={t('nextPage')}
          variant="outline"
          size="icon"
          className={cn("size-8", isRTL ? 'rotate-180' : '')}
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          <CaretRight className="size-4" aria-hidden="true" />
        </Button>
        <Button
          aria-label={t('lastPage')}
          variant="outline"
          size="icon"
          className={cn("hidden size-8 lg:flex", isRTL ? 'rotate-180' : '')}
          onClick={() => table.setPageIndex(table.getPageCount())}
          disabled={!table.getCanNextPage()}
        >
          <CaretDoubleRight className="size-4" aria-hidden="true" />
        </Button>
      </div>
    </div>
  );
}

export interface PaginationControlsWithServerProps<TData> {
  table: Table<TData>;
  start: number;
  end: number;
  total: number;
  className?: string;
  legacyPage?: boolean;
  onPageSizeChange?: (size: number) => void;
}

export function PaginationControlsWithServer<TData>({
  legacyPage = false,
  table,
  start,
  end,
  total,
  className,
}: PaginationControlsWithServerProps<TData>) {
  const [, setPaginationState] = usePagination({ legacyPage });

  const handlePageSizeChange = (size: number) => {
    setPaginationState({ pageSize: size });
  };

  return (
    <PaginationControls
      table={table}
      start={start}
      end={end}
      total={total}
      className={className}
      legacyPage={legacyPage}
      onPageSizeChange={handlePageSizeChange}
    />
  );
}

export default PaginationControlsWithServer;
