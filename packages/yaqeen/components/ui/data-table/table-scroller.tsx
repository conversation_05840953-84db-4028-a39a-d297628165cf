"use client";

import { ArrowDown, ArrowUp } from "@phosphor-icons/react";

export default function TableScroller() {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="space-y-4">
        <button
          className="flex h-12 w-12 cursor-pointer items-center justify-center rounded-full bg-slate-100 shadow hover:bg-slate-200 hover:shadow-lg "
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
        >
          <ArrowUp color="secondary-foreground" />
        </button>
        <button
          className="flex h-12 w-12 cursor-pointer items-center justify-center rounded-full bg-slate-100 shadow hover:bg-slate-200 hover:shadow-lg"
          onClick={() => window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })}
        >
          <ArrowDown color="secondary-foreground" />
        </button>
      </div>
    </div>
  );
}
