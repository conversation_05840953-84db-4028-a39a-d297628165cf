"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { usePagination } from "@/lib/hooks/usePagination";
import { getFilterInParams } from "@/lib/utils";
import { X } from "@phosphor-icons/react";
import { MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import debounce from "lodash-es/debounce";
import { usePathname, useSearchParams } from "next/navigation";
import { useQueryState } from "nuqs";
import { type ChangeEvent, useRef, useState, useEffect, useCallback } from "react";
import { Input } from "../input";
import { SearchFilters } from "./search-filters";
import { type TSelectItem } from "@/types";
import { type Route } from "next";
import { useLocale } from "next-intl";

interface SearchInputProps {
  placeholder: string;
  suggestions?: string[] | null;
  isTableEmpty?: boolean;
  searchFilters?: { label: string; value: string }[];
}

export const SearchInput = ({
  placeholder: searchPlaceholder,
  suggestions: searchSuggestions,
  isTableEmpty,
  searchFilters = [],
}: SearchInputProps) => {
  const pathname = usePathname();
  const params = Array.from(useSearchParams().entries());
  const searchOption = getFilterInParams(params, searchFilters);
  const locale = useLocale();
  const isRtl = locale === "ar";
  const [selectedOption, setSelectedOption] = useState<TSelectItem>(
    searchOption ?? searchFilters[0] ?? { label: "plateNo", value: "plateNo" }
  );
  const [, setPaginationState] = usePagination({ legacyPage: false });
  const inputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  const [query, setQuery] = useQueryState(selectedOption.value, {
    shallow: false,
  });

  // Create memoized debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      void setQuery(value || "");
      setPaginationState({
        pageNumber: 0,
        pageSize: 10,
      });
    }, 500), // Reduced debounce time for better responsiveness
    [setQuery, setPaginationState]
  );

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(event.target.value);
  };

  const handleSelectOption = (option: TSelectItem) => {
    setSelectedOption(option);
    void setQuery(null);
  };

  // Maintain focus on input after re-rendering
  useEffect(() => {
    if (inputRef.current && isFocused) {
      // Keep the cursor position
      const cursorPosition = inputRef.current.selectionStart;
      inputRef.current.focus();
      // Restore cursor position if available
      if (cursorPosition !== null) {
        inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
      }
    }
  }, [query, isFocused, selectedOption]);

  // keep focus after first render
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputRef]);

  return (
    <div className="relative w-fit">
      {searchPlaceholder && (
        <div className="flex rounded-lg md:w-[400px]">
          {searchFilters?.length ? (
            <SearchFilters
              name="searchfilter"
              defaultValue={query ?? selectedOption?.value}
              options={searchFilters}
              selectedOption={selectedOption}
              setSelectedOption={handleSelectOption}
              className=""
              translationNamespace="bookings.searchFilters"
            />
          ) : (
            <></>
          )}
          <Input
            ref={inputRef}
            className={`ms-auto h-8 ${isRtl ? "rounded-r-none" : "rounded-l-none"}`}
            placeholder={searchPlaceholder}
            defaultValue={query ?? ""}
            onChange={handleSearchChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            startAdornment={!isTableEmpty && <MagnifyingGlass className="h-4 w-4 ltr:ml-2 rtl:mr-2" />}
            iconButton={
              query && (
                <X
                  className="mr-3 cursor-pointer"
                  onClick={() => {
                    if (inputRef.current) {
                      inputRef.current.value = "";
                      inputRef.current.focus();
                    }
                    void setQuery(null);
                  }}
                  size={16}
                />
              )
            }
            disabled={isTableEmpty && !query}
          />
        </div>
      )}
      {searchSuggestions && (
        <div className="absolute top-10 z-10  w-full space-y-2 rounded border bg-white shadow-md">
          {searchSuggestions.length === 0 && <div className="px-4 py-2.5">No vehicles found.</div>}
          {searchSuggestions.map((suggestion) => {
            return (
              <ProgressBarLink
                href={`${pathname}/${encodeURI(suggestion)}` as Route}
                key={suggestion}
                className="flex cursor-pointer items-center gap-2 px-4 py-2.5  hover:bg-slate-100"
              >
                <span>{suggestion}</span>
              </ProgressBarLink>
            );
          })}
        </div>
      )}
    </div>
  );
};
