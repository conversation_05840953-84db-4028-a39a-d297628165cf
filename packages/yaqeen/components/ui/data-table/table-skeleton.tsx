import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@/components/ui/table";
import { CaretDown } from "@phosphor-icons/react/dist/ssr";

export default function TableSkeleton({
  filterCount,
  showPagination = true,
}: {
  filterCount?: number;
  showPagination?: boolean;
}) {
  return (
    <>
      {filterCount && (
        <div className="my-6 flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex gap-x-2">
              {Array.from({ length: filterCount }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-24" />
              ))}
            </div>
          </div>
        </div>
      )}
      <div className="mb-2 rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableCell>
                <Skeleton className="h-full w-full"></Skeleton>
              </TableCell>
              <TableCell>
                <Skeleton className="h-full w-full"></Skeleton>
              </TableCell>
              <TableCell>
                <Skeleton className="h-full w-full"></Skeleton>
              </TableCell>
              <TableCell>
                <Skeleton className="h-full w-full"></Skeleton>
              </TableCell>
              <TableCell>
                <Skeleton className="h-full w-full"></Skeleton>
              </TableCell>
              <TableCell>
                <Skeleton className="h-full w-full"></Skeleton>
              </TableCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[1, 2, 3, 4, 5].map((a) => (
              <TableRow key={a}>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {showPagination && (
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div>
              <Skeleton className="h-6 w-48" />
            </div>
            <div>
              <CaretDown className="h-6" size={16} />
            </div>
          </div>
          <div className="flex gap-x-1">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      )}
    </>
  );
}
