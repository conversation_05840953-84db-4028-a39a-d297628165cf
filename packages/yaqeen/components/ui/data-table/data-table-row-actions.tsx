"use client";

import React from "react";
import { type Row } from "@tanstack/react-table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../dropdown-menu";
import { CalendarPlus, Car, DotsThree, Info, PencilSimple, Prohibit } from "@phosphor-icons/react/dist/ssr";
import { Button } from "../button";
import { DownloadAgreement } from "@/app/(portal)/rental/branches/[id]/bookings/_components/download-agreement";
import { useTranslations } from "next-intl";
import { ProgressBarLink } from "@/components/progress-bar";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
  children?: React.ReactNode;
  showFullMenu?: boolean;
  showMenu?: boolean;
  pickupBranchId?: string;
  rowId?: string;
}

export function DataTableRowActions<TData>({
  row,
  children,
  showFullMenu = true,
  showMenu = true,
  pickupBranchId,
  rowId,
}: DataTableRowActionsProps<TData>) {
  const t = useTranslations("DataTable.RowActions");

  return (
    <div className=" flex items-center justify-end gap-x-2">
      {children}
      {showMenu && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center justify-end gap-x-1">
              <Button variant="outline" className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsThree className="h-4 w-4" />
                <span className="sr-only">{t("openMenu")}</span>
              </Button>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[190px]">
            {showFullMenu ? (
              <>
                <DropdownMenuItem disabled>
                  <CalendarPlus className="mr-2 h-4 w-4" />
                  {t("startAgreement")}
                </DropdownMenuItem>
                <DropdownMenuItem disabled>
                  <PencilSimple className="mr-2 h-4 w-4" />
                  {t("editBooking")}
                </DropdownMenuItem>
                <DropdownMenuItem disabled>
                  <Prohibit className="mr-2 h-4 w-4" />
                  {t("cancelBooking")}
                </DropdownMenuItem>
                <DropdownMenuItem disabled>
                  <Car className="mr-2 h-4 w-4" />
                  {t("assignVehicle")}
                </DropdownMenuItem>
                {row.original &&
                  typeof row.original === "object" &&
                  "status" in row.original &&
                  (row.original.status === "ONGOING" ||
                    row.original.status === "COMPLETED" ||
                    row.original.status === "LATE_RETURN") && (
                    <DownloadAgreement
                      className="border-0 p-0"
                      agreementNo={(row.original as { agreementNo?: string }).agreementNo ?? ""}
                    />
                  )}
              </>
            ) : (
              <>
                {row.original &&
                  typeof row.original === "object" &&
                  "status" in row.original &&
                  (row.original.status === "ONGOING" ||
                    row.original.status === "COMPLETED" ||
                    row.original.status === "LATE_RETURN") && (
                    <DownloadAgreement
                      className="border-0 p-0"
                      agreementNo={(row.original as { agreementNo?: string }).agreementNo ?? ""}
                    />
                  )}
              </>
            )}
            {pickupBranchId && rowId && (
              <DropdownMenuItem>
                <ProgressBarLink
                  href={`/rental/branches/${pickupBranchId}/bookings/${rowId}`}
                  className="flex w-full items-center justify-start"
                >
                  <Info className="mr-2 h-4 w-4" />
                  {t("moreDetails")}
                </ProgressBarLink>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
