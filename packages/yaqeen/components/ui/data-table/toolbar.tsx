"use client";

import { X } from "@phosphor-icons/react";
import { type Table } from "@tanstack/react-table";
import clsx from "clsx";
import { type ReactNode } from "react";
import { Button } from "../button";
import { usePathname, useRouter } from "next/navigation";
import { DataTableFilter } from "./table-filter";
import { type Route } from "next";
import { useTranslations } from "next-intl";

export interface DateRangeFilter {
  startDateKey: string;
  endDateKey: string;
  columnKey: string;
  filterName: string;
}
import { useProgressBar } from "@/components/progress-bar";
import { startTransition } from "react";

export interface FilterOption {
  filterKey: string;
  filterName: string;
  translationKey?: string;
  filterType?: string;
  columnKey: string;
  isMultiSelect: boolean;
  options?: {
    label: string;
    value: string;
    translationKey?: string;
  }[];
  dateRangeTitle?: string;
}

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filters: FilterOption[];
  translationNamespace?: string;
  children?: ReactNode;
  className?: string;
}

export function DataTableToolbar<TData>({
  table,
  filters,
  translationNamespace = "Filters",
  children,
  className,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const isEmpty = table.getCoreRowModel().rows.length === 0;
  const pathname = usePathname();
  const router = useRouter();
  const tFilters = useTranslations("bookings.filters");
  const tCommon = useTranslations("common.filters");
  const progress = useProgressBar();

  return (
    <div className={clsx("flex flex-wrap items-center gap-2", className)}>
      {children}
      <div className=" flex items-center justify-between">
        <div className="flex items-center">
          <div className="flex gap-x-2">
            {filters.map((filter) => {
              if (filter.filterType !== "daterange" && (filter.options?.length ?? 0) === 0) return null;

              const filterDisplayName = filter.translationKey
                ? // @ts-expect-error TODO useTranslations is not typed correctly
                  tFilters(filter.translationKey)
                : filter.filterName;

              // Map options to include translated labels
              const translatedOptions = filter.options?.map((option) => ({
                ...option,
                // @ts-expect-error TODO useTranslations is not typed correctly
                label: option.translationKey ? tFilters(option.translationKey) : option.label,
              }));

              return (
                <DataTableFilter
                  key={filter.filterKey}
                  filterKey={filter.filterKey}
                  filterType={filter.filterType}
                  title={filterDisplayName}
                  table={table}
                  options={translatedOptions}
                  column={table.getColumn(filter.columnKey)}
                  isMultiSelect={filter.isMultiSelect}
                  dateRangeTitle={filter.dateRangeTitle}
                  disabled={isEmpty && !isFiltered}
                  translationNamespace={translationNamespace}
                />
              );
            })}
          </div>
          {isFiltered && (
            <Button
              variant="ghost"
              className="ml-2 flex h-8 items-center px-2 !text-red-700 lg:px-3"
              onClick={() => {
                progress.start();
                startTransition(() => {
                  table.resetColumnFilters();
                  table.resetPagination();
                  router.push(pathname as Route);
                  progress.done();
                });
              }}
            >
              {tCommon("clearFilters")}
              <X className="ml-2" size={16} />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
