import React from "react";
import { RadioGroup, RadioGroupItem } from "./radio-group";
import { Label } from "./label";

interface RadioChipProps extends React.ComponentPropsWithoutRef<typeof RadioGroup> {
  options: { value: string; label: string }[];
  name: string;
}

const RadioChip = React.forwardRef<
  React.ElementRef<typeof RadioGroup>,
  RadioChipProps
>(({ options, name, className, ...props }, ref) => {
  return (
    <RadioGroup
      ref={ref}
      name={name}
      className={"flex gap-4 w-full max-w-4xl h-full p-4 " + className}
      {...props}
    >
      {options.map((option) => (
        <div key={option.value} className="relative flex-1">
          <RadioGroupItem
            value={option.value}
            id={option.value}
            className="peer sr-only"
          />
          <Label
            htmlFor={option.value}
            className="flex flex-col items-center justify-center w-full h-full rounded-lg border-2 border-muted bg-background p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-blue-600 peer-data-[state=checked]:bg-blue-50 cursor-pointer transition-colors"
          >
            <div className="text-sm font-medium peer-data-[state=checked]:font-bold">
              {option.label}
            </div>
          </Label>
        </div>
      ))}
    </RadioGroup>
  );
});

RadioChip.displayName = "RadioChip";

export default RadioChip;
