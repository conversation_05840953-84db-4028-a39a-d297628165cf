import React from "react";
import { useState } from "react";
import { X } from "@phosphor-icons/react/dist/ssr";
import { Input } from "../input";

export interface ChipInputProps {
  name: string;
  label?: string;
  placeHolder?: string;
  defaultValue: string[];
  testId?: string;
  chips: string[];
  setChips: (chips: string[]) => void;
}

const ChipInput = ({ placeHolder, defaultValue, testId, chips, setChips }: ChipInputProps) => {
  const [inputValue, setInputValue] = useState<string>("");

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && inputValue.trim() !== "") {
      event.preventDefault();
      if (!chips.includes(inputValue.trim())) {
        setChips([...chips, inputValue.trim()]);
      }
      setInputValue("");
    }
  };

  const handleDeleteChip = (chipToDelete: string) => {
    setChips(chips.filter((chip) => chip !== chipToDelete));
  };
  return (
    <div className=" w-full">
      <input name={"mytags"} defaultValue={JSON.stringify(chips)} hidden />
      <Input
        placeholder={placeHolder}
        defaultValue={defaultValue}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        data-testid={testId}
      />

      <div className="input-bordered mt-4 flex w-full flex-wrap">
        {chips.map((chip) => (
          <div
            key={chip}
            className="relative z-10 mb-2 mr-2 items-center px-2 py-1 text-sm after:absolute after:left-0 after:top-0 after:z-[-1] after:h-full after:w-full after:rounded-full after:bg-slate-200 after:transition after:content-[''] hover:after:bg-red-200"
          >
            <span>{chip}</span>
            <button
              type="button"
              className="ml-1 h-4 w-4 cursor-pointer rounded-lg bg-transparent text-xs transition-colors hover:bg-red-400"
              onClick={() => {
                handleDeleteChip(chip);
              }}
            >
              <X className=" mt-0.5 h-4 w-4" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChipInput;
