import * as React from "react";
import { <PERSON><PERSON><PERSON>, ChevronsUpDown } from "lucide-react";
import * as RPNInput from "react-phone-number-input";
import flags from "react-phone-number-input/flags";
import "react-phone-number-input/style.css";

import { But<PERSON> } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn, sortCountriesByPriority } from "@/lib/utils";

type PhoneInputProps = Omit<React.ComponentPropsWithoutRef<"input">, "onChange" | "value" | "ref"> &
  Omit<RPNInput.Props<typeof RPNInput.default>, "onChange"> & {
    onChange?: (value: { countryCode: string; phoneNumber: string }) => void;
    onCountryChange?: (countryCode: string) => void;
  };

type CountryEntry = { label: string; value: RPNInput.Country | undefined };

const PhoneInput = React.forwardRef<React.ElementRef<typeof RPNInput.default>, PhoneInputProps>(
  ({ className, onChange, onCountryChange, value, ...props }, ref) => {
    const [selectedCountry, setSelectedCountry] = React.useState<RPNInput.Country | undefined>();
    const [inputValue, setInputValue] = React.useState<RPNInput.Value | string>(value ?? "");

    React.useEffect(() => {
      if (value) {
        const parsedNumber = RPNInput.parsePhoneNumber(value);
        if (parsedNumber?.country) {
          setSelectedCountry(parsedNumber.country);
        }
        setInputValue(value);
      }
    }, [value]);

    const handleInputChange = (value: RPNInput.Value) => {
      setInputValue(value);

      if (value) {
        const parsedNumber = RPNInput.parsePhoneNumber(value);
        const countryCode = parsedNumber?.countryCallingCode ? `+${parsedNumber.countryCallingCode}` : "";
        const phoneNumber = parsedNumber?.nationalNumber ?? value;

        if (parsedNumber?.country) {
          setSelectedCountry(parsedNumber.country);
        }

        onChange?.({ countryCode, phoneNumber });
      }
    };

    const handleCountryChange = (country: RPNInput.Country) => {
      setSelectedCountry(country);
      setInputValue("");

      const countryCode = `+${RPNInput.getCountryCallingCode(country)}`;

      if (onCountryChange) {
        onCountryChange(countryCode);
      }

      // No need to update input value when couintry code change, we have to reset the value
      // if (onChange && inputValue) {
      //   // Convert inputValue to string, ensuring it works with RPNInput.Value type
      //   const phoneNumber = typeof inputValue === "string" ? inputValue : String(inputValue);
      //   onChange({ countryCode, phoneNumber });
      // }
    };

    return (
      <RPNInput.default
        ref={ref}
        className={cn("flex", className)}
        flagComponent={FlagComponent}
        defaultCountry="SA"
        international
        withCountryCallingCode
        countryCallingCodeEditable={false}
        countrySelectComponent={(props: {
          value: RPNInput.Country | undefined;
          onChange: (country: RPNInput.Country) => void;
          disabled?: boolean;
          options: CountryEntry[];
        }) => {
          // Sort the country options with priority order
          const sortedOptions = sortCountriesByPriority(
            props.options.map((option) => ({ ...option, alpha2: option.value, name: option.label }))
          );

          return (
            <CountrySelect
              {...props}
              value={selectedCountry}
              onChange={handleCountryChange}
              disabled={props?.disabled}
              options={sortedOptions}
            />
          );
        }}
        inputComponent={InputComponent}
        smartCaret={false}
        onChange={handleInputChange}
        value={inputValue}
        {...props}
      />
    );
  }
);
PhoneInput.displayName = "PhoneInput";

const InputComponent = React.forwardRef<HTMLInputElement, React.ComponentPropsWithoutRef<"input">>(
  ({ className, ...props }, ref) => (
    <Input className={cn("rounded-e-lg rounded-s-none", className)} {...props} ref={ref} />
  )
);
InputComponent.displayName = "InputComponent";

type CountrySelectProps = {
  disabled?: boolean;
  value: RPNInput.Country | undefined;
  options: CountryEntry[];
  onChange: (country: RPNInput.Country) => void;
};

const CountrySelect: React.FC<CountrySelectProps> = ({
  disabled,
  value: selectedCountry,
  options: countryList,
  onChange,
}) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          className="flex gap-1 rounded-e-none rounded-s-lg border-r-0 px-3 focus:z-10"
          disabled={disabled}
        >
          <FlagComponent country={selectedCountry ?? "SA"} countryName={selectedCountry ?? "Saudi Arabia"} />
          <ChevronsUpDown className={cn("-mr-2 size-4 opacity-50", disabled ? "hidden" : "opacity-100")} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search country..." />
          <CommandList>
            <ScrollArea className="h-72">
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {countryList.map(({ value, label }) =>
                  value ? (
                    <CountrySelectOption
                      key={value}
                      country={value}
                      countryName={label}
                      selectedCountry={selectedCountry}
                      onChange={onChange}
                    />
                  ) : null
                )}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

interface CountrySelectOptionProps extends RPNInput.FlagProps {
  selectedCountry: RPNInput.Country | undefined;
  onChange: (country: RPNInput.Country) => void;
}

const CountrySelectOption: React.FC<CountrySelectOptionProps> = ({
  country,
  countryName,
  selectedCountry,
  onChange,
}) => {
  return (
    <CommandItem className="gap-2" onSelect={() => onChange(country)}>
      <FlagComponent country={country} countryName={countryName} />
      <span className="flex-1 text-sm">{countryName}</span>
      <span className="text-sm text-foreground/50">{`+${RPNInput.getCountryCallingCode(country)}`}</span>
      <CheckIcon className={`ml-auto size-4 ${country === selectedCountry ? "opacity-100" : "opacity-0"}`} />
    </CommandItem>
  );
};

export const FlagComponent: React.FC<RPNInput.FlagProps> = ({ country, countryName }) => {
  const Flag = country ? flags[country] : null;

  return (
    <span className="flex h-4 w-6 overflow-hidden rounded-sm bg-foreground/20">
      {Flag && <Flag title={countryName} />}
    </span>
  );
};

export { PhoneInput };
