"use client";

import type React from "react";

import { useState, useRef, useEffect, useCallback } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export type AutocompleteProps<T> = {
  data?: T[];
  fetchData?: () => Promise<T[]>;
  filterFunction: (items: T[], inputValue: string) => T[];
  renderItem: (item: T, isSelected: boolean) => React.ReactNode;
  getItemValue: (item: T) => string;
  getItemKey: (item: T) => string | number;
  placeholder?: string;
  limit?: number;
  debounce?: number;
  onItemSelect?: (item: T) => void;
  className?: string;
  disabled?: boolean;
};

export function Autocomplete<T>({
  data,
  fetchData,
  filterFunction,
  renderItem,
  getItemValue,
  getItemKey,
  placeholder = "Search...",
  limit = 10,
  debounce = 300,
  onItemSelect,
  className,
  disabled = false,
}: AutocompleteProps<T>) {
  const [inputValue, setInputValue] = useState("");
  const [items, setItems] = useState<T[]>([]);
  const [filteredItems, setFilteredItems] = useState<T[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);
  const debounceTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (!fetchData) {
      if (data) {
        setItems(data);
      }
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const result = await fetchData();
        setItems(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
        setItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    void loadData();
  }, [fetchData, data]);

  const debouncedFilter = useCallback(
    (value: string) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        if (value.trim() === "") {
          setFilteredItems([]);
          setIsOpen(false);
          return;
        }

        const filtered = filterFunction(items, value).slice(0, limit);
        setFilteredItems(filtered);
        setIsOpen(filtered.length > 0);
        setSelectedIndex(-1);
      }, debounce);
    },
    [items, filterFunction, limit, debounce]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    debouncedFilter(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex((prevIndex) => (prevIndex < filteredItems.length - 1 ? prevIndex + 1 : prevIndex));
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0));
    } else if (e.key === "Enter" && selectedIndex >= 0) {
      e.preventDefault();
      const selectedItem = filteredItems[selectedIndex];
      if (selectedItem) {
        handleSelectItem(selectedItem);
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      setIsOpen(false);
    }
  };

  const handleSelectItem = (item: T) => {
    setInputValue(getItemValue(item));
    setIsOpen(false);
    setSelectedIndex(-1);

    if (onItemSelect) {
      onItemSelect(item);
    }
  };

  const handleFocus = () => {
    if (inputValue.trim() !== "" && filteredItems.length > 0) {
      setIsOpen(true);
    }
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(e.target as Node) &&
        listRef.current &&
        !listRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (selectedIndex >= 0 && listRef.current) {
      const selectedElement = listRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: "nearest",
        });
      }
    }
  }, [selectedIndex]);

  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <div className={cn("relative w-full", className)}>
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          className="pr-10"
          aria-label={placeholder}
          aria-expanded={isOpen}
          aria-autocomplete="list"
          aria-controls={isOpen ? "autocomplete-results" : undefined}
          aria-activedescendant={
            selectedIndex >= 0 && filteredItems[selectedIndex]
              ? `result-${getItemKey(filteredItems[selectedIndex])}`
              : undefined
          }
          disabled={disabled || isLoading}
        />
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-0 top-0 h-full w-10 px-0"
          tabIndex={-1}
          aria-hidden="true"
          disabled={disabled || isLoading}
        >
          <Search className="h-4 w-4 text-muted-foreground" />
          <span className="sr-only">Search</span>
        </Button>
      </div>

      {isLoading && (
        <div className="absolute z-10 mt-1 w-full rounded-md border bg-background p-4 text-center shadow-md">
          Loading...
        </div>
      )}

      {error && (
        <div className="absolute z-10 mt-1 w-full rounded-md border border-destructive bg-destructive/10 p-4 text-destructive shadow-md">
          Error: {error}
        </div>
      )}

      {isOpen && filteredItems.length > 0 && (
        <div
          ref={listRef}
          id="autocomplete-results"
          className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-background shadow-md"
          role="listbox"
        >
          {filteredItems.map((item, index) => (
            <div
              key={getItemKey(item)}
              id={`result-${getItemKey(item)}`}
              className={cn("cursor-pointer", selectedIndex === index ? "bg-muted" : "hover:bg-muted/50")}
              onClick={() => handleSelectItem(item)}
              role="option"
              aria-selected={selectedIndex === index}
            >
              {renderItem(item, selectedIndex === index)}
            </div>
          ))}
        </div>
      )}

      {isOpen && inputValue.trim() !== "" && filteredItems.length === 0 && !isLoading && (
        <div className="absolute z-10 mt-1 w-full rounded-md border bg-background p-4 text-center text-muted-foreground shadow-md">
          No results found
        </div>
      )}
    </div>
  );
}
