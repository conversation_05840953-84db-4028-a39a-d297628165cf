"use client";

import { format, addYears, isSameDay } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarBlankIcon } from "@phosphor-icons/react";
import "react-day-picker/style.css";

interface DatePickerProps {
  date: Date;
  setDate: (date: Date) => void;
  name?: string;
  disabled?: boolean;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  disableNextYearDates?: boolean;
}

export function DatePicker({
  date,
  setDate,
  name,
  disabled,
  disablePastDates = false,
  disableFutureDates = false,
  disableNextYearDates = false,
}: DatePickerProps) {
  const [open, setOpen] = useState(false);

  const t = useTranslations("datePicker");
  const locale = useLocale();
  const dateLocale = locale === "ar" ? ar : enUS;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
          disabled={disabled}
        >
          <CalendarBlankIcon className="me-1 size-5" />
          {date ? format(date, "EEEE, dd/MM/yyyy", { locale: dateLocale }) : <span>{t("pickDate")}</span>}
          <input hidden name={name} defaultValue={date?.toString()} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          disabled={(date) => {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // Allow current day to be selected
            if (isSameDay(date, today)) {
              return false;
            }

            // Disable past dates (days before today)
            if (disablePastDates && date < today) {
              return true;
            }

            // Disable future dates (days after today)
            if (disableFutureDates && date > today) {
              return true;
            }

            // Disable dates after one year
            if (disableNextYearDates) {
              const oneYearFromNow = addYears(today, 1);
              return date > oneYearFromNow;
            }

            return false;
          }}
          onSelect={(newDate: Date | undefined) => {
            if (newDate) {
              setDate(newDate);
              setOpen(false);
            }
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
