import { cn } from "@/lib/utils";
import { type HTMLAttributes } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./card";

function Skeleton({ className, ...props }: HTMLAttributes<HTMLDivElement>) {
  return <div className={cn("animate-pulse rounded-md bg-muted", className)} {...props} />;
}

export function CardSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-x-2">
          <Skeleton className="h-8 w-8" />
          <span className="my-auto flex-auto text-sm font-normal leading-4 text-slate-500">
            <Skeleton className="h-4 w-16" />
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex items-center">
        <p className="text-3xl font-bold tracking-tight text-slate-800">
          <Skeleton className="h-8 w-16" />
        </p>
        <div className="ml-auto">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="mt-2 h-2 w-6" />
        </div>
      </CardContent>
    </Card>
  );
}

function CardsSkeleton() {
  return (
    <>
      <CardSkeleton />
      <CardSkeleton />
      <CardSkeleton />
      <CardSkeleton />
    </>
  );
}

function DonutSkeleton() {
  return (
    <div>
      <Skeleton className="mb-4 h-8 w-32" />
      <div className="flex h-full w-full items-center justify-center">
        <Skeleton className="h-[300px] w-[300px]" />
      </div>
    </div>
  );
}

function BarSkeleton() {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <Skeleton className="h-[300px] w-full" />
    </div>
  );
}

export { Skeleton, CardsSkeleton, DonutSkeleton, BarSkeleton };
