"use client";

import { CircleNotch } from "@phosphor-icons/react";
import { useFormStatus } from "react-dom";
import { Button, type ButtonProps } from "./ui/button";
import { type ReactNode } from "react";

export function ContinueButton({
  className,
  children,
  ...rest
}: { className?: string; children?: ReactNode } & ButtonProps) {
  const { pending } = useFormStatus();

  return (
    <Button disabled={pending} className={className} type="submit" {...rest}>
      {pending && <CircleNotch className="mr-2 h-4 w-4 animate-spin" />}
      {children ?? "Continue"}
    </Button>
  );
}
