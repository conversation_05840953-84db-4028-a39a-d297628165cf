"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SidebarHeader, SidebarRail } from "@/components/ui/sidebar";
import * as React from "react";
import { fetchNavigationData } from "./content.mock";
import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { PlatformSwitcher } from "./platform-switcher";
import type { BranchesListRes } from "@/api/contracts/branch-contract";
import type { User } from "next-auth";
import { useParams } from "next/navigation";
import { useLocale } from "next-intl";
import { useTranslations } from "next-intl";

export function AppSidebar({
  side,
  branches,
  user,
  role,
}: {
  side: "left" | "right" | undefined;
  branches: BranchesListRes;
  user: User | null;
  role: string;
}) {
  const params = useParams();
  const locale = useLocale() as "ar" | "en";
  const t = useTranslations("sidebar");
  const navigationData = fetchNavigationData(Number(params?.id ?? branches?.[0]?.id), role, t);

  let branchId = Number(params?.id ?? branches?.[0]?.id);
  if (isNaN(branchId)) {
    branchId = Number(branches?.[0]?.id ?? 0);
  }
  if (branches.every((branch) => branch.id !== branchId)) {
    branchId = Number(branches?.[0]?.id ?? 0);
  }

  return (
    <Sidebar collapsible="icon" side={side}>
      <SidebarHeader>
        <PlatformSwitcher platforms={navigationData.platforms} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain groups={navigationData.navMain} platforms={navigationData.platforms} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser branches={branches} user={user ?? null} />
      </SidebarFooter>
      <SidebarRail locale={locale} />
    </Sidebar>
  );
}
