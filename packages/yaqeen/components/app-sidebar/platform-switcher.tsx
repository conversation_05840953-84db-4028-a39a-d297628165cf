"use client";

import { ChevronsUpDown } from "lucide-react";
import { useAtom } from "jotai";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import PlatformIcon from "../icons/platform-icon";
import { Separator } from "../ui/separator";
import { type Platform } from "./content.mock";
import { activePlatformIdAtom } from "./atoms";

export function PlatformSwitcher({ platforms }: { platforms: Platform[] }) {
  const { isMobile } = useSidebar();
  const [platformId, setPlatformId] = useAtom(activePlatformIdAtom);
  const activePlatform = platforms.find((p) => p.id === platformId);

  const handlePlatformChange = (platform: Platform) => {
    setPlatformId(platform.id);
  };

  if (platforms.length === 1) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" aria-label={platforms[0]?.name ?? ""}>
            <PlatformIcon variant={platforms[0]?.id ?? "rental"} />
            <div className="grid flex-1 text-start text-sm leading-tight">
              <span className="truncate font-semibold">Yaqeen</span>
              <span className="truncate text-xs">{platforms[0]?.name}</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              aria-label="Switch platform"
            >
              {activePlatform && <PlatformIcon variant={activePlatform.id} />}
              <div className="grid flex-1 text-start text-sm leading-tight">
                <span className="truncate font-semibold">Yaqeen</span>
                <span className="truncate text-xs">{activePlatform?.name}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-sm font-bold leading-none text-slate-900">
              Switch platform
            </DropdownMenuLabel>
            <Separator className="my-2" />
            {platforms.map((platform) => (
              <DropdownMenuItem
                className="cursor-pointer gap-2 p-2"
                key={platform.name}
                onClick={() => handlePlatformChange(platform)}
              >
                <PlatformIcon variant={platform.id} />
                {platform.name}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
