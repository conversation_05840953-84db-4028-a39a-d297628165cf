"use client";

import type { BranchesListRes, IBranch } from "@/api/contracts/branch-contract";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { signOutAction } from "@/lib/actions";
import { setUserLocale } from "@/services/locale";
import { SignOut } from "@phosphor-icons/react/dist/ssr";
import { Check, ChevronLeft, ChevronRight, RefreshCcw } from "lucide-react";
import { type User } from "next-auth";
import { useLocale, useTranslations } from "next-intl";
import { useParams } from "next/navigation";

export function NavUser({ branches, user }: { branches: BranchesListRes; user: User | null }) {
  const locale = useLocale() as "en" | "ar";
  const { isMobile } = useSidebar();
  const params = useParams();
  const t = useTranslations("common");
  const branchId = Number(params.id);
  const branch = branches.find((branch) => branch.id === branchId);
  const { open } = useSidebar();

  const [name] = (user?.email?.split("@")[0]?.split(".") ?? []).map((n) => n.charAt(0).toUpperCase() + n.slice(1));

  const userInitials = user?.email
    ?.split("@")[0]
    ?.split(".")
    .map((name) => name?.[0]?.toUpperCase() ?? "")
    .join("");

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton size="lg" aria-label="User menu">
              <div className="flex flex-1 gap-x-2 text-start text-sm leading-tight">
                <div className="flex h-10 w-10 items-center justify-center rounded bg-lime-400 text-center font-semibold">
                  <span className="ms-[-8px]">{userInitials}</span>
                </div>
                {open && (
                  <div className="grid flex-1 flex-col text-start text-sm leading-tight">
                    <span className="truncate text-sm font-medium">{name}</span>
                    <span className="truncate text-sm font-medium">{user?.email}</span>
                  </div>
                )}
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-80 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="gap-2 px-1 py-1.5 text-left text-sm">
                <div className="grid flex-1 truncate text-start text-sm leading-tight">
                  <span className="truncate font-semibold">{user?.name ?? "LUMI"}</span>
                  <span className="truncate text-xs">{user?.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault();
              }}
            >
              <BranchSelector branches={branches} branch={branch} />
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="!hover:bg-transparent flex justify-between text-sm text-slate-600">
              <span>{t("usermenu.changeLanguage")}</span>
              {locale === "en" && (
                <Button className="text-blue-500" variant="link" onClick={() => setUserLocale("ar")}>
                  {t("usermenu.Arabic")}
                </Button>
              )}
              {locale === "ar" && (
                <Button className="text-blue-500" variant="link" onClick={() => setUserLocale("en")}>
                  {t("usermenu.English")}
                </Button>
              )}
            </DropdownMenuItem>

            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Button
                onClick={() => signOutAction()}
                variant="link"
                className="h-full w-full justify-start p-0 font-normal"
              >
                <SignOut className="mr-2 h-4 w-4" /> {t("usermenu.logout")}
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

const BranchSelector = ({ branch, branches }: { branches: BranchesListRes; branch?: IBranch }) => {
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("common");
  const params = useParams();
  const branchId = Number(params.id);

  if (branches.length === 1) {
    return <div>{branch?.name?.[locale]}</div>;
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="flex w-full items-center justify-between gap-2">
          <div>{branch?.name?.[locale]}</div>
          <Button className="ml-auto" variant="secondary">
            <RefreshCcw className="mr-2 h-4 w-4" />
            {t("usermenu.switchBranch")}
          </Button>
        </div>
      </DialogTrigger>

      <DialogContent className="px-0 sm:max-w-[1024px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("usermenu.switchBranch2")}</DialogTitle>
          <DialogDescription>{t("usermenu.selectBranch")}</DialogDescription>
        </DialogHeader>
        <div className="max-h-96 overflow-y-scroll pt-4">
          {branches.map((branch) => (
            <a href={`/rental/branches/${branch.id}`} key={branch.id}>
              <Button className="flex w-full justify-start text-left" variant="link" key={branch.id}>
                {branch.id === branchId ? (
                  <div className="w-10">
                    <Check className="mr-2 h-4 w-4" />
                  </div>
                ) : (
                  <div className="w-10" />
                )}
                <div>{branch.name[locale]}</div>
                <div className="ml-auto">
                  {locale === "ar" ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </div>
              </Button>
            </a>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};
