"use client";

import { useAtom } from "jotai";
import { ChevronRight } from "lucide-react";
import { useState } from "react";
import clsx from "clsx";
import { usePathname } from "next/navigation";

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { ProgressBarLink } from "../progress-bar";
import { activePlatformIdAtom } from "./atoms";
import type { BaseRoute, NavigationGroup, Platform } from "./content.mock";
import { ClientPermissionGate } from "@/components/client-permission-gate";
import { useLocale } from "next-intl";
const constructRoute = (path: BaseRoute, owningPlatform: string): string => {
  return `/${owningPlatform}${path}`;
};

const extractPermissionsFromGroup = (group: NavigationGroup): string[] => {
  const permissions = new Set<string>();
  group.items?.forEach((item) => {
    if (item.permission) permissions.add(item.permission);
    item.permissions?.forEach((p) => permissions.add(p));
  });
  return Array.from(permissions);
};

export function NavMain({ groups, platforms }: { groups: NavigationGroup[]; platforms: Platform[] }) {
  const { open, setOpen } = useSidebar();
  const [openGroup, setOpenGroup] = useState<string | undefined>(groups.find((group) => group.isActive)?.title);
  const pathname = usePathname();
  const [platformId] = useAtom(activePlatformIdAtom);
  const locale = useLocale();
  const activePlatform = platforms.find((p) => p.id === platformId) ?? platforms[0];

  if (!activePlatform) return null;

  const filteredGroups = groups.filter((item) => activePlatform.groups.some((group) => group.path === item.url.path));

  const renderMenuItem = (group: NavigationGroup, subItem: NonNullable<NavigationGroup["items"]>[number]) => {
    const hasPermissionRestriction = subItem.permission || (subItem.permissions?.length ?? 0) > 0;

    const content = (
      <SidebarMenuSubItem key={subItem.title}>
        <SidebarMenuSubButton
          className={clsx(
            "font-normal",
            constructRoute(subItem.url, subItem.owningPlatform) === pathname && "bg-sidebar-accent"
          )}
          asChild
        >
          <ProgressBarLink href={constructRoute(subItem.url, subItem.owningPlatform)}>
            <span>{subItem.title}</span>
          </ProgressBarLink>
        </SidebarMenuSubButton>
      </SidebarMenuSubItem>
    );

    return hasPermissionRestriction ? (
      <ClientPermissionGate
        key={subItem.title}
        permission={subItem.permission}
        permissions={subItem.permissions}
        requireAll={subItem.requireAll}
      >
        {content}
      </ClientPermissionGate>
    ) : (
      content
    );
  };

  const renderGroup = (group: NavigationGroup) => {
    const allPermissions = extractPermissionsFromGroup(group);
    const hasPermissionRestriction = allPermissions.length > 0;

    const groupContent = (
      <Collapsible
        key={group.title}
        asChild
        open={openGroup === group.title}
        onOpenChange={(isOpen) => {
          setOpenGroup(isOpen ? group.title : undefined);
        }}
        className="group/collapsible"
      >
        <SidebarMenuItem className="group/label">
          <CollapsibleTrigger asChild>
            {group.items?.length ? (
              <SidebarMenuButton
                onClick={() => !open && setOpen(true)}
                tooltip={group.title}
                className={clsx(
                  group.items?.some((item) => constructRoute(item.url, item.owningPlatform) === pathname) &&
                    "font-medium text-slate-900"
                )}
              >
                {group.icon}
                <span>{group.title}</span>
                <ChevronRight
                  className={`ms-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 ${locale === "ar" ? "rotate-180" : ""}`}
                />
              </SidebarMenuButton>
            ) : group.permission || (group.permissions?.length ?? 0) > 0 ? (
              <ClientPermissionGate
                permission={group.permission}
                permissions={group.permissions}
                requireAll={group.requireAll}
              >
                <div>
                  <ProgressBarLink
                    href={constructRoute(
                      group.url.path,
                      activePlatform?.groups?.[0]?.owningPlatform ?? activePlatform.id
                    )}
                  >
                    <SidebarMenuButton
                      onClick={() => !open && setOpen(true)}
                      tooltip={group.title}
                      className={clsx(
                        constructRoute(group.url.path, activePlatform.id) === pathname && "font-medium text-slate-900"
                      )}
                    >
                      {group.icon}
                      <span>{group.title}</span>
                    </SidebarMenuButton>
                  </ProgressBarLink>
                </div>
              </ClientPermissionGate>
            ) : (
              <div>
                <ProgressBarLink
                  href={constructRoute(
                    group.url.path,
                    activePlatform?.groups?.[0]?.owningPlatform ?? activePlatform.id
                  )}
                >
                  <SidebarMenuButton
                    onClick={() => !open && setOpen(true)}
                    tooltip={group.title}
                    className={clsx(
                      constructRoute(group.url.path, activePlatform.id) === pathname && "font-medium text-slate-900"
                    )}
                  >
                    {group.icon}
                    <span>{group.title}</span>
                  </SidebarMenuButton>
                </ProgressBarLink>
              </div>
            )}
          </CollapsibleTrigger>
          {group.items?.length ? (
            <CollapsibleContent>
              <SidebarMenuSub>{group.items.map((subItem) => renderMenuItem(group, subItem))}</SidebarMenuSub>
            </CollapsibleContent>
          ) : null}
        </SidebarMenuItem>
      </Collapsible>
    );

    return hasPermissionRestriction ? (
      <ClientPermissionGate key={group.title} permissions={allPermissions} requireAll={false}>
        {groupContent}
      </ClientPermissionGate>
    ) : (
      groupContent
    );
  };

  return (
    <SidebarGroup>
      <SidebarMenu>{filteredGroups.map(renderGroup)}</SidebarMenu>
    </SidebarGroup>
  );
}
