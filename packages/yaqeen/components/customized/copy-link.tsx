"use client";

import { Link as <PERSON>Icon, Check } from "@phosphor-icons/react/dist/ssr";
import React, { useState } from "react";

export default function CopyLink() {
  const [showCheck, setShowCheck] = useState(false);

  const copyUrlHandler = () => {
    console.log("copyUrlHandler");
    void navigator.clipboard.writeText(window.location.href);
    setShowCheck(true);
    setTimeout(() => {
      setShowCheck(false);
    }, 2000);
  };

  return (
    <button
      className="flex items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted"
      onClick={copyUrlHandler}
    >
      {showCheck ? <Check className="h-5 w-5" /> : <LinkIcon className="h-5 w-5" />}
    </button>
  );
}
