"use client";

import { ArrowSquareOut } from "@phosphor-icons/react";
import Link from "next/link";
import React from "react";
import { type Route } from "next";

export default function OpenLink<T extends string>({
  url,
  className,
  target,
}: {
  url: Route<T> | URL;
  className?: string;
  target?: string;
}) {
  return (
    <Link
      href={url as Route<T>}
      target={target ?? "_blank"}
      className={`flex items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted ${className}`}
    >
      <ArrowSquareOut className="h-5 w-5" />
    </Link>
  );
}
