import React, { useState } from "react";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { type TSelectItem } from "@/types";

export interface MySelectProps {
  name: string;
  defaultValue: TSelectItem;
  options: TSelectItem[];
  className?: string;
}

export const MySelect = ({ name, defaultValue, options }: MySelectProps) => {
  const [selectedOption, setSelectedOption] = useState<TSelectItem>(defaultValue);

  const handleChange = (value: string | number) => {
    const option = options.find((option: TSelectItem) => option.value == value);
    setSelectedOption(option ?? defaultValue);
  };

  return (
    <Select name={name} onValueChange={handleChange} value={selectedOption?.value}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder={selectedOption?.label} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {options
            .filter((option: TSelectItem) => option.value !== "" && option.value !== undefined)
            .map((option: TSelectItem, index: number) => (
              <SelectItem value={option.value} key={index}>
                {option.label}
              </SelectItem>
            ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};
