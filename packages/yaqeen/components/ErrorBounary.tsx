'use client';

import type { ErrorBoundaryProps as NextErrorBoundaryProps } from "next/dist/client/components/error-boundary";

import React from "react";

interface ErrorBoundaryState {
  hasError: boolean;
}

class ErrorBoundary extends React.Component<NextErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: NextErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return this.props.errorComponent ? <this.props.errorComponent error={new Error()} reset={() => this.setState({ hasError: false })} /> : null;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;