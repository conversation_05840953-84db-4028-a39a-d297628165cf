# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/packages/yaqeen/node_modules/
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/packages/yaqeen/.next/
/.next/
/out/

# production
/build
/instructions
/mocks

# misc
.DS_Store
*.pem

.env

# Lokalise temporary files
.lokalise-cache

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.idea