{
  "compilerOptions": {
    "target": "es2015",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    /* Strictness */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "checkJs": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "composite": true,
    "tsBuildInfoFile": "./.tsbuildinfo",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"],
      "@rental-components/*": ["./app/(portal)/rental/_components/*"]
    }
  },
  "include": [
    ".eslintrc.json",
    "next-env.d.ts",
    "**/*.ts",
    "**/*.js",
    "**/*.tsx",
    "**/*.jsx",
    ".next/types/**/*.ts",
    "next.config.mjs",
    "**/*.cjs",
    "tailwind.config.ts"
  ],
  "exclude": ["node_modules"]
}
