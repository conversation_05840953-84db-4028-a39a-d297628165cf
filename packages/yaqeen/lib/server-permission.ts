import { getPermissions } from "@/app/(portal)/utils";

/**
 * Server-side utility to check if a user has a specific permission
 *
 * @param permissionId - The permission ID to check (format: "permission:group:subGroup:scope")
 * @returns Promise<boolean> - Whether the user has the permission
 */
export async function checkPermission(permissionId: string): Promise<boolean> {
  try {
    // Fetch user permissions (now returns both nested and flattened)
    const { nested, flattened } = await getPermissions();

    // First, check if the exact permission exists in the flattened list
    if (flattened.includes(permissionId)) {
      return true;
    }

    // If not found in flattened list, check using the nested structure (backward compatibility)
    const parts = permissionId.split(":");
    if (parts.length !== 4 || parts[0] !== "permission") {
      console.warn(`Invalid permission ID format: ${permissionId}`);
      return false;
    }

    const [, group, subGroup, scope] = parts;

    if (typeof group !== "string" || typeof subGroup !== "string") {
      return false;
    }
    return nested[group] !== undefined && nested[group][subGroup] === scope;
  } catch (error) {
    console.error("Error checking permission:", error);
    return false;
  }
}
