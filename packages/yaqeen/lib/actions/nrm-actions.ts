"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import type { CloseNrmRequest, CreateNrmRequest } from "@/api/contracts/rental/nrm-contract";

type OpenNRMState = {
  message: string | null;
  errors: {
    plateNo?: string;
    checkoutBranchId?: string;
    checkinBranchId?: string;
    driverId?: string;
    reasonId?: string;
  };
  success: boolean;
  nrmId?: number;
};

export type CloseNRMState = {
  message: string | null;
  errors: {
    nrmId?: string;
    fuelLevel?: string;
    odometerReading?: string;
    remarks?: string;
  };
  success: boolean;
};

export async function createNrm(prevState: OpenNRMState, formData: FormData): Promise<OpenNRMState> {
  try {
    const plateNo = formData.get("plateNo") as string;
    const checkoutBranchId = parseInt(formData.get("checkoutBranchId") as string, 10);
    const checkinBranchId = parseInt(formData.get("checkinBranchId") as string, 10);

    const driverId = parseInt((formData.get("driverId") as string) || "0", 10);
    const reasonId = parseInt(formData.get("reasonId") as string, 10);
    const remarks = formData.get("remarks") as string;

    const errors: OpenNRMState["errors"] = {};
    if (!plateNo) errors.plateNo = "Plate number is required";
    if (!checkoutBranchId) errors.checkoutBranchId = "Checkout branch is required";
    if (!checkinBranchId) errors.checkinBranchId = "Checkin branch is required";
    if (!reasonId) errors.reasonId = "Reason is required";

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    console.log("checkoutBranchId", checkoutBranchId);

    const nrmRequest: CreateNrmRequest = {
      plateNo,
      checkoutBranch: checkoutBranchId,
      checkinBranch: checkinBranchId,
      driverId,
      reasonId,
      checkoutRemarks: remarks,
    };

    const response = await api.nrm.create({
      body: nrmRequest,
    });

    if (response.status === 200) {
      revalidatePath("/rental/vehicles/needs-prep");
      return {
        message: "NRM created successfully",
        errors: {},
        success: true,
        nrmId: response.body.nrmId,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to create NRM",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error creating NRM:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function closeNrm(_: CloseNRMState, formData: FormData): Promise<CloseNRMState> {
  try {
    const nrmId = formData.get("nrmId") as string;
    const odometerReading = parseInt((formData.get("odometerReading") as string).replace(/,/g, ""), 10);

    const fuelLevel = parseInt(formData.get("fuelLevel") as string, 10);
    const remarks = formData.get("remarks") as string;

    const errors: CloseNRMState["errors"] = {};
    if (!nrmId) errors.nrmId = "NRM ID is required";
    if (!odometerReading) errors.odometerReading = "Odometer reading is required";
    if (!fuelLevel) errors.fuelLevel = "Fuel level is required";

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    const closeRequest: CloseNrmRequest = {
      nrmId: parseInt(nrmId, 10),
      fuelLevel,
      odometerReading,
      remarks,
    };

    const response = await api.nrm.close({
      body: closeRequest,
    });

    if (response.status === 200) {
      revalidatePath("/rental/vehicles/needs-prep");
      return {
        message: `NRM ${response.body.id} has been closed and the vehicle has been moved to out of service.`,
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to close NRM",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error closing NRM:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}
