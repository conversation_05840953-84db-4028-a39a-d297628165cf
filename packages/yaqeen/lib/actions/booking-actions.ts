"use server";

import { api } from "@/api";
import type { QuotePrice } from "@/api/contracts/booking/schema";

export type BookingActionState = {
  message: string | null;
  errors?: {
    englishName?: string;
    arabicName?: string;
  };
  body?: unknown;
  success: boolean;
};

export async function getVehicleUpgradeReaons() {
  try {
    const response = await api.suggestedVehicles.getUpgradeReasons();
    return response;
  } catch (error) {
    console.error("Error get vehicle upgrade reasons:", error);
    throw error;
  }
}

export async function getQuoteWithAddon(
  vehicleGroupId: string,
  offerId: string,
  insuranceId?: string | null,
  addonIds?: number[]
) {
  try {
    const response = await api.pricing.calculatorContract.getQuoteWithAddon({
      params: {
        offerId,
        vehicleGroupId,
      },
      query: {
        ...(insuranceId ? { insuranceId } : {}),
        ...(addonIds ? { addOnIds: addonIds.join(",") } : {}),
      },
    });
    return response;
  } catch (error) {
    console.error("Error get quote with addon:", error);
    throw error;
  }
}

export const getWalkinQuote = async (quoteId: string) => {
  let quoteResponse: QuotePrice | undefined = undefined;
  if (quoteId) {
    try {
      const quoteResponseRaw = await api.pricing.calculatorContract.getQuoteDetail({
        params: { quoteId },
      });
      quoteResponse = quoteResponseRaw.body as QuotePrice;
    } catch (error) {
      console.error("Error fetching quote details:", error);
    }
  }
  return quoteResponse;
};

export async function lockVehicle(plateNo: string) {
  try {
    const response = await api.pricing.vehiclesContract.lockVehicle({
      params: { plateNo },
      body: {},
    });
    return response;
  } catch (error) {
    console.error("Error locking vehicle:", error);
    throw error;
  }
}

export async function cancelBooking(bookingRef: string, message: string) {
  try {
    const response = await api.bookingDetails.cancelBooking({
      params: { bookingRef },
      body: { message },
    });
    return response;
  } catch (error) {
    console.error("Error locking vehicle:", error);
    throw error;
  }
}
