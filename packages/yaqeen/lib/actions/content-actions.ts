"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";

export type UploadResponse = {
  success: boolean;
  message?: string;
  url?: string;
};

export async function uploadFile(formData: FormData): Promise<UploadResponse> {
  try {
    const file = formData.get("file") as File;

    if (!file) {
      return {
        success: false,
        message: "No file provided",
      };
    }

    const response = await api.content.uploadInspectionImage({
      body: {
        file,
      },
      requiresAuth: false,
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: "Failed to upload file",
      };
    }

    revalidatePath("/");

    return {
      success: true,
      url: response.body.data,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to upload file",
    };
  }
}
