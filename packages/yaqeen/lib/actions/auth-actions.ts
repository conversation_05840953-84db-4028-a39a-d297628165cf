"use server";

import { redirect } from "next/navigation";

import { api } from "@/api";
import { signOut } from "@/auth";
import { env } from "@/env";

export async function initiateAuth(currentUrl?: string) {
  let redirectUrl = null;

  try {
    const data = await api.auth.initiate({
      query: {
        callbackUrl: currentUrl ?? env.AUTH_CALLBACK_URL,
      },
      requiresAuth: false,
    });

    if (data.status !== 200) {
      throw new Error(data.body.desc);
    }

    if (data.body.requiresRedirect) {
      redirectUrl = data.body.redirectUrl;
    }
  } catch (error) {
    console.log(error);
  }

  return redirectUrl ? redirect(redirectUrl) : void 0;
}

export async function signOutAction() {
  await api.auth.logout();
  await signOut({ redirectTo: "/auth" });
}
