"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type VehicleStatusType } from "@/api/contracts/rental/availability-contract";

type UpdateVehicleStatusState = {
  message: string | null;
  success: boolean;
};

export async function updateVehicleStatus(
  plateNo: string,
  statusType: VehicleStatusType,
  reasonId?: string
): Promise<UpdateVehicleStatusState> {
  try {
    if (!plateNo) {
      return {
        message: "Plate number is required",
        success: false,
      };
    }

    const response = await api.availability.updateVehicleStatus({
      body: {
        plateNo,
        statusType,
        ...(reasonId && { statusReason: reasonId }),
      },
    });

    if (response.status !== 200) {
      const errorBody = response.body as { message?: string };
      return {
        message: errorBody.message || "Failed to update vehicle status",
        success: false,
      };
    }

    revalidatePath("/rental/vehicles/ready");
    revalidatePath("/rental/vehicles/needs-prep");
    revalidatePath("/rental/vehicles/rented");

    return {
      message: "Vehicle status updated successfully",
      success: true,
    };
  } catch (error) {
    console.error("Failed to update vehicle status:", error);
    return {
      message: "An unexpected error occurred",
      success: false,
    };
  }
}
