"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";

export const closeCashRegister = async (registerId: number) => {
  try {
    const response = await api.cashRegister.close({ body: { registerId } });
    revalidatePath("/rental/financials/cash-register");
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const approveCashRegister = async (registerId: number, bankId: number) => {
  try {
    const response = await api.cashRegister.approve({
      body: { registerId, bankId },
    });
    revalidatePath("/rental/financials/cash-register");
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const depositCashRegister = async (registerId: number) => {
  try {
    const response = await api.cashRegister.deposit({
      body: { registerId },
    });

    revalidatePath("/rental/financials/cash-register");

    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
