"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type MakeConflictError } from "@/api/contracts/fleet/make/make-contract";
import { type InvoiceNoteFormData } from "@/app/(portal)/rental/branches/[id]/financials/_components/invoice-note-modal";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

export async function createInvoiceNote(formData: InvoiceNoteFormData): Promise<State> {
  try {
    const response = await api.invoices[
      formData.invoiceCategory === "CREDIT_NOTE" ? "postCreditNote" : "postDeditNote"
    ]({
      body: {
        agreementNo: formData.agreementNumber ?? "",
        invoiceType: formData.invoiceType ?? "B2C",
        reason: formData.reason,
        issueDate: formData.issueDate.toISOString(),
        amountBeforeVat: formData.creditedAmountBeforeVAT,
        vatPercentage: formData.vatPercentage,
        vatAmount: formData.vatAmount,
        amountAfterVat: formData.totalCredit,
        cancelInvoice: formData.cancelInvoice ?? false,
        invoiceCategory: formData.invoiceCategory ?? "",
      },
      params: {
        externalId: String(formData.originalInvoiceNumber),
      },
    });

    if (response.status === 409) {
      return {
        message:
          typeof response.body === "object" && response.body && "desc" in response.body
            ? String(response.body.desc)
            : "An error occurred",
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200 && response.status !== 201) {
      const errorBody = response.body as MakeConflictError;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    } else {
      revalidatePath("/(portal)/(rental)/branches/[id]/financials/invoices");
    }

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function fetchInvoiceByNumber(invoiceNumber: string) {
  const response = await api.invoices.getInvoice({
    params: {
      invoiceNumber,
    },
  });
  return response;
}

export async function fetchNoteReasons(invoiceCategory: string) {
  const response = await api.invoices.getNoteReason({
    query: {
      invoiceCategory,
    },
  });
  return response;
}

export async function downloadPaymentInvoice(id: string) {
  const response = await api.booking.getPaymentReciept({
    params: {
      id,
      type: "payment",
    },
  });
  return response;
}

export async function downloadRefundInvoice(id: string) {
  const response = await api.booking.getPaymentReciept({
    params: {
      id,
      type: "refund",
    },
  });
  return response;
}
