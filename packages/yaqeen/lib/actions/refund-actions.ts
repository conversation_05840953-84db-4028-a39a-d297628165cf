"use server";

import { api } from "@/api";
import { type RefundMethod } from "@/api/contracts/rental/refund-contract";
import { type RefundData } from "@/app/(portal)/rental/all-bookings/refund-requests/columns";
import { revalidatePath } from "next/cache";

export async function createRefund(formData: FormData, sourceRoute: string) {
  try {
    const bookingId = formData.get("bookingId") as string;
    const amount = Number(formData.get("amount"));
    const branchId = Number(formData.get("branchId"));
    const refundThrough = formData.get("refundThrough") as RefundMethod;

    if (!bookingId) {
      return { success: false, message: "Booking ID is required" };
    }

    if (isNaN(amount) || amount <= 0) {
      return { success: false, message: "Invalid refund amount" };
    }

    if (isNaN(branchId) || branchId <= 0) {
      return { success: false, message: "Invalid branch ID" };
    }

    const response = await api.refund.createRefund({
      body: {
        bookingId,
        amount,
        branchId,
        refundThrough,
      },
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: (response.body as { message?: string })?.message || "Failed to process refund",
      };
    }

    revalidatePath(`/rental/branches/${branchId}/${sourceRoute}`);

    return {
      success: true,
      message:
        refundThrough === "BANK_TRANSFER" ? "Refund request created successfully" : "Refund processed successfully",
      data: response.body,
    };
  } catch (error) {
    console.error("Error creating refund:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}

export async function processRefund(
  refundRequestId: number,
  recipientBankDetail: Omit<RefundData["recipientDetails"], "phoneNumber" | "email">,
  remarks?: string
) {
  const { recipientAccountName, recipientIBAN, recipientBankName, ibanLetterImageUrl } = recipientBankDetail;
  try {
    const response = await api.refund.processRefund({
      body: {
        refundRequestId,
        recipientBankDetail: {
          recipientAccountName: recipientAccountName || "",
          recipientIBAN: recipientIBAN || "",
          recipientBankName: recipientBankName || "",
          ibanLetterImageUrl: ibanLetterImageUrl || "",
          ...(remarks ? { remarks } : {}),
        },
      },
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: (response.body as { message?: string })?.message || "Failed to process refund",
      };
    }

    // Revalidate the refund list page
    revalidatePath("/rental/all-bookings/refund-requests");

    return {
      success: true,
      message: "Refund processed successfully",
      data: response.body,
    };
  } catch (error) {
    console.error("Error processing refund:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}
