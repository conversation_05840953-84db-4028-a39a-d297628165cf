import { useQueryStates, parseAsInteger } from "nuqs";
import { startTransition } from "react";
import { useProgressBar } from "@/components/progress-bar";

type BookingState = {
  dropOffBranchId: number | null;
  dropOffTimestamp: number | null;
};

type InitialValues = {
  dropOffBranchId?: number;
  dropOffTimestamp?: number;
};

export function useBookingDetails(initialValues?: InitialValues) {
  const progress = useProgressBar();
  const [states, setStates] = useQueryStates(
    {
      dropOffBranchId: parseAsInteger.withDefault(initialValues?.dropOffBranchId ?? 0),
      dropOffTimestamp: parseAsInteger.withDefault(initialValues?.dropOffTimestamp ?? 0),
    },
    {
      shallow: false,
      clearOnDefault: false,
    },
  );

  const setBookingQuery = (newState: Partial<BookingState>) => {
    progress.start();
    startTransition(() => {
      void setStates(newState);
      progress.done();
    });
  };

  return {
    dropOffBranchId: states.dropOffBranchId,
    dropOffTimestamp: states.dropOffTimestamp,
    setBookingQuery,
  };
}
