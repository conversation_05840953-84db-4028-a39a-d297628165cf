import { useEffect, useState } from "react";

const useCheckDevice = (size = "xl"): boolean => {
  /**  "2xl": "1336px",
        xl: "1280px",
        lg: "1024px",
        md: "768px",
        sm: "640px", */

  const [isBelow, setIsBelow] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;

      switch (size) {
        case "2xl":
          setIsBelow(width < 1336);
          break;
        case "xl":
          setIsBelow(width < 1280);
          break;
        case "lg":
          setIsBelow(width < 1024);
          break;
        case "md":
          setIsBelow(width < 768);
          break;
        case "sm":
          setIsBelow(width < 640);
          break;
        default:
          setIsBelow(false);
      }
    };

    checkDevice();

    window.addEventListener("resize", checkDevice);

    return () => {
      window.removeEventListener("resize", checkDevice);
    };
  }, [size]);

  return isBelow;
};

export { useCheckDevice };
