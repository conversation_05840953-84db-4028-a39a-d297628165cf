"use client";

import {
  type UseMutationOptions,
  type UseMutationResult,
  type UseQueryOptions,
  type UseQueryResult,
  useMutation,
  useQuery,
} from "@tanstack/react-query";

type FetcherOptions = {
  method?: string;
  headers?: Record<string, string>;
  body?: unknown;
  cache?: RequestCache;
};

export async function fetcher<T>(url: string, options?: FetcherOptions): Promise<T> {
  const response = await fetch(url, {
    method: options?.method || "GET",
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
    body: options?.body ? JSON.stringify(options.body) : undefined,
    cache: options?.cache || "default",
  });

  if (!response.ok) {
    throw new Error(`Request failed with status ${response.status}`);
  }

  return response.json();
}

export function useCustomQuery<T>(
  queryKey: string[],
  url: string,
  options?: Omit<UseQueryOptions<T, Error, T, string[]>, "queryKey" | "queryFn">
): UseQueryResult<T, Error> {
  return useQuery<T, Error, T, string[]>({
    queryKey,
    queryFn: () => fetcher<T>(url),
    ...options,
  });
}

export function useCustomMutation<TData, TVariables>(
  url: string,
  method = "POST",
  options?: Omit<UseMutationOptions<TData, Error, TVariables>, "mutationFn">
): UseMutationResult<TData, Error, TVariables> {
  return useMutation<TData, Error, TVariables>({
    mutationFn: (variables: TVariables) =>
      fetcher<TData>(url, {
        method,
        body: variables,
      }),
    ...options,
  });
}
