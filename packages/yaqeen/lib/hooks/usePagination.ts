import { useProgressBar } from "@/components/progress-bar";
import { parseAsInteger, useQueryStates } from "nuqs";
import { startTransition } from "react";

export const usePagination = ({ legacyPage = false }: { legacyPage?: boolean}) => {
  const progress = useProgressBar();
  const [state, setState] = useQueryStates(
    {
      pageSize: parseAsInteger.withDefault(10),
      pageNumber: parseAsInteger.withDefault(legacyPage ? 1 : 0),
    },
    {
      shallow: false,
      history: "push"
    },
  );

  const setPaginationState = (newState: Partial<{ pageSize: number; pageNumber: number }>) => {
    // when pagination gonna happen, show progress bar
    progress.start();
    startTransition(() => {
      void setState(newState);
      progress.done();
    });
  };

  return [state, setPaginationState] as const;
};
