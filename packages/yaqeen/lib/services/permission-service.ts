import { api } from "@/api";
import { FeaturePermissions } from "@/api/contracts/permission-contract";
import { PermissionGroup } from "../permission-mapper";

/**
 * Permission Service
 * Provides methods to fetch and manage permissions
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export class PermissionService {
  /**
   * Fetch all permissions from the API
   * @returns Promise<PermissionGroup[]> Array of permission groups
   */
  static async fetchAllPermissions(): Promise<PermissionGroup[]> {
    try {
      // Call the permissions API endpoint
      const response = await api.permissions.getAllPermissions({
        requiresAuth: true
      });
      
      if (response.status !== 200) {
        console.error('Failed to load permissions from API:', response.status);
        return [];
      }
      
      // Convert the API response to the expected PermissionGroup format
      return response.body.map(item => ({
        service: item.service,
        feature: item.feature,
        permissions: item.permissions.map(perm => ({
          scope: perm.scope,
          uri: perm.uri,
          method: perm.method,
          roles: perm.roles || []
        }))
      }));
    } catch (error) {
      console.error('Error loading permissions from API:', error);
      return [];
    }
  }
}
