export const mapBookingSource = (source: string, aggregator = "LUMI", locale = "ar") => {
  switch (source) {
    case "ONLINE":
      return locale === "ar" ? "عبر الإنترنت" : "Online";
    case "WALKIN":
      return locale === "ar" ? "زيارة مباشرة" : "Walk-in";
    case "CUSTOMER_SUPPORT":
      return locale === "ar" ? "دعم العملاء" : "Customer Support";
    default:
      return aggregator;
  }
};

// write another maper for bookingtype
// B2C -> Personal
// B2B2C -> "Corporate - coporate discount "
// B2B -> "Debtor - company name "

export const mapBookingType = (bookingType: string, discontCode = "", cName = "", locale = "ar") => {
  switch (bookingType) {
    case "B2C":
      return locale === "ar" ? "شخصي" : "Personal";
    case "B2B2C":
      return locale === "ar"
        ? `شركة ${discontCode ? `- ${discontCode}` : ""}`
        : `Corporate ${discontCode ? `- ${discontCode}` : ""}`;
    case "B2B":
      return locale === "ar" ? `مدين - ${cName ? `${cName}` : ""}` : `Debtor - ${cName ? `${cName}` : ""}`;
    default:
      return bookingType;
  }
};
