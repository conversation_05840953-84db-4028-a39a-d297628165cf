import { createSerializer, parseAsInteger, parseAsString, createSearchParamsCache } from "nuqs/server";

export const searchParams = {
  query: parseAsString,
  pageNumber: parseAsInteger.withDefault(0),
  pageSize: parseAsInteger.withDefault(10),
};

export const serialize = createSerializer(searchParams);

export const bookingSearchParamsCache = createSearchParamsCache({
  sort: parseAsString.withDefault("pickupDateTime"),
  order: parseAsString.withDefault("asc"),
  pageSize: parseAsInteger.withDefault(10),
  pageNumber: parseAsInteger.withDefault(0),
  bookingNo: parseAsString.withDefault(""),
  mobileNumber: parseAsString.withDefault(""),
  driverName: parseAsString.withDefault(""),
  status: parseAsString.withDefault(""),
  source: parseAsString.withDefault(""),
  paymentStatusFilter: parseAsString.withDefault(""),
  pickupDateRangeStart: parseAsString.withDefault(""),
  pickupDateRangeEnd: parseAsString.withDefault(""),
  dropOffDateRangeStart: parseAsString.withDefault(""),
  dropOffDateRangeEnd: parseAsString.withDefault(""),
  pickupBranch: parseAsString.withDefault(""),
  dropOffBranch: parseAsString.withDefault(""),
  agreementNo: parseAsString.withDefault(""),
  pickUpBranchIds: parseAsString.withDefault(""),
  dropOffBranchIds: parseAsString.withDefault(""),
});

export const bookingDetailsSearchParamsCache = createSearchParamsCache({
  bookingNo: parseAsInteger.withDefault(1),
});
