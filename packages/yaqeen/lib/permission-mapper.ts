import type { KeycloakPermission, KeycloakPolicy, MappedPermission } from "@/api/contracts/keycloak-contract";
import { PermissionService } from "./services/permission-service";

// Define the structure of your permission files
interface PermissionItem {
  scope: string;
  uri: string;
  method: string;
  description?: string;
  roles?: string[];
}

// Export the interface so it can be used by the permission service
export interface PermissionGroup {
  service: string;
  feature: string;
  permissions: PermissionItem[];
}

// Define the structure for mapped permissions
interface MappedUriPermission {
  service: string;
  feature: string;
  scope: string;
  uri: string;
  method: string;
  description?: string;
}

export interface MappedPermissionWithPolicies extends MappedPermission {
  policyId: string;
  policyName: string;
  policies: KeycloakPolicy[];
}

/**
 * Parse a Keycloak permission name into its components
 * Format: permission:service:feature:scope
 */
export function parsePermissionName(name: string): {
  service: string;
  feature: string;
  scope: string;
} | null {
  const parts = name.split(":");
  if (parts.length !== 4) return null;

  // Ensure all parts exist before returning
  const [_, service, feature, scope] = parts;
  if (!service || !feature || !scope) return null;

  return {
    service,
    feature,
    scope,
  };
}

/**
 * Load all permission definitions
 * This function first tries to fetch permissions from the API,
 * and falls back to reading from JSON files if the API call fails
 */
export async function loadAllPermissionDefinitions(): Promise<PermissionGroup[]> {
  try {
    // First, try to fetch permissions from the API
    const apiPermissions = await PermissionService.fetchAllPermissions();

    // If we got permissions from the API, return them
    if (apiPermissions && apiPermissions.length > 0) {
      console.log(`Successfully loaded ${apiPermissions.length} permission groups from API`);
      return apiPermissions;
    }

    // If API call failed or returned empty, fall back to file-based loading
    console.log("Falling back to file-based permission loading");
    return [];
  } catch (error) {
    console.error("Error loading permissions from API, falling back to files:", error);
    return [];
  }
}

/**
 * Map Keycloak permissions to application URIs
 * This function takes Keycloak permission names and maps them to URIs defined in the JSON files
 *
 * @param keycloakPermissionNames - Array of permission names from Keycloak in format "permission:service:feature:scope"
 * @param appPermissions - Application permission definitions from JSON files
 */
export function mapKeycloakPermissionNames(
  keycloakPermissionNames: string[],
  appPermissions: PermissionGroup[]
): {
  keycloakPermission: { name: string };
  appPermissions: MappedUriPermission[];
}[] {
  const mappedPermissions: {
    keycloakPermission: { name: string };
    appPermissions: MappedUriPermission[];
  }[] = [];

  for (const permissionName of keycloakPermissionNames) {
    const parsedPermission = parsePermissionName(permissionName);
    if (!parsedPermission) continue; // Skip permissions that don't follow the standard format

    const { service, feature, scope } = parsedPermission;

    // Find matching service and feature in app permissions
    const servicePermissions = appPermissions.find((p) => p.service === service && p.feature === feature);

    if (!servicePermissions) continue;

    // Find all URIs that match the scope
    const matchingUriPermissions = servicePermissions.permissions
      .filter((p) => p.scope === scope)
      .map((p) => ({
        service,
        feature,
        scope,
        uri: p.uri,
        method: p.method,
        description: p.description,
      }));

    if (matchingUriPermissions.length > 0) {
      mappedPermissions.push({
        keycloakPermission: { name: permissionName },
        appPermissions: matchingUriPermissions,
      });
    }
  }

  return mappedPermissions;
}

/**
 * Extract permission names from Keycloak permission objects
 */
export function extractPermissionNames(keycloakPermissions: KeycloakPermission[]): string[] {
  return keycloakPermissions.map((p) => p.name);
}

/**
 * Map Keycloak permission objects to application URIs
 */
export async function mapKeycloakPermissions(
  keycloakPermissions: KeycloakPermission[],
  policies?: KeycloakPolicy[]
): Promise<MappedPermissionWithPolicies[]> {
  try {
    // 1. Extract permission names from Keycloak permissions
    const permissionNames = extractPermissionNames(keycloakPermissions);
    console.log(`Found ${permissionNames.length} permission names:`, permissionNames);

    // 2. Load permission definitions from API
    const appPermissions = await loadAllPermissionDefinitions();
    console.log(`Loaded ${appPermissions.length} permission groups from API`);

    // 3. Map permission names to URIs
    const mappedPermissions = permissionNames
      .map((name) => {
        const parsedPermission = parsePermissionName(name);
        if (!parsedPermission) {
          console.log(`Could not parse permission name: ${name}`);
          return null;
        }

        const { service, feature, scope } = parsedPermission;
        console.log(`Processing permission: service=${service}, feature=${feature}, scope=${scope}`);

        // Find matching service and feature in app permissions
        const servicePermissions = appPermissions.find((p) => p.service === service && p.feature === feature);

        if (!servicePermissions) {
          console.log(`No service permissions found for ${service}:${feature}`);
          return null;
        }

        // Find all URIs that match the scope
        const matchingUriPermissions = servicePermissions.permissions
          .filter((p) => p.scope === scope)
          .map((p) => ({
            service,
            feature,
            scope,
            uri: p.uri,
            method: p.method,
            description: p.description,
          }));

        if (matchingUriPermissions.length === 0) {
          console.log(`No matching URI permissions found for ${name}`);
          return null;
        }

        // Find the corresponding Keycloak permission object
        const keycloakPermission = keycloakPermissions.find((p) => p.name === name);
        if (!keycloakPermission) {
          console.log(`No Keycloak permission found for ${name}`);
          return null;
        }

        // Find policies associated with this permission
        const permissionPolicies: KeycloakPolicy[] = [];
        if (policies && keycloakPermission.policies) {
          for (const policyId of keycloakPermission.policies) {
            const policy = policies.find((p) => p.id === policyId);
            if (policy) {
              permissionPolicies.push(policy);
              console.log(`Found policy ${policy.name} for permission ${name}`);
            }
          }
        }

        return {
          keycloakPermission,
          appPermissions: matchingUriPermissions,
          policies: permissionPolicies,
        };
      })
      .filter((item): item is MappedPermissionWithPolicies => item !== null);

    mappedPermissions.forEach((perm) => {
      const permissionName = perm?.keycloakPermission?.name; // e.g., "permission:agreement:agreement-extension:read"

      // Strip "permission:" and prepend "policy:role:"
      const stripped = permissionName.replace(/^permission:/, "");
      const expectedPolicyName = `policy:role:${stripped}`;

      // Find the policy
      const matchedPolicy = policies?.find((p) => p.name === expectedPolicyName);

      if (matchedPolicy) {
        perm.policyId = matchedPolicy.id;
        perm.policyName = matchedPolicy.name;
      }
    });

    console.log(`Successfully mapped ${mappedPermissions.length} permissions`);
    return mappedPermissions;
  } catch (error) {
    console.error('Error mapping permissions:', error);
    return [];
  }
}

/**
 * Get roles associated with a permission through its policies
 */
export function getRolesForPermission(permission: MappedPermissionWithPolicies): string[] {
  const roles: string[] = [];

  // Extract roles from policies
  permission.policies.forEach((policy) => {
    if (policy.config?.roles) {
      try {
        // Parse the roles JSON string from the config
        const rolesConfig = JSON.parse(policy.config.roles);
        if (Array.isArray(rolesConfig)) {
          rolesConfig.forEach((role) => {
            if (role.id) {
              // Role ID in Keycloak is usually in the format "realm-role/{roleName}"
              const roleName = role.id.split("/").pop();
              if (roleName) roles.push(roleName);
            }
          });
        }
      } catch (error) {
        console.error("Error parsing roles from policy config:", error);
      }
    }
  });

  return [...new Set(roles)]; // Remove duplicates
}
