import NextAuth, { type NextAuthConfig, type User } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import { authConfig } from "./auth.config";

const config = {
  ...authConfig,
  providers: [
    Credentials({
      name: "credentials",
      async authorize(credentials) {
        if ("token" in credentials) {
          const user = {
            ...credentials,
            provider: "sso",
            expiresAt: Math.floor(Date.now() / 1000 + (credentials.expiresIn as number)),
          };

          return user as User;
        }

        return null;
      },
    }),
  ],
} satisfies NextAuthConfig;

export const { auth, signIn, signOut } = NextAuth(config);
