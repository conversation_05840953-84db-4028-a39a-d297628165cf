import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  /*
   * Serverside Environment variables, not available on the client.
   * Will throw if you access these variables on the client.
   */
  server: {
    API_URL: z.string().url(),
    AUTH_CALLBACK_URL: z.string().url().optional(),
    AUTH_SECRET: z.string().min(1),
  },
  /*
   * Environment variables available on the client (and server).
   *
   * 💡 You'll get type errors if these are not prefixed with NEXT_PUBLIC_.
   */
  client: {
    // NEXT_PUBLIC_EXAMPLE_CLIENT_KEY: z.string().min(1),
  },
  /*
   * Due to how Next.js bundles environment variables on Edge and Client,
   * we need to manually destructure them to make sure all are included in bundle.
   *
   * 💡 You'll get type errors if not all variables from `server` & `client` are included here.
   */
  runtimeEnv: {
    API_URL: process.env.API_URL,
    AUTH_CALLBACK_URL: process.env.AUTH_CALLBACK_URL,
    AUTH_SECRET: process.env.AUTH_SECRET,
  },

  // Skip validation only if SKIP_ENV_VALIDATION is set to "1" during build time
  skipValidation: process.env.SKIP_ENV_VALIDATION === "1",
  emptyStringAsUndefined: true,
});
