Component: web
SubComponent: yaqeen

readinessProbePath: /ping
livenessProbePath: /ping

hpa:
  enabled: true
  targetCPUUtilizationPercentage: 95
  TargetAvgMemoryUtilization: 95
replicaCount: 2
maxReplicas: 2

secret_variable:
  enabled: true
  data:
    - KEYCLOAK_CLIENT_ID
    - AUTH_SECRET

ingress:
  enabled: true
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 10m
    nginx.ingress.kubernetes.io/proxy-buffer-size: "320k"
    nginx.ingress.kubernetes.io/large-client-header-buffers: "32 320k"
