environment: staging
service:
  type: ClusterIP
  scheme: HTTP
  port: 8080

hpa:
  enabled: true
  targetCPUUtilizationPercentage: 95
  TargetAvgMemoryUtilization: 95
replicaCount: 2
maxReplicas: 2

resources:
  requests:
    cpu: 50m
    memory: 100Mi
  limits:
    cpu: 200m
    memory: 256Mi

environment_variable:
  enabled: true
  data:
    API_URL: "http://lumi-api"
    APP_NAME: "web-yaqeen"
    APP_ENV: "staging"
    PORT: "8080"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: ingress-nginx-web
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  hosts:
    yaqeen-staging.lumirental.com:
      - /
