environment: dev
service:
  type: ClusterIP
  scheme: HTTP
  port: 8080

resources:
  requests:
    cpu: 50m
    memory: 100Mi
  limits:
    cpu: 200m
    memory: 256Mi

environment_variable:
  enabled: true
  data:
    APP_NAME: "web-yaqeen"
    APP_ENV: "development"
    PORT: "8080"
    API_URL: "https://api-dev.lumirental.com"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: ingress-nginx-web
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  hosts:
    yaqeen-dev.lumirental.com:
      - /
