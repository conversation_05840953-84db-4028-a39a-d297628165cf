FROM 639855326882.dkr.ecr.me-south-1.amazonaws.com/docker.io/library/node:21-alpine3.18 as base

FROM base AS deps
WORKDIR /app/packages/yaqeen
COPY packages/yaqeen/package*.json ./
RUN npm ci --legacy-peer-deps

FROM base AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/packages/yaqeen/node_modules ./packages/yaqeen/node_modules

ENV NODE_ENV production
ENV TZ="Asia/Riyadh"

RUN cd packages/yaqeen && npm run build

FROM base AS runner
WORKDIR /app

COPY --from=builder /app/packages/yaqeen/public ./public
RUN mkdir .next
COPY --from=builder /app/packages/yaqeen/.next/standalone ./
COPY --from=builder /app/packages/yaqeen/.next/static ./.next/static

ENV TZ="Asia/Riyadh"

CMD ["node", "server.js"]