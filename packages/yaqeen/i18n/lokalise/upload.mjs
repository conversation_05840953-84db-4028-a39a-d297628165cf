import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import get<PERSON>okalise<PERSON><PERSON> from "./index.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function uploadTranslations() {
  const lokaliseApi = getLokaliseApi();

  try {
    const messagesDir = path.join(process.cwd(), "i18n/messages/locale");
    const files = fs.readdirSync(messagesDir);

    for (const file of files) {
      const filePath = path.join(messagesDir, file);

      // Skip if not a file or not a JSON file
      if (!fs.statSync(filePath).isFile() || !file.endsWith(".json")) {
        continue;
      }

      try {
        const content = fs.readFileSync(filePath, "utf8");
        const lang = file.replace(".json", "");
        const data_base64 = Buffer.from(content).toString("base64");

        await lokaliseApi.files().upload("23077453671b89cb2c2a33.71262443", {
          data: data_base64,
          filename: file,
          lang_iso: lang,
          convert_placeholders: false,
          detect_icu_plurals: true,
          tags: ["next-intl"],
        });

        console.log(`✓ Uploaded ${file} for language ${lang} successfully.`);
      } catch (fileError) {
        console.error(`× Failed to process ${file}:`, fileError.message);
      }
    }
  } catch (error) {
    console.error("Error accessing translations directory:", error.message);
    process.exit(1);
  }
}

uploadTranslations();
