{"fleetManagement": {"all-vehicles": {"searchPlaceholder": "Search by plate no", "emptyMessage": "No vehicles found", "column": {"plateNo": "Plate No.", "group": "Group", "vehicle": "Vehicle", "category": "Category", "statusAndSubStatus": "Status & Sub Status", "location": "Location", "serviceType": "Service Type"}, "filters": {"serviceType": "Service Type", "location": "Location", "group": "Group", "status": "Status", "subStatus": "Sub Status", "category": "Category", "vehicle": "Vehicle", "model": "Model"}, "tabs": {"overview": "Vehicle Overview", "details": "Details", "condition": "Condition", "history": "History", "documents": "Documents"}}}}