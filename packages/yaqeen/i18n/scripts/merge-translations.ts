import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const LOCALE_DIR = path.join(__dirname, "../messages/locale");
const SUPPORTED_LOCALES = ["en", "ar"];

type TranslationObject = {
  [key: string]: string | number | boolean | TranslationObject;
};

function mergeTranslations(): void {
  SUPPORTED_LOCALES.forEach((locale) => {
    const mainFilePath = path.join(LOCALE_DIR, `${locale}.json`);
    let mainTranslations: TranslationObject = {};

    if (fs.existsSync(mainFilePath)) {
      mainTranslations = JSON.parse(fs.readFileSync(mainFilePath, "utf8"));
    }

    const projectFolders = fs
      .readdirSync(LOCALE_DIR, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    projectFolders.forEach((folder) => {
      const projectLocaleFile = path.join(LOCALE_DIR, folder, `${locale}.json`);

      if (!fs.existsSync(projectLocaleFile)) {
        return;
      }

      try {
        const projectTranslations = JSON.parse(fs.readFileSync(projectLocaleFile, "utf8")) as TranslationObject;
        Object.assign(mainTranslations, projectTranslations);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`Error processing ${projectLocaleFile}:`, errorMessage);
      }
    });

    fs.writeFileSync(mainFilePath, JSON.stringify(mainTranslations, null, 2));

    console.log(`Successfully merged translations for ${locale} locale`);
  });
}

mergeTranslations();
