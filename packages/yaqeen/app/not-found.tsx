"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, SmileyXEyes } from "@phosphor-icons/react/dist/ssr";
import { useRouter } from "next/navigation";

export default function NotFound() {
  const router = useRouter();

  return (
    <main className="flex h-screen flex-col items-center justify-center gap-2">
      <SmileyXEyes className="h-10 w-10 text-lumi-600" />
      <h2 className="text-xl font-semibold">404 Not Found</h2>
      <p>Could not find the requested page.</p>
      <Button
        className="mt-4 rounded-md bg-lumi-500 px-4 py-2 text-sm text-white transition-colors hover:bg-lumi-400"
        onClick={() => router.back()}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Go Back
      </Button>
    </main>
  );
}
