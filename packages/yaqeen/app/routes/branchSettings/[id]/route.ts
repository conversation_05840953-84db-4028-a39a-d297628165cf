import { api } from "@/api";
import { NextResponse } from "next/server";

export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;
  const body = await request.json();

  const backendResponse = await api.branch.updateBranch({
    params: { id },
    body: body,
  });

  if (backendResponse.status === 200) {
    return NextResponse.json({ ...backendResponse, status: 200 });
  } else if (backendResponse.status === 404) {
    return NextResponse.json({ ...backendResponse, status: 404 });
  } else {
    return NextResponse.json({ ...backendResponse, status: 500 });
  }
}
