"use client";

import {useState, useEffect} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {ShieldCheck} from "@phosphor-icons/react";
import {CreateRoleDrawer} from "./create-role-drawer";

/**
 * Button component that opens the Create Role drawer
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function CreateRoleButton() {
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Force drawer to close when component unmounts
  useEffect(() => {
    return () => {
      setDrawerOpen(false);
    };
  }, []);

  return (
    <>
      <Button
        onClick={() => setDrawerOpen(true)}
        className="flex items-center"
      >
        <ShieldCheck className="mr-2 h-4 w-4"/>
        Create Role
      </Button>

      <CreateRoleDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
      />
    </>
  );
}
