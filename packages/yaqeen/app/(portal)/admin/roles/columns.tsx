"use client";

import {type ColumnDef} from "@tanstack/react-table";
import {Badge} from "@/components/ui/badge";
import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {Check, X} from "@phosphor-icons/react/dist/ssr";

/**
 * Columns definition for the Keycloak roles table
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export const columns: ColumnDef<KeycloakRole>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({row}) => {
      return <div className="font-medium">{row.getValue("name")}</div>;
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({row}) => {
      return <div>{row.getValue("description") || "-"}</div>;
    },
  },
  {
    accessorKey: "composite",
    header: "Composite",
    cell: ({row}) => {
      const isComposite = row.getValue("composite");

      if (isComposite) {
        return (
          <div className="flex items-center">
            <Badge variant="default" className="flex items-center gap-1 bg-blue-100 text-blue-800">
              <Check className="h-3.5 w-3.5"/>
              <span>Yes</span>
            </Badge>
          </div>
        );
      } else {
        return (
          <div className="flex items-center">
            <Badge variant="outline" className="flex items-center gap-1">
              <X className="h-3.5 w-3.5"/>
              <span>No</span>
            </Badge>
          </div>
        );
      }
    },
  },
];
