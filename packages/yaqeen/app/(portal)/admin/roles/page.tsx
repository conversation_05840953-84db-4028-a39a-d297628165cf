import {api} from "@/api";
import {Warning} from "@phosphor-icons/react/dist/ssr";
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert";
import {SearchForm} from "./search-form";
import {Pagination} from "../users/pagination";
import {RoleDataTable} from "./role-data-table";
import Header from "@/app/(portal)/rental/tariff/_components/Header";
import {CreateRoleButton} from "./create-role-button";

/**
 * Keycloak Roles List Page with server-side data fetching and client-side pagination
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  searchParams,
}: {
  params: Promise<object>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // Fetch all roles server-side
  const rolesResponse = await api.keycloak.getAllRealmRoles({
    requiresAuth: true,
  });

  if ('code' in rolesResponse.body) {
    return (
      <Alert variant="destructive" className="mb-6 mx-6 mt-6">
        <Warning className="h-4 w-4"/>
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to fetch roles from Keycloak server.
          <div className="mt-2 text-xs">
            <details>
              <summary>Error details</summary>
              <pre className="mt-2 whitespace-pre-wrap text-xs">{rolesResponse.body.desc}</pre>
            </details>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  const searchParamsResult = await searchParams;

  // Get pagination and search parameters
  const pageParam = searchParamsResult.page;
  const searchParam = searchParamsResult.search;
  const pageSize = 10;

  // Convert to appropriate types with 1-based page number
  const pageNumber = typeof pageParam === 'string' ? Math.max(1, parseInt(pageParam, 10)) : 1;
  const search = typeof searchParam === 'string' ? searchParam : '';

  // Filter and sort roles
  const filteredRoles = search
    ? rolesResponse.body.filter(
      (role) =>
        role.name.toLowerCase().includes(search.toLowerCase()) ||
        (role.description?.toLowerCase() || "").includes(search.toLowerCase())
    )
    : rolesResponse.body;

  const sortedRoles = [...filteredRoles].sort((a, b) => a.name.localeCompare(b.name));

  return (
    <div className="grid grid-rows-[auto_1fr]">
      <Header
        pageName="Roles"
        subTitle="Manage Users & Roles"
        actions={<CreateRoleButton/>}
      />

      <div className="px-6 py-6">
        <SearchForm/>

        <RoleDataTable
          data={sortedRoles}
          pageNumber={pageNumber}
          pageSize={pageSize}
          searchQuery={search}
        />

        <Pagination
          currentPage={pageNumber}
          pageSize={pageSize}
          totalItems={sortedRoles.length}
        />
      </div>
    </div>
  );
}
