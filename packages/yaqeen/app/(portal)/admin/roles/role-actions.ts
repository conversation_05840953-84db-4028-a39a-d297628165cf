"use server";

import {revalidatePath} from "next/cache";
import {api} from "@/api";

/**
 * Server action to create a new Keycloak role
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function createRole(roleData: {
  name: string;
  description?: string;
}) {
  try {
    const response = await api.keycloak.createRealmRole({
      body: {
        name: roleData.name,
        description: roleData.description,
      },
      requiresAuth: true,
    });

    if (response.status === 201) {
      revalidatePath("/admin/roles");
      return {success: true, roleName: roleData.name};
    } else {
      console.error("Failed to create role:", response.status);
      return {success: false, error: `Failed to create role: ${response.status}`};
    }
  } catch (error) {
    console.error("Error creating role:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}
