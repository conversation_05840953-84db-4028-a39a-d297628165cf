"use client";

import {useState} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {Users} from "@phosphor-icons/react/dist/ssr";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {ManageUsersDialog} from "./manage-users-dialog";
import {useRouter} from "next/navigation";

interface ManageUsersButtonProps {
  roleName: string;
  usersInRole: KeycloakUser[];
}

/**
 * Button component to open the manage users dialog
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function ManageUsersButton({roleName, usersInRole}: ManageUsersButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const router = useRouter();

  const handleUsersUpdated = () => {
    // Refresh the page to show updated user list
    router.refresh();
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setDialogOpen(true)}
      >
        <Users className="mr-2 h-4 w-4"/>
        Manage Users
      </Button>

      <ManageUsersDialog
        roleName={roleName}
        usersInRole={usersInRole}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onUsersUpdated={handleUsersUpdated}
      />
    </>
  );
}
