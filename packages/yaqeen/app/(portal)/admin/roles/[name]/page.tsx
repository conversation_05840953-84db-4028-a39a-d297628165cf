import {Suspense} from "react";
import {api} from "@/api";
import {Warning} from "@phosphor-icons/react/dist/ssr";
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert";
import Loading from "@/app/(portal)/loading";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Badge} from "@/components/ui/badge";


/**
 * Keycloak Role Details Page
 * Server Component that fetches and displays Keycloak role details
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  params,
}: {
  params: Promise<{ name: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // In Next.js 14, we need to await the params object before accessing its properties
  const decodedParams = await params;
  const roleName = decodeURIComponent(decodedParams.name);

  // Create a suspense key
  const suspenseKey = `role-${roleName}-${Date.now()}`;

  // Fetch role data from Keycloak
  let roleData = null;
  let error = null;

  try {
    console.log(`Fetching Keycloak role: ${roleName}`);

    // Fetch role details
    const roleResponse = await api.keycloak.getRoleById({
      params: {
        roleName,
      },
      requiresAuth: true,
    });

    // Check if response is successful and has expected properties
    if (roleResponse.status === 200 && 'id' in roleResponse.body && 'name' in roleResponse.body) {
      roleData = roleResponse.body;
    } else {
      throw new Error('Invalid role data received from server');
    }

    console.log(`Successfully fetched role details`);
  } catch (err) {
    console.error(`Error fetching Keycloak role ${roleName}:`, err);
    error = err;
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <Warning className="h-4 w-4"/>
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to fetch role details from Keycloak server.
          <div className="mt-2 text-xs">
            <details>
              <summary>Error details</summary>
              <pre className="mt-2 whitespace-pre-wrap text-xs">
                {error instanceof Error ? error.message : 'An unexpected error occurred'}
              </pre>
            </details>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (!roleData) return <Loading/>;

  return (
    <Suspense key={suspenseKey} fallback={<Loading/>}>
      <Card>
        <CardHeader className="border-b bg-muted/40 px-3 py-4">
          <CardTitle className="text-md">Role Information</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <dl className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">ID</dt>
              <dd className="font-mono text-sm">{roleData.id}</dd>
            </div>
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Name</dt>
              <dd>{roleData.name}</dd>
            </div>
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Description</dt>
              <dd>{roleData.description || "-"}</dd>
            </div>
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Composite</dt>
              <dd>
                {roleData.composite ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700">Yes</Badge>
                ) : (
                  <Badge variant="secondary">No</Badge>
                )}
              </dd>
            </div>
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Client Role</dt>
              <dd>
                {roleData.clientRole ? (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">Yes</Badge>
                ) : (
                  <Badge variant="secondary">No</Badge>
                )}
              </dd>
            </div>
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Container ID</dt>
              <dd className="font-mono text-sm">{roleData.containerId || "-"}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>
    </Suspense>
  );
}