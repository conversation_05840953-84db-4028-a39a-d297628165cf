import {api} from "@/api";
import {ManageUsersButton} from "../manage-users-button";
import {UserDataTable} from "../user-data-table";
import {Users, MagnifyingGlass, UserPlus, Warning} from "@phosphor-icons/react/dist/ssr";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import Link from "next/link";
import {Alert, AlertTitle, AlertDescription} from "@/components/ui/alert";
import {Pagination} from "../../../users/pagination";

/**
 * Keycloak Role Users Tab with server-side data fetching and client-side pagination
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ name: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const roleName = await params.then(p => decodeURIComponent(p.name));
  const searchParamsResult = await searchParams;

  // Get pagination and search parameters
  const pageParam = searchParamsResult.page;
  const searchParam = searchParamsResult.search;
  const pageSize = 10;

  // Convert to appropriate types with 1-based page number
  const pageNumber = typeof pageParam === 'string' ? Math.max(1, parseInt(pageParam, 10)) : 1;
  const search = typeof searchParam === 'string' ? searchParam : '';

  // Fetch users from server
  try {
    const response = await api.keycloak.getUsersInRole({
      params: { roleName },
      requiresAuth: true,
    });

    if (response.status !== 200) {
      throw new Error(`Failed to fetch users: ${response.status}`);
    }

    // Type guard to check if response is an array
    if (!Array.isArray(response.body)) {
      throw new Error("Invalid response format from server");
    }

    // Sort users by username
    const sortedUsers = [...response.body].sort((a, b) => 
      (a.username || "").localeCompare(b.username || "")
    );

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="relative flex-1">
            {sortedUsers.length > 0 && (
              <>
                <MagnifyingGlass className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
                <Input
                  type="search"
                  placeholder="Search users... (press Enter to search)"
                  className="w-full pl-8"
                  id="users-search"
                />
              </>
            )}
            {sortedUsers.length === 0 && (
              <h2 className="text-lg font-medium">Users in Role</h2>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              asChild
            >
              <Link href="/admin/users/create">
                <UserPlus className="mr-2 h-4 w-4"/>
                Create User
              </Link>
            </Button>
            <ManageUsersButton roleName={roleName} usersInRole={sortedUsers}/>
          </div>
        </div>

        {sortedUsers.length > 0 ? (
          <>
            <div className="rounded-md border">
              <UserDataTable
                data={sortedUsers}
                searchInputId="users-search"
                pageNumber={pageNumber}
                pageSize={pageSize}
                searchQuery={search}
              />
            </div>
            <Pagination
              currentPage={pageNumber}
              pageSize={pageSize}
              totalItems={sortedUsers.length}
            />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 bg-muted/20 rounded-md border">
            <Users className="h-12 w-12 text-muted-foreground/50"/>
            <h3 className="mt-4 text-lg font-medium">No users in this role</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Use &quot;the Manage Users&quot; button to add users to this role.
            </p>
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error(`Error fetching users for role ${roleName}:`, error);
    return (
      <Alert variant="destructive" className="mb-6">
        <Warning className="h-4 w-4"/>
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to fetch users in role from Keycloak server.
          <div className="mt-2 text-xs">
            <details>
              <summary>Error details</summary>
              <pre className="mt-2 whitespace-pre-wrap text-xs">
                {error instanceof Error ? error.message : 'An unexpected error occurred'}
              </pre>
            </details>
          </div>
        </AlertDescription>
      </Alert>
    );
  }
}