"use client";

import {usePathname} from "next/navigation";
import {ROLE_TABS} from "../navigation-items";
import {cn} from "@/lib/utils";
import {ShieldStar, Users, LockKey} from "@phosphor-icons/react";
import {ProgressBarLink} from "@/components/progress-bar";

interface TabNavigationProps {
  roleName: string;
}

/**
 * Tab Navigation for Keycloak Role Details
 * Client Component that renders the tab navigation for role details
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function TabNavigation({roleName}: TabNavigationProps) {
  const pathname = usePathname();
  const tabs = ROLE_TABS(roleName);

  // Map of icon names to icon components
  const iconMap = {
    ShieldStar: ShieldStar,
    Users: Users,
    LockKey: LockKey,
  };

  return (
    <div className="border-b">
      <nav className="flex space-x-4">
        {tabs.map((tab) => {
          const isActive = tab.exact
            ? pathname === tab.href
            : pathname.startsWith(tab.href);

          const IconComponent = tab.icon ? iconMap[tab.icon as keyof typeof iconMap] : null;

          return (
            <ProgressBarLink
              key={tab.href}
              href={tab.href}
              className={cn(
                "px-3 py-2 text-sm font-medium transition-colors hover:text-primary flex items-center gap-2",
                isActive
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground"
              )}
            >
              {IconComponent && (
                <IconComponent
                  className={cn(
                    "h-4 w-4",
                    isActive ? "text-primary" : "text-muted-foreground"
                  )}
                  weight={isActive ? "fill" : "regular"}
                />
              )}
              {tab.title}
            </ProgressBarLink>
          );
        })}
      </nav>
    </div>
  );
}