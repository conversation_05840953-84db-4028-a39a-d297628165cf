'use server';

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type KeycloakRole, type <PERSON>c<PERSON>akUser } from "@/api/contracts/keycloak-contract";

interface ActionResult {
  success: boolean;
  error?: string;
  users: KeycloakUser[];
}

/**
 * Get role by name
 */
async function getRole(roleName: string): Promise<KeycloakRole | null> {
  try {
    const response = await api.keycloak.getRoleByName({
      params: { roleName },
      requiresAuth: true,
    });

    if (response.status !== 200) {
      console.error(`Failed to fetch role: ${response.status}`);
      return null;
    }

    return response.body;
  } catch (error) {
    console.error('Error fetching role:', error);
    return null;
  }
}

/**
 * Add multiple users to a role
 */
export async function addUsersToRole(roleName: string, userIds: string[]): Promise<ActionResult> {
  const role = await getRole(roleName);

  if (!role) {
    return {
      success: false,
      error: `Role ${roleName} not found`,
      users: [],
    };
  }

  try {
    await api.keycloak.addUsersToRole({
      params: { roleName },
      body: userIds,
      requiresAuth: true,
    });

    revalidatePath(`/admin/roles/${encodeURIComponent(roleName)}`);

    // Fetch updated user list
    const usersResponse = await api.keycloak.getUsersInRole({
      params: { roleName },
      requiresAuth: true,
    });

    if (!Array.isArray(usersResponse.body)) {
      throw new Error('Invalid response format for users');
    }

    return {
      success: true,
      users: usersResponse.body,
    };
  } catch (error) {
    console.error(`Error assigning role to users:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unexpected error",
      users: [],
    };
  }
}

/**
 * Remove a user from a role
 */
export async function removeUserFromRole(roleName: string, userId: string): Promise<ActionResult> {
  const role = await getRole(roleName);

  if (!role) {
    return {
      success: false,
      error: `Role ${roleName} not found`,
      users: [],
    };
  }

  try {
    await api.keycloak.removeUserFromRole({
      params: { roleName, userId },
      requiresAuth: true,
    });

    revalidatePath(`/admin/roles/${encodeURIComponent(roleName)}`);

    // Fetch updated user list
    const usersResponse = await api.keycloak.getUsersInRole({
      params: { roleName },
      requiresAuth: true,
    });

    if (!Array.isArray(usersResponse.body)) {
      throw new Error('Invalid response format for users');
    }

    return {
      success: true,
      users: usersResponse.body,
    };
  } catch (error) {
    console.error(`Error removing user from role:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unexpected error",
      users: [],
    };
  }
}

/**
 * Search for users by name or email
 */
export async function searchUsers(query: string): Promise<ActionResult> {
  console.log(`Searching for users with query: ${query}`);

  try {
    const response = await api.keycloak.getUsers({
      query: {
        search: query,
        max: "20",
      },
      requiresAuth: true,
    });

    if (response.status !== 200) {
      return {
        success: false,
        error: `Search failed with status ${response.status}`,
        users: [],
      };
    }

    // Type guard to ensure response.body is an array
    if (!Array.isArray(response.body)) {
      return {
        success: false,
        error: 'Invalid response format from server',
        users: [],
      };
    }

    return {
      success: true,
      users: response.body,
    };
  } catch (error) {
    console.error('Error searching users:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to search users',
      users: [],
    };
  }
}
