'use server';

import {api} from "@/api";
import {revalidatePath} from "next/cache";
import type {KeycloakPermission} from "@/api/contracts/keycloak-contract";
import type {EndpointItem} from "@/app/(portal)/admin/roles/[name]/permissions/permissions-client";

interface PermissionWithEndpoints {
  name: string;
  isAssigned: boolean;
  endpoints: EndpointItem[];
  keycloakPermission: KeycloakPermission;
  policyId?: string;
  policyName?: string;
}

/**
 * Assigns a permission to a role
 *
 * @param permissionId - The ID of the permission to assign
 * @param roleId - The ID of the role to assign the permission to
 * @param permission - The permission object with policy information
 * @returns A result object indicating success or failure
 */
export async function assignPermissionToRole(permissionId: string, roleId: string, permission: PermissionWithEndpoints) {
  try {
    console.log(`Assigning permission ${permissionId} to role ${roleId}`);

    // If we don't have a policy ID, we can't assign the role
    if (!permission.policyId) {
      console.error(`No policy ID found for permission ${permission.name}`);
      return {
        success: false,
        error: `No policy ID found for permission ${permission.name}`
      };
    }

    // 1. Get the policy
    const policyResponse = await api.keycloak.getPolicyById({
      params: {
        policyId: permission.policyId,
      },
    });

    if (policyResponse.status !== 200) {
      console.error(`Failed to fetch policy: ${policyResponse.status}`);
      return {
        success: false,
        error: `Failed to fetch policy: ${policyResponse.status}`
      };
    }

    const policy = policyResponse.body;
    console.log(`Retrieved policy: ${policy.name}`);

    // 2. Add the role to the policy if not already there
    let roles: { id: string; required: boolean }[] = [];
    if (policy.config?.roles) {
      // Handle the case where roles might be a JSON string
      if (typeof policy.config.roles === 'string') {
        try {
          const parsedRoles = JSON.parse(policy.config.roles);
          if (Array.isArray(parsedRoles)) {
            roles = parsedRoles.filter((r: { id: string }) => r.id !== roleId);
          }
        } catch (e) {
          console.error('Failed to parse roles JSON string:', e);
        }
      } else if (Array.isArray(policy.config.roles)) {
        const roleArray = policy.config.roles as Array<{ id: string; required: boolean }>;
        roles = roleArray.filter((r: { id: string }) => r.id !== roleId);
      }
    }

    // Add the new role if it doesn't exist
    if (!roles.some((r: { id: string }) => r.id === roleId)) {
      console.log(`Adding role ${roleId} to policy roles`);
      roles.push({id: roleId, required: false});
    } else {
      console.log(`Role ${roleId} already exists in policy`);
      return {success: true}; // Role already assigned
    }

    // 3. Update the policy with the new role
    const updateResponse = await api.keycloak.updateRolePolicy({
      params: {
        policyId: permission.policyId,
      },
      body: {
        id: policy.id,
        name: policy.name,
        type: policy.type,
        logic: policy.logic,
        decisionStrategy: policy.decisionStrategy,
        roles: roles,
      },
    });

    if (updateResponse.status === 201) {
      console.log(`Successfully updated policy ${permission.policyId} with role ${roleId}`);
      // Revalidate the path to update the UI
      revalidatePath(`/admin/keycloak-roles/[name]`);
      return {success: true};
    } else {
      console.error(`Failed to update policy: ${updateResponse.status}`);
      return {
        success: false,
        error: `Failed to update policy: ${updateResponse.status}`
      };
    }
  } catch (error) {
    console.error('Error assigning permission to role:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Removes a permission from a role
 *
 * @param permissionId - The ID of the permission to remove
 * @param roleId - The ID of the role to remove the permission from
 * @param permission - The permission object with policy information
 * @returns A result object indicating success or failure
 */
export async function removePermissionFromRole(permissionId: string, roleId: string, permission?: PermissionWithEndpoints) {
  try {
    console.log(`Removing permission ${permissionId} from role ${roleId}`);

    // If we have a policy ID, use that to remove the role
    if (permission?.policyId) {
      // 1. Get the policy
      const policyResponse = await api.keycloak.getPolicyById({
        params: {
          policyId: permission.policyId,
        },
      });

      if (policyResponse.status !== 200) {
        console.error(`Failed to fetch policy: ${policyResponse.status}`);
        return {
          success: false,
          error: `Failed to fetch policy: ${policyResponse.status}`
        };
      }

      const policy = policyResponse.body;
      console.log(`Retrieved policy: ${policy.name}`);

      // 2. Remove the role from the policy
      let roles: { id: string; required: boolean }[] = [];
      if (policy.config?.roles) {
        // Handle the case where roles might be a JSON string
        if (typeof policy.config.roles === 'string') {
          try {
            const parsedRoles = JSON.parse(policy.config.roles);
            if (Array.isArray(parsedRoles)) {
              roles = parsedRoles.filter((r: { id: string }) => r.id !== roleId);
            }
          } catch (e) {
            console.error('Failed to parse roles JSON string:', e);
          }
        } else if (Array.isArray(policy.config.roles)) {
          const roleArray = policy.config.roles as Array<{ id: string; required: boolean }>;
          roles = roleArray.filter((r: { id: string }) => r.id !== roleId);
        }
      }

      // 3. Update the policy with the role removed
      const updateResponse = await api.keycloak.updateRolePolicy({
        params: {
          policyId: permission.policyId,
        },
        body: {
          id: policy.id,
          name: policy.name,
          type: policy.type,
          logic: policy.logic,
          decisionStrategy: policy.decisionStrategy,
          roles: roles,
        },
      });

      if (updateResponse.status === 201) {
        console.log(`Successfully updated policy ${permission.policyId} to remove role ${roleId}`);
        // Revalidate the path to update the UI
        revalidatePath(`/admin/keycloak-roles/[name]`);
        return {success: true};
      } else {
        console.error(`Failed to update policy: ${updateResponse.status}`);
        return {
          success: false,
          error: `Failed to update policy: ${updateResponse.status}`
        };
      }
    } else {
      //TODO: stupid API there is no more fallback rules.
      // Fall back to the original method if we don't have a policy ID
      const response = await api.keycloak.removePermissionFromRole({
        params: {
          permissionId,
          roleId,
        },
      });

      if (response.status === 201 || response.status === 204) {
        console.log(`Successfully removed permission ${permissionId} from role ${roleId}`);
        // Revalidate the path to update the UI
        revalidatePath(`/admin/keycloak-roles/[name]`);
        return {success: true};
      } else {
        console.error(`Failed to remove permission: ${response.status}`);
        return {
          success: false,
          error: `Failed to remove permission: ${response.status}`
        };
      }
    }
  } catch (error) {
    console.error('Error removing permission from role:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
