"use client";

import {type ColumnDef} from "@tanstack/react-table";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";

/**
 * Columns definition for the users in role table
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export const columns: ColumnDef<KeycloakUser>[] = [
  {
    accessorKey: "username",
    header: "Username",
    cell: ({row}) => {
      return <div className="font-medium">{row.getValue("username") || "-"}</div>;
    },
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({row}) => {
      const user = row.original;
      const name = user.firstName && user.lastName
        ? `${user.firstName} ${user.lastName}`
        : user.firstName || user.lastName || "-";
      return <div>{name}</div>;
    },
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({row}) => {
      return <div>{row.getValue("email") || "-"}</div>;
    },
  },
];
