"use client";

import {useEffect, useState} from "react";
import {<PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle} from "@/components/ui/card";
import {Badge} from "@/components/ui/badge";
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from "@/components/ui/accordion";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from "@/components/ui/select";
import {Switch} from "@/components/ui/switch";
import {Label} from "@/components/ui/label";
import {Checkbox} from "@/components/ui/checkbox";
import {Button} from "@/components/ui/button";
import {ArrowsInLineHorizontal, ArrowsOutLineHorizontal, Spinner} from "@phosphor-icons/react";
import type {AppPermission, KeycloakRole, MappedPermission} from "@/api/contracts/keycloak-contract";
import {useToast} from "@/lib/hooks/use-toast";
import {assignPermissionTo<PERSON><PERSON>, removePermission<PERSON>rom<PERSON>ole} from "../assign-permission-to-role";

interface EndpointItem {
  uri: string;
  method: string;
  description: string;
  service: string;
  feature: string;
  scope: string;
}

interface PermissionWithEndpoints {
  name: string;
  isAssigned: boolean;
  endpoints: EndpointItem[];
  permissionId: string;
  policyId?: string;
  keycloakPermission: {
    id: string;
    name: string;
    type: string;
  };
}

interface PermissionsClientProps {
  roleData: KeycloakRole;
  permissions: Array<MappedPermission & {
    isAssigned: boolean;
    policyId?: string;
  }>;
}

export type {EndpointItem, PermissionWithEndpoints};
/**
 * Client component for displaying and filtering role permissions
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default function PermissionsClient({roleData, permissions: rawPermissions}: PermissionsClientProps) {
  const [selectedService, setSelectedService] = useState<string>("all");
  const [selectedFeature, setSelectedFeature] = useState<string>("all");
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [permissions, setPermissions] = useState<PermissionWithEndpoints[]>([]);
  const [showOnlyAssigned, setShowOnlyAssigned] = useState<boolean>(false);
  const [pendingChanges, setPendingChanges] = useState<Map<string, boolean>>(new Map());
  const [allExpanded, setAllExpanded] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);
  const {toast} = useToast();

  // Process permissions on component mount
  useEffect(() => {
    setIsLoading(true);
    console.log("Raw permissions:", rawPermissions);
    const organizedPermissions = organizePermissionsByName(rawPermissions);
    console.log("Organized permissions:", organizedPermissions);
    setPermissions(organizedPermissions);
    setIsLoading(false);
  }, [rawPermissions]);

  // By default, all panels should be collapsed
  useEffect(() => {
    setExpandedAccordions([]);
  }, [permissions]);

  // Organize permissions by their name with associated endpoints
  const organizePermissionsByName = (mappedPermissions: Array<MappedPermission & { isAssigned: boolean; policyId?: string }>): PermissionWithEndpoints[] => {
    console.log("Organizing permissions:", mappedPermissions);

    const permissionsMap = new Map<string, PermissionWithEndpoints>();

    if (!mappedPermissions || mappedPermissions.length === 0) {
      console.log("No permissions to organize");
      return [];
    }

    mappedPermissions.forEach(permission => {
      const permName = permission.keycloakPermission.name;
      const isAssigned = permission.isAssigned;
      const permissionId = permission.keycloakPermission.id;
      const policyId = permission.policyId;

      console.log("Processing permission:", {
        name: permName,
        isAssigned,
        permissionId,
        policyId,
        appPermissions: permission.appPermissions
      });

      // Process each app permission
      if (permission.appPermissions && Array.isArray(permission.appPermissions)) {
        permission.appPermissions.forEach((appPerm) => {
          // Extract service, feature, scope from permission name
          // Format: permission:service:feature:scope
          const [_, service, feature, scope] = permName.split(':');

          const endpointItem: EndpointItem = {
            uri: appPerm.uri,
            method: appPerm.method,
            description: appPerm.description,
            service: service || '',
            feature: feature || '',
            scope: scope || '',
          };

          if (permissionsMap.has(permName)) {
            // Add endpoint to existing permission
            permissionsMap.get(permName)!.endpoints.push(endpointItem);
          } else {
            // Create new permission entry
            permissionsMap.set(permName, {
              name: permName,
              isAssigned,
              endpoints: [endpointItem],
              permissionId,
              policyId,
              keycloakPermission: {
                id: permissionId,
                name: permName,
                type: "resource", // Default type for Keycloak permissions
              },
            });
          }
        });
      }
    });

    const result = Array.from(permissionsMap.values());
    console.log("Organized permissions result:", result);
    return result;
  };

  // Toggle permission assignment
  const togglePermission = async (permission: PermissionWithEndpoints, isChecked: boolean) => {
    console.log(`Toggling permission ${permission.name} to ${isChecked}`);
    setIsAssigning(true);
    try {
      // Update pending changes
      setPendingChanges(prev => {
        const newMap = new Map(prev);
        newMap.set(permission.permissionId, isChecked);
        return newMap;
      });

      // Update UI immediately
      setPermissions(prev =>
        prev.map(p =>
          p.permissionId === permission.permissionId
            ? {...p, isAssigned: isChecked}
            : p
        )
      );
      // Call server action to update permission
      if (isChecked) {
        // Assign permission to role
        await assignPermissionToRole(
          permission.permissionId,
          roleData.id,
          {
            name: permission.name,
            isAssigned: permission.isAssigned,
            endpoints: permission.endpoints,
            keycloakPermission: permission.keycloakPermission,
            policyId: permission.policyId
          }
        );
      } else {
        // Remove permission from role
        await removePermissionFromRole(
          permission.permissionId,
          roleData.id,
          {
            name: permission.name,
            isAssigned: permission.isAssigned,
            endpoints: permission.endpoints,
            keycloakPermission: permission.keycloakPermission,
            policyId: permission.policyId
          }
        );
      }

      // Remove from pending changes after successful API call
      setPendingChanges(prev => {
        const newMap = new Map(prev);
        newMap.delete(permission.permissionId);
        return newMap;
      });

    } catch (error) {
      console.error("Error updating permission:", error);

      // Revert UI change on error
      setPermissions(prev =>
        prev.map(p =>
          p.permissionId === permission.permissionId
            ? {...p, isAssigned: !isChecked}
            : p
        )
      );

      // Remove from pending changes
      setPendingChanges(prev => {
        const newMap = new Map(prev);
        newMap.delete(permission.permissionId);
        return newMap;
      });

      toast({
        title: "Error",
        description: `Failed to ${isChecked ? 'assign' : 'remove'} permission: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsAssigning(false);
    }
  };

  // Get filtered permissions based on selected service and feature
  const getFilteredPermissions = () => {
    console.log("Filtering permissions:", {
      selectedService,
      selectedFeature,
      showOnlyAssigned,
      totalPermissions: permissions.length
    });

    let filtered = [...permissions];

    // Filter by service if not "all"
    if (selectedService !== "all") {
      filtered = filtered.filter(permission => {
        const match = permission.endpoints.some(endpoint => endpoint.service === selectedService);
        if (!match) {
          console.log(`Permission ${permission.name} filtered out - wrong service`);
        }
        return match;
      });
    }

    // Filter by feature if not "all"
    if (selectedFeature !== "all") {
      filtered = filtered.filter(permission => {
        const match = permission.endpoints.some(endpoint => endpoint.feature === selectedFeature);
        if (!match) {
          console.log(`Permission ${permission.name} filtered out - wrong feature`);
        }
        return match;
      });
    }

    // Filter by assignment status if showOnlyAssigned is true
    if (showOnlyAssigned) {
      filtered = filtered.filter(permission => {
        const match = permission.isAssigned;
        if (!match) {
          console.log(`Permission ${permission.name} filtered out - not assigned`);
        }
        return match;
      });
    }

    console.log(`Filtered permissions: ${filtered.length} remaining`);
    return filtered;
  };

  // Get all unique services for dropdown
  const getAllServices = () => {
    const services = new Set<string>();

    permissions.forEach(permission => {
      const service = permission.name.split(':')[1];
      if (service) {
        services.add(service);
      }
    });

    return Array.from(services).sort();
  };

  // Get all unique features for the selected service
  const getAllFeatures = () => {
    const features = new Set<string>();

    permissions.forEach(permission => {
      const [, service, feature] = permission.name.split(':');
      if (feature && (selectedService === "all" || service === selectedService)) {
        features.add(feature);
      }
    });

    return Array.from(features).sort();
  };

  // Get method badge color
  const getMethodBadge = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET':
        return <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
          GET
        </Badge>;
      case 'POST':
        return <Badge variant="outline"
                      className="bg-emerald-50 text-emerald-800 border-emerald-200">
          POST
        </Badge>;
      case 'PUT':
        return <Badge variant="outline"
                      className="bg-amber-50 text-amber-800 border-amber-200">
          PUT
        </Badge>;
      case 'DELETE':
        return <Badge variant="outline"
                      className="bg-rose-50 text-rose-800 border-rose-200">
          DELETE
        </Badge>;
      default:
        return <Badge variant="outline"
                      className="bg-slate-50 text-slate-800 border-slate-200">
          {method}
        </Badge>;
    }
  };

  // Get scope badge
  const getScopeBadge = (scope: string) => {
    switch (scope.toLowerCase()) {
      case 'read':
        return <Badge variant="outline"
                      className="bg-indigo-50 text-indigo-800 border-indigo-200">
          {scope}
        </Badge>;
      case 'write':
        return <Badge variant="outline"
                      className="bg-violet-50 text-violet-800 border-violet-200">
          {scope}
        </Badge>;
      case 'admin':
        return <Badge variant="outline"
                      className="bg-purple-50 text-purple-800 border-purple-200">
          {scope}
        </Badge>;
      default:
        return <Badge variant="outline"
                      className="bg-slate-50 text-slate-800 border-slate-200">
          {scope}
        </Badge>;
    }
  };

  // Expand all accordions
  const handleToggleAll = () => {
    setAllExpanded(!allExpanded);
    if (!allExpanded) {
      // Expand all
      setExpandedAccordions(permissions.map(p => p.permissionId));
    } else {
      // Collapse all
      setExpandedAccordions([]);
    }
  };


  // Get count of assigned permissions
  const getAssignedPermissionsCount = () => {
    return permissions.filter(p => p.isAssigned).length;
  };

  // Reset all filters
  const resetFilters = () => {
    setSelectedService("all");
    setSelectedFeature("all");
    setShowOnlyAssigned(false);
  };

  // When service changes, reset feature filter
  useEffect(() => {
    setSelectedFeature("all");
  }, [selectedService]);

  const filteredPermissions = getFilteredPermissions();

  return (
    <div className="space-y-4">
      {/* Controls and filters section outside the permissions card */}
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-assigned"
              checked={showOnlyAssigned}
              onCheckedChange={setShowOnlyAssigned}
            />
            <Label htmlFor="show-assigned">Show only assigned</Label>
          </div>

          <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
            {getAssignedPermissionsCount()} of {permissions.length} permissions assigned
          </Badge>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Select
              value={selectedService}
              onValueChange={(value) => {
                setSelectedService(value);
                setSelectedFeature("all");
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by service"/>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All services</SelectItem>
                {getAllServices().map((service) => (
                  <SelectItem key={service} value={service}>
                    {service}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedFeature}
              onValueChange={setSelectedFeature}
              disabled={selectedService === "all"}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by feature"/>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All features</SelectItem>
                {getAllFeatures().map((feature) => (
                  <SelectItem key={feature} value={feature}>
                    {feature}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleAll}
              className="flex items-center gap-1"
            >
              {allExpanded ? (
                <ArrowsInLineHorizontal size={16}/>
              ) : (
                <ArrowsOutLineHorizontal size={16}/>
              )}
              {allExpanded ? 'Collapse all' : 'Expand all'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="flex items-center gap-1"
            >
              Reset filters
            </Button>
          </div>
        </div>
      </div>

      {/* Permissions card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Role Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Spinner size={32} className="animate-spin text-slate-500"/>
            </div>
          ) : (
            <div>
              <Accordion
                type="multiple"
                value={expandedAccordions}
                onValueChange={setExpandedAccordions}
                className="space-y-4"
              >
                {filteredPermissions.map((permission) => {
                  // Determine card background color based on method
                  let cardBgClass = "";
                  let uriBgClass = "";
                  switch (permission.endpoints[0]?.method.toUpperCase()) {
                    case 'GET':
                      cardBgClass = "bg-blue-50/40";
                      uriBgClass = "bg-blue-100/20 border-blue-200/50";
                      break;
                    case 'POST':
                      cardBgClass = "bg-emerald-50/40";
                      uriBgClass = "bg-emerald-100/20 border-emerald-200/50";
                      break;
                    case 'PUT':
                      cardBgClass = "bg-amber-50/40";
                      uriBgClass = "bg-amber-100/20 border-amber-200/50";
                      break;
                    case 'DELETE':
                      cardBgClass = "bg-rose-50/40";
                      uriBgClass = "bg-rose-100/20 border-rose-200/50";
                      break;
                    default:
                      cardBgClass = "bg-slate-50/40";
                      uriBgClass = "bg-slate-100/20 border-slate-200/50";
                  }

                  return (
                    <AccordionItem
                      key={permission.permissionId}
                      value={permission.permissionId}
                      className="border rounded-md overflow-hidden bg-white"
                    >
                      <AccordionTrigger className={`w-full ${cardBgClass}`}>
                        <div className="p-4 w-full">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <Checkbox
                                id={permission.name}
                                checked={permission.isAssigned}
                                disabled={pendingChanges.has(permission.permissionId) || isAssigning}
                                onCheckedChange={(checked) => togglePermission(permission, checked === true)}
                                className="mr-3"
                                onClick={(e) => e.stopPropagation()} // Prevent accordion toggle when clicking checkbox
                              />
                              <span className="text-sm font-medium text-slate-800">
                                {permission.name}
                              </span>
                              {pendingChanges.has(permission.permissionId) &&
                                <span className="text-muted-foreground text-sm ml-2">Saving...</span>}
                            </div>


                            <div className="flex items-center gap-2">
                              {pendingChanges.has(permission.permissionId) ? (
                                <Badge variant="outline" className="border-amber-100 bg-amber-50 text-amber-600">
                                  Saving...
                                </Badge>
                              ) : permission.isAssigned ? (
                                <Badge variant="outline" className="border-emerald-100 bg-emerald-50 text-emerald-600">
                                  Assigned
                                </Badge>
                              ) : null}
                              <Badge variant="outline" className="bg-white/50 backdrop-blur-sm">
                                {permission.endpoints.length} endpoint{permission.endpoints.length !== 1 ? 's' : ''}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className="pt-4 pb-2 px-4 bg-blue-50/20">
                        <div className="space-y-3">
                          {permission.endpoints.map((endpoint, idx) => (
                            <div
                              key={idx}
                              className={`p-3 rounded-md ${uriBgClass} backdrop-blur-sm bg-white/30`}
                            >
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <div className="col-span-2">
                                  <span className="font-mono text-xs text-slate-700">
                                    {endpoint.uri}
                                  </span>
                                </div>
                                <div className="col-span-1 flex items-center space-x-2">
                                  {getMethodBadge(endpoint.method)}
                                  {getScopeBadge(endpoint.scope)}
                                </div>
                              </div>
                              {endpoint.description && (
                                <p className="text-sm text-slate-600 mt-2">
                                  {endpoint.description}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>

              {filteredPermissions.length === 0 && (
                <div className="text-center py-12 text-slate-500">
                  No permissions found with the current filters
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
