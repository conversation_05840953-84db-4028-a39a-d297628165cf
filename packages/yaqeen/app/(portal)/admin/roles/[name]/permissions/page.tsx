import {Suspense} from "react";
import {api} from "@/api";
import {type MappedPermission} from "@/api/contracts/keycloak-contract";

import Loading from "@/app/(portal)/loading";
import {getRoleMappedPermissionsWithPolicies} from "../role-permissions-with-policies";
import PermissionsClient from "./permissions-client";

interface PermissionsResult {
  success: boolean;
  error?: string;
  permissions: Array<MappedPermission & {
    isAssigned: boolean;
    policyId?: string;
  }>;
}

/**
 * Keycloak Role Permissions Page
 * Server Component that fetches and displays permissions for a specific role
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  params,
}: {
  params: Promise<{ name: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const decodedParams = await params;
  const roleName = decodeURIComponent(decodedParams.name);

  async function PermissionsContent({roleName}: { roleName: string }) {
    console.log(`Fetching Keycloak role: ${roleName}`);

    // Fetch role data first
    const roleResponse = await api.keycloak.getRoleByName({
      params: {roleName},
      requiresAuth: true,
    });

    if (roleResponse.status !== 200) {
      throw new Error(`Failed to fetch role: ${roleResponse.status}`);
    }

    const roleData = roleResponse.body;

    let permissionsResult: PermissionsResult = {
      success: false,
      permissions: [],
    };

    try {
      // Fetch permissions
      const permissionsResponse = await getRoleMappedPermissionsWithPolicies(roleData.id, roleName);

      if (!permissionsResponse.success || !permissionsResponse.permissions) {
        throw new Error(permissionsResponse.error || "Failed to fetch permissions");
      }

      permissionsResult = {
        success: true,
        permissions: permissionsResponse.permissions,
      };

      console.log(`Successfully fetched ${permissionsResult.permissions.length} permissions`);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      permissionsResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch permissions',
        permissions: [],
      };
    }

    return (
      <div className="space-y-6">
        <div className="space-y-1">
          <h2 className="text-2xl font-semibold tracking-tight">Role Permissions</h2>
          <p className="text-sm text-muted-foreground">
            View and manage permissions assigned to this role.
          </p>
        </div>

        {permissionsResult.error ? (
          <div className="rounded-md border p-4">
            <div className="text-sm text-red-600">
              {permissionsResult.error}
            </div>
          </div>
        ) : (
          <PermissionsClient
            roleData={roleData}
            permissions={permissionsResult.permissions}
          />
        )}
      </div>
    );
  }

  return (
    <Suspense fallback={<Loading/>}>
      <PermissionsContent roleName={roleName}/>
    </Suspense>
  );
}