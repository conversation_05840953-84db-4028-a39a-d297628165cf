"use client";

import {useState} from "react";
import {<PERSON><PERSON>} from "@/components/ui/button";
import {Shield} from "@phosphor-icons/react";
import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";


// Import the ViewPermissionsDialog component directly here
// This avoids the need for dynamic imports
import ViewPermissionsDialog from "./view-permissions-dialog";

interface ViewPermissionsButtonProps {
  role: KeycloakRole;
}

/**
 * Button component for viewing role permissions
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function ViewPermissionsButton({role}: ViewPermissionsButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setDialogOpen(true)}
        className="flex items-center gap-2"
      >
        <Shield className="h-4 w-4"/>
        View Permissions
      </Button>

      {dialogOpen && (
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
            <DialogHeader>
              <DialogTitle>Role Permissions: {role.name}</DialogTitle>
              <DialogDescription>
                View all API endpoints this role has permission to access
              </DialogDescription>
            </DialogHeader>

            <div className="flex-1 overflow-auto">
              <PermissionsContent role={role} onClose={() => setDialogOpen(false)}/>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}

/**
 * Content component for the permissions dialog
 * This component is separated to avoid issues with dynamic imports
 */
function PermissionsContent({role, onClose}: { role: KeycloakRole; onClose: () => void }) {
  return <ViewPermissionsDialog role={role} open={true} onOpenChange={onClose}/>;
}
