/**
 * Navigation items for the Keycloak Role Details page
 * Defines the tabs available in the role details view
 * Following Java Spring Boot architecture standards with proper layer separation
 */

export const ROLE_TABS = (roleName: string) => [
  {
    title: "Role Details",
    href: `/admin/roles/${encodeURIComponent(roleName)}`,
    exact: true,
    icon: "ShieldStar",
  },
  {
    title: "Users in Role",
    href: `/admin/roles/${encodeURIComponent(roleName)}/users`,
    icon: "Users",
  },
  {
    title: "Permissions",
    href: `/admin/roles/${encodeURIComponent(roleName)}/permissions`,
    icon: "LockKey",
  },
];