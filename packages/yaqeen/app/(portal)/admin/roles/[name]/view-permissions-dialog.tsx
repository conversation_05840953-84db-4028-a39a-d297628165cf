"use client";

import {useState, useEffect} from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {Badge} from "@/components/ui/badge";
import {Spinner, ArrowsOutLineHorizontal, ArrowsInLineHorizontal} from "@phosphor-icons/react";
import {Switch} from "@/components/ui/switch";
import {Label} from "@/components/ui/label";
import {Checkbox} from "@/components/ui/checkbox";
import {Button} from "@/components/ui/button"; // Import Button component
import {getRoleMappedPermissionsWithPolicies} from "./role-permissions-with-policies";
import {type KeycloakRole, type MappedPermission, type KeycloakPolicy} from "@/api/contracts/keycloak-contract";

interface ViewPermissionsDialogProps {
  role: KeycloakRole;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Extended interface for mapped permissions that includes policies
interface MappedPermissionWithPolicies extends MappedPermission {
  policies: KeycloakPolicy[];
  isAssigned?: boolean;
}

interface EndpointItem {
  uri: string;
  method: string;
  description: string;
  service: string;
  feature: string;
  scope: string;
}

interface PermissionWithEndpoints {
  name: string;
  isAssigned: boolean;
  endpoints: EndpointItem[];
}

/**
 * Dialog component for viewing role permissions
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default function ViewPermissionsDialog({
                                                role,
                                                open,
                                                onOpenChange,
                                              }: ViewPermissionsDialogProps) {
  const [selectedService, setSelectedService] = useState<string>("all");
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [permissions, setPermissions] = useState<PermissionWithEndpoints[]>([]);
  const [showOnlyAssigned, setShowOnlyAssigned] = useState<boolean>(true);
  const [assignedPermissions, setAssignedPermissions] = useState<Set<string>>(new Set());

  // Fetch permissions when dialog opens
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await getRoleMappedPermissionsWithPolicies(role.id, role.name);
        
        if (!response.success || !response.permissions) {
          throw new Error(response.error || "Failed to fetch permissions");
        }

        // Ensure we have a valid array of permissions
        const validPermissions = Array.isArray(response.permissions) ? response.permissions : [];
        
        setPermissions(validPermissions.map(permission => ({
          name: permission.keycloakPermission.name,
          isAssigned: permission.isAssigned === true,
          endpoints: permission.appPermissions.map(endpoint => ({
            uri: endpoint.uri,
            method: endpoint.method,
            description: endpoint.description,
            service: endpoint.service,
            feature: endpoint.feature,
            scope: endpoint.scope
          }))
        })));
        
        // Initialize assigned permissions set
        const assignedPerms = new Set(
          validPermissions
            .filter(p => p.isAssigned)
            .map(p => p.keycloakPermission.name)
        );
        setAssignedPermissions(assignedPerms);
      } catch (error) {
        console.error("Error fetching permissions:", error);
        setError(error instanceof Error ? error.message : "An unexpected error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    if (open) {
      fetchPermissions().catch(console.error);
    }
  }, [open, role]);

  // By default, all panels should be collapsed
  useEffect(() => {
    setExpandedAccordions([]);
  }, [permissions]);

  // Filter permissions by service if a service is selected
  const getFilteredPermissions = () => {
    console.log("Filtering permissions by service:", selectedService, "from", permissions.length, "permissions");

    if (selectedService === "all") {
      return permissions;
    }

    return permissions.filter(permission => {
      // Check if any endpoint belongs to the selected service
      return permission.endpoints.some(endpoint => endpoint.service === selectedService);
    });
  };

  // Filter permissions by assignment status if showOnlyAssigned is true
  const getVisiblePermissions = () => {
    const filtered = getFilteredPermissions();
    console.log("Filtered by service:", filtered.length, "permissions");

    if (showOnlyAssigned) {
      const assigned = filtered.filter(permission => permission.isAssigned);
      console.log("Filtered by assignment:", assigned.length, "permissions");
      return assigned;
    }

    return filtered;
  };

  // Get all unique services for dropdown
  const getAllServices = () => {
    const services = new Set<string>();

    permissions.forEach(permission => {
      permission.endpoints.forEach(endpoint => {
        services.add(endpoint.service);
      });
    });

    return ["all", ...Array.from(services)];
  };

  // Get method badge color
  const getMethodBadge = (method: string) => {
    const colors: Record<string, string> = {
      GET: "bg-blue-100 text-blue-800",
      POST: "bg-green-100 text-green-800",
      PUT: "bg-yellow-100 text-yellow-800",
      DELETE: "bg-red-100 text-red-800",
      PATCH: "bg-purple-100 text-purple-800",
    };

    return (
      <Badge variant="outline" className={colors[method] || "bg-gray-100"}>
        {method}
      </Badge>
    );
  };

  // Convert Set to array before calling has()
  const isPermissionAssigned = (permissionName: string) => {
    return Array.from(assignedPermissions).includes(permissionName);
  };

  // Toggle permission selection
  const togglePermission = (permName: string, isChecked: boolean) => {
    setAssignedPermissions((prev: Set<string>) => {
      const newSet = new Set(prev);
      if (isChecked) {
        newSet.add(permName);
      } else {
        newSet.delete(permName);
      }
      return newSet;
    });
  };

  // Get scope badge
  const getScopeBadge = (scope: string) => {
    const colors: Record<string, string> = {
      read: "bg-blue-100 text-blue-800",
      write: "bg-green-100 text-green-800",
      admin: "bg-purple-100 text-purple-800",
    };

    return (
      <Badge variant="outline" className={colors[scope] || "bg-gray-100"}>
        {scope}
      </Badge>
    );
  };

  // Expand all accordions
  const expandAll = () => {
    const allPermissionNames = permissions.map(p => p.name);
    setExpandedAccordions(allPermissionNames);
  };

  // Collapse all accordions
  const collapseAll = () => {
    setExpandedAccordions([]);
  };

  // Get count of assigned permissions
  const getAssignedPermissionsCount = () => {
    return permissions.filter(p => p.isAssigned).length;
  };

  return (
    <Dialog open={open} onOpenChange={(open) => onOpenChange(open)}>
      <DialogContent className="max-w-6xl w-[90vw] h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Role Permissions: {role.name}</DialogTitle>
          <DialogDescription>
            View all API endpoints this role has permission to access
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-12 flex-grow">
            <Spinner className="h-8 w-8 animate-spin text-slate-400"/>
            <span className="ml-3 text-slate-600">Loading permissions...</span>
          </div>
        ) : error ? (
          <div className="p-4 text-red-600 border border-red-200 rounded-md bg-red-50 flex-shrink-0">
            {error}
          </div>
        ) : (
          <div className="flex flex-col flex-grow overflow-hidden">
            <div className="flex justify-between items-center mb-4 flex-shrink-0">
              <div className="w-64">
                <Select
                  value={selectedService}
                  onValueChange={setSelectedService}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a service"/>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Services</SelectItem>
                    {getAllServices().filter(s => s !== "all").map((service) => (
                      <SelectItem key={service} value={service}>
                        {service}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-4">
                <Badge variant="outline" className="bg-slate-100">
                  {getAssignedPermissionsCount()} / {permissions.length} permissions assigned
                </Badge>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-assigned"
                    checked={showOnlyAssigned}
                    onCheckedChange={setShowOnlyAssigned}
                  />
                  <Label htmlFor="show-assigned">Show only assigned</Label>
                </div>
              </div>
            </div>

            <div className="flex-grow overflow-auto pr-3 min-h-[300px]">
              <div className="space-y-4">
                <div className="flex justify-end mb-2">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={expandAll}
                      className="text-xs"
                    >
                      <ArrowsOutLineHorizontal size={16} className="mr-1"/>
                      Expand All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={collapseAll}
                      className="text-xs"
                    >
                      <ArrowsInLineHorizontal size={16} className="mr-1"/>
                      Collapse All
                    </Button>
                  </div>
                </div>

                <Accordion
                  type="multiple"
                  value={expandedAccordions}
                  onValueChange={setExpandedAccordions}
                  className="space-y-2"
                >
                  {getVisiblePermissions().map((permission) => (
                    <AccordionItem
                      key={permission.name}
                      value={permission.name}
                      className="border rounded-md overflow-hidden"
                    >
                      <div className="flex items-center w-full">
                        <div className="flex items-center px-4 py-3 flex-1">
                          <Checkbox
                            id={permission.name}
                            checked={isPermissionAssigned(permission.name)}
                            onCheckedChange={(checked) =>
                              togglePermission(permission.name, checked === true)
                            }
                            className="mr-3 flex-shrink-0"
                          />
                          <div className="flex-1 flex items-center">
                            <div className="flex items-center overflow-hidden">
                              <span className="text-sm font-mono truncate">{permission.name}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 ml-auto mr-2 flex-shrink-0">
                            {permission.isAssigned && (
                              <Badge variant="secondary"
                                     className="flex-shrink-0 bg-green-100 text-green-800 border-green-200">
                                Assigned
                              </Badge>
                            )}
                            <Badge variant="outline" className="flex-shrink-0">
                              {permission.endpoints.length} endpoint{permission.endpoints.length !== 1 ? 's' : ''}
                            </Badge>
                          </div>
                        </div>
                        <AccordionTrigger className="pr-4 py-3 flex-shrink-0"/>
                      </div>
                      <AccordionContent className="px-4 py-3 border-t">
                        <div className="space-y-3">
                          {permission.endpoints.map((endpoint, idx) => (
                            <div
                              key={idx}
                              className={`border rounded-md p-3 flex items-start ${
                                endpoint.scope === 'read' ? 'bg-blue-50 border-blue-100' :
                                  endpoint.scope === 'write' ? 'bg-green-50 border-green-100' :
                                    'bg-purple-50 border-purple-100'
                              }`}
                            >
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex flex-col">
                                    <span className="font-mono text-xs bg-white px-2 py-1 rounded text-slate-700">
                                      {endpoint.uri}
                                    </span>
                                    <div className="flex items-center mt-1 space-x-2">
                                      {getMethodBadge(endpoint.method)}
                                      {getScopeBadge(endpoint.scope)}
                                    </div>
                                  </div>
                                </div>
                                <p className="text-sm text-slate-700">{endpoint.description}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>

                {getVisiblePermissions().length === 0 && (
                  <div className="text-center py-12 text-slate-500">
                    No permissions found for this role
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
