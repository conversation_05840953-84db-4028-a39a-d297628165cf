"use client";

import { type KeycloakUser } from "@/api/contracts/keycloak-contract";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import { useEffect, useState } from "react";

interface UserDataTableProps {
  data: KeycloakUser[];
  searchInputId?: string;
  pageNumber: number;
  pageSize: number;
  searchQuery: string;
}

/**
 * Data table component for users in a Keycloak role with client-side pagination
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function UserDataTable({ data, searchInputId, pageNumber, pageSize, searchQuery }: UserDataTableProps) {
  const [filteredData, setFilteredData] = useState<KeycloakUser[]>(data);

  // Connect to external search input if provided
  useEffect(() => {
    if (searchInputId) {
      const handleSearch = () => {
        const searchInput = document.getElementById(searchInputId) as HTMLInputElement;
        if (searchInput) {
          const query = searchInput.value.toLowerCase();
          if (!query) {
            setFilteredData([...data].sort((a, b) => (a.username || "").localeCompare(b.username || "")));
          } else {
            const filtered = data
              .filter(
                (user) =>
                  user.username?.toLowerCase().includes(query) ||
                  user.email?.toLowerCase().includes(query) ||
                  user.firstName?.toLowerCase().includes(query) ||
                  user.lastName?.toLowerCase().includes(query)
              )
              .sort((a, b) => (a.username || "").localeCompare(b.username || ""));
            setFilteredData(filtered);
          }
        }
      };

      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Enter") {
          handleSearch();
        }
      };

      const searchInput = document.getElementById(searchInputId) as HTMLInputElement;
      if (searchInput) {
        // Only trigger search on Enter key press
        searchInput.addEventListener("keydown", handleKeyDown);

        // Initial filter with sorted data
        setFilteredData([...data].sort((a, b) => (a.username || "").localeCompare(b.username || "")));

        return () => {
          searchInput.removeEventListener("keydown", handleKeyDown);
        };
      }
    } else {
      // If no external search input, just sort the data
      setFilteredData([...data].sort((a, b) => (a.username || "").localeCompare(b.username || "")));
    }
  }, [data, searchInputId]);

  // Calculate start and end indices for current page (1-based page number)
  const startIndex = (pageNumber - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  // Paginate data
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return (
    <DataTable
      columns={columns}
      data={{
        data: paginatedData,
        total: filteredData.length,
      }}
      searchPlaceholder=""
      emptyMessage={
        searchQuery ? `No users matching "${searchQuery}" found in this role` : "No users assigned to this role"
      }
      paginationEnabled={false}
      rowClickId="id"
      baseRedirectPath="/admin/users"
      clientPagination={true}
      pageSize={pageSize}
    />
  );
}
