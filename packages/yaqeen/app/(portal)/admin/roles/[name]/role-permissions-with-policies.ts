"use server";

import {api} from "@/api";
import {type KeycloakPermission, type KeycloakPolicy} from "@/api/contracts/keycloak-contract";
import {mapKeycloakPermissions} from "@/lib/permission-mapper";

/**
 * Get permissions and policies for a specific role and map them to URIs
 * Following Java Spring Boot architecture standards with proper layer separation
 *
 * @param roleId The ID of the role
 * @param roleName The name of the role
 */
export async function getRoleMappedPermissionsWithPolicies(roleId: string, roleName: string) {
  try {
    console.log(`Fetching permissions and policies for role: ${roleName} (${roleId})`);

    // Fetch permissions and policies
    try {
      const [permissionsResponse, policiesResponse] = await Promise.all([
        api.keycloak.getClientPermissions({
          requiresAuth: true,
        }),
        api.keycloak.getClientPolicies({
          query: {
            max: "1000", // Limit to 1000 policies
          },
          requiresAuth: true,
        }),
      ]);

      if (permissionsResponse.status !== 200 || policiesResponse.status !== 200) {
        console.error("Error fetching permissions or policies:", {
          permissionsStatus: permissionsResponse.status,
          policiesStatus: policiesResponse.status,
        });
        return {
          success: false,
          error: "Failed to fetch permissions or policies",
        };
      }

      // Type guard to check if response is an array
      const isPermissionArray = Array.isArray(permissionsResponse.body);
      const isPolicyArray = Array.isArray(policiesResponse.body);

      if (!isPermissionArray || !isPolicyArray) {
        console.error("Invalid response format:", {
          permissionsIsArray: isPermissionArray,
          policiesIsArray: isPolicyArray,
        });
        return {
          success: false,
          error: "Invalid response format from server",
        };
      }

      const permissions: KeycloakPermission[] = permissionsResponse.body;
      const policies: KeycloakPolicy[] = policiesResponse.body;

      console.log(`Found ${permissions.length} permissions and ${policies.length} policies`);

      // 3. Map permissions to application URIs with policies
      const mappedPermissions = await mapKeycloakPermissions(permissions, policies);
      console.log(`Mapped ${mappedPermissions.length} permissions to URIs`);

      // 4. Find permissions relevant to the specified role
      // First, filter policies that contain the role name or ID
      const rolePolicies = policies.filter(policy => {
        if (policy.config?.roles) {
          try {
            const rolesConfig = JSON.parse(policy.config.roles);
            if (Array.isArray(rolesConfig)) {
              return rolesConfig.some(role => {
                if (role.id) {
                  return role.id === roleId;
                }
                return false;
              });
            }
          } catch (error) {
            console.error('Error parsing roles from policy config:', error);
          }
        }
        return false;
      });

      console.log(`Found ${rolePolicies.length} policies for role ${roleName}`);

      // Get permission names from policies that match the role
      const permissionNamesForRole = new Set<string>();
      rolePolicies.forEach(policy => {
        // Policy names for permissions follow the pattern: policy:role:service:feature:scope
        if (policy.name.startsWith('policy:role:')) {
          // Extract the permission name from the policy name
          const parts = policy.name.split(':');
          if (parts.length >= 5) {
            const permissionName = `permission:${parts[2]}:${parts[3]}:${parts[4]}`;
            permissionNamesForRole.add(permissionName);
            console.log(`Found permission name for role: ${permissionName}`);
          }
        }
      });

      console.log(`Found ${permissionNamesForRole.size} permission names for role ${roleName}`);

      // Filter permissions that match the permission names
      const rolePermissions = mappedPermissions.filter(permission => {
        const matches = permissionNamesForRole.has(permission.keycloakPermission.name);
        if (matches) {
          console.log(`Permission matches: ${permission.keycloakPermission.name}`);
        }
        return matches;
      });

      console.log(`Found ${rolePermissions.length} mapped permissions for role ${roleName}`);

      // If we have no permissions for this role, return all permissions but mark them as not assigned
      if (rolePermissions.length === 0) {
        console.log(`No permissions found for role ${roleName}, returning all available permissions`);

        // Return all permissions but mark them as not assigned
        return {
          success: true,
          permissions: mappedPermissions.map(permission => ({
            ...permission,
            isAssigned: false
          })),
        };
      }

      // Mark all permissions with their assignment status
      const allPermissionsWithAssignmentStatus = mappedPermissions.map(permission => {
        const isAssigned = permissionNamesForRole.has(permission.keycloakPermission.name);
        return {
          ...permission,
          isAssigned
        };
      });

      return {
        success: true,
        permissions: allPermissionsWithAssignmentStatus,
      };
    } catch (error) {
      console.error("Error fetching permissions or policies:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      };
    }
  } catch (error) {
    console.error(`Error fetching permissions for role ${roleName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
      permissions: [],
    };
  }
}
