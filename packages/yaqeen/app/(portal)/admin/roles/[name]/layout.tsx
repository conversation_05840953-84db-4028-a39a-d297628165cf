import {Suspense} from 'react';
import {api} from '@/api';
import {TabNavigation} from './_components/tab-navigation';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {ProgressBarLink} from "@/components/progress-bar";

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{
    name: string;
  }>;
}

// Separate component for role data fetching
async function RoleHeader({roleName}: { roleName: string }) {
  const roleResponse = await api.keycloak.getRoleById({
    params: {roleName},
    requiresAuth: true,
  });

  // Check if response is successful and not an error
  const roleData = roleResponse.status === 200 ? roleResponse.body : null;

  return (
    <div className="flex w-full items-start justify-between py-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-medium tracking-tight">{roleName}</h1>
        {roleData && 'id' in roleData && (
          <div className="flex items-center gap-2">
            <span className="text-slate-700 font-mono text-sm">ID: {roleData.id}</span>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Layout for Keycloak Role Details
 * Server Component that provides the tab navigation for role details
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Layout({children, params}: LayoutProps) {
  // In Next.js 14, we need to await the params object before accessing its properties
  const decodedParams = await params;
  const roleName = decodeURIComponent(decodedParams.name);

  return (
    <div className="grid grid-rows-[auto_auto_1fr]">
      <section className="border-b bg-slate-50">
        <div className="mx-auto flex w-full flex-col self-stretch px-6">
          <Breadcrumb className="pt-4">
            <BreadcrumbList className="text-xs">
              <BreadcrumbItem>
                <BreadcrumbLink className="text-slate-700" asChild>
                  <ProgressBarLink href="/">
                    Home
                  </ProgressBarLink>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator/>
              <BreadcrumbItem>
                <BreadcrumbLink className="text-slate-700" asChild>
                  <ProgressBarLink href="/admin/roles">
                    Roles
                  </ProgressBarLink>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator/>
              <BreadcrumbItem>
                <BreadcrumbPage className="text-slate-500">{roleName}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <Suspense fallback={<div className="py-6">
            <div className="h-8 w-48 animate-pulse rounded bg-muted"/>
          </div>}>
            <RoleHeader roleName={roleName}/>
          </Suspense>
        </div>
      </section>

      <TabNavigation roleName={roleName}/>

      <div className="px-6 py-6">
        {children}
      </div>
    </div>
  );
}
