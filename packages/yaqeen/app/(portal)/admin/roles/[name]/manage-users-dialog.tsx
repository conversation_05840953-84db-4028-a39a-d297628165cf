'use client';

import {useState} from "react";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {Button} from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {Input} from "@/components/ui/input";
import {toast} from "@/lib/hooks/use-toast";
import {MagnifyingGlass, Spinner, UserPlus} from "@phosphor-icons/react";
import {addUsersToRole, removeUserFromRole, searchUsers} from "./actions";

interface ManageUsersDialogProps {
  roleName: string;
  usersInRole: KeycloakUser[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUsersUpdated: () => void;
}

export function ManageUsersDialog({
                                    roleName,
                                    usersInRole,
                                    open,
                                    onOpenChange,
                                    onUsersUpdated,
                                  }: ManageUsersDialogProps) {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchResult, setSearchResult] = useState<KeycloakUser | null>(null);
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const [removing, setRemoving] = useState<string | null>(null);

  // Handle user search
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResult(null);
      return;
    }

    setIsSearching(true);
    try {
      const result = await searchUsers(searchQuery);
      if (result.success && Array.isArray(result.users)) {
        // Find the first user that isn't already in the role
        const userIdsInRole = new Set(usersInRole.map(user => user.id));
        const availableUser = result.users.find((user: KeycloakUser) => !userIdsInRole.has(user.id));

        if (availableUser) {
          setSearchResult(availableUser);
        } else {
          toast({
            title: "No users available",
            description: "All matching users are already assigned to this role",
          });
          setSearchResult(null);
        }
      } else {
        toast({
          title: "No users found",
          description: "No users match your search criteria",
        });
        setSearchResult(null);
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred during search",
        variant: "destructive",
      });
      setSearchResult(null);
    } finally {
      setIsSearching(false);
    }
  };

  // Add a user to the role
  const handleAddUser = async () => {
    if (!searchResult) return;

    setIsAdding(true);
    try {
      const result = await addUsersToRole(roleName, [searchResult.id]);
      if (result.success) {
        const displayName = searchResult.username || searchResult.id;

        toast({
          title: "Success",
          description: `User "${displayName}" added to role successfully`,
        });

        // Clear search result and query
        setSearchResult(null);
        setSearchQuery("");

        onUsersUpdated();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to add user to role",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsAdding(false);
    }
  };

  // Remove a user from the role
  const handleRemoveUser = async (user: KeycloakUser) => {
    setRemoving(user.id);
    try {
      const result = await removeUserFromRole(roleName, user.id);
      if (result.success) {
        toast({
          title: "Success",
          description: `User "${user.username || user.id}" removed from role successfully`,
        });
        onUsersUpdated();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to remove user from role",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setRemoving(null);
    }
  };

  // Sort users in role alphabetically for display
  const sortedUsersInRole = [...usersInRole].sort((a, b) =>
    (a.username || "").localeCompare(b.username || "")
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Manage Users in Role</DialogTitle>
          <DialogDescription>
            Add or remove users for the <strong>{roleName}</strong> role.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Search for Users</h4>
            <div className="flex items-end gap-2">
              <div className="flex-1">
                <Input
                  placeholder="Search by username or email"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleSearch().catch(console.error);
                    }
                  }}
                />
              </div>
              <Button
                onClick={handleSearch}
                disabled={isSearching || !searchQuery.trim()}
                size="sm"
              >
                {isSearching ? (
                  <Spinner className="h-4 w-4 animate-spin mr-2"/>
                ) : (
                  <MagnifyingGlass className="h-4 w-4 mr-2"/>
                )}
                Search
              </Button>
            </div>
          </div>

          {searchResult && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Search Result</h4>
              <div className="border rounded-md p-3 flex items-center justify-between">
                <div className="flex flex-col">
                  <span className="font-medium">{searchResult.username || searchResult.id}</span>
                  {searchResult.email && (
                    <span className="text-xs text-muted-foreground">{searchResult.email}</span>
                  )}
                </div>
                <Button
                  size="sm"
                  onClick={handleAddUser}
                  disabled={isAdding}
                >
                  {isAdding ? (
                    <Spinner className="h-4 w-4 animate-spin mr-2"/>
                  ) : (
                    <UserPlus className="h-4 w-4 mr-2"/>
                  )}
                  Add to Role
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Current Users in Role</h4>
            <div className="border rounded-md p-3">
              {sortedUsersInRole.length > 0 ? (
                <ul className="space-y-2 max-h-[200px] overflow-y-auto">
                  {sortedUsersInRole.map((user) => (
                    <li key={user.id} className="flex items-center justify-between text-sm">
                      <div className="flex flex-col">
                        <span>{user.username || user.id}</span>
                        {user.email && (
                          <span className="text-xs text-muted-foreground">{user.email}</span>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveUser(user)}
                        disabled={removing === user.id}
                      >
                        {removing === user.id ? (
                          <Spinner className="h-4 w-4 animate-spin"/>
                        ) : (
                          "Remove"
                        )}
                      </Button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-muted-foreground">No users assigned to this role</p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
