"use server";

import {api} from "@/api";
import {type KeycloakPermission} from "@/api/contracts/keycloak-contract";
import {mapKeycloakPermissions, type MappedPermissionWithPolicies} from "@/lib/permission-mapper";

/**
 * Get permissions for a specific role and map them to URIs
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function getRoleMappedPermissions(roleName: string): Promise<{
  success: boolean;
  error?: string;
  permissions: MappedPermissionWithPolicies[];
}> {
  try {
    console.log(`Fetching permissions for role: ${roleName}`);

    // Fetch permissions from Keycloak
    const response = await api.keycloak.getRolePermissions({
      params: { roleName },
      requiresAuth: true,
    });

    if (response.status !== 200) {
      console.error(`Failed to fetch permissions: ${response.status}`);
      return {
        success: false,
        error: `Failed to fetch permissions for role ${roleName}`,
        permissions: [],
      };
    }

    // Type guard to check if response is an array
    if (!Array.isArray(response.body)) {
      console.error("Invalid response format:", response.body);
      return {
        success: false,
        error: "Invalid response format from server",
        permissions: [],
      };
    }

    const rolePermissions: KeycloakPermission[] = response.body;
    console.log(`Found ${rolePermissions.length} permissions for role ${roleName}`);

    // Map Keycloak permissions to application URIs
    const mappedPermissions = await mapKeycloakPermissions(rolePermissions);

    return {
      success: true,
      permissions: mappedPermissions,
    };
  } catch (error) {
    console.error(`Error fetching permissions for role ${roleName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
      permissions: [],
    };
  }
}
