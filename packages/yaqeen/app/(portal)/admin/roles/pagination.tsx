"use client";

import {useRouter} from "next/navigation";
import {
  Pagination as UIPagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface PaginationProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
}

/**
 * Client-side pagination component for Keycloak roles
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function Pagination({currentPage, pageSize, totalItems}: PaginationProps) {
  const router = useRouter();

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  // If there's only one page, don't show pagination
  if (totalPages <= 1) {
    return null;
  }

  // Handle page change
  const handlePageChange = (newPage: number) => {
    // Ensure page is within bounds
    newPage = Math.max(0, Math.min(newPage, totalPages - 1));

    // Update URL with new page
    const params = new URLSearchParams(window.location.search);
    params.set("page", newPage.toString());

    // Navigate to the new URL
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl);
  };

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];

    // Always show first page
    pages.push(0);

    // Show pages around current page
    for (let i = Math.max(1, currentPage - 1); i <= Math.min(totalPages - 2, currentPage + 1); i++) {
      if (!pages.includes(i)) {
        pages.push(i);
      }
    }

    // Always show last page if there is more than one page
    if (totalPages > 1) {
      pages.push(totalPages - 1);
    }

    // Sort pages
    return pages.sort((a, b) => a - b);
  };

  const pageNumbers = getPageNumbers();

  // Generate page items
  const items: (number | string)[] = [];
  const prevPage = pageNumbers[pageNumbers.length - 2];

  // Add first page
  if (pageNumbers.length > 0) {
    items.push(1);
  }

  // Add ellipsis and pages around current page
  pageNumbers.forEach((page, index) => {
    if (index > 0) {
      const pageNum = Number(page);
      const prevPageNum = Number(prevPage || 0);
      
      if (pageNum - (prevPageNum || 0) > 1) {
        items.push('...');
      }
      items.push(pageNum);
    }
  });

  return (
    <UIPagination>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            onClick={() => handlePageChange(currentPage - 1)}
            className={currentPage === 0 ? "pointer-events-none opacity-50" : "cursor-pointer"}
          />
        </PaginationItem>

        {items.map((item, index) => {
          if (item === '...') {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis/>
              </PaginationItem>
            );
          }

          return (
            <PaginationItem key={item}>
              <PaginationLink
                onClick={() => handlePageChange(typeof item === 'number' ? item - 1 : 0)}
                isActive={typeof item === 'number' && item - 1 === currentPage}
                className="cursor-pointer"
              >
                {item}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        <PaginationItem>
          <PaginationNext
            onClick={() => handlePageChange(currentPage + 1)}
            className={currentPage >= totalPages - 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
          />
        </PaginationItem>
      </PaginationContent>
    </UIPagination>
  );
}
