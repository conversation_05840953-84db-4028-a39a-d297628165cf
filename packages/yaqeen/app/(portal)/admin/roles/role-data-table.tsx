"use client";

import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {DataTable} from "@/components/ui/data-table/data-table";
import {columns} from "./columns";

interface RoleDataTableProps {
  data: KeycloakRole[];
  pageNumber: number;
  pageSize: number;
  searchQuery: string;
}

/**
 * Data table component for Keycloak roles with client-side pagination
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function RoleDataTable({data, pageNumber, pageSize, searchQuery}: RoleDataTableProps) {
  // Filter data based on search query
  const filteredData = searchQuery
    ? data.filter(
      (role) =>
        role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (role.description?.toLowerCase() || "").includes(searchQuery.toLowerCase())
    )
    : data;

  // Sort data alphabetically by name
  const sortedData = [...filteredData].sort((a, b) => a.name.localeCompare(b.name));

  // Calculate start and end indices for current page (1-based page number)
  const startIndex = (pageNumber - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  // Paginate data
  const paginatedData = sortedData.slice(startIndex, endIndex);

  // Calculate total for pagination
  const totalCount = sortedData.length;

  return (
    <DataTable
      columns={columns}
      data={{
        data: paginatedData,
        total: totalCount,
      }}
      searchPlaceholder=""
      emptyMessage={searchQuery ? `No roles matching "${searchQuery}" were found.` : "No roles are available in this realm."}
      paginationEnabled={false}
      rowClickId="name"
      baseRedirectPath="/admin/roles"
      clientPagination={true}
      pageSize={pageSize}
    />
  );
}
