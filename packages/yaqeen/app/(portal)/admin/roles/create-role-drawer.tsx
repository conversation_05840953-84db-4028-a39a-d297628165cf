"use client";

import React, {useState} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Textarea} from "@/components/ui/textarea";
import {useToast} from "@/lib/hooks/use-toast";
import {useRouter} from "next/navigation";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import {ShieldCheck, Check} from "@phosphor-icons/react";
import {createRole} from "./role-actions";

interface CreateRoleDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Drawer component for creating a new Keycloak role
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function CreateRoleDrawer({open, onOpenChange}: CreateRoleDrawerProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [roleData, setRoleData] = useState({
    name: "",
    description: ""
  });

  const {toast} = useToast();
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const {name, value} = e.target;
    setRoleData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateRole = async () => {
    if (!roleData.name.trim()) {
      toast({
        title: "Error",
        description: "Role name is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await createRole(roleData);

      if (result.success) {
        toast({
          title: "Success",
          description: "Role created successfully",
        });

        // Reset form and close drawer
        handleClose();

        // Navigate to the role permissions page
        router.push(`/admin/roles/${encodeURIComponent(roleData.name)}/permissions`);
      } else {
        throw new Error(result.error || "Failed to create role");
      }
    } catch (error) {
      console.error("Error creating role:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create role",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset state when closing
    setRoleData({
      name: "",
      description: ""
    });
    onOpenChange(false);
  };

  return (
    <Drawer
      direction="right"
      open={open}
      onOpenChange={handleClose}
    >
      <DrawerContent className="w-[30%] max-w-md">
        <div className="grid h-screen grid-rows-[auto_1fr_auto]">
          <DrawerHeader className="p-6">
            <DrawerTitle className="text-2xl font-bold flex items-center">
              <ShieldCheck className="mr-2 h-5 w-5"/>
              Create New Role
            </DrawerTitle>
            <DrawerDescription>
              Create a new role in Keycloak
            </DrawerDescription>
          </DrawerHeader>

          <div className="overflow-auto p-6 space-y-4">
            <div>
              <Label htmlFor="name" className="text-sm font-medium text-slate-900">
                Role Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={roleData.name}
                onChange={handleInputChange}
                placeholder="Enter role name"
                required
              />
            </div>

            <div>
              <Label htmlFor="description" className="text-sm font-medium text-slate-900">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={roleData.description}
                onChange={handleInputChange}
                placeholder="Enter role description"
                rows={4}
              />
            </div>
          </div>

          <DrawerFooter className="p-6 flex flex-row justify-between">
            <DrawerClose asChild>
              <Button variant="outline">
                Cancel
              </Button>
            </DrawerClose>
            <Button
              type="button"
              onClick={handleCreateRole}
              disabled={isSubmitting || !roleData.name}
            >
              {isSubmitting ? "Creating..." : (
                <>
                  <Check className="mr-2 h-4 w-4"/>
                  Create Role
                </>
              )}
            </Button>
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
