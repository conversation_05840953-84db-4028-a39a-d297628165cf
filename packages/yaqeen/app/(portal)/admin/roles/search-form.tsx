"use client";

import {useRouter} from "next/navigation";
import {useState, useEffect} from "react";
import {useProgressBar} from "@/components/progress-bar";
import {Button} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {MagnifyingGlass} from "@phosphor-icons/react/dist/ssr";

/**
 * Search form component for Keycloak roles
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function SearchForm() {
  const [search, setSearch] = useState("");
  const router = useRouter();
  const progress = useProgressBar();

  // Initialize search from URL on mount
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const searchParam = params.get("search");
    if (searchParam) {
      setSearch(searchParam);
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    progress.start();

    // Build URL with search parameter
    const params = new URLSearchParams(window.location.search);

    if (search) {
      params.set("search", search);
    } else {
      params.delete("search");
    }

    // Reset to page 0 when searching
    params.set("page", "0");

    // Navigate to the new URL
    const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl);

    // Force a refresh to ensure data is reloaded
    setTimeout(() => {
      progress.done();
      router.refresh();
    }, 100);
  };

  return (
    <form onSubmit={handleSubmit} className="mb-6 flex w-full items-center space-x-2">
      <div className="relative flex-1">
        <MagnifyingGlass className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"/>
        <Input
          type="text"
          placeholder="Search roles by name or description..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-10 w-full"
        />
      </div>
      <Button type="submit" className="shrink-0">
        Search
      </Button>
    </form>
  );
}
