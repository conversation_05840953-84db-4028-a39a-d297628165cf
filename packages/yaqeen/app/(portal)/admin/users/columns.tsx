"use client";

import {type ColumnDef} from "@tanstack/react-table";
import {Badge} from "@/components/ui/badge";
import {format} from "date-fns";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {CheckCircle, XCircle, Info} from "@phosphor-icons/react/dist/ssr";
import {EditUserButton} from "./edit-user-button";
import {Button} from "@/components/ui/button";
import Link from "next/link";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";

/**
 * Columns definition for the Keycloak users table
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export const columns: ColumnDef<KeycloakUser>[] = [
  {
    accessorKey: "keycloakId",
    header: "Keycloak ID",
    cell: ({row}) => {
      const id = row.original.id;
      return (
        <div className="font-mono text-xs text-muted-foreground whitespace-nowrap overflow-visible" title={id}>
          {id}
        </div>
      );
    },
    size: 300,
  },
  {
    accessorKey: "username",
    header: "Username",
    cell: ({row}) => {
      return <div className="font-medium">{row.getValue("username")}</div>;
    },
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({row}) => {
      return <div>{row.getValue("email") || "-"}</div>;
    },
  },
  {
    accessorKey: "firstName",
    header: "First Name",
    cell: ({row}) => {
      return <div>{row.getValue("firstName") || "-"}</div>;
    },
  },
  {
    accessorKey: "lastName",
    header: "Last Name",
    cell: ({row}) => {
      return <div>{row.getValue("lastName") || "-"}</div>;
    },
  },
  {
    accessorKey: "enabled",
    header: "Status",
    cell: ({row}) => {
      const enabled = row.getValue("enabled");

      if (enabled) {
        return (
          <div className="flex items-center">
            <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800">
              <CheckCircle className="h-3.5 w-3.5"/>
              <span>Active</span>
            </Badge>
          </div>
        );
      } else {
        return (
          <div className="flex items-center">
            <Badge variant="destructive" className="flex items-center gap-1 bg-red-100 text-red-800">
              <XCircle className="h-3.5 w-3.5"/>
              <span>Disabled</span>
            </Badge>
          </div>
        );
      }
    },
  },
  {
    accessorKey: "createdTimestamp",
    header: "Created Date",
    cell: ({row}) => {
      const timestamp = row.original.createdTimestamp;
      if (!timestamp) return <div>N/A</div>;

      const date = new Date(timestamp);
      return <div>{format(date, "MMM d, yyyy")}</div>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({row}) => {
      const user = row.original;
      return (
        <div className="flex justify-end gap-1" data-action="true">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  asChild
                >
                  <Link href={`/admin/users/${user.id}`}>
                    <Info className="h-4 w-4" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>View Details</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <EditUserButton userId={user.id} />
        </div>
      );
    },
  },
];
