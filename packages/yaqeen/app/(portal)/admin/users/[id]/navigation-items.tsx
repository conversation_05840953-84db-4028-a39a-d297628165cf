export interface TabItem {
  label: string;
  href: string;
  count?: number;
}

export const USER_TABS = (userId: string): TabItem[] => [
  {
    label: "User Details",
    href: `/admin/users/${userId}`,
  },
  {
    label: "Roles",
    href: `/admin/users/${userId}/roles`,
  },
  {
    label: "Groups",
    href: `/admin/users/${userId}/groups`,
  },
  {
    label: "User Location",
    href: `/admin/users/${userId}/locations`,
  },
];
