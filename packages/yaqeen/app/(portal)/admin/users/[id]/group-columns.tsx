"use client";

import {type ColumnDef} from "@tanstack/react-table";
import {Badge} from "@/components/ui/badge";
import {type KeycloakGroup} from "@/api/contracts/keycloak-contract";

/**
 * Columns definition for the Keycloak groups table
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export const groupColumns: ColumnDef<KeycloakGroup>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({row}) => {
      return <div className="font-medium">{row.getValue("name")}</div>;
    },
  },
  {
    accessorKey: "path",
    header: "Path",
    cell: ({row}) => {
      return <div className="text-xs text-muted-foreground">{row.getValue("path")}</div>;
    },
  },
  {
    accessorKey: "subGroups",
    header: "Subgroups",
    cell: ({row}) => {
      const subGroups = row.original.subGroups || [];
      return (
        <Badge variant="secondary">
          {subGroups.length}
        </Badge>
      );
    },
  },
  {
    accessorKey: "id",
    header: "ID",
    cell: ({row}) => {
      return <div className="text-xs text-muted-foreground">{row.getValue("id")}</div>;
    },
  },
];
