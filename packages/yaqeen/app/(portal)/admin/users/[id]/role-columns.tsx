"use client";

import type {ColumnDef} from "@tanstack/react-table";
import {Badge} from "@/components/ui/badge";
import type {KeycloakRole} from "@/api/contracts/keycloak-contract";

/**
 * Columns definition for the Keycloak roles table
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export const roleColumns: ColumnDef<KeycloakRole>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({row}) => {
      return <div className="font-medium">{row.getValue("name")}</div>;
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({row}) => {
      return <div>{row.getValue("description") || "-"}</div>;
    },
  },
  {
    accessorKey: "clientRole",
    header: "Type",
    cell: ({row}) => {
      const isClientRole = row.getValue("clientRole");
      return (
        <Badge variant={isClientRole ? "secondary" : "outline"}>
          {isClientRole ? "Client" : "Realm"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "containerId",
    header: "Container",
    cell: ({row}) => {
      return <div className="text-xs text-muted-foreground">{row.getValue("containerId") || "-"}</div>;
    },
  },
  {
    accessorKey: "id",
    header: "ID",
    cell: ({row}) => {
      return <div className="text-xs text-muted-foreground">{row.getValue("id")}</div>;
    },
  },
];
