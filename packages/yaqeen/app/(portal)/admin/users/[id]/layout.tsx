import {USER_TABS} from "./navigation-items";
import TabNavigation from "./_components/tab-navigation";
import {Suspense} from "react";
import Loading from "@/app/(portal)/loading";
import {api} from "@/api";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {ProgressBarLink} from "@/components/progress-bar";

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{
    id: string;
  }>;
}

/**
 * Layout for Keycloak User Details
 * Server Component that provides the tab navigation for user details
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Layout({children, params}: LayoutProps) {
  // In Next.js 14, we need to await the params object before accessing its properties
  const decodedParams = await params;
  const userId = decodedParams.id;
  let userData = null;

  try {
    const userResponse = await api.keycloak.getUserById({
      params: {
        id: userId,
      },
      requiresAuth: true,
    });

    if ('code' in userResponse.body) {
      throw new Error(userResponse.body.desc);
    }

    userData = userResponse.body;
  } catch (err) {
    console.error("Error fetching user data:", err);
  }

  const tabs = USER_TABS(userId);

  // Create a suspense key for better rendering performance
  const suspenseKey = `user-layout-${userId}-${Date.now()}`;



  // Construct the page name with username if available
  const pageName = userData ? `${userData.username}` : "User Details";

  return (
    <div className="grid grid-rows-[auto_auto_1fr]">
      <section className="border-b bg-slate-50">
        <div className="mx-auto flex w-full flex-col self-stretch px-6">
          <Breadcrumb className="pt-4">
            <BreadcrumbList className="text-xs">
              <BreadcrumbItem>
                <BreadcrumbLink className="text-slate-700" asChild>
                  <ProgressBarLink href="/">
                    Home
                  </ProgressBarLink>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator/>
              <BreadcrumbItem>
                <BreadcrumbLink className="text-slate-700" asChild>
                  <ProgressBarLink href="/admin/users">
                    Users
                  </ProgressBarLink>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator/>
              <BreadcrumbItem>
                <BreadcrumbPage className="text-slate-500">{pageName}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex w-full items-start justify-between py-6">
            <div className="flex flex-col gap-2">
              <h1 className="text-3xl font-medium tracking-tight">{pageName}</h1>
              {userData && (
                <div className="flex items-center gap-2">
                  <span className="text-slate-700 font-mono text-sm">ID: {userId}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
      <TabNavigation tabs={tabs}/>
      <div className="px-6 py-6">
        <Suspense key={suspenseKey} fallback={<Loading/>}>
          {children}
        </Suspense>
      </div>
    </div>
  );
}
