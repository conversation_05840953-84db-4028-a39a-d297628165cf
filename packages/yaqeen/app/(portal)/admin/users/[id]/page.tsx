import { Suspense } from "react";
import { api } from "@/api";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";
import Loading from "@/app/(portal)/loading";
import { format } from "date-fns";
import { type KeycloakUser } from "@/api/contracts/keycloak-contract";

/**
 * Keycloak User Details Page - User Information Tab
 * Server Component that fetches and displays basic user information
 * Following Java Spring Boot architecture standards with proper layer separation
 */
async function UserContent({ userId }: { userId: string }) {
  let userData: KeycloakUser | null = null;

  try {
    const response = await api.keycloak.getUserById({
      params: { id: userId },
      requiresAuth: true,
    });

    if (response.status !== 200) {
      throw new Error(`Failed to fetch user: ${response.status}`);
    }

    // Type guard to ensure response.body is a KeycloakUser
    if ('username' in response.body) {
      userData = response.body;
    } else {
      throw new Error('Invalid user data received');
    }

  } catch (error) {
    console.error("Error fetching Keycloak user details:", error);
    return <div>Error loading user data</div>;
  }

  if (!userData) {
    return <div>User not found</div>;
  }

  return (
    <Card className="overflow-hidden border bg-card text-card-foreground shadow">
      <CardHeader className="border-b bg-muted/40 px-3 py-4">
        <CardTitle className="text-md">User Information</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <dl className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="flex flex-col">
            <dt className="font-semibold text-sm text-muted-foreground mb-1">Username</dt>
            <dd>{userData.username}</dd>
          </div>
          {userData.email && (
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Email</dt>
              <dd>
                {userData.email}
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-xs text-muted-foreground">Verified:</span>
                  {userData.emailVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-500"/>
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500"/>
                  )}
                </div>
              </dd>
            </div>
          )}
          <div className="flex flex-col">
            <dt className="font-semibold text-sm text-muted-foreground mb-1">Status</dt>
            <dd>
              {userData.enabled ? (
                <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
              ) : (
                <Badge variant="destructive">Disabled</Badge>
              )}
            </dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-sm text-muted-foreground mb-1">First Name</dt>
            <dd>{userData.firstName || '-'}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-sm text-muted-foreground mb-1">Last Name</dt>
            <dd>{userData.lastName || '-'}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-sm text-muted-foreground mb-1">TOTP Enabled</dt>
            <dd>
              {userData.totp ? (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Yes</Badge>
              ) : (
                <Badge variant="secondary">No</Badge>
              )}
            </dd>
          </div>
          {userData.createdTimestamp && (
            <div className="flex flex-col">
              <dt className="font-semibold text-sm text-muted-foreground mb-1">Created</dt>
              <dd>{format(new Date(userData.createdTimestamp), 'MMM d, yyyy')}</dd>
            </div>
          )}
          <div className="flex flex-col">
            <dt className="font-semibold text-sm text-muted-foreground mb-1">ID</dt>
            <dd className="font-mono text-sm">{userData.id}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

/**
 * Keycloak User Details Page
 * Server Component that fetches and displays Keycloak user details
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const userId = await params.then(params => params.id);

  return (
    <Suspense fallback={<Loading/>}>
      <UserContent userId={userId}/>
    </Suspense>
  );
}
