"use client";

import {ProgressBarLink} from "@/components/progress-bar";
import {cn} from "@/lib/utils";
import {usePathname} from "next/navigation";
import {type TabItem} from "../navigation-items";

interface TabNavigationProps {
  tabs: TabItem[];
}

export default function TabNavigation({tabs}: TabNavigationProps) {
  const pathname = usePathname();

  return (
    <div className="border-b">
      <div className="flex px-6">
        {tabs.map((tab) => (
          <ProgressBarLink
            key={tab.label}
            href={tab.href}
            className={cn(
              "mx-3 box-border cursor-pointer gap-2 py-3 text-sm first:ml-0 last:mr-0",
              "border-b-2 border-transparent",
              pathname === tab.href
                ? "border-b-slate-900 font-bold text-slate-900"
                : "text-slate-700 hover:border-slate-400 hover:text-slate-900",
              "box-border transition duration-300"
            )}
          >
            {tab.label}
            {tab.count !== undefined && tab.count > 0 && (
              <span className="ml-2 rounded-full bg-primary px-2 py-0.5 text-xs font-medium text-white">
                {tab.count}
              </span>
            )}
          </ProgressBarLink>
        ))}
      </div>
    </div>
  );
}
