import {Suspense} from "react";
import {api} from "@/api";
import {DataTable} from "@/components/ui/data-table/data-table";
import {roleColumns} from "../role-columns";
import {ManageRolesButton} from "../manage-roles-button";
import {ShieldPlus, MagnifyingGlass} from "@phosphor-icons/react/dist/ssr";
import Loading from "@/app/(portal)/loading";
import {Input} from "@/components/ui/input";
import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {Alert, AlertTitle, AlertDescription} from "@/components/ui/alert";

/**
 * Keycloak User Roles Tab
 * Server Component that fetches and displays roles assigned to a user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
async function RolesContent({userId}: { userId: string }) {
  let userRoles: KeycloakRole[] = [];

  try {
    const response = await api.keycloak.getUserRoles({
      params: {userId},
      requiresAuth: true,
    });

    if (response.status !== 200) {
      throw new Error(`Failed to fetch roles: ${response.status}`);
    }

    // Type guard to ensure response.body is an array
    if (!Array.isArray(response.body)) {
      throw new Error('Invalid response format from server');
    }

    userRoles = response.body;
    console.log(`Successfully fetched ${userRoles.length} roles`);
  } catch (error) {
    console.error('Error fetching roles:', error);
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="relative flex-1">
            <h2 className="text-lg font-medium">User Roles</h2>
          </div>
          <ManageRolesButton userId={userId} userRoles={userRoles}/>
        </div>
        <div className="rounded-md border">
          <div className="p-4">
            <Alert variant="destructive" className="mb-6">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                Failed to fetch user roles from Keycloak server.
                <div className="mt-2 text-xs">
                  <details>
                    <summary>Error details</summary>
                    <pre className="mt-2 whitespace-pre-wrap text-xs">
                      {error instanceof Error ? error.message : 'An unexpected error occurred'}
                    </pre>
                  </details>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1">
          {userRoles.length > 0 && (
            <>
              <MagnifyingGlass className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
              <Input
                type="search"
                placeholder="Search roles..."
                className="w-full pl-8"
                id="roles-search"
              />
            </>
          )}
          {userRoles.length === 0 && (
            <h2 className="text-lg font-medium">User Roles</h2>
          )}
        </div>
        <ManageRolesButton userId={userId} userRoles={userRoles}/>
      </div>

      {userRoles.length > 0 ? (
        <div className="rounded-md border">
          <DataTable
            columns={roleColumns}
            data={{ total: userRoles.length, data: userRoles }}
            emptyMessage="No roles assigned to this user"
            paginationEnabled={false}
          />
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-12 bg-muted/20 rounded-md border">
          <ShieldPlus className="h-12 w-12 text-muted-foreground/50"/>
          <h3 className="mt-4 text-lg font-medium">No roles assigned</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            This user doesnt have any roles assigned yet.
          </p>
        </div>
      )}
    </div>
  );
}

/**
 * Keycloak User Roles Tab
 * Server Component that fetches and displays roles assigned to a specific user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const userId = (await params).id;

  return (
    <Suspense fallback={<Loading/>}>
      <RolesContent userId={userId}/>
    </Suspense>
  );
}
