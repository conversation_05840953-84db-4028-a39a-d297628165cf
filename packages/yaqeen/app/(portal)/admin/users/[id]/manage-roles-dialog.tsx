'use client';

import {useState, useEffect} from "react";
import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {Button} from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {toast} from "@/lib/hooks/use-toast";
import {PlusCir<PERSON>, Spinner, CaretDown, Check} from "@phosphor-icons/react";
import {addRolesToUser, removeRolesFromUser} from "./actions";
// TODO: Implement addRolesToUser function in actions.ts
import {cn} from "@/lib/utils";

interface ManageRolesDialogProps {
  userId: string;
  userRoles: KeycloakRole[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRolesUpdated: () => void;
  availableRoles?: KeycloakRole[];
}

export function ManageRolesDialog({
                                    userId,
                                    userRoles,
                                    open,
                                    onOpenChange,
                                    onRolesUpdated,
                                    availableRoles: initialAvailableRoles,
                                  }: ManageRolesDialogProps) {
  const [availableRoles, setAvailableRoles] = useState<KeycloakRole[]>(initialAvailableRoles || []);
  const [selectedRoleId, setSelectedRoleId] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [removing, setRemoving] = useState<string | null>(null);
  const [commandOpen, setCommandOpen] = useState(false);

  // Use effect to update available roles when props change
  useEffect(() => {
    if (initialAvailableRoles) {
      console.log("ManageRolesDialog - Received available roles:", initialAvailableRoles.length);
      setAvailableRoles(initialAvailableRoles);
      setSelectedRoleId("");
    }
  }, [initialAvailableRoles]);

  // Add a role to the user
  const handleAddRole = async () => {
    if (!selectedRoleId) return;

    setLoading(true);
    try {
      const roleToAdd = availableRoles.find(role => role.id === selectedRoleId);
      if (!roleToAdd) {
        throw new Error("Selected role not found");
      }

      const result = await addRolesToUser(userId, [roleToAdd]);
      if (result.success) {
        toast({
          title: "Success",
          description: `Role "${roleToAdd.name}" added successfully`,
        });
        onRolesUpdated();
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to add role",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Remove a role from the user
  const handleRemoveRole = async (role: KeycloakRole) => {
    setRemoving(role.id);
    try {
      const result = await removeRolesFromUser(userId, [role]);
      if (result.success) {
        toast({
          title: "Success",
          description: `Role "${role.name}" removed successfully`,
        });
        onRolesUpdated();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to remove role",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setRemoving(null);
    }
  };

  // Sort user roles alphabetically for display
  const sortedUserRoles = [...userRoles].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  // Get the selected role name for display
  const selectedRoleName = selectedRoleId
    ? availableRoles.find(role => role.id === selectedRoleId)?.name
    : null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Manage User Roles</DialogTitle>
          <DialogDescription>
            Add or remove roles for this user.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4 flex-1 overflow-auto">
          <div className="flex items-end gap-2">
            <div className="flex-1">
              <Popover open={commandOpen} onOpenChange={setCommandOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={commandOpen}
                    className="w-full justify-between"
                    disabled={loading || availableRoles.length === 0}
                  >
                    {selectedRoleName || "Select a role to add"}
                    <CaretDown className="ml-2 h-4 w-4 shrink-0 opacity-50"/>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-[400px]">
                  <Command>
                    <CommandInput placeholder="Search roles..."/>
                    <CommandEmpty>No roles found.</CommandEmpty>
                    <CommandGroup>
                      <CommandList className="max-h-[300px]">
                        {availableRoles.map((role) => (
                          <CommandItem
                            key={role.id}
                            value={role.name}
                            onSelect={() => {
                              setSelectedRoleId(role.id);
                              setCommandOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                selectedRoleId === role.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {role.name}
                          </CommandItem>
                        ))}
                      </CommandList>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
            <Button
              onClick={handleAddRole}
              disabled={!selectedRoleId || loading}
              size="sm"
            >
              {loading ? (
                <Spinner className="h-4 w-4 animate-spin mr-2"/>
              ) : (
                <PlusCircle className="h-4 w-4 mr-2"/>
              )}
              Add
            </Button>
          </div>

          <div className="border rounded-md p-4 flex-1 overflow-auto">
            <h4 className="text-sm font-medium mb-3">Current Roles</h4>
            {sortedUserRoles.length > 0 ? (
              <ul className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
                {sortedUserRoles.map((role) => (
                  <li key={role.id}
                      className="flex items-center justify-between text-sm p-2 hover:bg-muted/50 rounded-md">
                    <span className="font-medium">{role.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveRole(role)}
                      disabled={removing === role.id}
                      className="ml-2"
                    >
                      {removing === role.id ? (
                        <Spinner className="h-4 w-4 animate-spin"/>
                      ) : (
                        "Remove"
                      )}
                    </Button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-muted-foreground">No roles assigned</p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
