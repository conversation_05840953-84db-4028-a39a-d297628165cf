'use client';

import {useState, useEffect} from "react";
import {type KeycloakGroup} from "@/api/contracts/keycloak-contract";
import {Button} from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {toast} from "@/lib/hooks/use-toast";
import {PlusCircle, Spinner, CaretDown, Check} from "@phosphor-icons/react";
import {addUserToGroup, removeUserFromGroup} from "./actions";
import {cn} from "@/lib/utils";

interface ManageGroupsDialogProps {
  userId: string;
  userGroups: KeycloakGroup[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGroupsUpdated: () => void;
  availableGroups?: KeycloakGroup[];
}

export function ManageGroupsDialog({
                                     userId,
                                     userGroups,
                                     open,
                                     onOpenChange,
                                     onGroupsUpdated,
                                     availableGroups: initialAvailableGroups,
                                   }: ManageGroupsDialogProps) {
  const [availableGroups, setAvailableGroups] = useState<KeycloakGroup[]>(initialAvailableGroups || []);
  const [selectedGroupId, setSelectedGroupId] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [removing, setRemoving] = useState<string | null>(null);
  const [commandOpen, setCommandOpen] = useState(false);

  // Use effect to update available groups when props change
  useEffect(() => {
    if (initialAvailableGroups) {
      console.log("ManageGroupsDialog - Received available groups:", initialAvailableGroups.length);
      setAvailableGroups(initialAvailableGroups);
      setSelectedGroupId("");
    }
  }, [initialAvailableGroups]);

  // Add a group to the user
  const handleAddGroup = async () => {
    if (!selectedGroupId) return;

    setLoading(true);
    try {
      const groupToAdd = availableGroups.find(group => group.id === selectedGroupId);
      if (!groupToAdd) {
        throw new Error("Selected group not found");
      }

      const result = await addUserToGroup(userId, groupToAdd.id);
      if (result.success) {
        toast({
          title: "Success",
          description: `Group "${groupToAdd.name}" added successfully`,
        });
        onGroupsUpdated();
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to add group",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Remove a group from the user
  const handleRemoveGroup = async (group: KeycloakGroup) => {
    setRemoving(group.id);
    try {
      const result = await removeUserFromGroup(userId, group.id);
      if (result.success) {
        toast({
          title: "Success",
          description: `Group "${group.name}" removed successfully`,
        });
        onGroupsUpdated();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to remove group",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setRemoving(null);
    }
  };

  // Sort user groups alphabetically for display
  const sortedUserGroups = [...userGroups].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  // Get the selected group name for display
  const selectedGroupName = selectedGroupId
    ? availableGroups.find(group => group.id === selectedGroupId)?.name
    : null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Manage User Groups</DialogTitle>
          <DialogDescription>
            Add or remove groups for this user.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex items-end gap-2">
            <div className="flex-1">
              <Popover open={commandOpen} onOpenChange={setCommandOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={commandOpen}
                    className="w-full justify-between"
                    disabled={loading || availableGroups.length === 0}
                  >
                    {selectedGroupName || "Select a group to add"}
                    <CaretDown className="ml-2 h-4 w-4 shrink-0 opacity-50"/>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-[300px]">
                  <Command>
                    <CommandInput placeholder="Search groups..."/>
                    <CommandEmpty>No groups found.</CommandEmpty>
                    <CommandGroup>
                      <CommandList>
                        {availableGroups.map((group) => (
                          <CommandItem
                            key={group.id}
                            value={group.name}
                            onSelect={() => {
                              setSelectedGroupId(group.id);
                              setCommandOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                selectedGroupId === group.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {group.name}
                          </CommandItem>
                        ))}
                      </CommandList>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
            <Button
              onClick={handleAddGroup}
              disabled={!selectedGroupId || loading}
              size="sm"
            >
              {loading ? (
                <Spinner className="h-4 w-4 animate-spin mr-2"/>
              ) : (
                <PlusCircle className="h-4 w-4 mr-2"/>
              )}
              Add
            </Button>
          </div>

          <div className="border rounded-md p-3">
            <h4 className="text-sm font-medium mb-2">Current Groups</h4>
            {sortedUserGroups.length > 0 ? (
              <ul className="space-y-2">
                {sortedUserGroups.map((group) => (
                  <li key={group.id} className="flex items-center justify-between text-sm">
                    <span>{group.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveGroup(group)}
                      disabled={removing === group.id}
                    >
                      {removing === group.id ? (
                        <Spinner className="h-4 w-4 animate-spin"/>
                      ) : (
                        "Remove"
                      )}
                    </Button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-muted-foreground">No groups assigned</p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
