import {Suspense} from "react";
import {api} from "@/api";
import {DataTable} from "@/components/ui/data-table/data-table";
import {groupColumns} from "../group-columns";
import {ManageGroupsButton} from "../manage-groups-button";
import {Users, MagnifyingGlass} from "@phosphor-icons/react/dist/ssr";
import Loading from "@/app/(portal)/loading";
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert";
import {Warning} from "@phosphor-icons/react/dist/ssr";
import {Input} from "@/components/ui/input";
import {type KeycloakGroup} from "@/api/contracts/keycloak-contract";

/**
 * Keycloak User Groups Tab
 * Server Component that fetches and displays groups assigned to a specific user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // In Next.js 14, we need to await the params object before accessing its properties
  const decodedParams = await params;
  const userId = decodedParams.id;

  // Create a suspense key that includes the user ID
  const suspenseKey = `user-groups-${userId}-${Date.now()}`;

  // Fetch user groups from Keycloak
  let userGroups: KeycloakGroup[] = [];
  let error: Error | null = null;

  try {
    console.log(`Fetching Keycloak user groups: userId=${userId}`);

    // Fetch user groups
    const groupsResponse = await api.keycloak.getUserGroups({
      params: {
        userId: userId,
      },
      requiresAuth: true,
    });

    if ('code' in groupsResponse.body) {
      // Error response
      throw new Error(groupsResponse.body.desc);
    }

    userGroups = groupsResponse.body;

    console.log(`Successfully fetched ${userGroups.length} groups`);
  } catch (err) {
    console.error("Error fetching Keycloak user groups:", err);
    error = err instanceof Error ? err : new Error(String(err));
    userGroups = [];
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-6 mx-6 mt-6">
        <Warning className="h-4 w-4"/>
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to fetch user groups.
          <div className="mt-2 text-xs">
            <details>
              <summary>Error details</summary>
              <pre className="mt-2 whitespace-pre-wrap text-xs">
                {error instanceof Error 
                  ? error.message 
                  : JSON.stringify(error, null, 2)}
              </pre>
            </details>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Suspense key={suspenseKey} fallback={<Loading/>}>
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="relative flex-1">
            {userGroups.length > 0 && (
              <>
                <MagnifyingGlass className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
                <Input
                  type="search"
                  placeholder="Search groups..."
                  className="w-full pl-8"
                  id="groups-search"
                />
              </>
            )}
            {userGroups.length === 0 && (
              <h2 className="text-lg font-medium">User Groups</h2>
            )}
          </div>
          <ManageGroupsButton userId={userId} userGroups={userGroups}/>
        </div>

        {userGroups.length > 0 ? (
          <div className="rounded-md border">
            <DataTable
              columns={groupColumns}
              data={{ total: userGroups.length, data: userGroups }}
              emptyMessage="No groups assigned to this user"
              paginationEnabled={false}
            />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 bg-muted/20 rounded-md border">
            <Users className="h-12 w-12 text-muted-foreground/50"/>
            <h3 className="mt-4 text-lg font-medium">No groups assigned</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              This user doesnt belong to any groups yet.
            </p>
          </div>
        )}
      </div>
    </Suspense>
  );
}
