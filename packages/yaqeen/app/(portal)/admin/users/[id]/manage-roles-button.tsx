'use client';

import {useState, useEffect} from "react";
import {Button} from "@/components/ui/button";
import {<PERSON><PERSON><PERSON>, Spinner} from "@phosphor-icons/react";
import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {ManageRolesDialog} from "./manage-roles-dialog";
import {useRouter} from "next/navigation";
import {getAvailableRoles} from "./actions";
import {toast} from "@/lib/hooks/use-toast";

interface ManageRolesButtonProps {
  userId: string;
  userRoles: KeycloakRole[];
}

export function ManageRolesButton({userId, userRoles}: ManageRolesButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [availableRoles, setAvailableRoles] = useState<KeycloakRole[]>([]);
  const router = useRouter();

  // Debug log when component mounts
  useEffect(() => {
    console.log("ManageRolesButton - Component mounted with userId:", userId);
    console.log("ManageRolesButton - User has", userRoles.length, "roles");
  }, [userId, userRoles]);

  const handleRolesUpdated = () => {
    // Refresh the page data
    console.log("ManageRolesButton - Roles updated, refreshing page");
    router.refresh();
  };

  const handleButtonClick = async () => {
    console.log("ManageRolesButton - Button clicked, fetching roles");

    setLoading(true);
    try {
      const result = await getAvailableRoles(userId);
      console.log("ManageRolesButton - Direct API call result:", result);

      if (result.success) {
        // Sort roles alphabetically by name
        const sortedRoles = [...result.roles].sort((a, b) =>
          a.name.localeCompare(b.name)
        );

        setAvailableRoles(sortedRoles);
        setDialogOpen(true);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to fetch available roles",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("ManageRolesButton - Error in direct API call:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    console.log("ManageRolesButton - Dialog open state changed to:", open);
    setDialogOpen(open);
  };

  return (
    <>
      <Button
        variant="default"
        size="sm"
        onClick={handleButtonClick}
        className="bg-[#9CE642] text-black hover:bg-[#8AD532] flex items-center gap-1"
        disabled={loading}
      >
        {loading ? (
          <Spinner className="h-4 w-4 animate-spin mr-2"/>
        ) : (
          <ShieldPlus className="h-4 w-4 mr-2"/>
        )}
        <span>Manage Roles</span>
      </Button>

      {/* Only render the dialog when it's open to avoid state conflicts */}
      {dialogOpen && (
        <ManageRolesDialog
          userId={userId}
          userRoles={userRoles}
          open={dialogOpen}
          onOpenChange={handleDialogOpenChange}
          onRolesUpdated={handleRolesUpdated}
          availableRoles={availableRoles}
        />
      )}
    </>
  );
}
