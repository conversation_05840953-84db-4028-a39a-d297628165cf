"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type Table, type Row } from "@tanstack/react-table";
import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Save } from "lucide-react";
import { useToast } from "@/lib/hooks/use-toast";
import { getUserById, updateUserLocations } from "../actions";
import { useProgressBar } from "@/components/progress-bar";
import { useRouter } from "next/navigation";
import type { UserDetailsWithRolesResponse } from "@/api/contracts/user/user-contract";

export type Branch = {
  id: number;
  name: {
    en: string;
    ar: string;
  };
  code: string;
  type: string;
  city: {
    name: {
      en: string;
      ar: string;
    };
    region: {
      name: {
        en: string;
        ar: string;
      };
    };
  };
  longitude: string;
  latitude: string;
};

// Memoize the createColumns function to prevent unnecessary re-renders
const createColumns = (
  _locationCodes: string[] = [],
  selectedLocations: string[],
  onLocationToggle: (branchCode: string, selected: boolean) => void
) => [
  {
    id: "select",
    header: ({ table }: { table: Table<Branch> }) => {
      // Check if all visible rows are selected
      const allSelected = table.getFilteredRowModel().rows.every(row =>
        selectedLocations.includes(row.original.code)
      );

      // Check if some visible rows are selected
      const someSelected = table.getFilteredRowModel().rows.some(row =>
        selectedLocations.includes(row.original.code)
      ) && !allSelected;

      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={allSelected}
            data-state={someSelected ? "indeterminate" : undefined}
            onCheckedChange={(value: boolean) => {
              // Select or deselect all visible rows
              table.getFilteredRowModel().rows.forEach(row => {
                onLocationToggle(row.original.code, !!value);
              });
            }}
            aria-label="Select all"
          />
          <span className="text-xs text-muted-foreground">Select</span>
        </div>
      );
    },
    cell: ({ row }: { row: Row<Branch> }) => {
      const branch = row.original;
      // Check if this branch is in the selectedLocations array
      const isSelected = selectedLocations.includes(branch.code);

      return (
        <div className="flex items-center">
          <Checkbox
            checked={isSelected}
            onCheckedChange={(value: boolean) => {
              onLocationToggle(branch.code, !!value);
            }}
            aria-label="Select row"
            className="transition-all duration-200 hover:scale-110"
          />
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "name",
    header: "Branch Name",
    cell: ({ row }: { row: Row<Branch> }) => {
      const branch = row.original;
      const isSelected = selectedLocations.includes(branch.code);
      return (
        <div className={`flex items-center ${isSelected ? 'font-medium text-green-700' : ''}`}>
          {isSelected && (
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
          )}
          {`${branch.name.en} (${branch.name.ar})`}
        </div>
      );
    },
  },
  {
    accessorKey: "code",
    header: "Branch Code",
    cell: ({ row }: { row: Row<Branch> }) => {
      const branch = row.original;
      return branch.code;
    },
  },
  {
    accessorKey: "city",
    header: "City",
    cell: ({ row }: { row: Row<Branch> }) => {
      const branch = row.original;
      return `${branch.city.name.en} (${branch.city.name.ar})`;
    },
  },
  {
    accessorKey: "region",
    header: "Region",
    cell: ({ row }: { row: Row<Branch> }) => {
      const branch = row.original;
      return `${branch.city.region.name.en} (${branch.city.region.name.ar})`;
    },
  },
];

interface BranchesTableProps {
  branches: Branch[];
  locationCodes?: string[];
  pageNumber: number;
  pageSize: number;
  userId: string;
  numericId?: string; // The numeric ID from searchUserLocation API
}

export function BranchesTable({ branches, locationCodes = [], pageNumber: _pageNumber, pageSize, userId, numericId }: BranchesTableProps) {
  const { toast } = useToast();
  const progress = useProgressBar();
  const router = useRouter();

  // Track selected locations
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [originalLocations, setOriginalLocations] = useState<string[]>([]);
  const [nonYaqeenLocations, setNonYaqeenLocations] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  // Get all branch codes from the yaqeenMigrated branches
  const yaqeenBranchCodes = useMemo(() => {
    return branches.map(branch => branch.code);
  }, [branches]);

  // Initialize selected locations from props
  useEffect(() => {
    // Split the location codes into yaqeen and non-yaqeen
    const yaqeenLocations = locationCodes.filter(code => yaqeenBranchCodes.includes(code));
    const otherLocations = locationCodes.filter(code => !yaqeenBranchCodes.includes(code));

    console.log('Yaqeen locations:', yaqeenLocations);
    console.log('Non-yaqeen locations:', otherLocations);

    // Set the selected locations to only those in yaqeenMigrated
    setSelectedLocations([...yaqeenLocations]);
    setOriginalLocations([...yaqeenLocations]);

    // Keep track of non-yaqeen locations separately
    setNonYaqeenLocations([...otherLocations]);
  }, [locationCodes, yaqeenBranchCodes]);

  // Handle location selection/deselection
  const handleLocationToggle = useMemo(() => {
    return (branchCode: string, selected: boolean) => {
      setSelectedLocations(prev => {
        if (selected) {
          return [...prev, branchCode];
        } else {
          return prev.filter(code => code !== branchCode);
        }
      });
    };
  }, []);

  // Create columns with the locationCodes for checkbox selection
  // Use useMemo to prevent recreation of columns on every render
  const columns = useMemo(
    () => createColumns(locationCodes, selectedLocations, handleLocationToggle),
    [locationCodes, selectedLocations, handleLocationToggle]
  );

  // We'll let the DataTable handle pagination with clientPagination=true

  // Check if there are any changes
  const hasChanges = useMemo(() => {
    // Create sorted copies to compare
    const sortedSelected = [...selectedLocations].sort();
    const sortedOriginal = [...originalLocations].sort();
    return JSON.stringify(sortedSelected) !== JSON.stringify(sortedOriginal);
  }, [selectedLocations, originalLocations]);

  // Save changes
  const saveChanges = async () => {
    try {
      if (!numericId) {
        throw new Error('User numeric ID is required');
      }

      setIsSaving(true);
      progress.start();

      // Get current user details to get roles
      const userResult = await getUserById(numericId);

      if (!userResult.success) {
        throw new Error(userResult.error || 'Failed to fetch user details');
      }

      const user = userResult.data as UserDetailsWithRolesResponse;

      // Calculate added and removed locations
      const addedLocations = selectedLocations.filter(id => !originalLocations.includes(id));
      const removedLocations = originalLocations.filter(id => !selectedLocations.includes(id));

      console.log('Original locations:', originalLocations);
      console.log('Selected locations:', selectedLocations);
      console.log('Added locations:', addedLocations);
      console.log('Removed locations:', removedLocations);

      // Update user locations
      // We need to combine the selected yaqeen branches with the non-yaqeen locations
      // This maintains the superset of all locations
      const combinedLocations = [...selectedLocations, ...nonYaqeenLocations];

      console.log('Selected yaqeen branch codes:', selectedLocations);
      console.log('Non-yaqeen locations (preserved):', nonYaqeenLocations);
      console.log('Combined locations for update:', combinedLocations);

      const updateResult = await updateUserLocations(numericId, {
        locationIds: combinedLocations.map(id => parseInt(id, 10)),
        isEnable: true,
        email: (user?.email) || '',
        roles: user?.roles ? user.roles.map(role => ({ name: role.name || '', id: role.id || '' })) : []
      });

      if (!updateResult.success) {
        throw new Error(updateResult.error || 'Failed to update user locations');
      }

      // Update original locations
      setOriginalLocations([...selectedLocations]);

      toast({
        title: 'Success',
        description: 'User locations updated successfully',
        variant: 'success'
      });

      // Refresh the data without a full page reload
      router.refresh();
    } catch (error) {
      console.error('Error saving locations:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update user locations',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
      progress.done();
    }
  };

  // Add search and filter functionality
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [showAssignedOnly] = useState(false);

  // Get unique regions for filtering
  const regions = useMemo(() => {
    return Array.from(new Set(branches.map(branch => branch.city.region.name.en)));
  }, [branches]);

  // Filter branches based on search term and selected region
  const filteredBranches = useMemo(() => {
    return branches.filter(branch => {
      // Filter by assigned status if enabled
      if (showAssignedOnly && !originalLocations.includes(branch.code)) {
        return false;
      }

      // Filter by region
      if (selectedRegion !== 'all' && branch.city.region.name.en !== selectedRegion) {
        return false;
      }

      // Then filter by search term
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        branch.name.en.toLowerCase().includes(searchLower) ||
        branch.name.ar.toLowerCase().includes(searchLower) ||
        branch.code.toLowerCase().includes(searchLower) ||
        branch.city.name.en.toLowerCase().includes(searchLower) ||
        branch.city.region.name.en.toLowerCase().includes(searchLower)
      );
    });
  }, [branches, selectedRegion, searchTerm, showAssignedOnly, originalLocations]);

  // Count selected branches (only from yaqeen branches)
  const selectedCount = selectedLocations.length;
  const totalCount = branches.length;

  // Count non-yaqeen locations (for information only)
  const nonYaqeenCount = nonYaqeenLocations.length;

  // Handle select all branches
  const handleSelectAll = useMemo(() => {
    return (selected: boolean) => {
      if (selected) {
        // Select all branches
        const allCodes = branches.map(branch => branch.code);
        setSelectedLocations(allCodes);
      } else {
        // Deselect all branches
        setSelectedLocations([]);
      }
    };
  }, [branches]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 mb-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          {/* Search box */}
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search branches..."
              className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                onClick={() => setSearchTerm('')}
              >
                ×
              </button>
            )}
          </div>

          {/* Selection summary and controls */}
          <div className="flex items-center gap-4">
            <div className="text-sm">
              <span className="font-medium">{selectedCount}</span> of <span className="font-medium">{totalCount}</span> Yaqeen branches selected
              {nonYaqeenCount > 0 && (
                <span className="ml-2 text-muted-foreground">
                  (+{nonYaqeenCount} other locations preserved)
                </span>
              )}
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(true)}
              >
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(false)}
              >
                Clear All
              </Button>
            </div>
          </div>
        </div>

        {/* Region filter */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium mr-2 self-center">Filter by Region:</span>
          <Button
            variant={selectedRegion === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedRegion('all')}
            className="rounded-full"
          >
            All Regions
          </Button>
          {regions.map(region => (
            <Button
              key={region}
              variant={selectedRegion === region ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedRegion(region)}
              className="rounded-full"
            >
              {region}
            </Button>
          ))}
        </div>
      </div>

      {/* Selected Branches Section */}
      {selectedLocations.length > 0 && (
        <div className="mb-6 border rounded-md p-3 bg-gray-50 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <h3 className="text-base font-semibold">Selected Branches</h3>
              <span className="px-1.5 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">
                {selectedLocations.length}
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedLocations([])}
              className="text-red-500 hover:text-red-700 h-7 text-xs px-2"
            >
              Clear All
            </Button>
          </div>

          <div className="flex flex-wrap gap-1.5 max-h-48 overflow-y-auto p-1">
            {selectedLocations.map(code => {
              const branch = branches.find(b => b.code === code);
              if (!branch) return null;

              return (
                <div
                  key={code}
                  className="flex items-center bg-white border border-green-100 rounded-md py-1 px-2 text-sm shadow-sm hover:shadow-md transition-shadow duration-200 max-w-[250px]"
                >
                  <div className="flex-1 min-w-0 mr-1">
                    <div className="font-medium truncate">{branch.name.en}</div>
                    <div className="text-xs text-muted-foreground truncate">{branch.code}</div>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleLocationToggle(code, false)}
                    className="text-gray-400 hover:text-red-500 focus:outline-none h-5 w-5 flex items-center justify-center rounded-full hover:bg-gray-100"
                  >
                    <span className="sr-only">Remove</span>
                    ×
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* All Branches Table */}
      <div className="rounded-md border">
        <style jsx global>{`
          tr.selected-row {
            background-color: rgba(0, 128, 0, 0.05);
          }
          tr.selected-row:hover {
            background-color: rgba(0, 128, 0, 0.1);
          }
        `}</style>
        <DataTable
          columns={columns}
          data={{ total: branches.length, data: filteredBranches }}
          clientPagination={true}
          pageSize={pageSize} // Using the pageSize from props (default 500)
          paginationEnabled={true}
          searchPlaceholder=""
          styleClasses={{
            wrapper: "border-none"
          }}
          // Custom row styling can be added here if needed
        />
      </div>

      {/* Show a message when no branches match the filter */}
      {filteredBranches.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          {showAssignedOnly ?
            originalLocations.length === 0 ?
              "No branches are currently assigned to this user." :
              "No assigned branches match your search criteria. Try adjusting your filters." :
            "No branches match your search criteria. Try adjusting your filters."}
          {selectedLocations.length > 0 && (
            <div className="mt-2">
              <span className="font-medium">{selectedLocations.length}</span> branches are selected and will be saved when you click &quot;Save Location Changes&quot;.
            </div>
          )}
        </div>
      )}

      {hasChanges && (
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-muted-foreground">
            {selectedCount > originalLocations.length ?
              `Adding ${selectedCount - originalLocations.length} Yaqeen branch(es)` :
              selectedCount < originalLocations.length ?
              `Removing ${originalLocations.length - selectedCount} Yaqeen branch(es)` :
              'Yaqeen branches changed'}
            {nonYaqeenCount > 0 && (
              <span className="ml-2">
                • {nonYaqeenCount} other location(s) preserved
              </span>
            )}
          </div>
          <Button
            onClick={saveChanges}
            disabled={isSaving}
            className="bg-green-600 hover:bg-green-700"
          >
            {isSaving ? (
              <>
                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Location Changes
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
