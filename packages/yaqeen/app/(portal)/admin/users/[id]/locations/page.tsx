import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { type Branch } from "@/api/contracts/branch-contract";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getYaqeenBranches, searchUserLocation, getKeycloakUserById } from "../actions";
import { redirect } from "next/navigation";
import { headers } from "next/headers";
import { BranchesTable } from "./branches-table";
import UserLocationInfo from "./user-location-info";
import { type UserLocationData } from "./types";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // Extract and validate the ID parameter
  const resolvedParams = await params;
  const userId = resolvedParams.id;
  if (!userId || typeof userId !== 'string') {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Invalid user ID parameter
        </AlertDescription>
      </Alert>
    );
  }

  // Get pagination parameters
  const resolvedSearchParams = await searchParams;
  const pageParam = resolvedSearchParams.page;
  const pageSizeParam = resolvedSearchParams.pageSize;

  // Default values
  
  const defaultPageSize = 500;
  const pageSize = typeof pageSizeParam === 'string' ? parseInt(pageSizeParam, 10) : defaultPageSize;
  const pageNumber = typeof pageParam === 'string' ? Math.max(1, parseInt(pageParam, 10)) : 1;

  // If pageSize is not specified in the URL, redirect to add it
  if (!pageSizeParam) {
    const headersList = await headers();
    const host = headersList.get('host') || '';
    const protocol = process.env.NODE_ENV === 'development' ? 'http' : 'https';
    const pathname = `/admin/users/${userId}/locations`;

    const url = new URL(`${protocol}://${host}${pathname}`);
    url.searchParams.set('pageSize', defaultPageSize.toString());
    if (!pageParam) {
      url.searchParams.set('page', '1');
    }
    return redirect(url.toString());
  }

  // First get the Keycloak user details
  const keycloakUserResult = await getKeycloakUserById(userId);

  // If we can't get the Keycloak user, show an error
  if (!keycloakUserResult.success) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {keycloakUserResult.error || "Failed to fetch Keycloak user details"}
        </AlertDescription>
      </Alert>
    );
  }

  // Get user location data using Keycloak user email or ID
  const keycloakUser = keycloakUserResult.data;
  // Use Keycloak username for searching
  const searchParam = typeof keycloakUser?.username === 'string' ? keycloakUser.username : '';
  const userLocationResult = await searchUserLocation(searchParam, userId);
  const branchesResult = await getYaqeenBranches();

  if (!userLocationResult.success) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {userLocationResult.error || "User data not found. Please try again."}
        </AlertDescription>
      </Alert>
    );
  }

  if (!branchesResult.success) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {branchesResult.error || "Failed to fetch branches data"}
        </AlertDescription>
      </Alert>
    );
  }

  // Get the user location data
  console.log('User location result:', JSON.stringify(userLocationResult, null, 2));
  // Always use the API response data, even if some fields are empty
  const userLocationData = userLocationResult.data as unknown as UserLocationData;
  console.log('User location data:', userLocationData);
  const branches = branchesResult.data?.data || [];

  // Get the numeric ID from the userLocationData
  const numericId = userLocationData?.id;

  // The locationIds from the API are actually already branch codes
  // We'll use them directly
  const locationCodes = userLocationData?.locationIds?.map(id => id.toString()) || [];
  console.log('Location codes:', locationCodes);

  return (
    <div className="space-y-6">
      {/* User Location Information */}
      <Card>
        <CardHeader className="text-xl font-semibold">User Location Information</CardHeader>
        <CardContent>
          <UserLocationInfo userLocationData={userLocationData || undefined} userId={userId} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="text-xl font-semibold">Yaqeen Migrated Branches</CardHeader>
        <CardContent>
          <BranchesTable
            branches={branches as Branch[]}
            locationCodes={locationCodes}
            pageNumber={pageNumber}
            pageSize={pageSize}
            userId={userId}
            numericId={numericId?.toString()}
          />
          {/* Using DataTable's built-in pagination */}
        </CardContent>
      </Card>
    </div>
  );
}
