"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { CheckCircle2, <PERSON>Circle, User<PERSON>he<PERSON>, Loader2 } from "lucide-react";
import { assignUserToKeycloak } from "../actions";
import { useProgressBar } from "@/components/progress-bar";
import { useState } from "react";

import { type UserLocationInfoProps } from "./types";

export default function UserLocationInfo({ userLocationData, userId }: UserLocationInfoProps) {
  const { toast } = useToast();
  const progress = useProgressBar();
  const [isLoading, setIsLoading] = useState(false);

  // If userLocationData is null or undefined, show a message
  if (!userLocationData) {
    return (
      <div className="flex items-center p-4 rounded-lg border bg-card">
        <div className="flex items-center text-amber-600">
          <XCircle className="w-6 h-6 mr-2" />
          <span className="text-lg font-medium">No location data found for this user</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Match Status */}
      <div className="flex items-center justify-between p-4 rounded-lg border bg-card">
        <div className="flex items-center">
          {userLocationData.keycloakMatch ? (
            <div className="flex items-center text-green-600">
              <CheckCircle2 className="w-6 h-6 mr-2" />
              <span className="text-lg font-medium">Keycloak Match: Verified</span>
            </div>
          ) : (
            <div className="flex items-center text-red-600">
              <XCircle className="w-6 h-6 mr-2" />
              <span className="text-lg font-medium">Keycloak Match: Not Verified</span>
            </div>
          )}
        </div>
        {!userLocationData.keycloakMatch && (
          <div className="flex flex-col items-end space-y-2">
            <p className="text-sm text-muted-foreground">
              {`Assign keycloak id: '${userId}' to the user with ${userLocationData.email.includes('@') ? 'email' : 'success factor id'} '${userLocationData.email.includes('@') ? userLocationData.email : userLocationData.successFactorId}'`}
            </p>
            <Button
              className="w-full sm:w-auto"
              size="sm"
              disabled={isLoading}
              onClick={async () => {
                try {
                  setIsLoading(true);
                  progress.start();

                  const result = await assignUserToKeycloak(userId, {
                    ...userLocationData,
                    id: String(userLocationData.id), // Convert number to string
                    externalId: userId // userId here is the keycloakId from URL params
                  });

                  if (result.success) {
                    toast({
                      title: "Success",
                      description: "User successfully assigned to Keycloak",
                      variant: "success"
                    });
                  } else {
                    toast({
                      title: "Error",
                      description: result.error || 'Failed to assign user',
                      variant: "destructive"
                    });
                  }
                } catch (error) {
                  toast({
                    title: "Error",
                    description: error instanceof Error ? error.message : 'An unexpected error occurred',
                    variant: "destructive"
                  });
                } finally {
                  setIsLoading(false);
                  progress.done();
                }
              }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Assigning...
                </>
              ) : (
                <>
                  <UserCheck className="w-4 h-4 mr-2" />
                  Assign to Keycloak
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* User Details */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="text-sm text-muted-foreground mb-2">ID</div>
          <div className="font-medium truncate">{userLocationData.id}</div>
        </div>

        <div className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="text-sm text-muted-foreground mb-2">Email</div>
          <div className="font-medium truncate">{userLocationData.email}</div>
        </div>

        <div className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="text-sm text-muted-foreground mb-2">External ID</div>
          <div className="font-medium truncate">{userLocationData.externalId}</div>
        </div>

        <div className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="text-sm text-muted-foreground mb-2">Success Factor ID</div>
          <div className="font-medium truncate">{userLocationData.successFactorId}</div>
        </div>
      </div>

      {/* Match Details */}
      <div className="p-4 rounded-lg border bg-card">
        <div className="text-sm font-medium mb-2">Match Details</div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-muted-foreground">Keycloak ID (from URL)</div>
            <div className="font-medium break-all">{userId}</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">External ID (from API)</div>
            <div className="font-medium break-all">{userLocationData.externalId}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
