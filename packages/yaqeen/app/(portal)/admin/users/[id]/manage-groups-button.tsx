'use client';

import {useState, useEffect} from "react";
import {Button} from "@/components/ui/button";
import {UserCirclePlus, Spinner} from "@phosphor-icons/react";
import {type KeycloakGroup} from "@/api/contracts/keycloak-contract";
import {ManageGroupsDialog} from "./manage-groups-dialog";
import {useRouter} from "next/navigation";
import {getAvailableGroups} from "./actions";
import {toast} from "@/lib/hooks/use-toast";

interface ManageGroupsButtonProps {
  userId: string;
  userGroups: KeycloakGroup[];
}

export function ManageGroupsButton({userId, userGroups}: ManageGroupsButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [availableGroups, setAvailableGroups] = useState<KeycloakGroup[]>([]);
  const router = useRouter();

  // Debug log when component mounts
  useEffect(() => {
    console.log("ManageGroupsButton - Component mounted with userId:", userId);
    console.log("ManageGroupsButton - User has", userGroups.length, "groups");
  }, [userId, userGroups]);

  const handleGroupsUpdated = () => {
    // Refresh the page data
    console.log("ManageGroupsButton - Groups updated, refreshing page");
    router.refresh();
  };

  const handleButtonClick = async () => {
    console.log("ManageGroupsButton - Button clicked, fetching groups");

    setLoading(true);
    try {
      const result = await getAvailableGroups();
      console.log("ManageGroupsButton - Direct API call result:", result);

      if (result.success) {
        // Filter out groups the user is already a member of
        const userGroupIds = userGroups.map(group => group.id);
        const filteredGroups = result.groups.filter(
          group => !userGroupIds.includes(group.id)
        );

        // Sort groups alphabetically by name
        const sortedGroups = [...filteredGroups].sort((a, b) =>
          a.name.localeCompare(b.name)
        );

        console.log(`ManageGroupsButton - Filtered ${sortedGroups.length} available groups`);
        setAvailableGroups(sortedGroups);
        setDialogOpen(true);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to fetch available groups",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("ManageGroupsButton - Error in direct API call:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    console.log("ManageGroupsButton - Dialog open state changed to:", open);
    setDialogOpen(open);
  };

  return (
    <>
      <Button
        variant="default"
        size="sm"
        onClick={handleButtonClick}
        className="bg-[#9CE642] text-black hover:bg-[#8AD532] flex items-center gap-1"
        disabled={loading}
      >
        {loading ? (
          <Spinner className="h-4 w-4 animate-spin mr-2"/>
        ) : (
          <UserCirclePlus className="h-4 w-4 mr-2"/>
        )}
        <span>Manage Groups</span>
      </Button>

      {/* Only render the dialog when it's open to avoid state conflicts */}
      {dialogOpen && (
        <ManageGroupsDialog
          userId={userId}
          userGroups={userGroups}
          open={dialogOpen}
          onOpenChange={handleDialogOpenChange}
          onGroupsUpdated={handleGroupsUpdated}
          availableGroups={availableGroups}
        />
      )}
    </>
  );
}
