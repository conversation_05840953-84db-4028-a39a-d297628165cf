"use client";

import {useCallback, useEffect} from "react";

/**
 * TabButton component for custom tabs implementation
 * Client component that handles tab selection
 */
export function TabButton({id, label, defaultActive = false}: { id: string; label: string; defaultActive?: boolean }) {

  useEffect(() => {
    // Set default active tab on mount
    if (defaultActive) {
      document.querySelectorAll('[role="tabpanel"]').forEach(panel => {
        panel.setAttribute('hidden', 'true');
      });
      document.querySelectorAll('[role="tab"]').forEach(tab => {
        tab.setAttribute('aria-selected', 'false');
        tab.classList.remove('border-primary', 'text-primary');
        tab.classList.add('text-muted-foreground');
      });

      const tab = document.getElementById(`tab-${id}`);
      const panel = document.getElementById(`tabpanel-${id}`);

      if (tab) {
        tab.setAttribute('aria-selected', 'true');
        tab.classList.add('border-primary', 'text-primary');
        tab.classList.remove('text-muted-foreground');
      }

      if (panel) {
        panel.removeAttribute('hidden');
      }
    }
  }, [defaultActive, id]);

  const handleClick = useCallback(() => {
    // Hide all panels
    document.querySelectorAll('[role="tabpanel"]').forEach(panel => {
      panel.setAttribute('hidden', 'true');
    });

    // Deselect all tabs
    document.querySelectorAll('[role="tab"]').forEach(tab => {
      tab.setAttribute('aria-selected', 'false');
      tab.classList.remove('border-primary', 'text-primary');
      tab.classList.add('text-muted-foreground');
    });

    // Select clicked tab
    const tab = document.getElementById(`tab-${id}`);
    const panel = document.getElementById(`tabpanel-${id}`);

    if (tab) {
      tab.setAttribute('aria-selected', 'true');
      tab.classList.add('border-primary', 'text-primary');
      tab.classList.remove('text-muted-foreground');
    }

    if (panel) {
      panel.removeAttribute('hidden');
    }
  }, [id]);

  return (
    <button
      id={`tab-${id}`}
      role="tab"
      aria-controls={`tabpanel-${id}`}
      aria-selected={defaultActive ? "true" : "false"}
      onClick={handleClick}
      className={`px-4 py-2 font-medium border-b-2 -mb-px transition-colors ${
        defaultActive
          ? 'border-primary text-primary'
          : 'border-transparent text-muted-foreground hover:text-foreground'
      }`}
    >
      {label}
    </button>
  );
}

/**
 * TabPanel component for custom tabs implementation
 * Client component that displays tab content
 */
export function TabPanel({id, children}: { id: string; children: React.ReactNode }) {
  return (
    <div
      id={`tabpanel-${id}`}
      role="tabpanel"
      aria-labelledby={`tab-${id}`}
      hidden={id !== "roles"}
    >
      {children}
    </div>
  );
}
