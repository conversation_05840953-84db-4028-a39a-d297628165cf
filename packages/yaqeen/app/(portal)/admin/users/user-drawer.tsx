"use client";

import React, {useState, useEffect} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
// import {Switch} from "@/components/ui/switch";
import {useToast} from "@/lib/hooks/use-toast";
import {useRouter} from "next/navigation";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import {CaretRight, CaretLeft, Check} from "@phosphor-icons/react";
import {createUser, updateUser, resetPassword, assignRolesToUser, fetchRealmRoles, getUserById, checkUserCredentials} from "./user-actions";
import {type KeycloakRole, type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {Checkbox} from "@/components/ui/checkbox";
import {Badge} from "@/components/ui/badge";

interface UserDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId?: string;
  initialUser?: KeycloakUser;
  mode: "create" | "edit";
}

/**
 * Drawer component for creating or editing a Keycloak user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function UserDrawer({open, onOpenChange, userId, initialUser, mode = "create"}: UserDrawerProps) {
  const [step, setStep] = useState<number>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [userData, setUserData] = useState({
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    enabled: true
  });
  const [showPasswordField, setShowPasswordField] = useState(mode === "create");
  const [hasPasswordSet, setHasPasswordSet] = useState<boolean>(false);
  const [createdUserId, setCreatedUserId] = useState<string>("");
  const [roles, setRoles] = useState<KeycloakRole[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState<string>("");

  const {toast} = useToast();
  const router = useRouter();

  // Filter roles based on search query
  const filteredRoles = searchQuery
    ? roles.filter(role =>
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (role.description?.toLowerCase() || "").includes(searchQuery.toLowerCase())
    )
    : roles;

  // Check if user has password set
  const checkUserPassword = async (id: string) => {
    try {
      const result = await checkUserCredentials(id);
      if (result.success) {
        setHasPasswordSet(result.hasPassword || false);
      } else {
        console.error("Error checking user password:", result.error);
      }
    } catch (error) {
      console.error("Error checking user password:", error);
    }
  };

  // Load user data when in edit mode
  useEffect(() => {
    if (open && mode === "edit" && userId) {
      if (initialUser) {
        // Use the provided user data if available
        setUserData({
          username: initialUser.username || "",
          firstName: initialUser.firstName || "",
          lastName: initialUser.lastName || "",
          email: initialUser.email || "",
          password: "",
          enabled: initialUser.enabled || false
        });
        // Check password status for existing user
        if (userId) {
          void checkUserPassword(userId);
        }
      } else {
        // Fetch user data if not provided
        void loadUserData(userId);
      }
    } else if (open && mode === "create") {
      // Reset form for create mode
      setUserData({
        username: "",
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        enabled: true
      });
      setShowPasswordField(true);
    }
  }, [open, mode, userId, initialUser]);

  // Fetch all available roles when the drawer opens and we're on step 2
  useEffect(() => {
    if (open && step === 2) {
      loadRoles().catch(console.error);
    }
  }, [open, step]);

  const loadUserData = async (userId: string) => {
    try {
      setIsLoading(true);
      const result = await getUserById(userId);

      if (result.success && result.user) {
        setUserData({
          username: result.user.username,
          firstName: result.user.firstName || "",
          lastName: result.user.lastName || "",
          email: result.user.email || "",
          password: "",
          enabled: result.user.enabled || false,
        });
        // Get user roles from a different source or set empty
        setSelectedRoles(new Set([]));
        // Check password status when user data is loaded
        await checkUserPassword(userId);
      } else {
        throw new Error("Failed to fetch user data");
      }
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to load user data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const result = await fetchRealmRoles();

      if (result.success && result.roles) {
        setRoles(result.roles);
      } else {
        throw new Error("Failed to fetch roles");
      }
    } catch (_error) {
      toast({
        title: "Error",
        description: "Failed to load roles",
        variant: "destructive",
      });
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const handleUserDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {name, value, type, checked} = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleRoleToggle = (roleId: string) => {
    setSelectedRoles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(roleId)) {
        newSet.delete(roleId);
      } else {
        newSet.add(roleId);
      }
      return newSet;
    });
  };

  const handleSaveUser = async () => {
    setIsSubmitting(true);

    try {
      if (mode === "create") {
        await handleCreateUser();
      } else {
        await handleUpdateUser();
      }
    } catch (error) {
      console.error("Error saving user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const handleCreateUser = async () => {
    try {
      const userDataPayload = {
        username: userData.username,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        enabled: userData.enabled,
        credentials: [
          {
            type: "password",
            value: userData.password,
            temporary: false,
          },
        ],
        requiredActions: [],
        emailVerified: true
      };

      const result = await createUser(userDataPayload);

      if (result.success) {
        toast({
          title: "Success",
          description: "User created successfully",
        });

        // Store the user ID for role assignment
        if (result.userId) {
          setCreatedUserId(result.userId);
          // Move to the next step
          setStep(2);
        } else {
          throw new Error("User ID not returned from creation");
        }
      } else {
        throw new Error(result.error || "Failed to create user");
      }
    } catch (error) {
      console.error("Error creating user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateUser = async () => {
    try {
      const targetUserId = userId || createdUserId;
      if (!targetUserId) {
        throw new Error("User ID is required for update");
      }

      // Update user details
      const updateResult = await updateUser(targetUserId, {
        username: userData.username,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        enabled: userData.enabled,
      });

      if (!updateResult.success) {
        throw new Error(updateResult.error || "Failed to update user");
      }

      // Update password if provided
      if (userData.password) {
        const passwordResult = await resetPassword(targetUserId, userData.password);
        if (!passwordResult.success) {
          throw new Error(passwordResult.error || "Failed to update password");
        }
      }

      toast({
        title: "Success",
        description: "User updated successfully",
      });

      // Move to the next step or close the drawer
      if (mode === "edit") {
        setStep(2);
      } else {
        handleClose();
      }
    } catch (error) {
      console.error("Error updating user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAssignRoles = async () => {
    setIsSubmitting(true);

    try {
      const targetUserId = userId || createdUserId;
      if (!targetUserId) {
        throw new Error("User ID is required for role assignment");
      }

      // Get the selected roles from the set
      const selectedRoleObjects = roles.filter(role => selectedRoles.has(role.id));

      if (selectedRoleObjects.length > 0) {
        const result = await assignRolesToUser(targetUserId, selectedRoleObjects);

        if (result.success) {
          toast({
            title: "Success",
            description: `${selectedRoleObjects.length} role(s) assigned successfully`,
          });

          // Close the drawer and redirect to the user page
          handleClose();
          router.push(`/admin/users/${targetUserId}?highlight=${targetUserId}`);
        } else {
          throw new Error(result.error || "Failed to assign roles");
        }
      } else {
        // No roles selected, just close the drawer
        handleClose();
        router.push(`/admin/users/${targetUserId}?highlight=${targetUserId}`);
      }
    } catch (error) {
      console.error("Error assigning roles:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset the form state
    setStep(1);
    setUserData({
      username: "",
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      enabled: true
    });
    setCreatedUserId("");
    setSelectedRoles(new Set());
    setSearchQuery("");
    // Close the drawer
    onOpenChange(false);
  };

  if (isLoading) {
    return (
      <Drawer open={open} onOpenChange={onOpenChange} direction="right">
        <DrawerContent className="w-full sm:w-[500px] md:w-[600px]">
          <div className="mx-auto w-full">
            <DrawerHeader>
              <DrawerTitle>Loading User Data</DrawerTitle>
              <DrawerDescription>Please wait...</DrawerDescription>
            </DrawerHeader>
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Drawer open={open} onOpenChange={handleClose} direction="right">
      <DrawerContent className="w-full sm:w-[500px] md:w-[600px]">
        <div className="mx-auto w-full">
          <DrawerHeader>
            <DrawerTitle>
              {mode === "create" ? "Create New User" : "Edit User"}
            </DrawerTitle>
            <DrawerDescription>
              {step === 1
                ? (mode === "create"
                   ? "Enter user details to create a new account"
                   : "Update user details")
                : "Assign roles to the user"}
            </DrawerDescription>
          </DrawerHeader>

          {step === 1 ? (
            <div className="p-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  value={userData.username}
                  onChange={handleUserDataChange}
                  placeholder="Username"
                  required
                  disabled={mode === "edit"}
                  className={mode === "edit" ? "bg-slate-100" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={userData.firstName}
                  onChange={handleUserDataChange}
                  placeholder="First Name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={userData.lastName}
                  onChange={handleUserDataChange}
                  placeholder="Last Name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={userData.email}
                  onChange={handleUserDataChange}
                  placeholder="Email"
                  required
                />
              </div>

              <div className="grid gap-4">
                {mode === "edit" && (
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-2">
                      <Label>Password Status:</Label>
                      {hasPasswordSet ? (
                        <Badge variant="secondary" className="bg-green-600 text-white hover:bg-green-600">
                          Password Set
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-yellow-500 text-white hover:bg-yellow-500">
                          No Password Set
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="showPassword"
                        checked={showPasswordField}
                        onCheckedChange={(checked) => setShowPasswordField(checked as boolean)}
                      />
                      <Label htmlFor="showPassword">Set new password</Label>
                    </div>
                  </div>
                )}

                {(mode === "create" || showPasswordField) && (
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      value={userData.password}
                      onChange={handleUserDataChange}
                      placeholder="Password"
                      required={mode === "create"}
                    />
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="p-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="searchRoles">Search Roles</Label>
                <Input
                  id="searchRoles"
                  name="searchRoles"
                  type="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="text-sm text-slate-500 mb-2">
                {selectedRoles.size} role(s) selected
              </div>

              {isLoadingRoles ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredRoles.length > 0 ? (
                    filteredRoles.map(role => (
                      <div key={role.id}
                           className="flex items-center space-x-2 p-2 border rounded-md hover:bg-slate-50">
                        <Checkbox
                          id={`role-${role.id}`}
                          checked={selectedRoles.has(role.id)}
                          onCheckedChange={() => handleRoleToggle(role.id)}
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor={`role-${role.id}`}
                            className="text-sm font-medium cursor-pointer"
                          >
                            {role.name}
                          </Label>
                          {role.description && (
                            <p className="text-xs text-slate-500">{role.description}</p>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-slate-500">
                      {searchQuery ? "No roles found matching your search" : "No roles available"}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          <DrawerFooter className="p-6 flex flex-row justify-between">
            {step === 1 ? (
              <>
                <DrawerClose asChild>
                  <Button variant="outline">
                    Cancel
                  </Button>
                </DrawerClose>
                <Button
                  type="button"
                  onClick={handleSaveUser}
                  disabled={isSubmitting || !userData.username || !userData.email || (mode === "create" && !userData.password)}
                >
                  {isSubmitting ? (mode === "create" ? "Creating..." : "Updating...") : (
                    <>
                      {mode === "edit" ? "Save" : "Next"} {mode === "create" && <CaretRight className="ml-2 h-4 w-4"/>}
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={() => setStep(1)}
                  disabled={isSubmitting}
                >
                  <CaretLeft className="mr-2 h-4 w-4"/>
                  Back
                </Button>
                <Button
                  type="button"
                  onClick={handleAssignRoles}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : (
                    <>
                      <Check className="mr-2 h-4 w-4"/>
                      Finish
                    </>
                  )}
                </Button>
              </>
            )}
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
