import {Suspense} from "react";
import {api} from "@/api";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";

import {Warning} from "@phosphor-icons/react/dist/ssr";
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert";
import Loading from "@/app/(portal)/loading";

import {SearchForm} from "./search-form";
import {Pagination} from "./pagination";
import {UserDataTable} from "./user-data-table";
import Header from "@/app/(portal)/rental/tariff/_components/Header";
import {CreateUserButton} from "./create-user-button";

/**
 * Keycloak Users List Page
 * Server Component that fetches and displays all Keycloak users
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export default async function Page({
  searchParams,
}: {
  params: Promise<object>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // Properly await searchParams
  const searchParamsValue = await searchParams;

  // Extract pagination and search parameters with proper type handling
  const pageParam = searchParamsValue.page;
  const sizeParam = searchParamsValue.size;
  const searchParam = searchParamsValue.search;

  // Convert to appropriate types
  const pageNumber = Number(pageParam || 1);
  const pageSize = Number(sizeParam) || 10;
  const search = typeof searchParam === 'string' ? searchParam : '';

  // Create a suspense key that includes all relevant parameters
  // This ensures the component revalidates when any parameter changes
  const suspenseKey = `users-page-${pageNumber}-size-${pageSize}-search-${search}-${Date.now()}`;

  // Fetch users data from Keycloak
  let usersData: KeycloakUser[] = [];
  let totalCount = 0;
  let error: Error | null = null;

  try {
    console.log(`Fetching Keycloak users: page=${pageNumber}, size=${pageSize}, search=${search}`);

    // Fetch users and total count in parallel
    const [usersResponse, countResponse] = await Promise.all([
      api.keycloak.getUsers({
        query: {
          first: String((pageNumber - 1) * pageSize),
          max: String(pageSize),
          search: search || undefined,
        },
        requiresAuth: true,
      }),
      api.keycloak.getUserCount({
        query: {
          search: search || undefined,
        },
        requiresAuth: true,
      }),
    ]);

    // Type guard for users response
    if (!Array.isArray(usersResponse.body)) {
      throw new Error('Invalid response format for users');
    }

    // Type guard for count response
    if (typeof countResponse.body !== 'number') {
      throw new Error('Invalid response format for count');
    }

    usersData = usersResponse.body;
    totalCount = countResponse.body;

    console.log(`Successfully fetched ${usersData.length} users out of ${totalCount} total`);
  } catch (err) {
    console.error("Error fetching Keycloak users:", err);
    error = err as Error;
  }

  return (
    <div className="grid grid-rows-[auto_1fr]">
      <Header
        pageName="Users"
        subTitle="Manage Users & Roles"
        actions={<CreateUserButton/>}
      />
      {error && (
        <Alert variant="destructive" className="mb-6">
          <Warning className="h-4 w-4"/>
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to fetch users from Keycloak server.
            <div className="mt-2 text-xs">
              <details>
                <summary>Error details</summary>
                <pre className="mt-2 whitespace-pre-wrap text-xs">{error.message}</pre>
              </details>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Use a unique suspense key that includes page, size, and search */}
      <Suspense key={suspenseKey} fallback={<Loading/>}>

        <div className="px-6 py-6">

          <SearchForm/>

          <UserDataTable
            data={usersData || []}
            totalCount={totalCount}
          />

          <div className="mt-4">
            <Pagination
              currentPage={pageNumber}
              pageSize={pageSize}
              totalItems={totalCount}
            />
          </div>

        </div>
      </Suspense>
    </div>
  );
}
