"use server";

import {revalidatePath} from "next/cache";
import {api} from "@/api";
import type {Create<PERSON><PERSON><PERSON><PERSON>akUser, KeycloakRole, KeycloakUser} from "@/api/contracts/keycloak-contract";

/**
 * Server action to create a new Keycloak user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function createUser(userData: CreateKeycloakUser) {
  try {
    const response = await api.keycloak.createUser({
      body: userData,
      requiresAuth: true,
    });

    if (response.status === 201) {
      // Extract user ID from the location header
      const locationHeader = response.headers.get("location");
      let userId = null;

      if (locationHeader) {
        // Location header format: /admin/realms/lumi/users/{userId}
        const parts = locationHeader.split("/");
        userId = parts[parts.length - 1];
      }

      revalidatePath("/admin/users");
      return {success: true, userId};
    } else {
      console.error("Failed to create user:", response.status);
      return {success: false, error: `Failed to create user: ${response.status}`};
    }
  } catch (error) {
    console.error("Error creating user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

/**
 * Server action to fetch all realm roles
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function fetchRealmRoles() {
  try {
    const response = await api.keycloak.getAllRealmRoles({
      requiresAuth: true,
    });

    if (response.status === 200) {
      return {success: true, roles: response.body};
    } else {
      console.error("Failed to fetch roles:", response.status);
      return {success: false, error: `Failed to fetch roles: ${response.status}`};
    }
  } catch (error) {
    console.error("Error fetching roles:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

/**
 * Server action to assign roles to a Keycloak user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function assignRolesToUser(userId: string, roles: KeycloakRole[]) {
  try {
    if (!userId) {
      return {success: false, error: "User ID is required"};
    }

    if (!roles || roles.length === 0) {
      return {success: true, message: "No roles to assign"};
    }

    // Prepare the role representations for the API
    const roleRepresentations = roles.map(role => ({
      id: role.id,
      name: role.name
    }));

    // Call the Keycloak API to assign roles to the user
    const response = await api.keycloak.addRealmRolesToUser({
      params: {userId},
      body: roleRepresentations,
      requiresAuth: true,
    });

    if (response.status === 204) {
      revalidatePath("/admin/users");
      return {success: true};
    } else {
      console.error("Failed to assign roles:", response.status);
      return {success: false, error: `Failed to assign roles: ${response.status}`};
    }
  } catch (error) {
    console.error("Error assigning roles:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

/**
 * Server action to get a Keycloak user by ID
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function getUserById(userId: string) {
  try {
    if (!userId) {
      return {success: false, error: "User ID is required"};
    }

    const response = await api.keycloak.getUserById({
      params: {id: userId},
      requiresAuth: true,
    });

    if (response.status === 200) {
      return {success: true, user: response.body};
    } else {
      console.error("Failed to fetch user:", response.status);
      return {success: false, error: `Failed to fetch user: ${response.status}`};
    }
  } catch (error) {
    console.error("Error fetching user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

/**
 * Server action to update a Keycloak user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function updateUser(userId: string, userData: Partial<KeycloakUser>) {
  try {
    if (!userId) {
      return {success: false, error: "User ID is required"};
    }

    // Prepare the user data for update
    // Omit fields that should not be sent in an update
    const userDataPayload = {
      username: userData.username,
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      enabled: userData.enabled,
      emailVerified: userData.emailVerified,
      requiredActions: userData.requiredActions,
    };

    const response = await api.keycloak.updateUser({
      params: {userId},
      body: {
        username: userDataPayload.username || '',
        email: userDataPayload.email || undefined,
        firstName: userDataPayload.firstName || undefined,
        lastName: userDataPayload.lastName || undefined,
        enabled: userDataPayload.enabled,
        requiredActions: userDataPayload.requiredActions
      }, // Ensure all properties have the correct types
      requiresAuth: true,
    });

    if (response.status === 204) {
      revalidatePath("/admin/users");
      return {success: true};
    } else {
      console.error("Failed to update user:", response.status);
      return {success: false, error: `Failed to update user: ${response.status}`};
    }
  } catch (error) {
    console.error("Error updating user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

/**
 * Server action to reset a Keycloak user's password
 * Following Java Spring Boot architecture standards with proper layer separation
 */
/**
 * Server action to check if a user has a password set
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export async function checkUserCredentials(userId: string) {
  try {
    if (!userId) {
      return {success: false, error: "User ID is required"};
    }

    const response = await api.keycloak.getUserCredentials({
      params: {userId},
      requiresAuth: true,
    });

    if (response.status === 200) {
      return {
        success: true,
        hasPassword: response.body.some(cred => cred.type === "password")
      };
    } else {
      console.error("Failed to check user credentials:", response.status);
      return {success: false, error: `Failed to check user credentials: ${response.status}`};
    }
  } catch (error) {
    console.error("Error checking user credentials:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

export async function resetPassword(userId: string, password: string, temporary = false) {
  try {
    if (!userId) {
      return {success: false, error: "User ID is required"};
    }

    if (!password) {
      return {success: false, error: "Password is required"};
    }

    const response = await api.keycloak.resetPassword({
      params: {userId},
      body: {
        type: "password",
        value: password,
        temporary,
      },
      requiresAuth: true,
    });

    if (response.status === 204) {
      return {success: true};
    } else {
      console.error("Failed to reset password:", response.status);
      return {success: false, error: `Failed to reset password: ${response.status}`};
    }
  } catch (error) {
    console.error("Error resetting password:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}
