"use client";

import React, {useState, useEffect} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Switch} from "@/components/ui/switch";
import {useToast} from "@/lib/hooks/use-toast";
import {useRouter} from "next/navigation";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import {UserPlus, MagnifyingGlass, CaretRight, CaretLeft, Check} from "@phosphor-icons/react";
import {createUser, assignRolesToUser, fetchRealmRoles} from "./user-actions";
import {type KeycloakRole} from "@/api/contracts/keycloak-contract";
import {Checkbox} from "@/components/ui/checkbox";

interface CreateUserDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Drawer component for creating a new Keycloak user
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function CreateUserDrawer({open, onOpenChange}: CreateUserDrawerProps) {
  const [step, setStep] = useState<number>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userData, setUserData] = useState({
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    enabled: true
  });
  const [userId, setUserId] = useState<string>("");
  const [roles, setRoles] = useState<KeycloakRole[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isLoadingRoles, setIsLoadingRoles] = useState<boolean>(false);

  const {toast} = useToast();
  const router = useRouter();

  // Fetch all available roles when the drawer opens and we're on step 2
  useEffect(() => {
    if (open && step === 2) {
      loadRoles().catch(console.error);
    }
  }, [open, step]);

  const loadRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const result = await fetchRealmRoles();

      if (result.success && result.roles) {
        setRoles(result.roles);
      } else {
        throw new Error(result.error || "Failed to fetch roles");
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch roles",
        variant: "destructive",
      });
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const handleUserDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {name, value, type, checked} = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleRoleToggle = (roleId: string) => {
    setSelectedRoles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(roleId)) {
        newSet.delete(roleId);
      } else {
        newSet.add(roleId);
      }
      return newSet;
    });
  };

  const handleCreateUser = async () => {
    setIsSubmitting(true);

    try {
      const userDataPayload = {
        username: userData.username,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        enabled: userData.enabled,
        credentials: [
          {
            type: "password",
            value: userData.password,
            temporary: false,
          },
        ],
        requiredActions: [],
        emailVerified: true
      };

      const result = await createUser(userDataPayload);

      if (result.success) {
        toast({
          title: "Success",
          description: "User created successfully",
        });

        // Store the user ID for role assignment
        if (result.userId) {
          setUserId(result.userId);
          // Move to the next step
          setStep(2);
        } else {
          throw new Error("User ID not returned from creation");
        }
      } else {
        throw new Error(result.error || "Failed to create user");
      }
    } catch (error) {
      console.error("Error creating user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create user",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAssignRoles = async () => {
    if (selectedRoles.size === 0) {
      // If no roles selected, just close the drawer
      toast({
        title: "Info",
        description: "No roles selected. User created without role assignments.",
      });
      onOpenChange(false);
      // Navigate to the user details page
      router.push(`/admin/users/${userId}`);
      return;
    }

    setIsSubmitting(true);

    try {
      // Get the selected roles data
      const rolesToAssign = roles.filter(role => selectedRoles.has(role.id));

      const result = await assignRolesToUser(userId, rolesToAssign);

      if (result.success) {
        toast({
          title: "Success",
          description: `User created and assigned ${selectedRoles.size} role(s)`,
        });
        onOpenChange(false);
        // Navigate to the user details page
        router.push(`/admin/users/${userId}`);
      } else {
        throw new Error(result.error || "Failed to assign roles");
      }
    } catch (error) {
      console.error("Error assigning roles:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to assign roles",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset state when closing
    setStep(1);
    setUserData({
      username: "",
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      enabled: true
    });
    setUserId("");
    setSelectedRoles(new Set());
    setSearchQuery("");
    onOpenChange(false);
  };

  // Filter roles based on search query
  const filteredRoles = searchQuery
    ? roles.filter(role =>
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (role.description?.toLowerCase().includes(searchQuery.toLowerCase()))
    )
    : roles;

  return (
    <Drawer
      direction="right"
      open={open}
      onOpenChange={handleClose}
    >
      <DrawerContent className="w-[30%] max-w-md">
        <div className="grid h-screen grid-rows-[auto_1fr_auto]">
          <DrawerHeader className="p-6">
            <DrawerTitle className="text-2xl font-bold flex items-center">
              <UserPlus className="mr-2 h-5 w-5"/>
              {step === 1 ? "Create New User" : "Assign Roles"}
            </DrawerTitle>
            <DrawerDescription>
              {step === 1 ? "Step 1: Enter user details" : "Step 2: Assign roles to user"}
            </DrawerDescription>
          </DrawerHeader>

          {step === 1 ? (
            <div className="overflow-auto p-6 space-y-4">
              <div>
                <Label htmlFor="username" className="text-sm font-medium text-slate-900">
                  Username <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="username"
                  name="username"
                  value={userData.username}
                  onChange={handleUserDataChange}
                  placeholder="Enter username"
                  required
                />
              </div>

              <div>
                <Label htmlFor="firstName" className="text-sm font-medium text-slate-900">
                  First Name
                </Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={userData.firstName}
                  onChange={handleUserDataChange}
                  placeholder="Enter first name"
                />
              </div>

              <div>
                <Label htmlFor="lastName" className="text-sm font-medium text-slate-900">
                  Last Name
                </Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={userData.lastName}
                  onChange={handleUserDataChange}
                  placeholder="Enter last name"
                />
              </div>

              <div>
                <Label htmlFor="email" className="text-sm font-medium text-slate-900">
                  Email <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={userData.email}
                  onChange={handleUserDataChange}
                  placeholder="Enter email address"
                  required
                />
              </div>

              <div>
                <Label htmlFor="password" className="text-sm font-medium text-slate-900">
                  Password <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={userData.password}
                  onChange={handleUserDataChange}
                  placeholder="Enter password"
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="enabled"
                  name="enabled"
                  checked={userData.enabled}
                  onCheckedChange={(checked) => setUserData(prev => ({...prev, enabled: checked}))}
                />
                <Label htmlFor="enabled">Account Enabled</Label>
              </div>
            </div>
          ) : (
            <div className="overflow-auto p-6 space-y-4">
              <div className="relative">
                <MagnifyingGlass className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"/>
                <Input
                  type="text"
                  placeholder="Search roles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="text-sm text-slate-500 mb-2">
                {selectedRoles.size} role(s) selected
              </div>

              {isLoadingRoles ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredRoles.length > 0 ? (
                    filteredRoles.map(role => (
                      <div key={role.id}
                           className="flex items-center space-x-2 p-2 border rounded-md hover:bg-slate-50">
                        <Checkbox
                          id={`role-${role.id}`}
                          checked={selectedRoles.has(role.id)}
                          onCheckedChange={() => handleRoleToggle(role.id)}
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor={`role-${role.id}`}
                            className="text-sm font-medium cursor-pointer"
                          >
                            {role.name}
                          </Label>
                          {role.description && (
                            <p className="text-xs text-slate-500">{role.description}</p>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-slate-500">
                      {searchQuery ? "No roles found matching your search" : "No roles available"}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          <DrawerFooter className="p-6 flex flex-row justify-between">
            {step === 1 ? (
              <>
                <DrawerClose asChild>
                  <Button variant="outline">
                    Cancel
                  </Button>
                </DrawerClose>
                <Button
                  type="button"
                  onClick={handleCreateUser}
                  disabled={isSubmitting || !userData.username || !userData.email || !userData.password}
                >
                  {isSubmitting ? "Creating..." : (
                    <>
                      Next <CaretRight className="ml-2 h-4 w-4"/>
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={() => setStep(1)}
                  disabled={isSubmitting}
                >
                  <CaretLeft className="mr-2 h-4 w-4"/>
                  Back
                </Button>
                <Button
                  type="button"
                  onClick={handleAssignRoles}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : (
                    <>
                      <Check className="mr-2 h-4 w-4"/>
                      Finish
                    </>
                  )}
                </Button>
              </>
            )}
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
