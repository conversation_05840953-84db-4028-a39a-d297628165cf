"use client";

import {useState, useEffect} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {UserPlus} from "@phosphor-icons/react";
import {UserDrawer} from "./user-drawer";

/**
 * Button component that opens the Create User drawer
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function CreateUserButton() {
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Force drawer to close when component unmounts
  useEffect(() => {
    return () => {
      setDrawerOpen(false);
    };
  }, []);

  return (
    <>
      <Button
        onClick={() => setDrawerOpen(true)}
        className="flex items-center"
      >
        <UserPlus className="mr-2 h-4 w-4"/>
        Create User
      </Button>

      <UserDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        mode="create"
      />
    </>
  );
}
