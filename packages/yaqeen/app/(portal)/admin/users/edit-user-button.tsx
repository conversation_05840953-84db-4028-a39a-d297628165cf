"use client";

import {useState, useEffect} from "react";
import {But<PERSON>} from "@/components/ui/button";
import {PencilSimple} from "@phosphor-icons/react";
import {UserDrawer} from "./user-drawer";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";

interface EditUserButtonProps {
  userId: string;
  user?: KeycloakUser;
}

/**
 * Button component that opens the Edit User drawer
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function EditUserButton({userId, user}: EditUserButtonProps) {
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Force drawer to close when component unmounts
  useEffect(() => {
    return () => {
      setDrawerOpen(false);
    };
  }, []);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={(e) => {
                e.stopPropagation();
                setDrawerOpen(true);
              }}
              variant="ghost"
              size="icon"
              className="h-8 w-8"
            >
              <PencilSimple className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Edit User</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <UserDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        userId={userId}
        initialUser={user}
        mode="edit"
      />
    </>
  );
}
