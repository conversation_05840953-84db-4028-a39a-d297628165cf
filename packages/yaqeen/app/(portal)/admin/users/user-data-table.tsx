"use client";

import {useSearchParams} from "next/navigation";
import {useEffect, useRef} from "react";
import {type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {DataTable} from "@/components/ui/data-table/data-table";
import {columns} from "./columns";

/**
 * Client component for Keycloak users data table with row click handling
 * Following Java Spring Boot architecture standards with proper layer separation
 */
interface UserDataTableProps {
  data: KeycloakUser[];
  totalCount: number;
}

export function UserDataTable({data, totalCount}: UserDataTableProps) {
  const searchParams = useSearchParams();
  const highlightUserId = searchParams.get('highlight');
  const tableRef = useRef<HTMLDivElement>(null);

  // Debug log to verify data has IDs
  console.log("User data with IDs:", data.map(user => ({id: user.id, username: user.username})));

  // Highlight the newly created user and scroll to it
  useEffect(() => {
    if (highlightUserId && tableRef.current) {
      // Find all rows in the table
      const rows = tableRef.current.querySelectorAll('tbody tr');

      // Find the row with the matching user ID
      let highlightedRow: Element | null = null;
      rows.forEach(row => {
        // Try to find the user ID in the row's cells
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
          if (cell?.textContent?.includes(highlightUserId)) {
            row?.classList?.add('bg-blue-50', 'transition-colors', 'duration-1000');
            highlightedRow = row;
          }
        });
      });

      // Scroll to the highlighted row
      if (highlightedRow) {
        setTimeout(() => {
          highlightedRow?.scrollIntoView({behavior: 'smooth', block: 'center'});

          // Remove highlight after 3 seconds
          setTimeout(() => {
            highlightedRow?.classList.remove('bg-blue-50');
          }, 3000);
        }, 300);
      }
    }

    // Add hover effect to all table rows
    const addHoverStyles = () => {
      if (tableRef.current) {
        const style = document.createElement('style');
        style.textContent = `
          .user-data-table tbody tr {
            transition: background-color 0.2s ease;
          }
          .user-data-table tbody tr:hover {
            background-color: rgba(34, 197, 94, 0.05) !important;
          }
        `;
        document.head.appendChild(style);
      }
    };

    addHoverStyles();

    // Cleanup function
    return () => {
      const styles = document.querySelectorAll('style');
      styles.forEach(style => {
        if (style.textContent?.includes('.user-data-table tbody tr:hover')) {
          document.head.removeChild(style);
        }
      });
    };
  }, [highlightUserId, data]);

  return (
    <div ref={tableRef} className="user-data-table">
      <DataTable
        columns={columns}
        data={{
          data: data,
          total: totalCount,
        }}
        searchPlaceholder=""
        emptyMessage="No users found"
        paginationEnabled={false}
      />
    </div>
  );
}
