"use client";

import {useRouter} from "next/navigation";
import {useProgressBar} from "@/components/progress-bar";
import {But<PERSON>} from "@/components/ui/button";
import {CaretLeft, CaretRight} from "@phosphor-icons/react/dist/ssr";

interface PaginationProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
}

/**
 * Pagination component for Keycloak users
 * Following Java Spring Boot architecture standards with proper layer separation
 */
export function Pagination({currentPage, pageSize, totalItems}: PaginationProps) {
  const router = useRouter();
  const progress = useProgressBar();

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / pageSize);

  // Convert to 0-based for internal calculations
  const currentPageZeroBased = currentPage - 1;

  const handlePageChange = (newPage: number) => {
    // Convert to 1-based for URL and API
    const newPageOneBased = newPage + 1;
    
    if (newPageOneBased === currentPage) return;

    progress.start();

    // Build URL with pagination parameters
    const params = new URLSearchParams(window.location.search);

    // Update page parameter (1-based)
    params.set("page", newPageOneBased.toString());

    // Navigate to the new URL
    const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}`;

    // Use router.push to navigate to the new URL
    // This will automatically trigger a data refetch since we're using a server component
    router.push(newUrl);

    // Complete the progress bar
    setTimeout(() => {
      progress.done();
    }, 100);
  };

  // Don't render pagination if there's only one page or no pages
  if (totalPages <= 1) return null;

  // Calculate which page numbers to show
  const getPageNumbers = () => {
    const maxVisiblePages = 5;
    let startPage = 0;
    let endPage = Math.min(maxVisiblePages - 1, totalPages - 1);

    if (totalPages > maxVisiblePages) {
      // If current page is near the beginning
      if (currentPageZeroBased < 3) {
        startPage = 0;
        endPage = maxVisiblePages - 1;
      }
      // If current page is near the end
      else if (currentPageZeroBased >= totalPages - 3) {
        startPage = totalPages - maxVisiblePages;
        endPage = totalPages - 1;
      }
      // If current page is in the middle
      else {
        startPage = currentPageZeroBased - 2;
        endPage = currentPageZeroBased + 2;
      }
    }

    return Array.from({length: endPage - startPage + 1}, (_, i) => startPage + i);
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="mt-6 flex items-center justify-between">
      <div className="text-sm text-muted-foreground">
        Showing {(currentPageZeroBased * pageSize) + 1}-{Math.min((currentPageZeroBased + 1) * pageSize, totalItems)} of {totalItems} items
      </div>

      <div className="flex items-center space-x-2">
        {/* Previous page button */}
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8"
          onClick={() => handlePageChange(currentPageZeroBased - 1)}
          disabled={currentPageZeroBased === 0}
        >
          <CaretLeft className="h-4 w-4"/>
        </Button>

        {/* Page numbers */}
        {pageNumbers.map((pageNumber) => (
          <Button
            key={pageNumber}
            variant={pageNumber === currentPageZeroBased ? "default" : "outline"}
            size="icon"
            className="h-8 w-8"
            onClick={() => handlePageChange(pageNumber)}
          >
            {pageNumber + 1}
          </Button>
        ))}

        {/* Next page button */}
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8"
          onClick={() => handlePageChange(currentPageZeroBased + 1)}
          disabled={currentPageZeroBased >= totalPages - 1}
        >
          <CaretRight className="h-4 w-4"/>
        </Button>
      </div>
    </div>
  );
}
