"use client";

import { useEffect } from "react";
import { useTranslations } from "next-intl";
export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  const t = useTranslations("common");

  useEffect(() => {
    console.error(error.message);
  }, [error]);

  return (
    <div className="flex h-[calc(100vh-140px)] flex-col items-center justify-center">
      <div>
        <h2 className="text-center">
          {error.message === "401" ? t("errors.unauthorized") : t("errors.somethingWentWrong")}
        </h2>
      </div>
      <button
        className="mt-4 rounded-md bg-lumi-500 px-4 py-2 text-sm text-white transition-colors hover:bg-lumi-400"
        onClick={() => reset()}
      >
        {t("tryAgain")}
      </button>
    </div>
  );
}
