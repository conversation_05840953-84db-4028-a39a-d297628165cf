import { api } from "@/api";
import { auth } from "@/auth";
import { AppSidebar } from "@/components/app-sidebar";
import { PermissionProvider } from "@/components/client-permission-gate";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { redirect } from "next/navigation";
import { Suspense, type ReactNode } from "react";
import { getUserLocale } from "@/services/locale";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default async function Layout({ children }: { children: ReactNode }) {
  const session = await auth();
  const role = session?.roles.includes("rental:finance:admin") ? "finance" : "cse";
  const locale = await getUserLocale();
  if (!session) {
    return redirect("/auth");
  }

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  return (
    <SidebarProvider defaultOpen={false}>
      <PermissionProvider>
        <Suspense fallback={<LoadingSpinner />}>
          <AppSidebar
            user={session?.user ?? null}
            branches={branches.body.data}
            side={(locale === "ar" ? "right" : "left") as "left" | "right" | undefined}
            role={role}
          />
        </Suspense>
        <SidebarInset>
          <section className="box-border flex min-h-[calc(100vh-70px)] flex-col">
            <main className="mb-5">{children}</main>
          </section>
        </SidebarInset>
      </PermissionProvider>
    </SidebarProvider>
  );
}
