"use client";

import { updateCategory } from "@/lib/actions";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

interface EditCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultValues: {
    id: number;
    englishName: string;
    arabicName: string;
  };
}

function SubmitButton({ confirmed }: { confirmed: boolean }) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending || !confirmed}>
      {pending ? "Updating..." : "Update Category"}
    </Button>
  );
}

export function EditCategoryDialog({ open, onOpenChange, defaultValues }: EditCategoryDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [confirmed, setConfirmed] = useState(false);

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateCategory, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Category updated successfully",
        variant: "success",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="px-0 sm:max-w-[425px]"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>Edit category</DialogTitle>
        </DialogHeader>
        <form className="px-4" action={formAction}>
          <input type="hidden" name="categoryId" value={defaultValues.id} />
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="categoryEnglishName">Category English Name</Label>
              <Input
                id="categoryEnglishName"
                name="englishName"
                defaultValue={defaultValues.englishName}
                placeholder="Enter category name in English"
                required
              />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="categoryArabicName">Category Arabic Name</Label>
              <Input
                id="categoryArabicName"
                name="arabicName"
                defaultValue={defaultValues.arabicName}
                placeholder="Enter category name in Arabic"
                dir="rtl"
                required
              />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
            <div className="flex space-x-2">
              <Checkbox
                className="mt-1"
                id="confirmed"
                name="confirmed"
                checked={confirmed}
                onCheckedChange={() => setConfirmed(!confirmed)}
              />
              <Label htmlFor="confirmed" className="text-sm text-muted-foreground">
                Are you sure to edit this category? This will lead to changes in different areas
              </Label>
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              Cancel
            </Button>
            <SubmitButton confirmed={confirmed} />
          </div>
        </form>
        {state?.message && <div className="mt-4 text-center text-sm text-red-500">{state.message}</div>}
      </DialogContent>
    </Dialog>
  );
}
