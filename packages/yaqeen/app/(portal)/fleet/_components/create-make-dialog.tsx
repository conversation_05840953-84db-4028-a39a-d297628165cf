"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CarProfile } from "@phosphor-icons/react/dist/ssr";
import { useActionState } from "react";
import { createMake } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useFormStatus } from "react-dom";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Creating..." : "Create Make"}
    </Button>
  );
}

export function CreateMakeDialog() {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(createMake, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Make created successfully",
        variant: "success",
        duration: 3000,
      });
      setOpen(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [state, toast, router]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <CarProfile className="size-4" />
          Create new make
        </Button>
      </DialogTrigger>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>Create new make</DialogTitle>
        </DialogHeader>
        <form className="px-4" action={formAction}>
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="makeEnglishName">Make English Name</Label>
              <Input id="makeEnglishName" name="englishName" placeholder="Place make name" required />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="makeArabicName">Make Arabic Name</Label>
              <Input id="makeArabicName" name="arabicName" placeholder="ضع اسم الشركة المصنعة" dir="rtl" required />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)} type="button">
              Cancel
            </Button>
            <SubmitButton />
          </DialogFooter>
        </form>
        {state?.message && <div className="mt-4 text-center text-sm text-red-500">{state.message}</div>}
      </DialogContent>
    </Dialog>
  );
}
