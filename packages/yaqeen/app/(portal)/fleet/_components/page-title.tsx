import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbItem,
  B<PERSON><PERSON><PERSON>bLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ProgressBarLink } from "@/components/progress-bar";

interface PageTitleProps {
  title: string;
  description: string;
  action?: React.ReactNode;
}

export default function PageTitle({ title, description, action }: PageTitleProps) {
  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/">Home</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/fleet/vehicles">Vehicles</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-medium tracking-tight">{title}</h1>
            <div className="flex items-center gap-2">
              <span className="text-slate-700">{description}</span>
            </div>
          </div>
          {action}
        </div>
      </div>
    </section>
  );
}
