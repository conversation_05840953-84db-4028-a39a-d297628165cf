"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { updateMake } from "@/lib/actions";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

interface EditMakeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultValues: {
    id: number;
    englishName: string;
    arabicName: string;
  };
}

function SubmitButton({ confirmed }: { confirmed: boolean }) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending || !confirmed}>
      {pending ? "Updating..." : "Update Make"}
    </Button>
  );
}

export function EditMakeDialog({ open, onOpenChange, defaultValues }: EditMakeDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [confirmed, setConfirmed] = useState(false);

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateMake, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Make updated successfully",
        variant: "success",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>Edit make</DialogTitle>
        </DialogHeader>
        <form action={formAction}>
          <input type="hidden" name="makeId" value={defaultValues.id} />
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="makeEnglishName">Make English Name</Label>
              <Input
                id="makeEnglishName"
                name="englishName"
                defaultValue={defaultValues.englishName}
                placeholder="Enter make name in English"
                required
              />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="makeArabicName">Make Arabic Name</Label>
              <Input
                id="makeArabicName"
                name="arabicName"
                defaultValue={defaultValues.arabicName}
                placeholder="أدخل اسم الماركة باللغة العربية"
                dir="rtl"
                required
              />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
            <div className="flex space-x-2">
              <Checkbox
                className="mt-1"
                id="confirmed"
                name="confirmed"
                checked={confirmed}
                onCheckedChange={() => setConfirmed(!confirmed)}
              />
              <Label htmlFor="confirmed" className="text-sm text-muted-foreground">
                Are you sure to edit this make? This will lead to changes in different areas
              </Label>
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              Cancel
            </Button>
            <SubmitButton confirmed={confirmed} />
          </div>
        </form>
        {state?.message && <div className="mt-4 text-center text-sm text-red-500">{state.message}</div>}
      </DialogContent>
    </Dialog>
  );
}
