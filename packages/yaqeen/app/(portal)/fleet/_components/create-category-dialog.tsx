"use client";

import { createCategory } from "@/lib/actions/fleet-actions";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { CalendarPlus } from "@phosphor-icons/react/dist/ssr";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Creating..." : "Create Category"}
    </Button>
  );
}

export function CreateCategoryDialog() {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(createCategory, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Category created successfully",
        variant: "success",
        duration: 3000,
      });
      setOpen(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [state, toast, router]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <CalendarPlus className="size-4" />
          Create new category
        </Button>
      </DialogTrigger>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>Create new category</DialogTitle>
        </DialogHeader>
        <form className="px-4" action={formAction}>
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="categoryEnglishName">Category English Name</Label>
              <Input id="categoryEnglishName" name="englishName" placeholder="Place category name" required />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="categoryArabicName">Category Arabic Name</Label>
              <Input id="categoryArabicName" name="arabicName" placeholder="ضع اسم الفئة" dir="rtl" required />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)} type="button">
              Cancel
            </Button>
            <SubmitButton />
          </DialogFooter>
        </form>
        {state?.message && <div className="mt-4 text-center text-sm text-red-500">{state.message}</div>}
      </DialogContent>
    </Dialog>
  );
}
