import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

interface CreateSpecDialogProps {
  trigger: React.ReactNode;
}

export function CreateSpecDialog({ trigger }: CreateSpecDialogProps) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <div onClick={() => setOpen(true)}>{trigger}</div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create new spec</DialogTitle>
            <DialogDescription>Add a new specification that vehicles can have.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="spec">Spec</Label>
              <Input id="spec" placeholder="Enter spec name" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="value-options">Value options</Label>
              <Input id="value-options" placeholder="Enter value options" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="unit">Unit</Label>
              <Input id="unit" placeholder="Enter unit" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="condition">Condition options</Label>
              <Input id="condition" placeholder="Enter condition options" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Create spec</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
