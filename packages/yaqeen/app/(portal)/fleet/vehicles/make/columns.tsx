"use client";

import { type Make } from "@/api/contracts/fleet/make/make-contract";
import { But<PERSON> } from "@/components/ui/button";
import { PencilSimple } from "@phosphor-icons/react";
import { type ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { EditMakeDialog } from "../../_components/edit-make-dialog";

function MakeActions({ row }: { row: { original: Make } }) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  return (
    <>
      <div className="flex justify-end gap-2">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            setIsEditDialogOpen(true);
          }}
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
        >
          <PencilSimple className="h-4 w-4" />
        </Button>
      </div>
      {isEditDialogOpen && (
        <EditMakeDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          defaultValues={{
            id: row.original.id,
            englishName: row.original.name.en,
            arabicName: row.original.name.ar,
          }}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<Make>[] = [
  {
    accessorKey: "name.en",
    header: "Name",
    cell: ({ row }) => row.original.name?.en ?? "N/A",
  },
  {
    accessorKey: "name.ar",
    header: "الاسم",
    cell: ({ row }) => row.original.name?.ar ?? "N/A",
  },
  {
    accessorKey: "vehicleCount",
    header: "Fleet Count",
  },
  {
    id: "actions",
    cell: ({ row }) => <MakeActions row={row} />,
  },
];
