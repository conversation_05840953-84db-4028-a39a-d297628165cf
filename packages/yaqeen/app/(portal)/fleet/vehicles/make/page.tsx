import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import PageTitle from "../../_components/page-title";
import { CreateMakeDialog } from "../../_components/create-make-dialog";
import { api } from "@/api";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string }>;
}) {
  return (
    <div>
      <PageTitle title="Makes" description="All vehicles in LUMI" action={<CreateMakeDialog />} />
      <div className="container px-24 py-6">
        <Suspense fallback={<TableSkeleton />}>
          <MakesTable searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}

const MakesTable = async ({ searchParams }: { searchParams: Promise<{ pageNumber?: string; pageSize?: string }> }) => {
  const _searchParams = await searchParams;

  const response = await api.fleet.makeContract.list({
    query: {
      pageNumber: Number(_searchParams.pageNumber) || 0,
      pageSize: Number(_searchParams.pageSize) || 10,
    },
  });

  if (response.status !== 200) {
    throw new Error("Failed to fetch makes");
  }

  const makes = response.body;

  return (
    <DataTable
      columns={columns}
      data={{
        total: makes.totalElements,
        data: makes.content,
      }}
      emptyMessage="No makes found"
      searchPlaceholder="Search makes..."
      rowClickId="name.en"
      paginationEnabled
    />
  );
};
