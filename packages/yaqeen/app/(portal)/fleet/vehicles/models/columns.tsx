"use client";

import { type Model } from "@/api/contracts/fleet/model/model-contract";
import { type ColumnDef } from "@tanstack/react-table";
import { useLocale } from "next-intl";
import { cn } from "@/lib/utils";

const LocalizedObject = ({
  className,
  localizedObject,
}: {
  className?: string;
  localizedObject?: { en: string; ar?: string } | null;
}) => {
  const locale = useLocale();
  if (!localizedObject) return <div className="text-start">Unknown</div>;
  return <div className={cn("text-start", className)}>{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

export const columns: ColumnDef<Model>[] = [
  {
    id: "make",
    cell: ({ row }) => {
      return <LocalizedObject localizedObject={row.original.make.name} />;
    },
    header: "Make",
  },
  {
    id: "model",
    cell: ({ row }) => {
      return <LocalizedObject localizedObject={row.original.name} />;
    },
    header: "Model",
  },
  {
    accessorKey: "vehicleGroup",
    header: "Vehicle Group",
  },
  {
    accessorKey: "version",
    header: "Version",
  },
  {
    accessorKey: "series",
    header: "Series",
  },
  {
    id: "modelAr",
    accessorFn: (row) => row.name?.ar,
    header: "نموذج السيارة",
  },
  {
    id: "makeAr",
    accessorFn: (row) => row.make?.name?.ar,
    header: "نوع السيارة",
  },
];
