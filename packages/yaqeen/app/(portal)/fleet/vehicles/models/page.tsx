import { api } from "@/api";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type FilterOption } from "@/components/ui/data-table/toolbar";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Plus } from "lucide-react";
import { Suspense } from "react";
import PageTitle from "../../_components/page-title";
import { columns } from "./columns";
import { getLocale } from "next-intl/server";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string }>;
}) {
  return (
    <div>
      <PageTitle
        title="Vehicle Models"
        description="All models across all vehicle makes"
        action={
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create New Model
          </Button>
        }
      />
      <div className="container px-24 pb-6">
        <Suspense fallback={<TableSkeleton filterCount={2} />}>
          <ModelsTable searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}

const ModelsTable = async ({ searchParams }: { searchParams: Promise<{ pageNumber?: string; pageSize?: string }> }) => {
  const _searchParams = await searchParams;
  const locale = (await getLocale()) as "en" | "ar";

  const allModelsResponse = await api.fleet.modelContract.list({
    query: {
      pageNumber: 0,
      pageSize: 1000,
    },
  });

  if (allModelsResponse.status !== 200) {
    throw new Error("Failed to fetch all models");
  }

  const paginatedResponse = await api.fleet.modelContract.list({
    query: {
      pageNumber: Number(_searchParams.pageNumber) || 0,
      pageSize: Number(_searchParams.pageSize) || 10,
    },
  });

  if (paginatedResponse.status !== 200) {
    throw new Error("Failed to fetch paginated models");
  }

  const uniqueMakes = Array.from(
    new Set(allModelsResponse.body.content.map((model) => model.make.name[locale] || model.make.name.en))
  );
  const uniqueModels = Array.from(
    new Set(allModelsResponse.body.content.map((model) => model.name[locale] || model.name.en))
  );

  const filters: FilterOption[] = [
    {
      filterKey: "make",
      filterName: "Make",
      columnKey: "make",
      isMultiSelect: true,
      options: uniqueMakes.map((make) => ({
        label: make,
        value: make.toUpperCase().replace(/\s+/g, "_"),
      })),
    },
    {
      filterKey: "model",
      filterName: "Model",
      columnKey: "model",
      isMultiSelect: true,
      options: uniqueModels.map((model) => ({
        label: model,
        value: model.toUpperCase().replace(/\s+/g, "_"),
      })),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={{ total: paginatedResponse.body.totalElements, data: paginatedResponse.body.content }}
      emptyMessage="No models found"
      rowClickId="id"
      filters={filters}
      paginationEnabled
    />
  );
};
