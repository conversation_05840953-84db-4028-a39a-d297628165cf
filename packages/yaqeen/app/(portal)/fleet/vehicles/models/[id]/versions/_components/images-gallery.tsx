import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";

interface ImagesGalleryProps {
  images: string[];
}

export function ImagesGallery({ images }: ImagesGalleryProps) {
  const displayImages = images.slice(0, images.length > 5 ? 4 : 5);
  const remainingCount = images.length > 5 ? images.length - 4 : 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Images</CardTitle>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            Add Image
          </Button>
          <Button variant="outline" size="sm">
            View all images
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {images.length > 0 ? (
          <div className="flex flex-wrap gap-4">
            {displayImages.map((image, index) => (
              <div key={index} className="relative h-20 w-20 overflow-hidden rounded-md border">
                <Image src={image} alt={`Vehicle image ${index + 1}`} fill className="object-contain" />
              </div>
            ))}

            {remainingCount > 0 && (
              <div className="flex h-20 w-20 items-center justify-center rounded-md border text-sm text-slate-600">
                +{remainingCount}
              </div>
            )}
          </div>
        ) : (
          <div className="py-4 text-center text-slate-500">No images available</div>
        )}
      </CardContent>
    </Card>
  );
}
