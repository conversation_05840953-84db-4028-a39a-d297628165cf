export const mockData = {
  versions: {
    count: 20,
    items: [
      {
        id: 1,
        name: "Default version",
        model: "Toyota Camry",
        image: "/placeholder.svg",
      },
      {
        id: 2,
        name: "LSE",
        model: "Toyota Camry",
        image: "/placeholder.svg",
      },
      {
        id: 3,
        name: "XSE",
        model: "Toyota Camry",
        image: "/placeholder.svg",
      },
    ],
  },
  basicDetails: {
    versionName: "Toyota Camry",
    series: "************",
    sapMaterialId: "************",
    year: "2024",
  },
  specs: {
    general: {
      numberOfSeats: "5",
      numberOfDoors: "3",
      luggageSpaceBig: "2",
      luggageSpaceMedium: "NA",
      luggageSpaceSmall: "1",
    },
    performanceEngine: {
      transmission: "Automatic",
      transmissionType: "4WD",
      engineSize: "4 CC",
      horsepower: "500 HP",
      fuelType: "Petrol-95",
      fuelCapacity: "300 L",
    },
  },
  features: {
    popular: [
      { name: "Sun roof", checked: true },
      { name: "Radio", checked: true },
      { name: "Bluetooth", checked: true },
      { name: "Camera sensors / Rear parking", checked: true },
      { name: "Apple Carplay", checked: true },
      { name: "Cruise control", checked: true },
    ],
    comfort: [
      { name: "Air conditioning", checked: true },
      { name: "Heated seats", checked: false },
      { name: "Power windows", checked: true },
    ],
    technology: [
      { name: "Navigation system", checked: true },
      { name: "Bluetooth connectivity", checked: true },
      { name: "USB ports", checked: true },
    ],
    safety: [
      { name: "ABS", checked: true },
      { name: "Airbags", checked: true },
      { name: "Lane assist", checked: false },
    ],
  },
  images: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
};
