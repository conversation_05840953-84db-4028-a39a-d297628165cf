import { api } from "@/api";
import { VersionsCards } from "./_components/versions-cards";
import { BasicDetails } from "./_components/basic-details";
import { VersionSpecs } from "./_components/version-specs";
import { ImagesGallery } from "./_components/images-gallery";
import { VersionsSkeleton } from "./_components/versions-skeleton";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { getLocale } from "next-intl/server";

async function VersionsContent({ id }: { id: string }) {
  const locale = (await getLocale()) as "en" | "ar";

  const modelResponse = await api.fleet.modelContract.details({
    params: {
      id: Number(id),
    },
  });

  if (modelResponse.status === 404) {
    notFound();
  }

  if (modelResponse.status !== 200) {
    return <div>Error loading model details</div>;
  }

  const modelDetails = modelResponse.body;

  const versionsData = {
    count: modelDetails.variants.length,
    items: modelDetails.variants.map((variant) => {
      return {
        id: variant.id,
        name: variant.version || variant.name.en,
        variantName: `${variant.make.name[locale] || variant.make.name.en} ${variant.name[locale] || variant.name.en}`,
        image: variant.primaryImageUrl || "/placeholder.svg",
        vehicleCount: variant.fleetCount,
        group: variant.vehicleGroup,
      };
    }),
  };

  const basicDetailsData = {
    versionName: modelDetails.name[locale] || modelDetails.name.en,
    series: modelDetails.series || "N/A",
    sapMaterialId: modelDetails.materialId || "N/A",
    year: new Date().getFullYear().toString(),
  };

  const specs = modelDetails.specification || {};
  const specsData = {
    general: {
      numberOfSeats: specs.seatingCapacity?.toString() || "N/A",
      numberOfDoors: specs.doors?.toString() || "N/A",
      luggageSpaceBig: specs.luggageCountBig?.toString() || "N/A",
      luggageSpaceMedium: specs.luggageCountMedium?.toString() || "N/A",
      luggageSpaceSmall: specs.luggageCountSmall?.toString() || "N/A",
    },
    performanceEngine: {
      transmission: specs.transmission || "N/A",
      transmissionType: specs.transmissionType || "N/A",
      engineSize: specs.engineSize?.toString() || "N/A",
      horsepower: specs.horsepower?.toString() || "N/A",
      fuelType: specs.fuelType || "N/A",
      fuelCapacity: specs.fuelCapacity?.toString() || "N/A",
    },
  };

  const extractFeatures = (featureType: "interiorFeatures" | "exteriorFeatures" | "safetyFeatures") => {
    const featureObj = specs[featureType] || {};
    return Object.keys(featureObj).map((key) => ({
      name: key,
      checked: !!featureObj[key],
    }));
  };

  const features = {
    interior: extractFeatures("interiorFeatures"),
    exterior: extractFeatures("exteriorFeatures"),
    safety: extractFeatures("safetyFeatures"),
  };

  const images = modelDetails.images?.map((image) => image.url) || [];

  return (
    <div className="flex flex-col space-y-6">
      <VersionsCards versions={versionsData} />
      <BasicDetails details={basicDetailsData} />
      <VersionSpecs specs={specsData} features={features} />
      <ImagesGallery images={images} />
    </div>
  );
}

export default async function ModelVersionsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return (
    <Suspense fallback={<VersionsSkeleton />}>
      <VersionsContent id={id} />
    </Suspense>
  );
}
