import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Pencil } from "lucide-react";

interface BasicDetailsProps {
  details: {
    versionName: string;
    series: string;
    sapMaterialId: string;
    year: string;
  };
}

export function BasicDetails({ details }: BasicDetailsProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Basic Details</CardTitle>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Pencil className="h-4 w-4" />
          <span className="sr-only">Edit</span>
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Version name</p>
            <p className="font-medium">{details.versionName}</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Series (Optional)</p>
            <p className="font-medium">{details.series}</p>
          </div>
        </div>
        <Separator />
        <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">SAP material ID</p>
            <p className="font-medium">{details.sapMaterialId}</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Year</p>
            <p className="font-medium">{details.year}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
