"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

interface TabItem {
  label: string;
  href: string;
}

interface TabNavigationProps {
  tabs: TabItem[];
}

export default function TabNavigation({ tabs }: TabNavigationProps) {
  const pathname = usePathname();

  return (
    <div className="border-t border-slate-200">
      <div className="flex px-6">
        {tabs.map((tab) => {
          const isOverviewTab = !tab.href.includes("/versions");
          const isVersionsTab = tab.href.includes("/versions");

          const isActive =
            (isOverviewTab && !pathname.includes("/versions")) || (isVersionsTab && pathname.includes("/versions"));

          return (
            <ProgressBarLink
              key={tab.label}
              href={tab.href}
              className={cn(
                "mx-3 box-border cursor-pointer gap-2 py-3 text-sm first:ml-0 last:mr-0",
                "border-b-2",
                isActive
                  ? "border-b-slate-900 font-bold text-slate-900"
                  : "border-transparent text-slate-700 hover:border-slate-400 hover:text-slate-900",
                "box-border transition duration-300"
              )}
            >
              {tab.label}
            </ProgressBarLink>
          );
        })}
      </div>
    </div>
  );
}
