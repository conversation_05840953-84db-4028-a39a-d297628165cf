"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, Plus } from "lucide-react";
import Image from "next/image";
import { VersionsSidesheet } from "./versions-sidesheet";
import { useState } from "react";
import { useRouter, useParams } from "next/navigation";

interface VersionCardItem {
  id: number;
  name: string;
  variantName: string;
  image: string;
  vehicleCount?: number;
  group: string;
}

interface VersionsCardsProps {
  versions: {
    count: number;
    items: VersionCardItem[];
  };
}

export function VersionsCards({ versions }: VersionsCardsProps) {
  const [sidesheetOpen, setSidesheetOpen] = useState(false);
  const router = useRouter();
  const params = useParams();
  const modelId = params.id as string;

  const displayVersions = versions.items.slice(0, 3);
  const hiddenCount = versions.items.length > 3 ? versions.items.length - 3 : 0;

  const handleCreateVersion = () => {
    router.push(`/fleet/vehicles/models/${modelId}/variants/create`);
  };

  return (
    <>
      <Card className="overflow-hidden">
        <div className="flex flex-col gap-4 border-b p-6 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-xl font-semibold">Versions ({versions.count} versions)</h2>
          <div className="flex flex-wrap items-center gap-3">
            <Button className="bg-[#9AE24C] text-black hover:bg-[#8bcb45]" onClick={handleCreateVersion}>
              <Plus className="mr-2 h-5 w-5" />
              Create new version
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2 border-slate-200"
              onClick={() => setSidesheetOpen(true)}
            >
              <Eye className="mr-2 h-5 w-5" />
              View all versions
            </Button>
          </div>
        </div>
        <CardContent className="grid grid-cols-1 gap-6 p-6 sm:grid-cols-2 lg:grid-cols-3">
          {displayVersions.length > 0 ? (
            <>
              {displayVersions.map((version, index) => (
                <div
                  key={version.id}
                  className={`flex flex-col overflow-hidden rounded-lg border ${
                    index === 0 ? "border-blue-100 bg-blue-50" : "bg-white"
                  }`}
                >
                  <div className="relative flex h-48 items-center justify-center overflow-hidden bg-white p-6">
                    {version.image && version.image !== "/placeholder.svg" ? (
                      <Image src={version.image} alt={version.name} fill className="object-contain p-2" />
                    ) : (
                      <div className="text-center text-lg text-slate-500">Image</div>
                    )}
                  </div>
                  <div className="border-t p-4">
                    <h3 className="text-lg font-medium">{version.name}</h3>
                    <div className="flex items-center space-x-1 text-slate-500">
                      <span>{version.variantName}</span>
                      <span className="inline-block h-1 w-1 rounded-full bg-gray-400 text-gray-400"></span>
                      <span>{version.vehicleCount} vehicles</span>
                      <span className="inline-block h-1 w-1 rounded-full bg-gray-400 text-gray-400"></span>
                      <span>{version.group}</span>
                    </div>
                  </div>
                </div>
              ))}

              {hiddenCount > 0 && (
                <div
                  className="flex cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-slate-300 bg-slate-50 p-6 transition-colors hover:bg-slate-100"
                  onClick={() => setSidesheetOpen(true)}
                >
                  <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-slate-100">
                    <span className="text-lg font-medium text-slate-600">+{hiddenCount}</span>
                  </div>
                  <p className="text-center text-sm text-slate-500">More versions</p>
                </div>
              )}
            </>
          ) : (
            <div className="col-span-full py-8 text-center text-slate-500">No versions available for this model</div>
          )}
        </CardContent>
      </Card>

      <VersionsSidesheet open={sidesheetOpen} onOpenChange={setSidesheetOpen} versions={versions.items} />
    </>
  );
}
