import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle } from "@/components/ui/sheet";
import Image from "next/image";

interface VersionItem {
  id: number;
  name: string;
  variantName: string;
  image: string;
  vehicleCount?: number;
  group: string;
}

interface VersionsSidesheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  versions: VersionItem[];
}

export function VersionsSidesheet({ open, onOpenChange, versions }: VersionsSidesheetProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full p-0 sm:max-w-[600px]">
        <SheetHeader className="flex justify-between p-6">
          <div>
            <SheetTitle className="text-2xl font-bold">Versions</SheetTitle>
            <p className="mt-1 text-gray-600">Full list of versions under this model</p>
          </div>
        </SheetHeader>
        <div className="max-h-[calc(100vh-100px)] overflow-y-auto">
          <div className="divide-y">
            {versions.length > 0 ? (
              versions.map((version) => (
                <div key={version.id} className="flex items-center gap-4 p-5">
                  <div className="relative h-24 w-24 flex-shrink-0 overflow-hidden bg-slate-100">
                    {version.image && version.image !== "/placeholder.svg" ? (
                      <Image src={version.image} alt={version.name} fill className="object-cover" />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center text-xs text-slate-500">Image</div>
                    )}
                  </div>
                  <div className="flex flex-1 flex-col">
                    <h3 className="text-xl font-semibold">{version.name}</h3>
                    <div className="flex items-center space-x-1">
                      <span className="text-md text-gray-700">{version.variantName}</span>
                      <span className="inline-block h-1 w-1 rounded-full bg-gray-400 text-gray-400"></span>
                      <span className="text-md text-gray-700">{version?.vehicleCount} vehicles</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="py-8 text-center text-slate-500">No versions available</div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
