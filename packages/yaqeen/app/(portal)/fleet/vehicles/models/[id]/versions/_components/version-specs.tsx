import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Check, Pencil } from "lucide-react";

interface Feature {
  name: string;
  checked: boolean;
}

interface VersionSpecsProps {
  specs: {
    general: {
      numberOfSeats: string;
      numberOfDoors: string;
      luggageSpaceBig: string;
      luggageSpaceMedium: string;
      luggageSpaceSmall: string;
    };
    performanceEngine: {
      transmission: string;
      transmissionType: string;
      engineSize: string;
      horsepower: string;
      fuelType: string;
      fuelCapacity: string;
    };
  };
  features: {
    interior: Feature[];
    exterior: Feature[];
    safety: Feature[];
  };
}

export function VersionSpecs({ specs, features }: VersionSpecsProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Version Specs</CardTitle>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Pencil className="h-4 w-4" />
          <span className="sr-only">Edit</span>
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        {/* General Section */}
        <div className="border-b">
          <div className="p-4">
            <h3 className="text-base font-medium">General</h3>
          </div>
          <div className="grid grid-cols-2 border-t p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Number of seats</p>
              <p className="font-medium">{specs.general.numberOfSeats}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Number of Doors</p>
              <p className="font-medium">{specs.general.numberOfDoors}</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-x-4 border-t p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Luggage Space (Big Bag)</p>
              <p className="font-medium">{specs.general.luggageSpaceBig}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Luggage Space (Medium Bag)</p>
              <p className="font-medium">{specs.general.luggageSpaceMedium}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Luggage Space (Small Bag)</p>
              <p className="font-medium">{specs.general.luggageSpaceSmall}</p>
            </div>
          </div>
        </div>

        {/* Performance & Engine Section */}
        <div>
          <div className="p-4">
            <h3 className="text-base font-medium">Performance & Engine</h3>
          </div>
          <div className="grid grid-cols-2 border-t p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Transmission</p>
              <p className="font-medium">{specs.performanceEngine.transmission}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Transmission Type</p>
              <p className="font-medium">{specs.performanceEngine.transmissionType}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 border-t p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Engine Size</p>
              <p className="font-medium">{specs.performanceEngine.engineSize}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Horsepower</p>
              <p className="font-medium">{specs.performanceEngine.horsepower}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 border-t p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Fuel Type</p>
              <p className="font-medium">{specs.performanceEngine.fuelType}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Fuel Capacity</p>
              <p className="font-medium">{specs.performanceEngine.fuelCapacity}</p>
            </div>
          </div>
        </div>

        {/* Features & Technology Section */}
        <div>
          <div className="p-4">
            <h3 className="text-base font-medium">Features & Technology</h3>
          </div>
          <div className="border-t">
            <Tabs defaultValue="interior" className="w-full">
              <div className="border-b">
                <TabsList className="flex w-full justify-start bg-transparent px-4">
                  <TabsTrigger
                    value="interior"
                    className="border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    Interior
                  </TabsTrigger>
                  <TabsTrigger
                    value="exterior"
                    className="border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    Exterior
                  </TabsTrigger>
                  <TabsTrigger
                    value="safety"
                    className="border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    Safety
                  </TabsTrigger>
                </TabsList>
              </div>
              <TabsContent value="interior" className="p-4">
                <FeatureList features={features.interior} />
              </TabsContent>
              <TabsContent value="exterior" className="p-4">
                <FeatureList features={features.exterior} />
              </TabsContent>
              <TabsContent value="safety" className="p-4">
                <FeatureList features={features.safety} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function FeatureList({ features }: { features: Feature[] }) {
  return (
    <div className="grid grid-cols-2 gap-4">
      {features.map((feature, index) => (
        <div key={index} className="flex items-center justify-between space-x-2">
          <span>{feature.name}</span>
          {feature.checked && (
            <div className="flex h-6 w-6 items-center justify-center rounded-full border border-slate-400 text-slate-400">
              <Check className="h-4 w-4" />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
