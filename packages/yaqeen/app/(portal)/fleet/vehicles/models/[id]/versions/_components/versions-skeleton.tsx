import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function VersionsSkeleton() {
  return (
    <div className="flex flex-col space-y-6">
      {/* Versions Cards Skeleton */}
      <Card className="overflow-hidden">
        <div className="flex items-center justify-between border-b p-6">
          <Skeleton className="h-7 w-64" />
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-40" />
          </div>
        </div>
        <CardContent className="grid grid-cols-3 gap-6 p-6">
          {Array(3)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="flex flex-col overflow-hidden rounded-lg border">
                <Skeleton className="h-48 w-full" />
                <div className="space-y-2 border-t p-4">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
            ))}
        </CardContent>
      </Card>

      {/* Basic Details Skeleton */}
      <Card>
        <div className="flex items-center justify-between border-b p-4">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        <CardContent className="p-0">
          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-32" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
          <div className="border-t" />
          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-32" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Version Specs Skeleton */}
      <Card>
        <div className="flex items-center justify-between border-b p-4">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        <CardContent className="p-0">
          <div className="p-4">
            <Skeleton className="mb-4 h-5 w-24" />
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-8" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-8" />
              </div>
            </div>
          </div>
          <div className="border-t" />
          <div className="p-4">
            <Skeleton className="mb-4 h-5 w-40" />
            <div className="flex flex-wrap gap-4">
              {Array(6)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={i} className="h-6 w-32" />
                ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Images Skeleton */}
      <Card>
        <div className="flex items-center justify-between border-b p-4">
          <Skeleton className="h-6 w-16" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-32" />
          </div>
        </div>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="h-20 w-20 rounded-md" />
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
