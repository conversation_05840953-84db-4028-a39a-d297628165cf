import { api } from "@/api";

export default async function ModelDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  const model = await api.fleet.modelContract.details({
    params: {
      id: Number(id),
    },
  });

  if (model.status !== 200) {
    return <div>Error</div>;
  }

  return (
    <div className="flex flex-col">
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h3 className="mb-4 text-lg font-medium">Model Type</h3>
        <div className="grid grid-cols-3 gap-6">
          <div>
            <p className="mb-2 text-sm text-slate-500">Group</p>
            <div className="flex items-center rounded-md border p-2">
              <span>C</span>
            </div>
          </div>
          <div>
            <p className="mb-2 text-sm text-slate-500">Make</p>
            <div className="flex items-center rounded-md border p-2">
              <span>{model.body.make.name.en}</span>
            </div>
          </div>
          <div>
            <p className="mb-2 text-sm text-slate-500">Category</p>
            <div className="flex items-center rounded-md border p-2">
              <span>Luxury</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
