import { api } from "@/api";
import {
  B<PERSON><PERSON>rumb,
  Bread<PERSON>rumbI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ProgressBarLink } from "@/components/progress-bar";
import { Suspense } from "react";
import TabNavigation from "./_components/tab-navigation";
import { getLocale } from "next-intl/server";
interface LayoutProps {
  children: React.ReactNode;
  params: {
    id: string;
  };
}

export default async function ModelLayout({ children, params }: LayoutProps) {
  const model = await api.fleet.modelContract.details({
    params: {
      id: Number(params.id),
    },
  });

  if (model.status !== 200) {
    return <div>Error</div>;
  }

  const tabs = [
    { label: "Model Overview", href: `/fleet/vehicles/models/${params.id}` },
    { label: "Versions & Specs", href: `/fleet/vehicles/models/${params.id}/versions` },
  ];

  return (
    <div className="flex flex-col">
      <ModelHeader model={model.body} tabs={tabs} />
      <div className="container py-6">
        <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>
      </div>
    </div>
  );
}

interface ModelHeaderProps {
  model: {
    id: number;
    name: {
      en: string;
      ar?: string;
    };
    make: {
      name: {
        en: string;
        ar?: string;
      };
    };
  };
  tabs: Array<{ label: string; href: string }>;
}

async function ModelHeader({ model, tabs }: ModelHeaderProps) {
  const locale = (await getLocale()) as "en" | "ar";

  return (
    <section className="flex w-full flex-col self-stretch border-b border-slate-200 bg-slate-50">
      <div className="px-6">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/">Home</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/fleet/vehicles">Vehicles</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/fleet/vehicles/models">Model</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{model.name[locale] ?? model.name.en}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex w-full items-start py-6 text-slate-900">
          <div className="flex w-full flex-col justify-center">
            <h2 className="mb-2 text-3xl font-medium tracking-tight">{model.name[locale] ?? model.name.en}</h2>
            <p className="text-slate-500">{model.make.name[locale] ?? model.make.name.en}</p>
          </div>
          <div className="ml-auto flex items-center">
            <button className="rounded-md bg-white px-4 py-2 text-sm font-medium text-slate-700 shadow-sm ring-1 ring-inset ring-slate-300 hover:bg-slate-50">
              ...
            </button>
          </div>
        </div>
      </div>

      <TabNavigation tabs={tabs} />
    </section>
  );
}
