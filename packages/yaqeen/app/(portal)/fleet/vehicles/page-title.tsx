"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import {
  Breadcrumb,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
  BreadcrumbList,
  BreadcrumbItem,
} from "@/components/ui/breadcrumb";
import { useTranslations } from "next-intl";

export default function PageTitle({
  title,
  breadcrumb,
  subtitle,
}: {
  title: string;
  breadcrumb: string;
  subtitle: string;
}) {
  const tNav = useTranslations("nav");

  return (
    <section className="flex w-full flex-col self-stretch bg-slate-50">
      <div className="px-6">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/">{tNav("home")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/fleet/vehicles">{tNav("vehicles")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{breadcrumb}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex w-full items-start py-6 text-slate-900">
          <div className="flex w-full flex-col justify-center">
            <h2 className="mb-2 text-3xl font-medium tracking-tight">{title}</h2>
            <p className="text-slate-500">{subtitle}</p>
          </div>
        </div>
      </div>
    </section>
  );
}
