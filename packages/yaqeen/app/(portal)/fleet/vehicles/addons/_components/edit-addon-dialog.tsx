"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { updateAddon } from "@/lib/actions/fleet-actions";

type State = {
  message: string | null;
  errors: {
    code?: string;
    englishName?: string;
    arabicName?: string;
    englishDescription?: string;
    arabicDescription?: string;
    imageUrl?: string;
  };
  success: boolean;
};

interface EditAddonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultValues: {
    id: number;
    code: string;
    englishName: string;
    arabicName: string;
    englishDescription?: string;
    arabicDescription?: string;
    imageUrl?: string;
    enabled: boolean;
  };
}

function SubmitButton({ confirmed }: { confirmed: boolean }) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending || !confirmed}>
      {pending ? "Updating..." : "Update Addon"}
    </Button>
  );
}

export function EditAddonDialog({ open, onOpenChange, defaultValues }: EditAddonDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [confirmed, setConfirmed] = useState(false);

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateAddon, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Addon updated successfully",
        variant: "success",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>Edit addon</DialogTitle>
        </DialogHeader>
        <form action={formAction}>
          <input type="hidden" name="addonId" value={defaultValues.id} />
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonCode">Addon Code</Label>
              <Input
                id="addonCode"
                name="code"
                defaultValue={defaultValues.code}
                placeholder="Enter addon code"
                required
              />
              {state?.errors?.code && <span className="text-sm text-red-500">{state.errors.code}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonEnglishName">Addon English Name</Label>
              <Input
                id="addonEnglishName"
                name="englishName"
                defaultValue={defaultValues.englishName}
                placeholder="Enter addon name"
                required
              />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonArabicName">Addon Arabic Name</Label>
              <Input
                id="addonArabicName"
                name="arabicName"
                defaultValue={defaultValues.arabicName}
                placeholder="أدخل اسم الإضافة"
                dir="rtl"
                required
              />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonEnglishDescription">English Description (Optional)</Label>
              <Input
                id="addonEnglishDescription"
                name="englishDescription"
                defaultValue={defaultValues.englishDescription}
                placeholder="Enter description"
              />
              {state?.errors?.englishDescription && (
                <span className="text-sm text-red-500">{state.errors.englishDescription}</span>
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonArabicDescription">Arabic Description (Optional)</Label>
              <Input
                id="addonArabicDescription"
                name="arabicDescription"
                defaultValue={defaultValues.arabicDescription}
                placeholder="أدخل الوصف"
                dir="rtl"
              />
              {state?.errors?.arabicDescription && (
                <span className="font-arabic w-full text-right text-sm text-red-500">
                  {state.errors.arabicDescription}
                </span>
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonImageUrl">Image URL (Optional)</Label>
              <Input
                id="addonImageUrl"
                name="imageUrl"
                defaultValue={defaultValues.imageUrl}
                placeholder="Enter image URL"
              />
              {state?.errors?.imageUrl && <span className="text-sm text-red-500">{state.errors.imageUrl}</span>}
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="addonEnabled" name="enabled" defaultChecked={defaultValues.enabled} />
              <Label htmlFor="addonEnabled">Enabled</Label>
            </div>
            <div className="flex space-x-2">
              <Checkbox
                className="mt-1"
                id="confirmed"
                name="confirmed"
                checked={confirmed}
                onCheckedChange={() => setConfirmed(!confirmed)}
              />
              <Label htmlFor="confirmed" className="text-sm text-muted-foreground">
                Are you sure to edit this addon? This will lead to changes in different areas
              </Label>
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              Cancel
            </Button>
            <SubmitButton confirmed={confirmed} />
          </div>
        </form>
        {state?.message && <div className="mt-4 text-center text-sm text-red-500">{state.message}</div>}
      </DialogContent>
    </Dialog>
  );
}
