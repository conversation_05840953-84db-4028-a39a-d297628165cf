"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/lib/hooks/use-toast";
import { createAddon } from "@/lib/actions/fleet-actions";
import { CalendarPlus } from "@phosphor-icons/react/dist/ssr";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useActionState } from "react";
import { useFormStatus } from "react-dom";

type State = {
  message: string | null;
  errors: {
    code?: string;
    englishName?: string;
    arabicName?: string;
    englishDescription?: string;
    arabicDescription?: string;
    imageUrl?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Creating..." : "Create Addon"}
    </Button>
  );
}

export function CreateAddonDialog() {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(createAddon, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Addon created successfully",
        variant: "success",
        duration: 3000,
      });
      setOpen(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [state, toast, router]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <CalendarPlus className="size-4" />
          Create new addon
        </Button>
      </DialogTrigger>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>Create new addon</DialogTitle>
        </DialogHeader>
        <form className="px-4" action={formAction}>
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonCode">Addon Code</Label>
              <Input id="addonCode" name="code" placeholder="Enter addon code" required />
              {state?.errors?.code && <span className="text-sm text-red-500">{state.errors.code}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonEnglishName">Addon English Name</Label>
              <Input id="addonEnglishName" name="englishName" placeholder="Enter addon name" required />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonArabicName">Addon Arabic Name</Label>
              <Input id="addonArabicName" name="arabicName" placeholder="أدخل اسم الإضافة" dir="rtl" required />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonEnglishDescription">English Description (Optional)</Label>
              <Input id="addonEnglishDescription" name="englishDescription" placeholder="Enter description" />
              {state?.errors?.englishDescription && (
                <span className="text-sm text-red-500">{state.errors.englishDescription}</span>
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonArabicDescription">Arabic Description (Optional)</Label>
              <Input id="addonArabicDescription" name="arabicDescription" placeholder="أدخل الوصف" dir="rtl" />
              {state?.errors?.arabicDescription && (
                <span className="font-arabic w-full text-right text-sm text-red-500">
                  {state.errors.arabicDescription}
                </span>
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <Label htmlFor="addonImageUrl">Image URL (Optional)</Label>
              <Input id="addonImageUrl" name="imageUrl" placeholder="Enter image URL" />
              {state?.errors?.imageUrl && <span className="text-sm text-red-500">{state.errors.imageUrl}</span>}
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="addonEnabled" name="enabled" defaultChecked />
              <Label htmlFor="addonEnabled">Enabled</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)} type="button">
              Cancel
            </Button>
            <SubmitButton />
          </DialogFooter>
        </form>
        {state?.message && <div className="mt-4 text-center text-sm text-red-500">{state.message}</div>}
      </DialogContent>
    </Dialog>
  );
}
