import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import PageTitle from "../../_components/page-title";
import { CreateAddonDialog } from "./_components/create-addon-dialog";
import { api } from "@/api";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string }>;
}) {
  return (
    <div>
      <PageTitle title="Addons" description="All addons in LUMI" action={<CreateAddonDialog />} />
      <div className="container px-24 py-6">
        <Suspense fallback={<TableSkeleton />}>
          <AddonsTable searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}

const AddonsTable = async ({ searchParams }: { searchParams: Promise<{ pageNumber?: string; pageSize?: string }> }) => {
  const _searchParams = await searchParams;

  const response = await api.fleet.addonContract.getAddons({
    query: {
      pageNumber: Number(_searchParams.pageNumber) || 0,
      pageSize: Number(_searchParams.pageSize) || 10,
    },
  });

  if (response.status !== 200) {
    throw new Error("Failed to fetch addons");
  }

  const addons = response.body;

  return (
    <DataTable
      columns={columns}
      data={{
        total: addons.totalElements,
        data: addons.content,
      }}
      emptyMessage="No addons found"
      rowClickId="name.en"
      paginationEnabled
    />
  );
};
