import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import { CreateCategoryDialog } from "../../_components/create-category-dialog";
import PageTitle from "../../_components/page-title";
import { columns } from "./columns";

export default async function Page() {
  return (
    <div>
      <PageTitle title="Categories" description="Categories of vehicles" action={<CreateCategoryDialog />} />
      <div className="container px-24 py-6">
        <Suspense fallback={<TableSkeleton showPagination={false} />}>
          <CategoriesTable />
        </Suspense>
      </div>
    </div>
  );
}
const CategoriesTable = async () => {
  const response = await api.fleet.categoryContract.vehicleClassList({
    query: {
      pageNumber: 0,
      pageSize: 100,
    },
  });

  if (response.status !== 200) {
    throw new Error("Failed to fetch categories");
  }

  const categories = response.body;

  return (
    <DataTable
      columns={columns}
      data={{
        total: categories.totalElements,
        data: categories.content,
      }}
      emptyMessage="No categories found"
      searchPlaceholder="Search categories..."
      rowClickId="name.en"
      paginationEnabled={false}
    />
  );
};
