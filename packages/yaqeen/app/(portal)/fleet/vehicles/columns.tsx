"use client";

import { type Vehicle } from "@/api/contracts/fleet/vehicles";
import { VehicleCell } from "@/app/(portal)/rental/vehicles/_components/table-cells/vehicle-cell";
import { Badge } from "@/components/ui/badge";
import { cn, convertPlateToArabic, toNormal } from "@/lib/utils";
import { type ColumnDef } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";

type MessageKey = "plateNo" | "group" | "vehicle" | "category" | "statusAndSubStatus" | "location" | "serviceType";

const Message = ({ messageKey }: { messageKey: MessageKey }) => {
  const t = useTranslations("fleetManagement");
  return <div className="text-start">{t(`all-vehicles.column.${messageKey}`)}</div>;
};

const LocalizedObject = ({
  className,
  localizedObject,
}: {
  className?: string;
  localizedObject?: { en: string; ar: string } | null;
}) => {
  const locale = useLocale();
  if (!localizedObject) return <div className="text-start">Unknown</div>;
  return <div className={cn("text-start", className)}>{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

export const columns: ColumnDef<Vehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => (
      <LocalizedObject
        className="text-blue-600 hover:text-blue-700"
        localizedObject={{ en: row.original.plateNo, ar: convertPlateToArabic(row.original.plateNo) }}
      />
    ),
  },
  {
    id: "group",
    header: () => <Message messageKey="group" />,
    accessorFn: (row) => row.model.vehicleGroup ?? "N/A",
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model.make.name}
        model={row.original.model.name}
        version={row.original.model.version}
      />
    ),
  },
  {
    id: "category",
    header: () => <Message messageKey="category" />,
    accessorFn: (row) => row.model.vehicleClass.name,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original.model.vehicleClass.name} />,
  },
  {
    id: "serviceType",
    header: () => <Message messageKey="serviceType" />,
    accessorFn: (row) => row.lineOfBusiness.serviceType,
    cell: ({ row }) => {
      const type = row.original.lineOfBusiness.serviceType.toLowerCase().replace(/^\w/, (c) => c.toUpperCase());
      return (
        <Badge className="pointer-events-none rounded-full bg-lumi-100 px-3 py-1 font-medium text-slate-900">
          {type}
        </Badge>
      );
    },
  },
  {
    id: "statusSubStatus",
    header: () => <Message messageKey="statusAndSubStatus" />,
    cell: ({ row }) => {
      const status = toNormal(row.original.vehicleStatus.status);
      const subStatus = toNormal(row.original.vehicleStatus.statusReason ?? "");

      return (
        <div className="flex gap-2">
          <Badge className="pointer-events-none rounded-full bg-blue-100 px-3 py-1 font-medium text-slate-900">
            {status}
          </Badge>
          {subStatus && <Badge variant="outline">{subStatus}</Badge>}
        </div>
      );
    },
  },
  {
    id: "location",
    header: () => <Message messageKey="location" />,
    accessorFn: (row) => row.location.name,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original.location.name} />,
  },
];
