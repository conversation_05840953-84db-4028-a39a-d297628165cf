"use client";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { shimmer, toBase64, toNormal } from "@/lib/utils";
import { GasPump, Gauge, MagnifyingGlass } from "@phosphor-icons/react";
import { Car } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ProgressBarLink } from "@/components/progress-bar";

interface VehicleDetailsCardProps {
  vehicleDetails: VehicleDetail | undefined;
  isLoading: boolean;
}

export function VehicleDetailsCard({ vehicleDetails, isLoading }: VehicleDetailsCardProps) {
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";
  if (isLoading) {
    return (
      <Card className="mt-4">
        <CardContent className="flex h-[100px] items-center justify-center p-4">
          <p className="text-sm text-muted-foreground">{t("loadingVehicleDetails")}</p>
        </CardContent>
      </Card>
    );
  }

  if (!vehicleDetails) {
    return (
      <Card className="mt-4">
        <CardContent className="flex h-[100px] items-center justify-center p-4">
          <p className="text-sm text-muted-foreground">{t("noVehicleDetailsAvailable")}</p>
        </CardContent>
      </Card>
    );
  }

  let plateNumber = "";
  let plateLetters = "";
  if (vehicleDetails.plateNo) {
    const parts = vehicleDetails.plateNo.split(" ");
    if (parts.length > 1) {
      plateNumber = parts[0] ?? "";
      plateLetters = parts[1] ?? "";
    } else {
      plateNumber = vehicleDetails.plateNo ?? "";
    }
  }

  const vehicleClassification = vehicleDetails.model?.vehicleClass?.name?.[locale] || "N/A";
  const vehicleVersion = vehicleDetails.model?.version || "";
  const vehicleOperation = vehicleDetails.vehicleOperationDTO;
  const lastUpdated = new Date();
  const lastSeenRelative = getRelativeTimeString(lastUpdated);
  const purchaseDate = vehicleDetails.vehicleFinancialDTO?.purchaseDate;

  return (
    <Card className="mt-4">
      <CardHeader className="border-b-2 p-4 pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-semibold">Vehicle Info</CardTitle>
          <p className="text-xs text-slate-500">
            Purchased on: {purchaseDate ? new Date(purchaseDate).toLocaleDateString() : "N/A"}
          </p>
        </div>
      </CardHeader>
      <CardContent className="px-0 py-4">
        <div className="flex space-x-4 px-4 rtl:space-x-reverse">
          <div className="relative">
            <div className="overflow-hidden rounded-lg">
              {vehicleDetails.model?.primaryImageUrl ? (
                <Image
                  src={vehicleDetails.model.primaryImageUrl}
                  alt={`${vehicleDetails.model.make.name[locale]} ${vehicleDetails.model.name[locale]}`}
                  className="h-[64px] w-[128px] object-cover"
                  width={128}
                  height={64}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(128, 64))}`}
                  priority
                />
              ) : (
                <div className="flex h-[64px] w-[128px] items-center justify-center bg-gray-100">
                  <Car className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>

            {vehicleDetails.plateNo && (
              <div className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 overflow-hidden rounded-lg border border-b border-slate-800 bg-white font-medium">
                <div className="grid grid-cols-2">
                  <div className="flex min-h-3 items-center justify-center border-b border-r border-slate-800 p-1">
                    <span className="text-xs leading-tight">{plateNumber}</span>
                  </div>
                  <div className="flex min-h-3 items-center justify-center border-b border-slate-800 p-1">
                    <span className="text-xs leading-tight">{plateLetters}</span>
                  </div>

                  <div className="flex min-h-3 items-center justify-center border-r border-slate-800 p-1" dir="rtl">
                    <span className="text-sm font-bold leading-tight">
                      {plateNumber?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[Number(d)] ?? d)}
                    </span>
                  </div>
                  <div className="flex min-h-3 items-center justify-center whitespace-nowrap p-1" dir="rtl">
                    <span className="text-sm font-bold leading-tight">
                      {vehicleDetails.plateNoAr?.split(/\d/).filter(Boolean).join("")}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-grow items-start justify-between px-4 pt-1">
            <div>
              <div className="mb-1 flex items-center gap-2">
                <Badge className="border-slate-300 bg-white font-medium text-slate-700" variant="outline">
                  Group: {vehicleDetails.model?.vehicleGroup || "N/A"}
                </Badge>
                {vehicleClassification !== "N/A" && (
                  <Badge className="border-slate-300 bg-white font-medium text-slate-700" variant="outline">
                    {vehicleClassification}
                  </Badge>
                )}
              </div>
              <div className="mb-1.5 flex items-center gap-2">
                <h2 className="text-lg font-semibold text-slate-800">
                  {vehicleDetails.model.make.name[locale]} {vehicleDetails.model.name[locale]}
                  {vehicleVersion && ` - ${vehicleVersion}`}
                </h2>
                <Badge className="pointer-events-none bg-lime-200 text-lime-800">
                  {toNormal(vehicleDetails.vehicleOperationDTO?.serviceType || "N/A")}
                </Badge>
              </div>
              <div className="flex items-center gap-3 text-xs text-slate-600">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1.5">
                        <Gauge className="h-4 w-4" />
                        <span>{vehicleOperation?.odometerReading?.toLocaleString() ?? "N/A"} KM</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("mileage")}</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1.5">
                        <GasPump className="h-4 w-4" />
                        <span>
                          {vehicleOperation?.fuelLevel?.fuelLevel ? `${vehicleOperation.fuelLevel.fuelLevel}/4` : "N/A"}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("fuelLevel")}</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1.5">
                        <MagnifyingGlass className="h-4 w-4" />
                        <span>{lastSeenRelative}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Last updated</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <Button variant="outline" size="sm">
              <ProgressBarLink href={`/fleet/vehicles/${vehicleDetails.plateNo}/model-specs`}>
                View model specs
              </ProgressBarLink>
            </Button>
          </div>
        </div>

        <Separator className="my-4 mt-8" />

        <div className="px-4">
          <h3 className="mb-3 text-lg font-semibold text-slate-800">Allocation</h3>
          <div className="flex flex-col">
            <div className="grid grid-cols-3 gap-x-4 gap-y-3 text-sm">
              <div className="space-y-2">
                <p className="text-xs text-slate-500">Service Type</p>
                <p className="font-medium text-slate-700">{toNormal(vehicleOperation?.serviceType || "N/A")}</p>
              </div>
              <div className="space-y-2">
                <p className="text-xs text-slate-500">Status</p>
                <p className="font-medium text-slate-700">
                  {toNormal(vehicleOperation?.vehicleStatus?.status || "N/A")}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-xs text-slate-500">Sub Status</p>
                <p className="font-medium text-slate-700">{toNormal(vehicleOperation?.subServiceType || "N/A")}</p>
              </div>
            </div>
            <Separator className="my-4" />
            <div className="grid grid-cols-3 gap-x-4 gap-y-3 text-sm">
              <div className="space-y-2">
                <p className="text-xs text-slate-500">Branch Owner</p>
                <p className="font-medium text-slate-700">{vehicleOperation?.ownerBranch?.name?.[locale] || "N/A"}</p>
              </div>
              <div className="space-y-2">
                <p className="text-xs text-slate-500">Location</p>
                <p className="font-medium text-slate-700">
                  {vehicleOperation?.currentLocation?.name?.[locale] || "N/A"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to get relative time string
function getRelativeTimeString(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? "" : "s"} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? "" : "s"} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
}
