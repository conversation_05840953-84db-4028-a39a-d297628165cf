"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  MapPin,
  Car,
  FileText,
  EnvelopeSimple,
  Phone,
  IdentificationCard,
} from "@phosphor-icons/react/dist/ssr";
import { CheckCircle } from "@phosphor-icons/react";
import { format } from "date-fns";
import { type NrmVehicleLogItem } from "@/api/contracts/rental/nrm-contract";

interface NrmDetailsCardProps {
  nrmData: NrmVehicleLogItem;
}

export function NrmDetailsCard({ nrmData }: NrmDetailsCardProps) {
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return format(date, "EEEE, dd/MM/yyyy");
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return format(date, "HH:mm");
  };

  const checkoutDate = nrmData.checkoutData?.date;
  const checkoutBranchId = nrmData.checkoutData?.branchId;
  const checkinBranchId = nrmData.checkinData?.branchId;

  return (
    <Card className="mt-4">
      <CardHeader className="border-b p-4">
        <CardTitle className="text-xl font-semibold">NRM details</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-1 gap-0 md:grid-cols-2">
          <div className="border-b md:border-r">
            <div className="border-b p-4">
              <h3 className="mb-3 text-lg font-semibold">NRM number</h3>
              <div className="flex items-center gap-2">
                <span className="text-base">{nrmData.id}</span>
                <Badge className="bg-lime-200 text-lime-800 hover:bg-lime-300">{nrmData.status}</Badge>
              </div>
            </div>

            <div className="border-b p-4">
              <h3 className="mb-3 text-lg font-semibold">Pickup</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-2">
                  <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0" />
                  <span>Branch ID: {checkoutBranchId}</span>
                </div>
                {checkoutDate && (
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      <span>{formatDate(checkoutDate)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      <span>{formatTime(checkoutDate)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="border-b p-4">
              <div className="flex items-center gap-2">
                <Car className="h-5 w-5" />
                <span>Reason: {nrmData.reason}</span>
              </div>
            </div>

            <div className="border-b p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                <span>TAMM Authorization: {nrmData.status === "OPEN" ? "Open" : "Closed"}</span>
                {nrmData.status === "OPEN" && <CheckCircle className="h-5 w-5 text-green-600" />}
              </div>
            </div>

            <div className="p-4">
              <h3 className="mb-3 text-lg font-semibold">Drop-off</h3>
              <div className="flex items-start gap-2">
                <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0" />
                <span>Branch ID: {checkinBranchId || "Not checked in yet"}</span>
              </div>
            </div>
          </div>

          <div className="border-b bg-slate-50">
            <div className="p-4">
              <h3 className="mb-3 text-lg font-semibold">Driver details</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <span>Driver ID: {nrmData.driverId}</span>
                </div>
                <div className="flex items-center gap-2">
                  <EnvelopeSimple className="h-5 w-5" />
                  <span>Email not available in API</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  <span>Phone not available in API</span>
                </div>
                <div className="flex items-center gap-2">
                  <IdentificationCard className="h-5 w-5" />
                  <span>License expiry not available in API</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
