"use client";

import { type VehicleDocument } from "@/api/contracts/fleet/vehicles";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { toNormal } from "@/lib/utils";
import { Plus } from "@phosphor-icons/react";
import { useState } from "react";
import { AddDocumentDialog } from "./add-document-dialog";
import { getDocumentColumns } from "./document-columns";
import { type DocumentsCardProps, type DocumentTypeWithMeta } from "./types";

export function DocumentsCard({ documents, documentTypeList, plateNo, showAll }: DocumentsCardProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<VehicleDocument[] | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [addDocumentDialogOpen, setAddDocumentDialogOpen] = useState(false);

  const handleViewDocuments = (docs: VehicleDocument[]) => {
    setSelectedDocuments(docs);
    setDialogOpen(true);
  };

  const handleAddDocument = () => {
    setAddDocumentDialogOpen(true);
  };

  const columns = getDocumentColumns(handleViewDocuments);

  const documentsByType = documents.reduce(
    (acc, doc) => {
      const typeCode = doc.type.code;
      if (!acc[typeCode]) {
        acc[typeCode] = {
          type: doc.type,
          documents: [],
          count: 0,
          issuingDate: doc.issuingDate,
          expiryDate: doc.expiryDate,
        };
      }
      acc[typeCode].documents.push(doc);
      acc[typeCode].count++;
      return acc;
    },
    {} as Record<string, DocumentTypeWithMeta>
  );

  const documentTypes = Object.values(documentsByType);
  const displayDocumentTypes = showAll ? documentTypes : documentTypes.slice(0, 2);
  const hasMoreDocumentTypes = documentTypes.length > 2;
  const hasDocumentTypes = documentTypes.length > 0;

  return (
    <>
      <Card className="mt-4">
        <CardHeader className="flex flex-row items-center justify-between border-b-2 p-4">
          <CardTitle className="text-xl font-semibold">Documents</CardTitle>
          {hasDocumentTypes && !showAll && (
            <Button className="!mt-0" variant="outline" size="sm">
              <ProgressBarLink href={`/fleet/vehicles/${plateNo}/documents`}>View all documents</ProgressBarLink>
            </Button>
          )}
          {showAll && (
            <Button className="!mt-0" variant="outline" size="sm" onClick={handleAddDocument}>
              <Plus className="mr-2 h-5 w-5" />
              Add new document
            </Button>
          )}
        </CardHeader>
        <CardContent className="p-0">
          {hasDocumentTypes ? (
            <>
              <DataTable
                columns={columns}
                data={{ total: displayDocumentTypes.length, data: displayDocumentTypes }}
                paginationEnabled={false}
                emptyMessage="No documents available"
              />
              {hasMoreDocumentTypes && !showAll && (
                <div className="p-4 text-sm text-muted-foreground">
                  <ProgressBarLink
                    href={`/fleet/vehicles/${plateNo}/documents`}
                    className="text-primary hover:underline"
                  >
                    View all documents
                  </ProgressBarLink>
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center justify-center bg-gray-50 py-16">
              <p className="text-xl text-muted-foreground">There are no new documents added</p>
              <Button className="mt-8" variant="outline" size="lg" onClick={handleAddDocument}>
                <Plus className="mr-2 h-5 w-5" />
                Add new document
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Documents Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="p-0 sm:max-w-[573px]">
          <div className="p-4">
            <DialogTitle className="text-lg font-bold text-slate-900">
              {selectedDocuments?.[0]?.type.code ? toNormal(selectedDocuments[0].type.code) : "Document"}
            </DialogTitle>
            <div className="py-4">
              <p className="text-slate-600">
                There are {selectedDocuments?.length} documents listed with this document
              </p>
            </div>
          </div>
          <div className="flex flex-col">
            <div className="border-t py-4">
              <h3 className="px-4 font-bold text-slate-900">Documents attached</h3>
              {selectedDocuments?.map((doc, index) => (
                <div key={index} className="border-b">
                  <div className="flex items-center justify-between p-4 ">
                    <div>{`${doc.type.name || toNormal(doc.type.code)} ${index + 1}`}</div>
                    <Button
                      className="px-4 py-2"
                      variant="outline"
                      onClick={() => doc.url && window.open(doc.url, "_blank")}
                    >
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-end px-4 pb-4">
              <Button variant="default" className="bg-lime-500 hover:bg-lime-600" onClick={() => setDialogOpen(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {addDocumentDialogOpen && (
        <AddDocumentDialog
          open={addDocumentDialogOpen}
          onOpenChange={setAddDocumentDialogOpen}
          documentTypeList={documentTypeList}
          plateNo={plateNo}
        />
      )}
    </>
  );
}
