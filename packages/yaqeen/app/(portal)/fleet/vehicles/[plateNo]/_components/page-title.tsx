"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import {
  Breadcrumb,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
  BreadcrumbList,
  BreadcrumbItem,
} from "@/components/ui/breadcrumb";
import { useTranslations } from "next-intl";
import { getVehicleTabs } from "../constants";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

export default function PageTitle({
  title,
  breadcrumb,
  subtitle,
}: {
  title: string;
  breadcrumb: string;
  subtitle: string;
}) {
  const tNav = useTranslations("nav");
  const pathname = usePathname();
  const t = useTranslations("fleetManagement");
  const vehicleTabs = getVehicleTabs(title);

  return (
    <section className="flex w-full flex-col self-stretch border-b border-slate-200 bg-slate-50">
      <div className="px-6">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/">{tNav("home")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/fleet/vehicles">{tNav("vehicles")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{breadcrumb}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex w-full items-start py-6 text-slate-900">
          <div className="flex w-full flex-col justify-center">
            <h2 className="mb-2 text-3xl font-medium tracking-tight">{title}</h2>
            <p className="text-slate-500">{subtitle}</p>
          </div>
        </div>

        <div className="box-border flex flex-wrap bg-slate-50">
          {vehicleTabs.map((tab, i) => (
            <ProgressBarLink
              key={i}
              href={tab.href}
              className={cn(
                "mx-3 box-border cursor-pointer gap-2 py-3 text-sm first:ml-0 last:mr-0",
                "border-b-2 border-transparent",
                decodeURIComponent(pathname).includes(tab.href)
                  ? "border-b-slate-900 font-bold text-slate-900"
                  : "text-slate-700 hover:border-slate-400 hover:text-slate-900",
                "box-border transition duration-300"
              )}
            >
              {t(`all-vehicles.tabs.${tab.labelKey}`)}
            </ProgressBarLink>
          ))}
        </div>
      </div>
    </section>
  );
}
