"use client";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toNormal } from "@/lib/utils";
import { Calendar, Car, Clock, MapPin } from "@phosphor-icons/react/dist/ssr";
import { useLocale, useTranslations } from "next-intl";

type OperationData = VehicleDetail["vehicleOperationDTO"];

export function NeedsPrepCard({ operationData }: { operationData: OperationData }) {
  const t = useTranslations("fleetManagement");
  const locale = useLocale() as "en" | "ar";
  const location = operationData?.currentLocation.name?.[locale] ?? "Unknown";
  const waitingTime = operationData?.vehicleStatus.waitingTime;
  const date = waitingTime
    ? new Date(waitingTime)
        .toLocaleDateString("en-US", {
          weekday: "long",
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })
        .replace(/(\d+)\/(\d+)\/(\d+)/, "$2/$1/$3")
    : "";
  const time = waitingTime
    ? new Date(waitingTime).toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      })
    : "";
  const reason = toNormal(operationData?.vehicleStatus?.statusReason?.split("_").join("/") ?? "Unknown");

  return (
    <Card className="mt-4">
      <CardHeader className="border-b p-4 pb-2">
        <CardTitle className="text-xl font-semibold">Needs Prep details</CardTitle>
      </CardHeader>
      <CardContent className="px-0 py-4">
        <div className="px-4">
          <h3 className="mb-3 text-lg font-semibold text-slate-800">Location</h3>
          <div className="flex items-center gap-2 text-slate-700">
            <MapPin className="h-5 w-5" />
            <span>{location}</span>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="px-4">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2 text-slate-700">
              <Calendar className="h-5 w-5" />
              <span>{date}</span>
            </div>
            <div className="flex items-center gap-2 text-slate-700">
              <Clock className="h-5 w-5" />
              <span>{time}</span>
            </div>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="px-4">
          <div className="flex items-center gap-2 text-slate-700">
            <Car className="h-5 w-5" />
            <span>Reason: {reason}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
