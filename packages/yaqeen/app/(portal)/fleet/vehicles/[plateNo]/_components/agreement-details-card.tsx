import { api } from "@/api";
import type { Branch } from "@/api/contracts/branch-contract";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Calendar,
  Clock,
  Envelope,
  IdentificationBadge,
  MapPin,
  Phone,
  User,
  ClockCountdown,
} from "@phosphor-icons/react/dist/ssr";
import { format, differenceInDays } from "date-fns";
import { toNormal } from "@/lib/utils";
import { getLocale } from "next-intl/server";

interface AgreementDetailsCardProps {
  plateNo: string;
  serviceType: string | null;
}

interface BranchInfo {
  location: string;
  date?: number | null;
}

interface CustomerInfo {
  firstName?: string;
  fullName?: string;
  email?: string;
  mobileNumber?: string;
  fullMobileNumber?: string;
  licenseExpiry?: number | string;
  [key: string]: unknown;
}

export async function AgreementDetailsCard({ plateNo, serviceType }: AgreementDetailsCardProps) {
  const locale = (await getLocale()) as "en" | "ar";
  const [agreementResponse, branchesResponse] = await Promise.all([
    api.booking.getAgreements({
      query: {
        vehiclePlateNos: plateNo,
      },
    }),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch agreement");
  }

  if (branchesResponse.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  if (!agreementResponse.body.data.length)
    return (
      <Card className="mt-4">
        <CardHeader className="flex flex-row items-start justify-between border-b p-4">
          <CardTitle className="text-xl font-semibold">Agreement details</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <h2 className="p-6 text-center text-lg text-muted-foreground">
            No agreement found in our records for this vehicle, please check CarPro
          </h2>
        </CardContent>
      </Card>
    );

  const agreement = agreementResponse.body.data[0]!; // Using non-null assertion
  const branchList: Branch[] = branchesResponse.body.data;
  const customer = (agreement?.driver || {}) as CustomerInfo;
  const agreementNumbers = agreement?.agreementNo ? [agreement.agreementNo] : [];
  const agreementStatus = agreement?.status || "Open";
  const bookedOn = agreement?.pickupDateTime;
  const isLeased = serviceType === "Leased";

  const pickupBranchId = agreement?.pickupBranchId;
  const dropOffBranchId = agreement?.dropOffBranchId;

  const pickupBranch = pickupBranchId ? branchList.find((branch) => branch.id === pickupBranchId) : undefined;
  const dropoffBranch = dropOffBranchId ? branchList.find((branch) => branch.id === dropOffBranchId) : undefined;

  const pickup: BranchInfo = {
    location: pickupBranch?.name[locale] || "-",
    date: agreement?.pickupDateTime,
  };

  const dropoff: BranchInfo = {
    location: dropoffBranch?.name[locale] || "-",
    date: agreement?.dropOffDateTime,
  };

  const formatDate = (date: string | number | undefined | null) => {
    if (!date) return "-";

    const timestamp = Number(date) * 1000;
    return format(new Date(timestamp), "EEEE, dd/MM/yyyy");
  };

  const formatDateShort = (date: string | number | undefined | null) => {
    if (!date) return "-";

    const timestamp = Number(date) * 1000;
    return format(new Date(timestamp), "dd/MM/yyyy");
  };

  const formatTime = (date: string | number | undefined | null) => {
    if (!date) return "-";

    const timestamp = Number(date) * 1000;
    return format(new Date(timestamp), "HH:mm");
  };

  const calculateDaysRemaining = () => {
    if (!dropoff.date) return null;
    const endDate = new Date(Number(dropoff.date) * 1000);
    const today = new Date();
    return differenceInDays(endDate, today);
  };

  const daysRemaining = calculateDaysRemaining();

  return (
    <Card className="mt-4">
      <CardHeader className="flex flex-row items-start justify-between border-b p-4">
        <CardTitle className="text-xl font-semibold">Agreement details</CardTitle>
        {bookedOn && (
          <div className="text-sm font-normal text-slate-500">
            Booked on: {isLeased ? formatDateShort(bookedOn) : formatDate(bookedOn)}{" "}
            {!isLeased && `- ${formatTime(bookedOn)}`}
          </div>
        )}
      </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-2">
          <div className="border-b md:border-r">
            <div className="border-b p-4">
              <h3 className="mb-3 text-lg font-semibold">Agreement number</h3>
              {agreementNumbers && agreementNumbers.length > 0 ? (
                agreementNumbers.map((num, idx) => (
                  <div key={idx} className="mb-1 flex items-center gap-2">
                    <span className="text-base">{num}</span>
                    <Badge className="pointer-events-none bg-lime-200 text-lime-800">{toNormal(agreementStatus)}</Badge>
                  </div>
                ))
              ) : (
                <span className="text-base">-</span>
              )}
            </div>

            {isLeased ? (
              <>
                <div className="border-b p-4">
                  <h3 className="mb-3 text-lg font-semibold">Agreement start date</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        <span>{formatDateShort(pickup.date)}</span>
                      </div>
                      {pickup.date && (
                        <div className="flex items-center gap-2">
                          <Clock className="h-5 w-5" />
                          <span>{formatTime(pickup.date)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="mb-3 text-lg font-semibold">Agreement end date</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      <span>{dropoff.date ? formatDateShort(dropoff.date) : "Today"}</span>
                    </div>

                    {daysRemaining !== null && (
                      <div className="mt-4 flex items-center gap-2">
                        <ClockCountdown className="h-5 w-5" />
                        <span>{daysRemaining} days remaining</span>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="border-b p-4">
                  <h3 className="mb-3 text-lg font-semibold">Pickup</h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-2">
                      <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0" />
                      <span>{pickup.location}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        <span>{formatDate(pickup.date)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        <span>{formatTime(pickup.date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="mb-3 text-lg font-semibold">Expected Drop-off</h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-2">
                      <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0" />
                      <span>{dropoff.location}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        <span>{formatDate(dropoff.date)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        <span>{formatTime(dropoff.date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
          <div className="border-b bg-slate-50">
            <div className="p-4">
              <h3 className="mb-3 text-lg font-semibold">Customer details</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  <span>{customer.firstName || customer.fullName || "-"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Envelope className="h-5 w-5" />
                  <span>{customer.email || "-"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  <span>{customer.mobileNumber || customer.fullMobileNumber || "-"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <IdentificationBadge className="h-5 w-5" />
                  <span>License expiry: {customer.licenseExpiry ? formatDate(customer.licenseExpiry) : "-"}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default AgreementDetailsCard;
