import { type DocumentTypeWithMeta } from "./types";
import { Button } from "@/components/ui/button";
import { type ColumnDef } from "@tanstack/react-table";
import { Eye } from "@phosphor-icons/react";
import { toNormal } from "@/lib/utils";
import { format } from "date-fns";

const formatDate = (dateValue: string | null | number[] | undefined) => {
  if (!dateValue) return "-";
  try {
    if (Array.isArray(dateValue)) {
      const [year, month, day] = dateValue;
      if (year !== undefined && month !== undefined && day !== undefined) {
        return format(new Date(year, month - 1, day), "dd/MM/yyyy");
      }
      return "-";
    }

    return format(new Date(dateValue), "dd/MM/yyyy");
  } catch (error) {
    console.error(error);
    return "-";
  }
};

export const getDocumentColumns = (
  handleViewDocuments: (docs: DocumentTypeWithMeta["documents"]) => void
): ColumnDef<DocumentTypeWithMeta>[] => [
  {
    accessorKey: "type.code",
    header: "Document Type",
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{toNormal(docType.type.code)}</div>;
    },
  },
  {
    accessorKey: "issuingDate",
    header: "Upload Date",
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{formatDate(docType.issuingDate)}</div>;
    },
  },
  {
    accessorKey: "expiryDate",
    header: "Expiry Date",
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{formatDate(docType.expiryDate)}</div>;
    },
  },
  {
    accessorKey: "count",
    header: "No. of Documents",
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{docType.count}</div>;
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const docType = row.original;
      return (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            if (docType.documents.length === 1) {
              const doc = docType.documents[0];
              if (doc?.url) {
                window.open(doc.url, "_blank");
              }
            } else {
              handleViewDocuments(docType.documents);
            }
          }}
        >
          <Eye className="h-4 w-4" />
        </Button>
      );
    },
  },
];
