import {
  type CreateVehicleDocumentBody,
  type DocumentType,
  type VehicleDocument,
} from "@/api/contracts/fleet/vehicles";
import { FileUpload } from "@/components/file-upload";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/lib/hooks/use-toast";
import { toNormal } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface AddDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentTypeList?: DocumentType[];
  plateNo: string;
  onSuccess?: () => void;
}

const uploadVehicleDocument = async (formData: FormData, plateNo: string) => {
  try {
    formData.append("plateNo", plateNo);

    const response = await fetch("/next-api/upload/vehicle-document", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to upload document");
    }

    return data;
  } catch (error) {
    console.error("Error in uploadVehicleDocument:", error);
    throw error;
  }
};

export function AddDocumentDialog({
  open,
  onOpenChange,
  documentTypeList,
  plateNo,
  onSuccess,
}: AddDocumentDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [markAsInternal, setMarkAsInternal] = useState(false);
  const [documentUrls, setDocumentUrls] = useState<string[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileUploadSuccess = (url: string) => {
    setDocumentUrls([...documentUrls, url]);
    setUploadError(null);
    toast({
      title: "Upload successful",
      description: "Document has been uploaded successfully",
      variant: "default",
    });
  };

  const handleFileUploadError = (error: string) => {
    setUploadError(error);
    toast({
      title: "Upload failed",
      description: error,
      variant: "destructive",
    });
  };

  const formatDateToISO = (date: Date | null): string => {
    if (!date) return new Date().toISOString();
    return date.toISOString();
  };

  const createDocument = async (documentData: CreateVehicleDocumentBody): Promise<VehicleDocument> => {
    try {
      const response = await fetch("/next-api/fleet/vehicles/documents", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(documentData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create document");
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating document:", error);
      throw error;
    }
  };

  const handleAddDocument = async () => {
    if (!selectedDocumentType || documentUrls.length === 0) {
      toast({
        title: "Missing information",
        description: "Please select a document type and upload at least one file",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    const totalDocuments = documentUrls.length;

    try {
      const documentPromises = documentUrls.map((url, index) => {
        const documentData: CreateVehicleDocumentBody = {
          plateNo,
          url,
          typeId: parseInt(selectedDocumentType),
          pageNo: index + 1,
          issuingDate: formatDateToISO(startDate),
          expiryDate: formatDateToISO(endDate),
          internal: markAsInternal,
        };

        return createDocument(documentData);
      });

      await Promise.all(documentPromises);

      onOpenChange(false);

      toast({
        title: "Documents created",
        description: `Successfully created ${totalDocuments} document${totalDocuments > 1 ? "s" : ""}`,
        variant: "default",
      });

      onSuccess?.();
      router.refresh();
    } catch (error) {
      toast({
        title: "Failed to create document",
        description: error instanceof Error ? error.message : "An error occurred while creating the document",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getFileName = (url: string) => {
    return url.split("/").pop() || "file_name_here.PDF";
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!isSubmitting) {
          onOpenChange(newOpen);
        }
      }}
    >
      <DialogContent className="sm:min-w-md p-0">
        <div className="p-4">
          <DialogTitle>Add new document</DialogTitle>
          <div className="my-4 space-y-4">
            <div>
              <p className="mb-2 text-sm font-medium">Document Type</p>
              <Select onValueChange={(value) => setSelectedDocumentType(value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypeList?.map((type) => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.name ?? toNormal(type.code)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <p className="mb-2 text-sm font-medium">Insurance start date</p>
                <DatePicker date={startDate ?? new Date()} setDate={(date) => setStartDate(date)} />
              </div>
              <div className="flex-1">
                <p className="mb-2 text-sm font-medium">Insurance end date</p>
                <DatePicker date={endDate ?? new Date()} setDate={(date) => setEndDate(date)} />
              </div>
            </div>

            {/* Main document upload section */}
            <div className="space-y-4 rounded-md">
              {documentUrls.map((url, index) => (
                <div key={index} className="flex items-center justify-between rounded-md bg-slate-50 p-4">
                  <p className="text-sm">{getFileName(url)}</p>
                </div>
              ))}

              <div className="rounded-md bg-slate-50 p-4">
                <FileUpload
                  onSuccess={handleFileUploadSuccess}
                  onError={handleFileUploadError}
                  buttonText="Upload document"
                  uploadFunction={(formData) => uploadVehicleDocument(formData, plateNo)}
                  hideFileName
                />
                {uploadError && <p className="mt-2 text-sm text-red-500">{uploadError}</p>}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2 px-4">
          <Checkbox
            id="internal-doc"
            checked={markAsInternal}
            onCheckedChange={(checked) => setMarkAsInternal(checked === true)}
          />
          <label
            htmlFor="internal-doc"
            className="font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Mark as an internal document
          </label>
        </div>

        <div className="flex justify-end gap-x-2 p-4">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            variant="default"
            className="bg-lime-500 hover:bg-lime-600"
            disabled={documentUrls.length === 0 || !selectedDocumentType || isSubmitting}
            onClick={handleAddDocument}
          >
            {isSubmitting ? "Adding..." : "Add document"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
