"use client";

import { type MaintenanceType, type MaintenanceLog } from "@/api/contracts/fleet/maintenance/maintenance-logs-contract";
import { Badge } from "@/components/ui/badge";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

export const columns: ColumnDef<MaintenanceLog>[] = [
  {
    accessorKey: "date",
    header: "Date",
    cell: ({ row }) => {
      const date = row.original.date;
      if (!date || date.length < 3) return "-";
      return format(new Date(date[0]!, date[1]! - 1, date[2]), "dd/MM/yyyy");
    },
  },
  {
    accessorKey: "km",
    header: "KM",
    cell: ({ row }) => {
      return row.original.km.toLocaleString();
    },
  },
  {
    accessorKey: "maintenanceLogTypes",
    header: "Service",
    cell: ({ row }) => {
      const maintenanceTypes = row.original.maintenanceLogTypes;

      return (
        <div className="flex flex-wrap gap-1">
          {maintenanceTypes.map((type: MaintenanceType, index: number) => (
            <Badge key={index} variant="secondary">
              {type.serviceType}
            </Badge>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: "remarks",
    header: "Remarks",
    cell: ({ row }) => row.original.remarks,
  },
];
