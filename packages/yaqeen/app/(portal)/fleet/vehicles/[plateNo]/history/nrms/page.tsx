import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import { columns } from "./columns";
import { getFilters } from "./filters";
import { getTranslations, getLocale } from "next-intl/server";
import { convertPlateToEnglish } from "@/lib/utils";

type SearchParams = {
  plateNo: string;
  nrmCheckOutLocationIds: string;
  nrmCheckInLocationIds: string;
  pageNumber: string;
  pageSize: string;
  statusReasonIds: string;
  modelIds: string;
};

async function NRMVehicles({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const _searchParams = await searchParams;
  const plateNo = _searchParams.plateNo || "";
  const nrmCheckOutLocationIds = _searchParams.nrmCheckOutLocationIds || "";
  const nrmCheckInLocationIds = _searchParams.nrmCheckInLocationIds || "";
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const statusReasonIds = _searchParams.statusReasonIds || "";
  const modelIds = _searchParams.modelIds || "";
  const t = await getTranslations("NRM");
  const locale = (await getLocale()) as "en" | "ar";

  const [models, nrmVehicles, preparationReasons, branches] = await Promise.all([
    api.availability.getModelList({
      query: { pageNumber: "0", pageSize: "10000" },
    }),
    api.nrm.getNRMVehicles({
      query: {
        pageNumber,
        pageSize,
        ...(plateNo && { plateNo: locale === "ar" ? convertPlateToEnglish(plateNo) : plateNo }),
        ...(nrmCheckOutLocationIds && { nrmCheckOutLocationIds }),
        ...(nrmCheckInLocationIds && { nrmCheckInLocationIds }),
        ...(statusReasonIds && { statusReasonIds }),
        ...(modelIds && { modelIds }),
      },
    }),
    api.nrm.getNrmReasons({}),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (models.status !== 200) {
    throw new Error("Error fetching models");
  }

  if (nrmVehicles.status !== 200) {
    throw new Error("Error fetching NRM vehicles");
  }

  if (branches.status !== 200) {
    throw new Error("Error fetching branch list");
  }

  if (preparationReasons.status !== 200) {
    throw new Error("Error fetching preparation reasons");
  }

  const filters = getFilters(
    models.body.content,
    preparationReasons.body,
    branches.body.data,
    locale,
    // @ts-expect-error - TODO: fix this
    t
  );

  return (
    <>
      <DataTable
        columns={columns}
        data={{
          total: nrmVehicles.body.totalElements,
          data: nrmVehicles.body.content,
        }}
        paginationEnabled={true}
        filters={filters}
        emptyMessage={t("emptyMessage")}
      />
    </>
  );
}

export default async function NRMVehiclesPage({ searchParams }: { searchParams: Promise<SearchParams> }) {
  return (
    <Suspense fallback={<TableSkeleton filterCount={4} showPagination={true} />}>
      <NRMVehicles searchParams={searchParams} />
    </Suspense>
  );
}
