import { type Branch } from "@/api/contracts/branch-contract";

export const getFilters = (
  branches: Branch[],
  locale: "en" | "ar",
  transations: {
    pickupBranch: string;
    dropOffBranch: string;
    dropOffTime: string;
    pickupTime: string;
    status: string;
  }
) => {
  const branchOptions = branches.map((branch) => ({
    label: branch.name[locale],
    value: String(branch.id),
  }));

  return [
    {
      filterKey: "pickupBranchIds",
      filterName: transations.pickupBranch,
      columnKey: "pickupBranch",
      options: branchOptions,
      isMultiSelect: true,
    },
    {
      filterKey: "dropOffBranchIds",
      filterName: transations.dropOffBranch,
      columnKey: "dropOffBranch",
      options: branchOptions,
      isMultiSelect: true,
    },
    {
      filterKey: "status",
      filterName: transations.status,
      columnKey: "status",
      isMultiSelect: true,
      options: [
        { label: "Ongoing", value: "ONGOING" },
        { label: "Late return", value: "LATE_RETURN" },
        { label: "Suspended", value: "SUSPENDED" },
        { label: "Cancelled", value: "CANCELLED" },
        { label: "Completed", value: "COMPLETED" },
      ],
    },
  ];
};

export const searchFilters = [
  {
    label: "Booking No",
    value: "bookingNo",
  },
  {
    label: "Agreement No",
    value: "agreementNo",
  },
  {
    label: "Driver Name",
    value: "driverName",
  },
];

export const BOOKING_DATE_TIME = "bookingDateTime";
