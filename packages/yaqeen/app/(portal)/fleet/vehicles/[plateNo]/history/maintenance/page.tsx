import { api } from "@/api";
import Loading from "@/app/(portal)/loading";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import { type MaintenanceLog } from "@/api/contracts/fleet/maintenance/maintenance-logs-contract";

type PageProps = {
  params: Promise<{ plateNo: string }>;
};

export default async function MaintenanceLogsHistoryPage(props: PageProps) {
  const { plateNo } = await props.params;
  const decodedPlateNo = decodeURIComponent(plateNo);

  const maintenanceLogsResponse = await api.fleet.maintenanceContract.getMaintenanceLogs({
    query: {
      plateNo: decodedPlateNo,
    },
  });

  if (maintenanceLogsResponse?.status !== 200) {
    throw new Error(`Error fetching maintenance logs: ${maintenanceLogsResponse?.status}`);
  }

  const maintenanceLogs: MaintenanceLog[] = maintenanceLogsResponse.body ?? [];

  return (
    <div className="mt-4">
      <Suspense fallback={<Loading />}>
        <DataTable
          columns={columns}
          data={{
            data: maintenanceLogs,
            total: maintenanceLogs.length,
          }}
          emptyMessage="No maintenance logs found for this vehicle."
        />
      </Suspense>
    </div>
  );
}
