import { api } from "@/api";
import type { Agreement } from "@/api/contracts/booking/schema";
import Loading from "@/app/(portal)/loading";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getLocale, getTranslations } from "next-intl/server";
import { type SearchParams } from "nuqs/server";
import { Suspense } from "react";
import { columns } from "./components/columns";
import { getFilters, searchFilters } from "./filters";

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<{ id: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const { pageSize, bookingNo, pageNumber, driverName, agreementNo, pickUpBranchIds, dropOffBranchIds, status } =
    searchParams;
  const locale = (await getLocale()) as "en" | "ar";
  const tFilters = await getTranslations("Filters");

  const { id } = await props.params;

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const suspenseKey = Object.entries(searchParams)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${Array.isArray(value) ? value.join(",") : (value ?? "")}`)
    .join("-");

  const [agreements] = await Promise.all([
    api.booking.getAgreements({
      query: {
        order: "asc",
        sort: "checkinDate",
        page: pageNumber ? Number(pageNumber) : undefined,
        size: pageSize ? Number(pageSize) : undefined,
        bookingNo: bookingNo ? String(bookingNo) : undefined,
        agreementNo: agreementNo ? String(agreementNo) : undefined,
        driverName: driverName ? String(driverName) : undefined,
        pickupBranchIds: pickUpBranchIds ? String(pickUpBranchIds) : undefined,
        dropOffBranchIds: dropOffBranchIds ? String(dropOffBranchIds) : undefined,
        status: status ? String(status) : undefined,
      },
    }),
  ]);

  const filters = getFilters(branches.body.data, locale, {
    pickupBranch: tFilters("pickupBranch"),
    dropOffBranch: tFilters("dropOffBranch"),
    dropOffTime: tFilters("dropOffTime"),
    pickupTime: tFilters("pickupTime"),
    status: tFilters("status"),
  });

  if (agreements?.status !== 200) {
    throw new Error(`Error: ${agreements.status}`);
  }

  const data: Agreement[] = agreements.body.data ?? [];
  const total = agreements.body.total ?? 0;

  const branchesMap = branches.body.data.reduce(
    (acc, branch) => {
      acc[branch.id] = branch;
      return acc;
    },
    {} as Record<number, (typeof branches.body.data)[0]>
  );

  return (
    <Suspense key={suspenseKey} fallback={<Loading />}>
      <DataTable
        searchPlaceholder="Search"
        columns={columns}
        filters={filters}
        searchFilters={searchFilters}
        rowClickId={"bookingId"}
        extraParams={["agreementNo"]}
        baseRedirectPath={`/rental/branches/${id}/bookings`}
        data={{
          data: data.map((agreement) => ({
            ...agreement,
            pickupBranch: branchesMap[agreement.pickupBranchId]!,
            dropOffBranch: branchesMap[agreement.dropOffBranchId]!,
          })),
          total: total,
        }}
        emptyMessage="There are no ongoing bookings."
      />
    </Suspense>
  );
}
