import { type Route } from "next";

export interface HistoryTabItem {
  labelKey: string;
  href: Route<string>;
}

export const getHistoryTabs = (plateNo: string): HistoryTabItem[] => {
  const historyTabs: HistoryTabItem[] = [
    {
      labelKey: "Agreements",
      href: `/fleet/vehicles/${plateNo}/history/agreements` as Route<string>,
    },
    {
      labelKey: "NRMs",
      href: `/fleet/vehicles/${plateNo}/history/nrms` as Route<string>,
    },
    {
      labelKey: "Maintenance Logs",
      href: `/fleet/vehicles/${plateNo}/history/maintenance` as Route<string>,
    },
  ] as const;

  return historyTabs;
};
