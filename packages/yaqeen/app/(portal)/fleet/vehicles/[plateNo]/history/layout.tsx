import { getHistoryTabs } from "./navigation-items";
import TabNavigation from "./_components/tab-navigation";

export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ plateNo: string }>;
}) {
  const { plateNo } = await params;
  const historyTabs = getHistoryTabs(plateNo);

  return (
    <div className="flex flex-col">
      <TabNavigation tabs={historyTabs} />
      <div className="mt-4 px-6">{children}</div>
    </div>
  );
}
