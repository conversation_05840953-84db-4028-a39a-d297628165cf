"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Car, Pencil } from "lucide-react";
import { Separator } from "@/components/ui/separator";
interface AllocationSectionProps {
  data: {
    serviceType: string;
    status: string;
    branchOwner: string;
    location: string;
  };
}

export function AllocationSection({ data }: AllocationSectionProps) {
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Allocation</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Car className="h-4 w-4" />
            <span>Open NRM</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Pencil className="h-4 w-4" />
            <span>Edit</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <>
          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Service Type</p>
              <p className="font-medium">{data.serviceType}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Status</p>
              <p className="font-medium">{data.status}</p>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Branch Owner</p>
              <p className="font-medium">{data.branchOwner}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Location</p>
              <p className="font-medium">{data.location}</p>
            </div>
          </div>
        </>
      </CardContent>
    </Card>
  );
}
