"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Eye } from "lucide-react";
import { Separator } from "@/components/ui/separator";
interface VersionSpecsSectionProps {
  data: {
    general: {
      numberOfSeats: string;
      numberOfDoors: string;
      luggageSpaceBig: string;
      luggageSpaceMedium: string;
      luggageSpaceSmall: string;
    };
    performanceEngine: {
      transmission: string;
      transmissionType: string;
      engineSize: string;
      horsepower: string;
      fuelType: string;
      fuelCapacity: string;
    };
  };
}

export function VersionSpecsSection({ data }: VersionSpecsSectionProps) {
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Version Specs</CardTitle>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Eye className="h-4 w-4" />
          <span>View model specs</span>
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <>
          <div className="p-4">
            <h3 className="mb-4 text-base font-medium">General</h3>
            <div className="grid grid-cols-2">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Number of seats</p>
                <p className="font-medium">{data.general.numberOfSeats}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Number of Doors</p>
                <p className="font-medium">{data.general.numberOfDoors}</p>
              </div>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-3 gap-x-4 gap-y-6 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Luggage Space (Big Bag)</p>
              <p className="font-medium">{data.general.luggageSpaceBig}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Luggage Space (Medium Bag)</p>
              <p className="font-medium">{data.general.luggageSpaceMedium}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Luggage Space (Small Bag)</p>
              <p className="font-medium">{data.general.luggageSpaceSmall}</p>
            </div>
          </div>
        </>

        <Separator />

        <div>
          <h3 className="pl-4 pt-4 text-base font-medium">Performance & Engine</h3>
          <>
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Transmission</p>
                <p className="font-medium">{data.performanceEngine.transmission}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Transmission Type</p>
                <p className="font-medium">{data.performanceEngine.transmissionType}</p>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Engine Size</p>
                <p className="font-medium">{data.performanceEngine.engineSize}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Horsepower</p>
                <p className="font-medium">{data.performanceEngine.horsepower}</p>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Fuel Type</p>
                <p className="font-medium">{data.performanceEngine.fuelType}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Fuel Capacity</p>
                <p className="font-medium">{data.performanceEngine.fuelCapacity}</p>
              </div>
            </div>
          </>
        </div>
      </CardContent>
    </Card>
  );
}
