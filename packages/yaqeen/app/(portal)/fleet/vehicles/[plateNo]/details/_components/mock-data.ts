// This file contains mock data that matches the structure of the real API data
// It can be used for testing or when the API is not available

export const vehicleTypeData = {
  group: "C",
  make: "Toyota",
  model: "Camry",
  version: "SLE",
};

export const basicVehicleInfoData = {
  plateNo: "1234 - ABC",
  color: "Gray",
  unitNo: "xxxxxxxxxxxxxxx",
  chassisNo: "xxxxxxxxxxxxxxx",
  policyNo: "62179387123",
  manufactureYear: "2024",
  currentMileage: "KM 123,876",
  fuelLevel: "4/4 (Full)",
};

export const allocationData = {
  serviceType: "Rental",
  status: "Out of service",
  branchOwner: "King Khaled T5",
  location: "King Khaled T5",
};

export const versionSpecsData = {
  general: {
    numberOfSeats: "5",
    numberOfDoors: "3",
    luggageSpaceBig: "2",
    luggageSpaceMedium: "NA",
    luggageSpaceSmall: "1",
  },
  performanceEngine: {
    transmission: "Automatic",
    transmissionType: "4WD",
    engineSize: "4 CC",
    horsepower: "500 HP",
    fuelType: "Petrol-95",
    fuelCapacity: "300 L",
  },
};

export const vehicleImages = [
  {
    id: 1,
    src: "https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
];
