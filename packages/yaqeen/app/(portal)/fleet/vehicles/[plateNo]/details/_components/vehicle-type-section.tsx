"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye } from "lucide-react";
import { Separator } from "@/components/ui/separator";
interface VehicleTypeSectionProps {
  data: {
    group: string;
    make: string;
    model: string;
    version: string;
  };
}

export function VehicleTypeSection({ data }: VehicleTypeSectionProps) {
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Vehicle Type</CardTitle>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Eye className="h-4 w-4" />
          <span>View model specs</span>
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <>
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Group</p>
              <p className="font-medium">{data.group}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Make</p>
              <p className="font-medium">{data.make}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Model</p>
              <p className="font-medium">{data.model}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Version</p>
              <p className="font-medium">{data.version}</p>
            </div>
          </div>
        </>
      </CardContent>
    </Card>
  );
}
