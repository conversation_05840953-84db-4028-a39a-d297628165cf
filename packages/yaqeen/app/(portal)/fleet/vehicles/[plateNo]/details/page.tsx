import { api } from "@/api";
import { VehicleTypeSection } from "./_components/vehicle-type-section";
import { BasicVehicleInfoSection } from "./_components/basic-vehicle-info-section";
import { AllocationSection } from "./_components/allocation-section";
import { VersionSpecsSection } from "./_components/version-specs-section";
import { ImagesSection } from "./_components/images-section";
import { notFound } from "next/navigation";
import { getLocale } from "next-intl/server";
import { Suspense } from "react";
import DetailsSkeleton from "./_components/details-skeleton";

async function DetailsContent({ plateNo }: { plateNo: string }) {
  const locale = (await getLocale()) as "en" | "ar";

  const vehicleDetailsResponse = await api.availability.getVehicleDetailsV2({
    query: {
      plateNo,
      requireOpsData: true,
      requireFinancialData: true,
      requireInspectionData: true,
    },
  });

  if (vehicleDetailsResponse.status === 404) {
    notFound();
  }

  if (vehicleDetailsResponse.status !== 200) {
    return <div>Error loading vehicle details</div>;
  }

  const vehicleDetails = vehicleDetailsResponse.body;

  const vehicleTypeData = {
    group: vehicleDetails.model.vehicleGroup || "N/A",
    make: vehicleDetails.model.make.name[locale] || "N/A",
    model: vehicleDetails.model.name[locale] || "N/A",
    version: vehicleDetails.model.version || "N/A",
  };

  const basicVehicleInfoData = {
    plateNo: vehicleDetails.plateNo || "N/A",
    color: vehicleDetails.color || "N/A",
    unitNo: vehicleDetails.assetId || "N/A",
    chassisNo: vehicleDetails.chassisNo || "N/A",
    policyNo: vehicleDetails.policyNo || "N/A",
    manufactureYear: vehicleDetails.modelYear?.toString() || "N/A",
    currentMileage: vehicleDetails.vehicleOperationDTO?.odometerReading
      ? `KM ${vehicleDetails.vehicleOperationDTO.odometerReading.toLocaleString()}`
      : "N/A",
    fuelLevel: vehicleDetails.vehicleOperationDTO?.fuelLevel
      ? `${vehicleDetails.vehicleOperationDTO.fuelLevel.fuelLevel}/4 (${vehicleDetails.vehicleOperationDTO.fuelLevel.displayName})`
      : "N/A",
  };

  const allocationData = {
    serviceType: vehicleDetails.vehicleOperationDTO?.serviceType || "N/A",
    status: vehicleDetails.vehicleOperationDTO?.vehicleStatus?.status || "N/A",
    branchOwner: vehicleDetails.vehicleOperationDTO?.ownerBranch?.name?.[locale] || "N/A",
    location: vehicleDetails.vehicleOperationDTO?.currentLocation?.name?.[locale] || "N/A",
  };

  const specs = vehicleDetails.model.specification || {};
  const versionSpecsData = {
    general: {
      numberOfSeats: specs.seatingCapacity?.toString() || "N/A",
      numberOfDoors: specs.doors || "N/A",
      luggageSpaceBig: specs.luggageCountBig?.toString() || "N/A",
      luggageSpaceMedium: specs.luggageCountMedium?.toString() || "N/A",
      luggageSpaceSmall: specs.luggageCountSmall?.toString() || "N/A",
    },
    performanceEngine: {
      transmission: specs.transmission || "N/A",
      transmissionType: specs.transmissionType || "N/A",
      engineSize: specs.engineSize?.toString() || "N/A",
      horsepower: specs.horsepower?.toString() || "N/A",
      fuelType: specs.fuelType || "N/A",
      fuelCapacity: specs.fuelCapacity ? `${specs.fuelCapacity} L` : "N/A",
    },
  };

  const vehicleImages =
    vehicleDetails.model.images?.map((image, index) => ({
      id: image.id || index,
      src: image.url || "",
    })) || [];

  return (
    <div className="container p-6">
      <VehicleTypeSection data={vehicleTypeData} />
      <BasicVehicleInfoSection data={basicVehicleInfoData} />
      <AllocationSection data={allocationData} />
      <VersionSpecsSection data={versionSpecsData} />
      <ImagesSection images={vehicleImages} />
    </div>
  );
}

export default async function DetailsPage({ params }: { params: Promise<{ plateNo: string }> }) {
  const { plateNo } = await params;

  return (
    <Suspense fallback={<DetailsSkeleton />}>
      <DetailsContent plateNo={plateNo} />
    </Suspense>
  );
}
