"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus } from "lucide-react";
import Image from "next/image";

interface ImagesSectionProps {
  images: Array<{
    id: number;
    src: string;
  }>;
}

export function ImagesSection({ images }: ImagesSectionProps) {
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between border-b px-6 pb-4 pt-6">
        <CardTitle className="text-xl font-semibold">Images</CardTitle>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          <span>Add images</span>
        </Button>
      </CardHeader>
      <CardContent className="p-6">
        <div className="flex flex-wrap gap-4">
          {images.map((image) => (
            <div key={image.id} className="h-16 w-16 overflow-hidden rounded-md border-gray-200">
              <Image
                src={image.src}
                alt={`Vehicle image ${image.id}`}
                width={64}
                height={64}
                className="h-full w-full object-contain"
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
