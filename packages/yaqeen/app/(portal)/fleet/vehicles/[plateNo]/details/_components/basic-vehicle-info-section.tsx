"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Pencil } from "lucide-react";
import { Separator } from "@/components/ui/separator";
interface BasicVehicleInfoSectionProps {
  data: {
    plateNo: string;
    color: string;
    unitNo: string;
    chassisNo: string;
    policyNo: string;
    manufactureYear: string;
    currentMileage: string;
    fuelLevel: string;
  };
}

export function BasicVehicleInfoSection({ data }: BasicVehicleInfoSectionProps) {
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-semibold">Basic Vehicle Information</CardTitle>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Pencil className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="">
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Plate no.</p>
              <p className="font-medium">{data.plateNo}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Color</p>
              <p className="font-medium">{data.color}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Unit no. (Asset ID) no.</p>
              <p className="font-medium">{data.unitNo}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Chasis No.</p>
              <p className="font-medium">{data.chassisNo}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Policy no.</p>
              <p className="font-medium">{data.policyNo}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Manufacture year</p>
              <p className="font-medium">{data.manufactureYear}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Current mileage</p>
              <p className="font-medium">{data.currentMileage}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Fuel level</p>
              <p className="font-medium">{data.fuelLevel}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
