import { api } from "@/api";
import { getLocale } from "next-intl/server";
import PageTitle from "./_components/page-title";
import { notFound } from "next/navigation";

export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ plateNo: string }>;
}) {
  const { plateNo } = await params;
  const locale = (await getLocale()) as "en" | "ar";
  const vehicle = await api.availability.getVehicleDetailsV3({ query: { plateNo } });

  if (vehicle.status !== 200) {
    return notFound();
  }

  const make = vehicle.body.model.make.name?.[locale];
  const model = vehicle.body.model.name?.[locale];
  const vehicleClass = vehicle.body.model?.vehicleClass?.name?.[locale] ?? "";
  const year = vehicle.body.modelYear;

  return (
    <div className="flex flex-col">
      <PageTitle
        title={decodeURIComponent(plateNo)}
        breadcrumb={decodeURIComponent(plateNo)}
        subtitle={`${make} ${model} - ${vehicleClass} ${year}`}
      />
      {children}
    </div>
  );
}
