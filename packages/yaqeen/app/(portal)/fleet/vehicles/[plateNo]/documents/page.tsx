import { api } from "@/api";
import { DocumentsCard } from "../_components/documents-card";

export default async function DocumentsPage({ params }: { params: Promise<{ plateNo: string }> }) {
  const { plateNo } = await params;
  const [vehicleDocuments, documentTypeList] = await Promise.all([
    api.fleet.vehiclesContract.getVehicleDocuments({
      params: {
        plateNo,
      },
    }),
    api.fleet.vehiclesContract.getDocumentTypes(),
  ]);

  if (vehicleDocuments.status !== 200) {
    return <div>Error loading vehicle documents</div>;
  }

  if (documentTypeList.status !== 200) {
    return <div>Error loading document types</div>;
  }

  return (
    <div className="container py-4">
      <DocumentsCard
        documents={vehicleDocuments.body}
        documentTypeList={documentTypeList.body.content}
        plateNo={plateNo}
        showAll
      />
    </div>
  );
}
