import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { DocumentsCard } from "../_components/documents-card";
import { NeedsPrepCard } from "../_components/need-prep-card";
import { NrmDetailsCard } from "../_components/nrm-details-card";
import { VehicleDetailsCard } from "../_components/vehicle-card";
import VehicleDetailsSkeleton from "../_components/vehicle-details-skeleton";
import AgreementDetailsCard from "../_components/agreement-details-card";
import { Skeleton } from "@/components/ui/skeleton";

async function VehicleDetailsContent({ plateNo }: { plateNo: string }) {
  const [vehicleDetailsResponse, documentsResponse, nrmLogsResponse, documentTypeListResponse] = await Promise.all([
    api.availability.getVehicleDetailsV2({
      query: {
        plateNo,
        requireOpsData: true,
        requireFinancialData: true,
        requireInspectionData: true,
      },
    }),
    api.fleet.vehiclesContract.getVehicleDocuments({
      params: {
        plateNo,
      },
    }),
    api.nrm.getNrmVehicleLogs({
      query: {
        plateNo,
        pageNumber: "0",
        pageSize: "1",
      },
    }),
    api.fleet.vehiclesContract.getDocumentTypes(),
  ]);

  if (vehicleDetailsResponse.status === 404 || documentsResponse.status === 404 || nrmLogsResponse.status === 404) {
    notFound();
  }

  if (
    vehicleDetailsResponse.status !== 200 ||
    documentsResponse.status !== 200 ||
    nrmLogsResponse.status !== 200 ||
    documentTypeListResponse.status !== 200
  ) {
    return <div>Error loading vehicle details</div>;
  }

  const vehicleDetails = vehicleDetailsResponse.body;
  const status = vehicleDetails?.vehicleOperationDTO?.vehicleStatus.status;
  const documents = documentsResponse.status === 200 ? documentsResponse.body : [];
  const nrmData =
    nrmLogsResponse.status === 200 && nrmLogsResponse.body.content.length > 0 ? nrmLogsResponse.body.content[0] : null;
  const documentTypeList = documentTypeListResponse.status === 200 ? documentTypeListResponse.body.content : [];

  return (
    <div className="container py-4">
      <VehicleDetailsCard vehicleDetails={vehicleDetails} isLoading={false} />
      {status === "NEED_PREPARATION" && <NeedsPrepCard operationData={vehicleDetails.vehicleOperationDTO} />}
      {status === "NRM_OPENED" && nrmData && <NrmDetailsCard nrmData={nrmData} />}
      {status === "RENTED" && (
        <Suspense fallback={<Skeleton className="my-4 h-[200px] w-full" />}>
          <AgreementDetailsCard
            plateNo={plateNo}
            serviceType={vehicleDetails.vehicleOperationDTO?.serviceType ?? null}
          />
        </Suspense>
      )}
      <DocumentsCard documents={documents} documentTypeList={documentTypeList} plateNo={plateNo} />
    </div>
  );
}

export default async function VehicleDetailsPage({ params }: { params: Promise<{ plateNo: string }> }) {
  const { plateNo } = await params;

  return (
    <Suspense fallback={<VehicleDetailsSkeleton />}>
      <VehicleDetailsContent plateNo={plateNo} />
    </Suspense>
  );
}
