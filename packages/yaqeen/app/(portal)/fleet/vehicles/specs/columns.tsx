import { type ColumnDef } from "@tanstack/react-table";
import { type TSpec } from "./content.mock";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const columns: ColumnDef<TSpec>[] = [
  {
    accessorKey: "spec",
    header: "Spec",
  },
  {
    accessorKey: "valueOptions",
    header: "Value options",
  },
  {
    accessorKey: "unit",
    header: "Unit",
  },
  {
    accessorKey: "conditionOptions",
    header: "Condition options",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => alert("View " + row.original.spec)}>View</DropdownMenuItem>
            <DropdownMenuItem onClick={() => alert("Edit " + row.original.spec)}>Edit</DropdownMenuItem>
            <DropdownMenuItem onClick={() => alert("Delete " + row.original.spec)}>Delete</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
