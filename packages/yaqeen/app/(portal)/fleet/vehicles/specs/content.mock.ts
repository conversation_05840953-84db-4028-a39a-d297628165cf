export type TSpec = {
  id: string;
  spec: string;
  valueOptions: string;
  unit: string;
  conditionOptions: string;
};

export const specs: TSpec[] = [
  {
    id: "1",
    spec: "No. of seats",
    valueOptions: "Numeral, Min 2, max 50",
    unit: "#",
    conditionOptions: "N.A.",
  },
  {
    id: "2",
    spec: "No. of bags",
    valueOptions: "Numeral, Min 2, max 50",
    unit: "#",
    conditionOptions: "N.A.",
  },
  {
    id: "3",
    spec: "Color",
    valueOptions: "Black, white, red, etc.",
    unit: "N.A.",
    conditionOptions: "N.A.",
  },
  {
    id: "4",
    spec: "Apple carplay",
    valueOptions: "Yes, No",
    unit: "N.A.",
    conditionOptions: "Working, not working",
  },
  {
    id: "5",
    spec: "Spare tire",
    valueOptions: "Yes, No",
    unit: "N.A.",
    conditionOptions: "Available, not available",
  },
  {
    id: "6",
    spec: "Fuel capacity",
    valueOptions: "Numeral, Min 200, max 1,000",
    unit: "liters",
    conditionOptions: "N.A.",
  },
  {
    id: "7",
    spec: "Main key",
    valueOptions: "Yes, No",
    unit: "N.A.",
    conditionOptions: "Available, not available",
  },
  {
    id: "8",
    spec: "Spare key",
    valueOptions: "Yes, No",
    unit: "N.A.",
    conditionOptions: "Available, not available",
  },
];
