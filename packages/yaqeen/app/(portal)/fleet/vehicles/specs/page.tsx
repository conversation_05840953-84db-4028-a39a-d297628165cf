"use client";

import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import { specs } from "./content.mock";
import PageTitle from "../../_components/page-title";
import { type TSelectItem } from "@/types";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { CreateSpecDialog } from "../../_components/create-spec-dialog";

const searchFilters: TSelectItem[] = [
  { label: "Spec", value: "spec" },
  { label: "Value options", value: "valueOptions" },
  { label: "Unit", value: "unit" },
  { label: "Condition options", value: "conditionOptions" },
];

export default function Page() {
  return (
    <div>
      <PageTitle
        title="Vehicle Specs"
        description="List of specs that a vehicle can have"
        action={
          <CreateSpecDialog
            trigger={
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create new spec
              </Button>
            }
          />
        }
      />
      <div className="container px-24 py-6">
        <DataTable
          columns={columns}
          data={{ total: specs.length, data: specs }}
          emptyMessage="No specs found"
          searchPlaceholder="Search specs..."
          rowClickId="spec"
          searchFilters={searchFilters}
        />
      </div>
    </div>
  );
}
