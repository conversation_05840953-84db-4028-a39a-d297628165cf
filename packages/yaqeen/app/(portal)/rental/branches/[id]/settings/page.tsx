// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */
// @ts-nocheck
import { api } from "@/api";
import { Suspense } from "react";
import DetailPage from "../components";

export default async function BranchDetailPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;

  const [
    { body: branch },
    { body: regions },
    { body: cities },
    { body: currentAvailability },
    { body: extendedAvailability },
  ] = await Promise.all([
    api.branch.getBranch({
      params: { id },
      cache: "no-cache",
    }),

    api.branch.getRegions(),
    api.branch.getCities(),
    api.branch.fetchCurrentGroupAvailabilityByBranchId({
      params: { id },
    }),
    api.branch.fetchExtendedGroupAvailabilityByBranchId({
      params: { id },
    }),
  ]);

  let currentAvailabilityGroupsCode: any[] = [],
    extendedAvailabilityGroupsCode: any[] = [];

  const isMigrated = currentAvailability.branchMigrated;

  if (isMigrated) {
    currentAvailabilityGroupsCode = currentAvailability.groupAvailability
      .filter((el) => el.available)
      .map((el) => {
        return el?.group?.code;
      });

    extendedAvailabilityGroupsCode = extendedAvailability.groupAvailability
      .filter((el) => el.available)
      .map((el) => {
        return el?.group?.code;
      });
  }
  return (
    <div className="flex flex-col">
      <Suspense>
        <DetailPage
          branch={branch}
          regions={regions}
          cities={cities}
          currentAvailabilityGroupsCode={currentAvailabilityGroupsCode}
          extendedAvailabilityGroupsCode={extendedAvailabilityGroupsCode}
        />
      </Suspense>
    </div>
  );
}
