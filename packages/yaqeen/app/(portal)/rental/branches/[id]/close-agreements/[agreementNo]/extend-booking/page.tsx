import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "./components/actions-bar";
import { PricingBreakdownSkeleton } from "../pricing-breakdown-skeleton";
import PricingBreakdown from "./components/pricing-breakdown";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { redirect } from "next/navigation";
import ExtendAgreementCard from "./components/extend-agreement-card";
import { ExtendAgreementCardSkeleton } from "./components/extend-agreement-skeleton";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ agreementNo: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const { agreementNo } = await params;
  const _searchParams = await searchParams;

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const agreement: AgreementInvoice = agreementResponse.body;

  const driverUId = agreement?.driver?.driverUId ?? "";

  const initialDropOffTimestamp = Number(agreement?.dropOffDateTime);
  const newDropOffTimestamp = Number(_searchParams?.dropOffTimestamp);

  const isDropOffChange =
    !isNaN(initialDropOffTimestamp) && !isNaN(newDropOffTimestamp) && newDropOffTimestamp > initialDropOffTimestamp;

  const priceCalculatorResponse = isDropOffChange
    ? await api.bookingDetails.calculateExtendAgreementPrice({
        params: {
          agreementNo,
        },
        query: {
          newCheckInDate: isNaN(newDropOffTimestamp) ? 0 : newDropOffTimestamp,
        },
      })
    : await api.bookingDetails.calculateAgreementPrice({
        params: {
          agreementNo,
        },
      });

  if (priceCalculatorResponse?.status === 404) {
    notFound();
  }

  if (priceCalculatorResponse.status !== 200) {
    throw new Error("Failed to fetch Agreement Price");
  }

  // Fetch branches data
  const branches = await api.branch.getDetailedBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const priceResponse = priceCalculatorResponse.body;
  const dropOffBranchId = String(agreement.dropOffBranchId);
  const dropOffTimestamp = agreement.dropOffDateTime;

  // If essential parameters are missing, redirect with default parameters
  if (!_searchParams.dropOffBranchId || !_searchParams.dropOffTimestamp) {
    const defaultSearchParams = new URLSearchParams();

    // Add required parameters
    defaultSearchParams.set("dropOffBranchId", dropOffBranchId);
    defaultSearchParams.set("dropOffTimestamp", String(dropOffTimestamp));
    // Only include dropOffTimestamp if it exists in the original search params
    if (_searchParams.dropOffTimestamp) {
      defaultSearchParams.set("dropOffTimestamp", String(_searchParams.dropOffTimestamp));
    }

    // Add all other existing search parameters
    Object.entries(_searchParams).forEach(([key, value]) => {
      if (value !== undefined && !defaultSearchParams.has(key)) {
        defaultSearchParams.set(key, String(value));
      }
    });

    redirect(`?${defaultSearchParams.toString()}`);
  }

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");

  const EXTEND_AGREEMENT_KEY = `${suspenseKey}_extend_agreement`;
  const PRICE_BREAKDOWN_KEY = `${suspenseKey}_price_breakdown`;

  return (
    <main className="w-full space-y-6">
      <section className="flex gap-x-10">
        <div className="col-span-8 flex w-[768px]  shrink-0 flex-col gap-y-6">
          <Suspense key={EXTEND_AGREEMENT_KEY} fallback={<ExtendAgreementCardSkeleton />}>
            <ExtendAgreementCard
              agreement={agreement}
              branches={branches.body.data}
              isDropOffChange={isDropOffChange}
              searchParams={_searchParams}
            />
          </Suspense>

          <ActionsBar
            agreementNo={agreement.agreementNo}
            bookingId={agreement.bookingId}
            quoteId={priceResponse?.quoteId ?? ""}
            className="w-full"
            successCtaDisabled={!isDropOffChange}
          />
        </div>
        <div className="col-span-4">
          <Suspense key={PRICE_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
            <PricingBreakdown agreement={agreement} priceResponse={priceResponse} searchParams={_searchParams}>
              {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
            </PricingBreakdown>
          </Suspense>
        </div>
      </section>
    </main>
  );
}
