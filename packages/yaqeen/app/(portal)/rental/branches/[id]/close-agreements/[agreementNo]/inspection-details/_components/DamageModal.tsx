"use client";

import { useState, type FormEvent, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Info, Upload } from "lucide-react";
import { addPenaltyAction, updatePenaltyAction } from "@/lib/actions/agreement-actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useTranslations } from "next-intl";

type Penalty = {
  id: number;
  penaltyAmount: string;
  penaltyDescription: string;
  penaltyDate: number;
  type: string;
  details: {
    type: "DAMAGE" | "TRAFFIC_FINE";
    policeReportUrl?: string;
    severity?: string;
  };
};

interface DamagePenaltyModalProps {
  open: boolean;
  comp: boolean | undefined;
  onOpenChange: (open: boolean) => void;
  agreementVehicleId: number;
  insuranceDeductible: number;
  penaltyToEdit?: Penalty | null;
}

export default function DamagePenaltyModal({
  open,
  onOpenChange,
  agreementVehicleId,
  penaltyToEdit,
  insuranceDeductible,
  comp,
}: DamagePenaltyModalProps) {
  const { toast } = useToast();
  const t = useTranslations("closeAgreement");
  const [applyCharge, setApplyCharge] = useState(false);
  const [attachReport, setAttachReport] = useState(false);
  const [description, setDescription] = useState("");
  const [severity, setSeverity] = useState("");
  const [amount, setAmount] = useState("");
  const [policeReportUrl, setPoliceReportUrl] = useState("");
  const [policeReportFile, setPoliceReportFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const isEditMode = !!penaltyToEdit;

  useEffect(() => {
    // If we're editing an existing penalty, initialize the form with its data
    if (penaltyToEdit) {
      setDescription(penaltyToEdit.penaltyDescription || "");
      setSeverity(penaltyToEdit.details.severity || "");

      // Set amount if it exists
      if (penaltyToEdit.penaltyAmount && penaltyToEdit.penaltyAmount !== "0.00") {
        setApplyCharge(true);
        setAmount(penaltyToEdit.penaltyAmount);
      }

      // Set police report if it exists
      if (penaltyToEdit.details.policeReportUrl) {
        setAttachReport(true);
        setPoliceReportUrl(penaltyToEdit.details.policeReportUrl);
      }
    } else {
      // Reset form when not in edit mode
      resetForm();
    }
  }, [penaltyToEdit]);

  const resetForm = () => {
    setDescription("");
    setSeverity("");
    setApplyCharge(false);
    setAmount("");
    setAttachReport(false);
    setPoliceReportUrl("");
    setPoliceReportFile(null);
  };

  const uploadFile = async (file: File): Promise<string> => {
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      // Use our proxy API route instead of the external API
      const response = await fetch("/next-api/upload/police-report", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Upload failed with status: ${response.status}`);
      }

      const result = await response.json();
      return result.data; // This contains the file URL
    } catch (error) {
      console.error("File upload error:", error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setPoliceReportFile(file);
      // We're just storing the file for later upload
      // The URL will be set during form submission after successful upload
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!description) {
      toast({
        title: t("Error"),
        description: t("Please provide a damage description"),
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      let fileUrl = policeReportUrl;

      // Upload file if one is selected
      if (attachReport && policeReportFile) {
        try {
          fileUrl = await uploadFile(policeReportFile);
        } catch {
          toast({
            title: t("Error"),
            description: t("Failed to upload police report file. Please try again."),
            variant: "destructive",
          });
          setIsSubmitting(false);
          return;
        }
      }

      const penaltyData = {
        penaltyDescription: description,
        penaltyAmount: applyCharge && amount ? amount : "0.00",
        details: {
          type: "DAMAGE" as const,
          severity: severity || undefined,
          policeReportUrl: attachReport && fileUrl ? fileUrl : undefined,
        },
      };

      if (isEditMode && penaltyToEdit) {
        // Update existing penalty
        await updatePenaltyAction(agreementVehicleId, penaltyToEdit.id, penaltyData);

        toast({
          title: t("Success"),
          description: t("Damage/penalty has been updated successfully"),
        });
      } else {
        // Add new penalty
        await addPenaltyAction(agreementVehicleId, penaltyData);

        toast({
          title: t("Success"),
          description: t("Damage/penalty has been added successfully"),
        });
      }

      onOpenChange(false);
    } catch (error) {
      console.error("Error with penalty:", error);
      toast({
        title: t("Error"),
        // @ts-expect-error TODO: Fix this
        description: t(`Failed to ${isEditMode ? t("update") : t("add")} damage/penalty. Please try again.`),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {isEditMode ? t("Edit damage/penalty") : t("Add new damage/penalty")}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Insurance Info */}
          <div className="flex items-start gap-2 rounded-lg border bg-slate-50 p-4">
            <Info className="mt-0.5 h-5 w-5 text-blue-600" />
            <div>
              <div className="font-medium">
                {comp ? t("Comprehensive insurance selected") : t("Basic insurance selected")}
              </div>
              <div className="text-slate-600">
                {t("Pay up to")} {t("SAR")} {insuranceDeductible} {t("with a police report")}
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="text-lg font-medium">{t("Description")}</label>
            <Textarea
              placeholder={t("Add damage/penalty description")}
              className="min-h-[100px]"
              value={description}
              maxLength={250}
              onChange={(e) => setDescription(e.target.value)}
              required
            />
          </div>

          {/* Severity */}
          <div className="space-y-2">
            <label className="text-lg font-medium">{t("Severity")}</label>
            <Select value={severity} onValueChange={setSeverity}>
              <SelectTrigger>
                <SelectValue placeholder={t("Select severity")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">{t("Low")}</SelectItem>
                <SelectItem value="Medium">{t("Medium")}</SelectItem>
                <SelectItem value="Major">{t("Major")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Apply Charge */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="apply-charge"
                checked={applyCharge}
                onCheckedChange={(checked) => setApplyCharge(checked === true)}
              />
              <label htmlFor="apply-charge" className="text-lg font-medium">
                {t("Apply charge")}
              </label>
            </div>

            {applyCharge && (
              <div className="ml-6 space-y-4 rounded-lg bg-slate-50 p-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <label className="font-medium">{t("Payable amount")}</label>
                  </div>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-600">{t("SAR")}</span>
                    <Input
                      className="pl-12"
                      placeholder={t("Enter payable amount here")}
                      value={amount}
                      type="text"
                      pattern="^[0-9٠-٩]+(\.[0-9٠-٩]{0,2})?$"
                      onChange={(e) => {
                        if (e.target.validity.valid) {
                          const numericDigits = e.target.value.replace(/[٠-٩]/g, (d) =>
                            "٠١٢٣٤٥٦٧٨٩".indexOf(d).toString()
                          );
                          setAmount(numericDigits);
                        }
                      }}
                    />
                  </div>
                </div>
                <div className="text-slate-600">{t("Payable amount")}</div>
              </div>
            )}
          </div>

          {/* Attach Police Report */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="attach-report"
                checked={attachReport}
                onCheckedChange={(checked) => setAttachReport(checked === true)}
              />
              <label htmlFor="attach-report" className="text-lg font-medium">
                {t("Attach police report")}
              </label>
            </div>

            {attachReport && (
              <div className="ml-6 space-y-2">
                <div className="flex items-center gap-2">
                  <label
                    htmlFor="police-report-upload"
                    className="flex min-h-10 w-full cursor-pointer items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm text-slate-500 hover:bg-slate-50"
                  >
                    <Upload size={16} />
                    {policeReportFile ? policeReportFile.name : t("Choose police report file")}
                  </label>
                  <Input
                    id="police-report-upload"
                    type="file"
                    className="hidden"
                    onChange={handleFileChange}
                    accept="image/*,.pdf"
                  />
                </div>
                {policeReportFile && (
                  <p className="text-xs text-slate-500">
                    {t("File selected")}: {policeReportFile.name} ({(policeReportFile.size / 1024).toFixed(1)} KB)
                  </p>
                )}
                {isUploading && <p className="text-xs text-blue-500">{t("Uploading file")}</p>}
              </div>
            )}
          </div>

          <DialogFooter className="gap-2 ">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              {t("Cancel")}
            </Button>
            <Button
              type="submit"
              className="bg-lumi-500 text-black hover:bg-lumi-600"
              disabled={isSubmitting || isUploading}
            >
              {isSubmitting
                ? isEditMode
                  ? t("Updating...")
                  : t("Adding...")
                : isEditMode
                  ? t("Save changes")
                  : t("Add damage or penalty")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
