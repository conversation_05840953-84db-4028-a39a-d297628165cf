import { api } from "@/api";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { notFound } from "next/navigation";
import { type ReactNode } from "react";
import { BookingProvider } from "./booking-provider";
import PageTitle from "./page-title";

interface LayoutProps {
  children: ReactNode;
  params: Promise<{
    agreementNo: string;
  }>;
}

export default async function Layout({ children, params }: LayoutProps) {
  const { agreementNo } = await params;

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const agreement: AgreementInvoice = agreementResponse.body;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(agreement.bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  return (
    <BookingProvider>
      <div className="flex flex-col pb-16 lg:min-h-screen">
        <PageTitle
          bookingType={bookingResponse.body.bookingType || ""}
          source={bookingResponse.body.source || ""}
          aggregatorName={bookingResponse.body?.aggregatorName || ""}
          booking={bookingResponse.body}
          agreementNo={agreement.agreementNo}
        />
        <div className="container my-6 px-24">{children}</div>
      </div>
    </BookingProvider>
  );
}
