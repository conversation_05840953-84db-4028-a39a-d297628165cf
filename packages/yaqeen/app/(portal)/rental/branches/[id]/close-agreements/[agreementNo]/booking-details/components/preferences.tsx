"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Star } from "@phosphor-icons/react/dist/ssr";
import Image from "next/image";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { useLocale, useTranslations } from "next-intl";

export default function Preferences({ agreement }: { agreement: AgreementInvoice }) {
  const t = useTranslations("closeAgreement");
  const locale = useLocale();
  const [group, make, modal] = agreement.assignedVehicle.vehicleGroup?.displayName?.split("_") ?? [];

  return (
    <div className="flex h-full w-full flex-col border-l bg-slate-50">
      <div className="flex flex-col p-4">
        <p className="flex w-full flex-col justify-center">
          <span className="text-base font-bold text-slate-900">{t("Customer selection")}</span>
          <span className="mt-1 text-sm ">{t("You can edit them in next steps")}</span>
        </p>
        <div className="flex w-full flex-col gap-y-3 py-4 text-sm font-medium text-slate-900 file:pt-4">
          <p className="flex gap-x-1.5 ">
            <Car className="size-5" />
            {modal || make || group ? (
              <span>
                {make ?? ""} {modal ?? ""} ({t("Group")} {group})
              </span>
            ) : (
              t("NA")
            )}
          </p>
          <p className="flex gap-x-1.5 ">
            <ShieldCheck className="size-5" />
            {agreement?.priceDetail?.insurances
              ? agreement.priceDetail.insurances.map((insurance) => (
                  <span key={insurance.id}>{insurance.name?.[locale as keyof typeof insurance.name] ?? t("NA")}</span>
                ))
              : t("NA")}
          </p>
          {/* Addons */}
          {agreement.priceDetail?.addOns?.length ? (
            agreement.priceDetail.addOns.map((addon) => (
              <p key={addon.id} className="flex gap-x-1.5 ">
                <Star className="size-5" />
                <span> {addon.name?.[locale as keyof typeof addon.name] ?? t("NA")}</span>
              </p>
            ))
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
}
