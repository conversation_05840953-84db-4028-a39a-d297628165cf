"use client";

import type { Pos } from "@/api/contracts/booking/schema";
import PaymentFlow from "@/app/(portal)/rental/branches/[id]/bookings/[bookingId]/payment/_components/PaymentFlow";
import { useState } from "react";

export const PaymentFlowWrapper = ({ posResponse }: { posResponse: Pos }) => {
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  return (
    <div className="max-w-[1200px] space-y-8">
      <PaymentFlow
        posResponse={posResponse}
        paymentModalOpen={paymentModalOpen}
        setPaymentModalOpen={setPaymentModalOpen}
        loading={loading}
        setLoading={setLoading}
      />
    </div>
  );
};
