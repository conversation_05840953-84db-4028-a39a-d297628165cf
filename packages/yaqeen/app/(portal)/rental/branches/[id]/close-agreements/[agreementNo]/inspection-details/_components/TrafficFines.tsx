"use client";

import { useState } from "react";

import { useLocale, useTranslations } from "next-intl";
import { Table, TableCell, TableHeader, TableRow, TableBody } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import type { TrafficFine } from "@/api/contracts/rental/traffic-fine-contract";
import { useQuery } from "@tanstack/react-query";
import { amountFormatter } from "@/lib/utils";
import TooltipComponent from "@/components/tooltip-component";
import { CheckCircleIcon, ReceiptIcon } from "@phosphor-icons/react";
import { downloadInvoiceHtml } from "../../../../financials/traffic-fines/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Card, CardContent } from "@/components/ui/card";

const TrafficFines = ({ agreementNumber }: { agreementNumber: string }) => {
  const t = useTranslations("closeAgreement");
  const locale = useLocale();
  const { toast } = useToast();

  const { data: trafficFines } = useQuery({
    queryKey: ["trafficFines", agreementNumber],
    queryFn: async () => {
      const res = await fetch(`/next-api/traffic-fine/agreements/fines?agreementNo=${agreementNumber}`);
      if (!res.ok) throw new Error("Failed to fetch traffic fines for agreement");
      return res.json();
    },
  });

  const [printInvoiceIdLoading, setPrintInvoiceIdLoading] = useState<string | null>(null);
  const totalFineAmount = trafficFines?.reduce((sum: number, fine: TrafficFine) => sum + fine.amount, 0) || 0;

  const handlePrintInvoice = async (invoiceNumber?: string) => {
    if (!invoiceNumber) {
      toast({
        title: t("trafficFines.invoice.notFound"),
        description: t("trafficFines.invoice.checkInvoiceNo"),
        variant: "destructive",
      });
      return;
    }

    setPrintInvoiceIdLoading(invoiceNumber);
    try {
      const res = await downloadInvoiceHtml(agreementNumber, invoiceNumber);

      if (res.status !== 200) {
        toast({
          title: t("trafficFines.invoice.downloadFailed"),
          description: t("trafficFines.invoice.failedToDownloadInvoice"),
          variant: "destructive",
        });
        return;
      }

      const data = res.body.htmlContent;

      const printWindow = window.open(
        "",
        "_blank",
        "width=" + window.screen.availWidth + ",height=" + window.screen.availHeight
      );
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.title = `Traffic Invoice - ${invoiceNumber}`;
        printWindow.document.write(data.html);
        printWindow.document.close();

        printWindow.onload = () => {
          printWindow.focus();
          printWindow.print();
        };
      } else {
        toast({
          title: t("trafficFines.invoice.printFailed"),
          description: t("trafficFines.invoice.failedToOpenPrintWindow"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error downloading invoice:", error);
      toast({
        title: t("trafficFines.invoice.downloadFailed"),
        description: t("trafficFines.invoice.failedToDownloadInvoice"),
        variant: "destructive",
      });
    } finally {
      setPrintInvoiceIdLoading(null);
    }
  };

  if (!trafficFines || trafficFines.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardContent>
        <div className="max-w-3xl py-6">
          <div className="mb-6 text-2xl font-semibold">{t("trafficFines.title")}</div>

          <div className="space-y-4">
            <div className="space-y-4">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableCell>{t("Description")}</TableCell>
                    <TableCell>{t("Payable (SAR)")}</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {trafficFines?.map((trafficFine: TrafficFine) => {
                    return (
                      <TableRow key={trafficFine.id}>
                        <TableCell>
                          {t("trafficFines.trafficFine")}:{" "}
                          {locale === "ar" ? trafficFine.metadata.violationCodeAr : trafficFine.metadata.violationCode}
                        </TableCell>

                        <TableCell className="flex items-center gap-3">
                          {amountFormatter(trafficFine.amount)}
                          {trafficFine.paymentStatus === "PAID" && (
                            <TooltipComponent
                              content={<span className="text-sm">{t("trafficFines.paid")}</span>}
                              withArrow={false}
                            >
                              <CheckCircleIcon size={16} weight="fill" className="cursor-pointer fill-lumi-600" />
                            </TooltipComponent>
                          )}
                        </TableCell>
                        <TableCell>
                          <div
                            className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-md border border-slate-300"
                            onClick={() => handlePrintInvoice(trafficFine.invoiceNumber)}
                          >
                            {printInvoiceIdLoading === trafficFine.invoiceNumber ? (
                              <LoadingSpinner size={16} className="text-lumi-900" />
                            ) : (
                              <ReceiptIcon size={16} className="fill-slate-900" weight="bold" />
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
              <Separator />
              <div className="flex justify-end text-sm text-slate-600">
                {t("trafficFines.total")}:{" "}
                <span className="ms-1 text-sm font-medium text-slate-900">
                  {amountFormatter(totalFineAmount)} {t("SAR")}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TrafficFines;
