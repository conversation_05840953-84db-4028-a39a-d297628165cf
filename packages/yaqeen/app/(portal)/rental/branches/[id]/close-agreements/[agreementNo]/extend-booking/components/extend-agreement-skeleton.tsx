import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export function ExtendAgreementCardSkeleton() {
  return (
    <Card className="w-full space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <Skeleton className="h-8 w-40" /> {/* Extend Agreement Title */}
      </div>

      <Separator className="my-6" />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-[2fr,1fr] md:gap-0">
        {/* Left Column */}
        <div className="space-y-6 pr-6">
          {/* Pickup Section */}
          <div className="space-y-4">
            <Skeleton className="h-7 w-32" /> {/* new dropoff text */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 w-80" /> {/* Selected Branch */}
            </div>
            {/* Date and Time Row */}
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-8 w-[180px]" /> {/* Today's date */}
                </div>
                <Skeleton className="h-8 w-32" /> {/* Time  */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
