"use client";

import OpenLink from "@/components/customized/open-link";
import { useParams } from "next/navigation";

export const OpenLinkDriver = ({ bookingId }: { bookingId: string }) => {
  const params = useParams();
  const { id } = params as { id: string };
  return (
    <OpenLink
      url={`/rental/branches/${id}/bookings/${bookingId}/booking-details`}
      className="flex items-center gap-2"
    />
  );
};
