"use client";

import { type Branch } from "@/api/contracts/branch-contract";
import { useProgressBar } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { calculatePriceAction } from "@/lib/actions";
import { toast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { isBefore, isToday, set } from "date-fns";
import { format } from "date-fns-tz";
import { type Route } from "next";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { startTransition, useState } from "react";

const MINUTE_OPTIONS = Array.from({ length: 48 }).map((_, i) => {
  const hour = Math.floor(i / 2)
    .toString()
    .padStart(2, "0");
  const minute = i % 2 === 0 ? "00" : "30";
  const time = `${hour}:${minute}`;
  return {
    value: time,
    label: time,
  };
});

interface DropOffState {
  branchId: number;
  date: Date;
  time: string;
}

export default function DropOff({
  dropOffBranchId,
  branchOptions,
  dropOffDateTime,
}: {
  dropOffBranchId: string;
  branchOptions: Branch[];
  dropOffDateTime: number;
}) {
  const { bookingNo } = useParams();
  const searchParams = useSearchParams();
  const dropOffTimestamp = searchParams.get("dropOffTimestamp") ?? dropOffDateTime;
  const _dropOffBranchId = searchParams.get("dropOffBranchId") ?? dropOffBranchId;

  const [state, setState] = useState<DropOffState>(() => {
    const timestamp = Number(dropOffTimestamp) * 1000;
    const date = new Date(`${new Date(timestamp).toISOString().split("T")[0]}T00:00:00.000+03:00`);
    const formattedTime = new Date(timestamp).toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      // timeZone: "Asia/Riyadh",
    });

    return {
      branchId: Number(_dropOffBranchId),
      date: date,
      time: formattedTime,
    };
  });

  const selectedBranch = branchOptions.find((branch) => branch.id === state.branchId);
  const router = useRouter();
  const pathname = usePathname();
  const progress = useProgressBar();

  const getAvailableTimeOptions = (): { value: string; label: string }[] => {
    if (!isToday(state.date)) {
      return MINUTE_OPTIONS;
    }

    const now = new Date();
    const currentTime = set(now, { seconds: 0, milliseconds: 0 });

    return MINUTE_OPTIONS.filter((option) => {
      const [hours, minutes] = option.value.split(":").map(Number);
      const optionDate = set(new Date(), { hours, minutes, seconds: 0, milliseconds: 0 });
      return !isBefore(optionDate, currentTime);
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const formattedDate = format(state.date, "yyyy-MM-dd");
    const parsedDate = new Date(`${formattedDate}T${state.time}:00.000+03:00`);
    const dropOffDateTime = Math.floor(parsedDate.getTime() / 1000);

    progress.start();

    const { data, error } = await calculatePriceAction({
      bookingId: bookingNo as string,
      dropOffBranchId: state.branchId,
      dropOffTime: dropOffDateTime,
    });

    if (error) {
      toast({ variant: "destructive", title: "Error", description: error.desc });
      progress.done();
      return;
    }

    startTransition(() => {
      const searchParams = new URLSearchParams();
      searchParams.set("dropOffBranchId", state.branchId.toString());
      searchParams.set("dropOffTimestamp", dropOffDateTime.toString());

      if (data?.quoteId) {
        searchParams.set("quoteId", data.quoteId);
      }

      router.push((pathname + "?" + searchParams.toString()) as Route);
      progress.done();
    });
  };

  return (
    <form className="flex flex-col gap-y-2 p-4" onSubmit={handleSubmit}>
      <Select
        defaultValue={selectedBranch?.id.toString()}
        onValueChange={(value) => {
          setState((prev) => ({ ...prev, branchId: Number(value) }));
        }}
      >
        <SelectTrigger
          className={cn(
            "flex items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0"
          )}
          aria-label="Select branch"
        >
          <SelectValue placeholder="Select a branch" />
        </SelectTrigger>
        <SelectContent>
          {branchOptions.map((branch) => (
            <SelectItem key={branch.id} value={branch.id.toString()}>
              <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                {branch.name.en}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="flex w-full flex-row justify-between gap-x-2">
        <div className="w-2/3">
          <DatePicker
            date={state.date}
            setDate={(newDate) => {
              const availableTimes = getAvailableTimeOptions();
              const [hours, minutes] = state.time.split(":").map(Number);
              const timeDate = set(new Date(), { hours, minutes, seconds: 0, milliseconds: 0 });

              setState((prev) => ({
                ...prev,
                date: newDate,
                // Reset time to first available slot if current time is invalid
                time: isToday(newDate) && isBefore(timeDate, new Date()) ? availableTimes[0]!.value : prev.time,
              }));
            }}
          />
        </div>
        <div className="w-1/3">
          <Select defaultValue={state.time} onValueChange={(value) => setState((prev) => ({ ...prev, time: value }))}>
            <SelectTrigger
              className={cn(
                "flex items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0"
              )}
              aria-label="Select time"
            >
              <SelectValue placeholder="Select time" />
            </SelectTrigger>
            <SelectContent>
              {getAvailableTimeOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                    {option.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="outline"
          className="w-fit"
          type="submit"
          disabled={!state.branchId || !state.date || !state.time}
        >
          Change
        </Button>
      </div>
    </form>
  );
}
