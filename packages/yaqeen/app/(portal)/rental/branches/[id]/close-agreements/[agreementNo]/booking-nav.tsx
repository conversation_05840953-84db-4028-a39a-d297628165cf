"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { useAtom } from "jotai";
import { useParams, usePathname, useSearchParams } from "next/navigation";
import { useMemo } from "react";
import { atomWithBookingNav } from "./atoms";
import type { Route } from "next";
import { useTranslations } from "next-intl";

interface BookingNavProps {
  agreementNo: string;
}

export function BookingNav({ agreementNo }: BookingNavProps) {
  const pathname = usePathname();
  const t = useTranslations("closeAgreement");
  const params = useParams<{ id: string }>();
  const searchParams = useSearchParams();
  const bookingNavAtom = useMemo(() => atomWithBookingNav(), []);
  const [navItems] = useAtom(bookingNavAtom);

  return (
    <div className="flex gap-x-6">
      {navItems.map((item, index) => {
        const href = `/rental/branches/${params.id}/close-agreements/${agreementNo}/${item.href}${
          searchParams.toString() ? `?${searchParams.toString()}` : ""
        }`;

        const isAccessible = true;

        return (
          <ProgressBarLink
            key={index}
            href={href as Route}
            className={`flex items-center gap-2 py-3 md:text-xs xl:text-sm ${
              pathname.includes(item.href)
                ? "border-b-2 border-slate-900 font-semibold text-slate-900"
                : "text-slate-700"
            } ${!isAccessible ? "pointer-events-none opacity-50" : ""}`}
            aria-disabled={!isAccessible}
          >
            {/* {item.completed ? (
              <CheckCircle
                weight="fill"
                className={`h-4 w-4 ${pathname.includes(item.href) ? "fill-slate-900" : "fill-slate-500"}`}
              />
            ) : null} */}
            {t(
              // @ts-expect-error any
              item.label
            )}
          </ProgressBarLink>
        );
      })}
    </div>
  );
}
