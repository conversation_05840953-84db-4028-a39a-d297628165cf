import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table/data-table";
import { api } from "@/api";
import { paymentColumns } from "../../../bookings/_components/payments-columns";

export default async function Payments({ bookingId }: { bookingId: string }) {
  const [transactionsResponse] = await Promise.all([
    api.payment.getBookingTransactions({
      params: {
        bookingId: String(bookingId),
      },
      query: {
        notInitiatedFor: "SECURITY_DEPOSIT_AUTHORIZATION",
      },
    }),
  ]);

  if (transactionsResponse?.status !== 200) {
    throw new Error(`Error: ${transactionsResponse.status}`);
  }

  const transactions = transactionsResponse.body;

  if (!transactions?.data.length) {
    return <></>;
  }

  return (
    <Card className="flex flex-col">
      <CardHeader className="px-4">
        <CardTitle className="text-lg font-bold">Payments</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="w-full p-0">
        <div className="h-fit w-full">
          {transactions?.data.length ? (
            <DataTable
              searchPlaceholder=""
              columns={paymentColumns}
              data={{
                data: transactions.data,
                total: transactions.total ?? 0,
              }}
              emptyMessage="There are no payments."
            />
          ) : (
            <p className="p-6 text-center">There are no payments!</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
