import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "../actions-bar";
import { PricingBreakdownSkeleton } from "../pricing-breakdown-skeleton";
import type { CalculatePrice } from "@/api/contracts/booking/schema";
import PricingBreakdown from "../pricing-breakdown";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { BookingDetailAndRentalRateSkeleton } from "../booking-details/components/skeleton/booking-detail-rental-skeleton";
import { InspectionReport } from "./_components/InspectionReport";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";

export default async function Page({ params }: { params: Promise<{ agreementNo: string }> }) {
  const { agreementNo } = await params;

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const agreement: AgreementInvoice = agreementResponse.body;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(agreement.bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;

  const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
  });

  const priceResponse: CalculatePrice = priceCalculatorResponse.body as CalculatePrice;

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
          <InspectionReport priceResponse={priceResponse} agreement={agreement} />
        </Suspense>
        <ActionsBar agreementNo={agreement.agreementNo} className="w-full" />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
