"use client";

import { Info } from "lucide-react";
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import BranchTimeSelector from "../../../../bookings/_components/branch-time-selector";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { type Branch } from "@/api/contracts/branch-contract";
import { convertIntervalToDuration } from "./pricing-breakdown";
import { toast } from "@/lib/hooks/use-toast";

export default function ExtendAgreementCard({
  agreement,
  branches,
  isDropOffChange,
  searchParams,
}: {
  agreement: AgreementInvoice;
  branches: Branch[];
  isDropOffChange: boolean;
  searchParams: Record<string, string | string[] | undefined>;
}) {
  const dropOffBranchId = String(agreement.dropOffBranchId);
  const dropOffTimestamp = agreement.dropOffDateTime;

  let durationSummary = "";

  const initialDropOffTimestamp = Number(agreement?.dropOffDateTime);
  const newDropOffTimestamp = Number(searchParams?.dropOffTimestamp);

  // if new drop-off date is in past from initial drop-off date
  if (initialDropOffTimestamp > newDropOffTimestamp) {
    toast({
      variant: "destructive",
      title: "Error while selecting drop-off date",
      description: "New drop-off date must be the future date",
      duration: 2000,
    });
  }

  if (isDropOffChange) {
    const initialDropOffDate = new Date(initialDropOffTimestamp * 1000);
    const newDropOffDate = new Date(newDropOffTimestamp * 1000);

    durationSummary = convertIntervalToDuration({
      start: initialDropOffDate,
      end: newDropOffDate,
    });
  }

  return (
    <div className="col-span-8 flex flex-col space-y-6">
      <div className="flex flex-col gap-x-3 rounded-md">
        <Card className="flex flex-col">
          <CardContent className="flex w-full p-0 max-md:flex-wrap">
            <div className="flex items-center gap-2 p-4">
              <Info className="h-5 w-5 text-gray-500" />
              <span className="text-sm font-medium">
                A Quick Pay link will be automatically generated and sent to the customer after finalizing the extension
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="flex flex-col gap-x-3 rounded-md">
        <Card className="flex flex-col shadow">
          <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
            <CardTitle className="text-lg font-bold">New drop-off details</CardTitle>
          </CardHeader>
          <Separator />
          <CardContent className="flex w-full p-0 max-md:flex-wrap">
            <div className="flex-1 flex-col gap-4 text-sm font-medium">
              <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">New drop-off date</p>
              <div className="w-full max-w-xs p-4">
                <BranchTimeSelector
                  branchId={dropOffBranchId}
                  branchOptions={branches}
                  branchDateTime={dropOffTimestamp}
                  branchType="dropOff"
                  isSubmitButton={false}
                  disablePastDates={true}
                  isBranchDisabled={true}
                  disableNextYearDates={true}
                />
              </div>
              {isDropOffChange && (
                <>
                  <Separator />
                  <div className="w-full max-w-xs p-4">
                    {durationSummary && (
                      <p className="text-slate-500">
                        Extension duration: <span className="text-black">{durationSummary}</span>
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
