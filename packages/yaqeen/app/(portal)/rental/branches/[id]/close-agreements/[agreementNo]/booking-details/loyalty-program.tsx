import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { api } from "@/api";

const LoyaltyCard = ({ children }: { children: React.ReactNode }) => {
  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="px-4">
        <CardTitle className="text-lg font-bold">Loyalty program</CardTitle>
      </CardHeader>
      <Separator />
      {children}
    </Card>
  );
};

export default async function LoyaltyProgram({ bookingId }: { bookingId: string }) {
  const [booking] = await Promise.all([
    api.bookingDetails.getBookingById({
      params: {
        id: Number(bookingId),
      },
    }),
    api.branch.getBranchList(),
  ]);

  if (booking?.status !== 200) {
    throw new Error(`Error: ${booking.status}`);
  }

  const [loyalty] = await Promise.all([
    api.bookingDetails.getLoyaltyByReference({
      params: {
        referenceNo: String(booking.body.referenceNo),
      },
    }),
  ]);

  if (loyalty?.status === 404) {
    return <></>;
  }

  if (loyalty?.status !== 200) {
    throw new Error(`Error: ${loyalty.status}`);
  }

  const { provider, customerIdentifier, value, valueType } = loyalty.body;

  return (
    <LoyaltyCard>
      <CardContent className="flex w-full flex-col items-center p-0">
        <div className="flex w-full p-4 text-sm">
          <div className="flex w-full flex-col gap-y-2">
            <span className=" text-slate-500">Loyalty program</span>
            <span className=" text-slate-900 ">{provider}</span>
          </div>
          <div className="flex w-full flex-col gap-y-2">
            <span className=" text-slate-500">Account number</span>
            <span className=" text-slate-900 ">{customerIdentifier}</span>
          </div>
          <div className="flex w-full flex-col gap-y-2">
            <span className=" text-slate-500">Points/Miles</span>
            <span className=" text-slate-900 ">{value ?? valueType}</span>
          </div>
        </div>
      </CardContent>
    </LoyaltyCard>
  );
}
