import { api } from "@/api";
import { auth } from "@/auth";
import BranchDashboard from "./dashboard";
import type { IBranch } from "@/api/contracts/branch-contract";

const DashboardPage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const session = await auth();
  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const paramsValue = await params;
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse.find((branch) => branch.id === Number(paramsValue.id));

  return <BranchDashboard branch={branch} session={session} />;
};

export default DashboardPage;
