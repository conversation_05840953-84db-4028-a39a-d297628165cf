"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
type CashRegisterRow = {
  item: string;
  cash: string;
  pos: string;
  bankTransfer: string;
  total: string;
};

type ColumnMessageKey = "item" | "cash" | "pos" | "bankTransfer" | "total";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("CashRegister");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<CashRegisterRow>[] = [
  {
    accessorKey: "item",
    header: () => <Message messageKey="item" />,
  },
  {
    accessorKey: "cash",
    header: () => <Message messageKey="cash" />,
    cell: ({ row }) => {
      const amount = row.getValue<string>("cash");
      return amount === "N/A" ? amount : amount;
    },
  },
  {
    accessorKey: "pos",
    header: () => <Message messageKey="pos" />,
    cell: ({ row }) => {
      const amount = row.getValue<string>("pos");
      return amount === "N/A" ? amount : amount;
    },
  },
  {
    accessorKey: "bankTransfer",
    header: () => <Message messageKey="bankTransfer" />,
    cell: ({ row }) => {
      const amount = row.getValue<string>("bankTransfer");
      return amount === "N/A" ? amount : amount;
    },
  },
  {
    accessorKey: "total",
    header: () => <Message messageKey="total" />,
    cell: ({ row }) => row.getValue<string>("total"),
  },
];

type Balance = {
  cashAmount: string;
  posAmount: string;
  bankTransferAmount: string;
};

export const transformBalanceToRow = (item: string, balance: Balance | undefined, total?: string): CashRegisterRow => {
  if (!balance) {
    return {
      item,
      cash: "0",
      pos: "0",
      bankTransfer: "0",
      total: total || "0",
    };
  }

  return {
    item,
    cash: balance.cashAmount,
    pos: balance.posAmount,
    bankTransfer: balance.bankTransferAmount,
    total:
      total || (Number(balance.cashAmount) + Number(balance.posAmount) + Number(balance.bankTransferAmount)).toString(),
  };
};
