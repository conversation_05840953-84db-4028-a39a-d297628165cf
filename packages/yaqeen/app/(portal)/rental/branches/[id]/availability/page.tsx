// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */
// @ts-nocheck
import { api } from "@/api";
import { Suspense } from "react";
import GroupAvailability from "../components/GroupAvailability";
import { formatDateTime } from "@/lib/utils";

const getFormattedAudit = ({
  updatedAt,
  updatedBy,
}: {
  /** TimeStamp e.g '1724835276' */
  updatedAt: number;
  updatedBy: string | undefined;
}) => {
  const formattedUpdatedAt = formatDateTime(updatedAt);
  const updatedByText = updatedBy ? `by ${updatedBy}` : "";
  return `Last update: ${formattedUpdatedAt} ${updatedByText}`;
};

export default async function GroupAvailabilityPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;

  const [{ body: branch }, { body: currentAvailability }, { body: extendedAvailability }] = await Promise.all([
    api.branch.getBranch({
      params: { id },
      cache: "no-cache",
    }),
    api.branch.fetchCurrentGroupAvailabilityByBranchId({
      params: { id },
    }),
    api.branch.fetchExtendedGroupAvailabilityByBranchId({
      params: { id },
    }),
  ]);

  const [currentUserDetails, extendedUserDetails] = await Promise.all([
    currentAvailability.lastUpdatedBy
      ? api.branch.fetchUserDetailsByID({
          params: { id: currentAvailability.lastUpdatedBy },
        })
      : null,
    extendedAvailability.lastUpdatedBy
      ? api.branch.fetchUserDetailsByID({
          params: { id: extendedAvailability.lastUpdatedBy },
        })
      : null,
  ]);

  const formattedCurrentAudit = getFormattedAudit({
    updatedAt: currentAvailability.lastUpdatedOn,
    updatedBy: currentUserDetails?.firstName
      ? `${currentUserDetails?.firstName} ${currentUserDetails?.lastName}`
      : currentUserDetails?.email,
  });

  const formattedExtendedAudit = getFormattedAudit({
    updatedAt: extendedAvailability.lastUpdatedOn,
    updatedBy: extendedUserDetails?.firstName
      ? `${extendedUserDetails?.firstName} ${extendedUserDetails?.lastName}`
      : extendedUserDetails?.email,
  });

  const formattedAvailableGroupsDetails = {
    currentAvailabilityFormatted: {
      groups: currentAvailability.groupAvailability.map((el) => {
        return {
          id: el?.id,
          label: el?.group?.code,
          subGroups: [
            {
              label: `${el.group?.displayName}`,
              value: el?.group.id,
              selected: el?.available,
            },
            ...el.virtualGroupsAvailability.map((sub) => {
              return {
                label: `${sub.group?.displayName}`,
                value: sub?.group.id,
                selected: sub?.available,
              };
            }),
          ],
        };
      }),
      formattedAudit: formattedCurrentAudit,
    },
    extendedAvailabilityFormatted: {
      groups: extendedAvailability.groupAvailability.map((el) => {
        return {
          id: el?.id,
          label: el?.group?.code,
          subGroups: [
            {
              label: `${el.group?.displayName}`,
              value: el?.group.id,
              selected: el?.available,
            },
            ...el.virtualGroupsAvailability.map((sub) => {
              return {
                label: `${sub?.group?.displayName}`,
                value: sub?.group.id,
                selected: sub?.available,
              };
            }),
          ],
        };
      }),
      formattedAudit: formattedExtendedAudit,
    },
  };

  let pauseAvailability;

  try {
    pauseAvailability = await api.branch.fetchPauseByBranchId({
      params: { id },
    });
  } catch (error) {
    console.error("no Pause Record");
  }

  const { currentAvailabilityFormatted, extendedAvailabilityFormatted } = formattedAvailableGroupsDetails;

  return (
    <div className="flex flex-col">
      <Suspense>
        <GroupAvailability
          branch={branch}
          currentAvailability={currentAvailabilityFormatted}
          extendedAvailability={extendedAvailabilityFormatted}
          pauseAvailability={pauseAvailability}
        />
      </Suspense>
    </div>
  );
}
