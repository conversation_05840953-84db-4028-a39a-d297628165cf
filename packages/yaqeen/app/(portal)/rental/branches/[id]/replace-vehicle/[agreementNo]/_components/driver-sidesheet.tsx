import { <PERSON>rollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Buildings, EnvelopeSimple, Phone, Clock, CalendarMinus, Key, EyeSlash } from "@phosphor-icons/react/dist/ssr";
import { api } from "@/api";
import { differenceInDays, format } from "date-fns";
import { getBadgeColor, getFullDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import CopyLink from "@/components/customized/copy-link";
import OpenLink from "@/components/customized/open-link";
import { Fragment } from "react";
import { type IDocument } from "../../../bookings/[bookingId]/types";
import { OpenLinkDriver } from "./OpenLinkDriver";

interface DriverProfileSheetProps {
  isOpen: boolean;
  driverUid: string;
}

export async function DriverProfileSheet({ driverUid }: DriverProfileSheetProps) {
  const driverResponse = await api.driverDetails.getDriverById({
    query: {
      driverUid: driverUid,
    },
  });

  if (driverResponse?.status !== 200) {
    throw new Error(`Error: ${driverResponse.status}`);
  }

  const driver = driverResponse.body;

  const {
    firstName = "",
    lastName = "",
    countryCode = 966,
    mobileNumber = "",
    email = "",
    dob = "",
    hijrahDob = "",
    idType = "",
    nationality = { code: 0, name: { en: "N/A" } },
  } = driver;

  const bookingResponse = await api.booking.getBookingSearch({
    query: {
      driverUid: driverUid,
      size: 5,
    },
  });

  if (bookingResponse?.status !== 200) {
    throw new Error(`Error: ${bookingResponse.status}`);
  }

  const bookings = bookingResponse.body.data;

  const bookingCountResponse = await api.driverDetails.getBookingCountByDriverUid({
    params: {
      driverUid: driverUid,
    },
  });

  let bookingCount = {
    totalAgreementCount: 0,
    totalNoShowCount: 0,
  };

  if (bookingCountResponse?.status === 200) {
    bookingCount = bookingCountResponse.body;
  }

  const age = dob ? `${(differenceInDays(new Date(), new Date(dob)) / 365)?.toFixed(0)}` : "N/A";

  const license = driver?.documents.find((doc) => doc.type === "LICENSE") ?? {
    code: 0,
    type: "LICENSE",
    documentNo: "XXX",
    expiry: "",
    hijrahExpiry: "",
    issuedPlace: { code: 0, name: { en: "" } },
  };

  let document: IDocument = {} as IDocument;

  switch (idType) {
    case "SAUDI_NATIONAL":
    case "GCC":
      document = driver.documents.find((doc) => doc.type === "ID" || doc.type === "NATIONAL_ID") ?? document;
      break;
    case "RESIDENT":
      document = driver.documents.find((doc) => doc.type === "IQAMA") ?? document;
      break;
    case "VISITOR":
      document = driver.documents.find((doc) => doc.type === "PASSPORT") ?? document;
      break;
  }

  const licenseCountry = license.issuedPlace?.name?.en ?? "N/A";

  const licenseExpiryDate = getFullDate(license.expiry, license.hijrahExpiry);

  const phone = mobileNumber ? `+${countryCode} ${mobileNumber}` : "N/A";

  const agreements = bookingCount?.totalAgreementCount;
  const noShows = bookingCount?.totalNoShowCount;
  const companyName = "N/A";

  return (
    <>
      {/* Header Section */}
      <div className="flex items-start justify-between px-6 pb-4 shadow">
        <div className="space-y-1">
          <div className="flex gap-2">
            <CopyLink />
            <OpenLink url={`#`} className="flex items-center gap-2" />
          </div>
          <div className="flex items-center gap-2">
            <h2 className="text-2xl font-bold">{`${firstName} ${lastName}`}</h2>
          </div>
        </div>
      </div>

      <ScrollArea
        className="hide-scrollbar h-full p-0 pb-24
        "
      >
        {/* Contact Information */}
        <div className="mb-6 space-y-4 px-6 pt-6">
          <div className="flex items-center gap-3">
            <Phone className="h-5 w-5" />
            <span>{phone ?? "N/A"}</span>
          </div>
          <div className="flex items-center gap-3">
            <EnvelopeSimple className="h-5 w-5" />
            <span>{email ?? "N/A"}</span>
          </div>
          {false && (
            <div className="flex items-center gap-3">
              <Buildings className="h-5 w-5" />
              <span>{companyName ?? "N/A"}</span>
            </div>
          )}
        </div>

        <Separator className="my-6" />

        {/* Statistics */}
        <div className="mb-6 flex gap-6 px-6">
          <div className="flex items-center gap-2">
            <Key className="h-5 w-5 " />
            <span>{agreements ?? "N/A"} agreements</span>
          </div>
          <div className="flex items-center gap-2">
            <EyeSlash className="h-5 w-5 " />
            <span>{noShows ?? "N/A"} No show</span>
          </div>
        </div>

        <Separator className="my-6 h-1" />

        {/* Driver Information */}
        <div className="mb-6 space-y-4 px-6">
          <h3 className="text-lg font-semibold">Driver information</h3>

          <div className="space-y-2">
            <p className="text-sm">Nationality</p>
            <p>{nationality?.name?.en ?? "N/A"}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="text-sm">ID number</p>
              <p>{document.documentNo ?? "N/A"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm">ID code</p>
              <p>{document.id ?? "N/A"}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="text-sm">Date of birth</p>
              <div>
                <p>{dob ? `${dob} CE` : "N/A"}</p>
                <p>{hijrahDob ? `${hijrahDob} AH` : "N/A"}</p>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm">Age</p>
              <p>{age ?? "N/A"}</p>
            </div>
          </div>
        </div>

        <Separator className="my-6 h-1" />

        {/* Driver License */}
        <div className="mb-6 space-y-4 px-6">
          <h3 className="text-lg font-semibold">Driver license</h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="text-sm">Origin</p>
              <p>{licenseCountry ?? "N/A"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm">License number</p>
              <p>{license.documentNo ?? "N/A"}</p>
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm">Expiry date</p>
            <p>{licenseExpiryDate.gregorean ? licenseExpiryDate.gregorean + " CE" : "N/A"}</p>
            <p>{licenseExpiryDate.hijri ? licenseExpiryDate.hijri + " AH" : "N/A"}</p>
          </div>
        </div>

        <Separator className="my-6 h-1" />

        {/* Last Bookings */}
        <div className="space-y-4 ">
          <h3 className="px-6 text-lg font-semibold">Last 5 bookings</h3>

          {bookings.length ? (
            bookings.map((booking) => {
              const days = differenceInDays(
                new Date(booking.dropOffDateTime * 1000),
                new Date(booking.pickupDateTime * 1000)
              );
              const _bookingDate = new Date(booking.bookingDateTime * 1000);
              const bookingDate = format(_bookingDate, "dd-MM-yyyy");
              return (
                <Fragment key={booking.dropOffDateTime + booking.pickupDateTime}>
                  <div className="space-y-4 px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-base font-bold">
                        <span>{booking.bookingNo ?? "N/A"}</span>
                        <Badge
                          variant="secondary"
                          className={`mx-2 rounded-full px-3 font-normal capitalize ${getBadgeColor(booking?.status ?? "UPCOMING")}`}
                        >
                          {booking?.status ?? "Upcoming"}
                        </Badge>
                      </div>
                      <OpenLinkDriver bookingId={booking.id.toString()} />
                    </div>
                    <div className="flex gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <CalendarMinus className="size-5" />
                        <span>{bookingDate ?? "N/A"}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="size-5" />
                        <span>{days ? days + " day(s)" : "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <Separator className="my-4" />
                </Fragment>
              );
            })
          ) : (
            <p>No booking information available</p>
          )}
        </div>
      </ScrollArea>
    </>
  );
}
