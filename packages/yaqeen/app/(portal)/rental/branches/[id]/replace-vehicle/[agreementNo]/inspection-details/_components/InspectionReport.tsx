import { api } from "@/api";
import { Info, InfoIcon } from "lucide-react";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { type z } from "zod";
import { type vehicleInspectionDetailsSchema } from "@/api/contracts/booking/booking-contract";
import { MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import Penalties from "./Penalties";
import { getTranslations } from "next-intl/server";
import type { CalculatePrice } from "@/api/contracts/booking/schema";
import { KmReadingDialog } from "./KmReading";
import { ImageDialog } from "./ImageDialog";
import clsx from "clsx";
import TrafficFines from "./TrafficFines";

type VehicleInspectionDetails = z.infer<typeof vehicleInspectionDetailsSchema>;

type InspectionSectionProps = {
  title: string;
  inspection: VehicleInspectionDetails["checkoutInspection"];
};

const InspectionSection = async ({ title, inspection }: InspectionSectionProps) => {
  const t = await getTranslations("closeAgreement");
  if (!inspection) {
    return (
      <div className="flex h-full w-full flex-col">
        <h3 className="mb-6 text-lg font-semibold">{title}</h3>
        <div className="flex h-full flex-col items-center justify-center gap-2 bg-slate-50 py-32">
          <MagnifyingGlass weight="regular" fontSize={64} className="text-slate-200" />
          <div className="text-slate-500">{t("No inspection details")}</div>
        </div>
      </div>
    );
  }
  return (
    <div className="w-full space-y-6">
      <h3 className="text-lg font-semibold">{title}</h3>

      {inspection?.images?.length > 0 ? (
        <>
          {inspection?.images?.[0]?.imageUrl ? (
            <ImageDialog src={inspection.images[0].imageUrl} alt="Inspection image" width={342} height={450} />
          ) : null}
          <div className="flex gap-2">
            {inspection.images.slice(1).map((image, index) => (
              <div key={`${image.typeId}-${index}`} className="relative aspect-square overflow-hidden rounded-lg">
                <ImageDialog src={image.imageUrl} alt={`Inspection image ${image.typeId}`} width={64} height={64} />
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="flex w-full flex-col">
          <div className="flex h-full flex-col items-center justify-center gap-2 bg-slate-50 py-32">
            <MagnifyingGlass weight="regular" fontSize={64} className="text-slate-200" />
            <div className="text-slate-500">{t("No inspection details")}</div>
          </div>
        </div>
      )}
      {inspection?.remark && (
        <div>
          <div className="mb-1 flex">
            <div>{t("Inspector's remarks")}</div>
            {inspection?.newDamage ? <div>{t("New Damage")}</div> : null}
          </div>
          <p className="rounded-lg bg-slate-100 p-2 text-slate-700">{inspection?.remark ?? "-"}</p>
        </div>
      )}
    </div>
  );
};

export const InspectionReport = async ({
  displayOnly = false,
  priceResponse,
  agreement,
}: {
  displayOnly?: boolean;
  priceResponse: CalculatePrice;
  agreement: AgreementInvoice;
}) => {
  const t = await getTranslations("closeAgreement");
  const vehicleInspection = await api.booking.getVehicleInspectionDetails({
    params: {
      agreementVehicleId: agreement.agreementVehicleId,
    },
  });

  const { driverExpenses } = priceResponse;
  const insuranceDeductible = priceResponse.tariffDetail?.insuranceDeductible ?? "";

  const extraKMCharges = driverExpenses?.find((expense) => expense.expenseType === "EXTRA_KM_CHARGES");
  const extraFuelCharges = driverExpenses?.find((expense) => expense.expenseType === "EXTRA_FUEL_CHARGES");

  if (vehicleInspection.status !== 200) {
    return null;
  }

  const data = vehicleInspection.body;

  const { checkinFuel, checkinKm, checkoutFuel, checkoutKm, checkoutInspectionRefId, checkinInspectionRefId } =
    data.vehicleInspectionDetails;

  const fuelDifference = (checkoutFuel ?? 0) - (checkinFuel ?? 0);
  return (
    <main className="flex w-full gap-6">
      <section className="flex grow flex-col gap-y-6">
        {/* TODO: place card for replace reason  */}
        {/* TODO: place card for vehicle status  */}
        <Card className="flex flex-col shadow">
          {!displayOnly && (
            <CardHeader>
              <CardTitle className="text-lg">{t("Inspection Report")}</CardTitle>
            </CardHeader>
          )}
          <Separator className="mb-4" />
          <CardContent className="flex h-full gap-4">
            <InspectionSection title={t("Pickup Inspection")} inspection={data.checkoutInspection} />
            <div className="h-auto w-px shrink-0 bg-slate-100" />
            <InspectionSection title={t("Drop-off Inspection")} inspection={data.checkinInspection} />
          </CardContent>
        </Card>
        {
          <Card className="flex flex-col shadow">
            <CardHeader className="px-4">
              <CardTitle className="text-lg">{t("Dashboard readings")}</CardTitle>
            </CardHeader>
            <CardContent className="w-full p-0">
              <table className="w-full text-sm">
                <thead className="w-full bg-slate-100 px-4 text-start">
                  <tr>
                    <th className="px-4 py-2 text-start font-light">{t("Item")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Pickup")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Drop-off")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Difference")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Extra Charge (SAR)")}</th>
                    <th className="px-4 py-2 text-start font-light"> </th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                  <tr>
                    <td className="px-4 py-2">
                      <div>
                        <h3 className="font-semibold">{t("KMs")}</h3>
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      {checkoutKm ? (
                        <span>
                          {checkoutKm} {t("KM")}
                        </span>
                      ) : (
                        <span>-</span>
                      )}
                    </td>
                    {checkinKm ? (
                      <td className="px-4 py-2">
                        {checkinKm} {t("KM")}
                      </td>
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}
                    {checkinKm ? (
                      <td className="px-4 py-2">
                        {(checkinKm ?? 0) - (checkoutKm ?? 0)} {t("KM")}
                      </td>
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}

                    <td
                      className={clsx(`px-4 py-2`, {
                        "line-through": extraKMCharges?.waiveOff,
                      })}
                    >
                      {extraKMCharges?.totalSum ?? 0} {t("SAR")}
                    </td>
                    <td className="px-4 py-2">
                      {!displayOnly && (
                        <KmReadingDialog
                          checkinInspectionRefId={checkinInspectionRefId}
                          checkoutInspectionRefId={checkoutInspectionRefId}
                          checkinFuel={checkinFuel ?? 0}
                          extraKMCharges={extraKMCharges?.totalSum}
                          checkinKm={checkinKm ?? 0}
                          checkoutFuel={checkoutFuel ?? 0}
                          checkoutKm={checkoutKm ?? 0}
                          type="KMs"
                          agreementVehicleId={agreement.agreementVehicleId}
                        />
                      )}
                    </td>
                  </tr>
                  {extraKMCharges?.waiveOff && extraKMCharges?.waiveOffReason && (
                    <tr>
                      <td colSpan={5}>
                        <div className="mx-2 flex w-full items-center gap-2 rounded-sm border border-solid px-2">
                          <InfoIcon className="h-4 w-4 text-gray-500" />
                          <div className="px-4 py-2 text-left font-light">
                            {t("Waive reason")}: {extraKMCharges?.waiveOffReason}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                  <tr>
                    <td className="px-4 py-2">
                      <div>
                        <h3 className="font-semibold">{t("Fuel Level")}</h3>
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      {checkoutFuel !== null && checkoutFuel !== undefined ? (
                        <span>{checkoutFuel === 4 ? `${checkoutFuel}/4 (Full)` : `${checkoutFuel}/4`}</span>
                      ) : (
                        <span>-</span>
                      )}
                    </td>
                    {checkinFuel !== null && checkinFuel !== undefined ? (
                      <td className="px-4 py-2">
                        {checkinFuel === 4 ? `${checkinFuel}/4 (Full)` : `${checkinFuel}/4`}
                      </td>
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}
                    {checkinFuel !== null && checkinFuel !== undefined ? (
                      fuelDifference === 0 ? (
                        <td className="px-4 py-2">
                          <div className="text-gray-500">Same</div>
                        </td>
                      ) : (
                        <td className="px-4 py-2">{fuelDifference}</td>
                      )
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}
                    <td
                      className={clsx(`px-4 py-2`, {
                        "line-through": extraFuelCharges?.waiveOff,
                      })}
                    >
                      {extraFuelCharges?.totalSum ?? 0} {t("SAR")}
                    </td>
                    <td className="px-4 py-2">
                      {!displayOnly && (
                        <KmReadingDialog
                          checkoutInspectionRefId={checkoutInspectionRefId}
                          checkinInspectionRefId={checkinInspectionRefId}
                          checkinFuel={checkinFuel ?? 0}
                          checkinKm={checkinKm ?? 0}
                          checkoutFuel={checkoutFuel ?? 0}
                          checkoutKm={checkoutKm ?? 0}
                          extraFuelCharges={extraFuelCharges?.totalSum}
                          type="Fuel"
                          agreementVehicleId={agreement.agreementVehicleId}
                        />
                      )}
                    </td>
                  </tr>
                  {extraFuelCharges?.waiveOff && extraFuelCharges?.waiveOffReason && (
                    <tr>
                      <td colSpan={5}>
                        <div className="mx-2 flex w-full items-center gap-2 rounded-sm border border-solid px-2">
                          <InfoIcon className="h-4 w-4 text-gray-500" />
                          <div className="px-4 py-2 text-left font-light">
                            {t("Waive reason")}: {extraFuelCharges?.waiveOffReason}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </CardContent>
          </Card>
        }
        {/* Damages/Penalties */}
        <Card>
          <CardContent>
            <div className="max-w-3xl py-6">
              <h2 className="mb-6 text-2xl font-semibold">{t("Damages/Penalties")}</h2>

              <Card className="mb-6 p-4">
                <div className="flex items-start gap-3">
                  <Info className="mt-0.5 h-5 w-5 text-muted-foreground" />
                  <div>
                    {priceResponse.includedComprehensiveInsurance ? (
                      <div className="mb-1 font-medium">{t("Comprehensive insurance selected")}</div>
                    ) : (
                      <div className="mb-1 font-medium">{t("Basic insurance selected")}</div>
                    )}
                    <p className="text-sm text-muted-foreground">
                      {t("Pay up to")}
                      <span className="pl-1">{t("SAR")}</span>
                      <span className="px-1">{insuranceDeductible}</span>
                      {t("with a police report")}
                    </p>
                  </div>
                </div>
              </Card>

              <Penalties
                insuranceDeductible={Number(insuranceDeductible)}
                comp={priceResponse.includedComprehensiveInsurance}
                displayOnly={displayOnly}
                agreementVehicleId={agreement.agreementVehicleId}
              />
            </div>
          </CardContent>
        </Card>

        {/* Traffic Fines */}

        <TrafficFines agreementNumber={agreement.agreementNo} />
      </section>
    </main>
  );
};
