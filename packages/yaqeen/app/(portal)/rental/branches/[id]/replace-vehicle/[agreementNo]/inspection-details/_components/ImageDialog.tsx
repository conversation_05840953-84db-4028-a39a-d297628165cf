"use client";

import { useState } from "react";
import { VisuallyHidden } from "radix-ui";
import Image from "next/image";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { shimmer, toBase64 } from "@/lib/utils";

interface ImageDialogProps {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
}

export const ImageDialog = ({ src, alt = "Image", width = 350, height = 400 }: ImageDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className="relative cursor-pointer overflow-hidden rounded-md" onClick={() => setIsOpen(true)}>
        <Image
          src={src}
          alt={alt}
          width={0}
          height={0}
          sizes="100vw"
          style={{ width: width, height: height }}
          className="object-cover transition-transform hover:scale-105"
          placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(width, height))}`}
          onError={(e) => {
            e.currentTarget.src = "/static/<EMAIL>";
          }}
        />
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <VisuallyHidden.Root>
          <DialogTitle>Image</DialogTitle>
        </VisuallyHidden.Root>
        <DialogContent className="max-w-3xl overflow-hidden p-0">
          <div className="relative w-full">
            <Image
              src={src}
              alt={alt}
              className="h-full w-full object-contain"
              width={1200}
              height={800}
              onError={(e) => {
                e.currentTarget.src = "/static/<EMAIL>";
              }}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
