"use client";

import { useState, useTransition } from "react";
import { Info } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { PencilSimple } from "@phosphor-icons/react/dist/ssr";
import { useParams } from "next/navigation";
import { toast } from "@/lib/hooks/use-toast";
import { createInspection, revalidateClientPath, updateInspection, waveOffExtraCharge } from "@/lib/actions";
import { useLocale, useTranslations } from "next-intl";
import clsx from "clsx";

export function KmReadingDialog({
  checkinInspectionRefId,
  checkoutInspectionRefId,
  agreementVehicleId,
  checkinFuel,
  checkinKm,
  checkoutFuel,
  checkoutKm,
  extraKMCharges,
  extraFuelCharges,
  type,
}: {
  checkinInspectionRefId: number | undefined;
  checkoutInspectionRefId: number;
  agreementVehicleId: number;
  checkinFuel: number;
  checkinKm: number;
  checkoutFuel: number;
  checkoutKm: number;
  extraKMCharges?: number;
  extraFuelCharges?: number;
  type: "KMs" | "Fuel";
}) {
  const params = useParams();
  const agreementNo = params.agreementNo as string;
  const branchId = params.id as string;
  const [isLoading, setIsLoading] = useState(false);

  const t = useTranslations("closeAgreement");
  const locale = useLocale() as "en" | "ar";

  const [pending, startTransition] = useTransition();

  // Note: pickup values refer to checkin and drop values refer to checkout
  const [checkinReading, setCheckinReading] = useState(checkinKm);
  const [checkinFuelReading, setCheckinFuelReading] = useState(checkinFuel);
  const [checkoutReading, setCheckoutReading] = useState(checkoutKm);
  const [checkoutFuelReading, setCheckoutFuelReading] = useState(checkoutFuel);
  const [waiveCharge, setWaiveCharge] = useState(false);
  const [waiveReason, setWaiveReason] = useState("other");
  const [description, setDescription] = useState("");
  const [hasApproval, setHasApproval] = useState(false);

  const [open, onOpenChange] = useState(false);

  const handleSaveChanges = async () => {
    if (!agreementVehicleId || !agreementNo) {
      toast({
        title: "Error",
        description: "Missing required information",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // inspectionType 1 for checkin when car is dropped
    // inspectionType 2 for checkout when customer takes the car
    try {
      startTransition(async () => {
        try {
          const updatedObject = {
            agreementNo,
            agreementVehicleId,
          };
          if (type === "KMs" && checkinInspectionRefId && checkinReading) {
            await updateInspection({
              ...updatedObject,
              odometerReading: checkinReading,
              inspectionId: checkinInspectionRefId,
              fuelLevel: checkinFuel,
              inspectionType: 1,
              branchId,
            });
          }

          if (type === "Fuel" && checkinInspectionRefId && checkinFuelReading) {
            await updateInspection({
              inspectionType: 1,
              ...updatedObject,
              odometerReading: checkinKm,
              inspectionId: checkinInspectionRefId,
              fuelLevel: checkinFuelReading,
              branchId,
            });
          }

          if (type === "Fuel" && checkoutReading) {
            if (checkoutInspectionRefId) {
              await updateInspection({
                ...updatedObject,
                odometerReading: checkoutKm,
                fuelLevel: checkoutFuelReading,
                inspectionId: checkoutInspectionRefId,
                inspectionType: 2,
                branchId,
              });
            } else {
              await createInspection({
                ...updatedObject,
                inspectionType: 2,
                fuelLevel: checkoutFuelReading,
                branchId,
              });
            }
          }

          if (type === "KMs" && checkoutReading) {
            if (checkoutInspectionRefId) {
              await updateInspection({
                ...updatedObject,
                odometerReading: checkoutReading,
                fuelLevel: checkoutFuel,
                inspectionId: checkoutInspectionRefId,
                inspectionType: 2,
                branchId,
              });
            } else {
              await createInspection({
                ...updatedObject,
                odometerReading: checkoutReading,
                inspectionType: 2,
                branchId,
              });
            }
          }

          // If we have a checkout, but no checkin, create a checkin inspection
          if (type === "Fuel" && checkinFuelReading && !checkinInspectionRefId) {
            await createInspection({
              ...updatedObject,
              inspectionType: 1,
              fuelLevel: checkinFuelReading,
              branchId,
            });
          }

          // If we have a checkout, but no checkin, create a checkin inspection
          if (type === "KMs" && checkinReading && !checkinInspectionRefId) {
            await createInspection({
              ...updatedObject,
              inspectionType: 1,
              odometerReading: checkinReading,
              branchId,
            });
          }

          if (checkoutInspectionRefId && checkinInspectionRefId) {
            if (waiveCharge) {
              await waveOffExtraCharge(
                agreementNo,
                type === "Fuel" ? "EXTRA_FUEL_CHARGES" : "EXTRA_KM_CHARGES",
                true,
                waiveReason === "other" ? description : waiveReason
              );
            } else {
              await waveOffExtraCharge(agreementNo, type === "Fuel" ? "EXTRA_FUEL_CHARGES" : "EXTRA_KM_CHARGES", false);
            }
          }

          toast({
            title: "Success",
            description: "Inspection details updated successfully",
          });
        } catch (e) {
          const description = "Invalid Odometer reading";
          toast({
            title: "Error",
            description,
            variant: "destructive",
          });
        }
      });

      // Close the dialog
      onOpenChange(false);

      // Refresh the page to show updated data
      void revalidateClientPath(
        `/rental/branches/${params.id as string}/close-agreements/${agreementNo}/inspection-details`
      );
    } catch (error) {
      const description = "Invalid Odometer reading";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Full screen loading  */}
      {pending && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
          <div className="loader animate-spin">
            <svg
              className="h-16 w-16 animate-spin text-gray-200"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <circle cx="12" cy="12" r="10" strokeLinecap="round" />
            </svg>
          </div>
        </div>
      )}
      {/* Button to open the dialog */}
      <Button
        onClick={() => {
          onOpenChange(true);
        }}
        variant={"outline"}
        className="px-3"
      >
        <PencilSimple />
      </Button>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md overflow-hidden p-0 sm:max-w-lg">
          <div className="flex items-center justify-between border-b p-6">
            {type === "KMs" ? (
              <h2 className="text-xl font-semibold">{t("Edit KMs reading")}</h2>
            ) : (
              <h2 className="text-xl font-semibold">{t("Edit fuel level")}</h2>
            )}
          </div>

          <div className="p-6">
            <div className="mb-6 grid grid-cols-2 gap-4">
              {type === "KMs" && (
                <>
                  <div>
                    <Label htmlFor="checkout-reading" className="mb-2 block">
                      {t("Check-out KMs reading")}
                    </Label>
                    <div className="relative">
                      <Input
                        id="checkout-reading"
                        onChange={(e) => setCheckoutReading(Number(e.target.value))}
                        defaultValue={checkoutKm}
                        type="number"
                        className="pr-12"
                      />
                      <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center">
                        <span className="text-gray-500">{t("KM")}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="checkin-reading" className="mb-2 block">
                      {t("Check-in KMs reading")}
                    </Label>
                    <div className="relative">
                      <Input
                        id="checkin-reading"
                        type="number"
                        defaultValue={checkinReading}
                        onChange={(e) => setCheckinReading(Number(e.target.value))}
                        className="pr-12"
                      />
                      <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center">
                        <span className="text-gray-500">{t("KM")}</span>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
            {type === "Fuel" && (
              <div className="mb-6 grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="checkout-fuel" className={clsx("mb-2 block text-start")}>
                    {t("Check-out fuel level")}
                  </Label>
                  <div className="relative">
                    <Input
                      id="checkout-fuel"
                      onChange={(e) => setCheckoutFuelReading(Number(e.target.value))}
                      type="number"
                      min="0"
                      max="4"
                      defaultValue={checkoutFuel}
                      className="pe-12 text-start"
                    />
                    <div
                      className={clsx(
                        "pointer-events-none absolute inset-y-0 flex items-center",
                        locale === "ar" ? "left-3" : "right-3"
                      )}
                    >
                      <span className="text-gray-500">/4</span>
                    </div>
                  </div>
                </div>
                <div>
                  <Label htmlFor="checkin-fuel" className={clsx("mb-2 block text-start")}>
                    {t("Check-in fuel level")}
                  </Label>
                  <div className="relative">
                    <Input
                      id="checkin-fuel"
                      type="number"
                      min="0"
                      max="4"
                      defaultValue={checkinFuelReading}
                      onChange={(e) => setCheckinFuelReading(Number(e.target.value))}
                      className="pe-12 text-start"
                    />
                    <div
                      className={clsx(
                        "pointer-events-none absolute inset-y-0 flex items-center",
                        locale === "ar" ? "left-3" : "right-3"
                      )}
                    >
                      <span className="text-gray-500">/4</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="mb-6 rounded-md bg-slate-50 p-4">
              <div className="flex items-start justify-between">
                <div>
                  <div className="mb-1 text-gray-600">{t("Extra charge (Including VAT)")}</div>
                  <div className="flex items-center gap-1">
                    <span className="font-semibold">
                      {t("SAR")}
                      {type === "Fuel" ? (extraFuelCharges ?? 0) : (extraKMCharges ?? 0)}
                    </span>
                    <Info className="h-4 w-4 text-gray-500" />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="waive-charge"
                    checked={waiveCharge}
                    onCheckedChange={(checked) => setWaiveCharge(checked as boolean)}
                  />
                  <Label htmlFor="waive-charge">{t("Waive extra charge")}</Label>
                </div>
              </div>
            </div>

            {waiveCharge && (
              <>
                <div className="mb-6">
                  <h3 className="mb-4 text-lg font-semibold">{t("Waive reason")}</h3>
                  <RadioGroup value={waiveReason} onValueChange={setWaiveReason} className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="loyal" id="loyal" />
                      <Label htmlFor="loyal">{t("Loyal customer")}</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="complementary" id="complementary" />
                      <Label htmlFor="complementary">{t("Complementary waive")}</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="other" id="other" />
                      <Label htmlFor="other">{t("Other reason")}</Label>
                    </div>
                  </RadioGroup>
                  <div className="mt-4">
                    <Input value={description} onChange={(e) => setDescription(e.target.value)} className="w-full" />
                  </div>
                </div>

                <div className="mb-6 flex items-center space-x-2">
                  <Checkbox
                    id="approval"
                    checked={hasApproval}
                    onCheckedChange={(checked) => setHasApproval(checked as boolean)}
                    className="data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500"
                  />
                  <Label htmlFor="approval">{t("I got approval from the branch supervisor/manager")}</Label>
                </div>
              </>
            )}
          </div>

          <div className="flex justify-end gap-3 border-t p-4">
            <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
              {t("Cancel")}
            </Button>
            <Button
              className="bg-[#a6d34e] text-black hover:bg-[#95c040]"
              onClick={handleSaveChanges}
              disabled={isLoading || (waiveCharge && !hasApproval)}
            >
              {isLoading ? t("Saving...") : t("Save changes")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
