"use client";

import { useState, useEffect, startTransition } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useProgressBar } from "@/components/progress-bar";

export function VehicleStatus() {
  const [status, setStatus] = useState("READY");
  const t = useTranslations("vehicle-status");
  const router = useRouter();
  const searchParams = useSearchParams();
  const progress = useProgressBar();

  useEffect(() => {
    // Get the vehicleStatus param from the URL and set it as the initial status
    const vehicleStatus = searchParams.get("vehicleStatus");
    if (vehicleStatus) {
      setStatus(vehicleStatus);
    }
  }, [searchParams]);

  const handleStatusChange = async (newStatus: string) => {
    setStatus(newStatus);
    // Start progress indicator
    progress.start();
    startTransition(async () => {
      try {
        // Update the URL with the new vehicleStatus parameter
        const params = new URLSearchParams(searchParams.toString());
        params.set("vehicleStatus", newStatus);
        await router.push(`?${params.toString()}`);
      } finally {
        // Stop progress indicator
        progress.done();
      }
    });
  };

  return (
    <div className="w-full rounded-lg border p-4">
      <div className="mb-4">
        <h2 className="mb-1 text-lg font-semibold">{t("title")}</h2>
        <p className="text-sm text-gray-500">{t("description")}</p>
      </div>

      <RadioGroup value={status} onValueChange={handleStatusChange} className="space-y-2">
        <div className="flex items-center py-2">
          <RadioGroupItem
            value="FUELING_CLEANING"
            id="needs-service"
            className="data-[state=checked]:border-blue-600 data-[state=checked]:bg-white"
          />
          <Label htmlFor="needs-service" className="ml-2 text-sm font-normal">
            {t("needs-service")}
          </Label>
        </div>
        <div className="flex items-center py-2">
          <RadioGroupItem
            value="WORKSHOP_TRANSFER"
            id="workshop"
            className="data-[state=checked]:border-blue-600 data-[state=checked]:bg-white"
          />
          <Label htmlFor="workshop" className="ml-2 text-sm font-normal">
            {t("workshop")}
          </Label>
        </div>
        <div className="flex items-center py-2">
          <RadioGroupItem
            value="READY"
            id="ready"
            className="data-[state=checked]:border-blue-600 data-[state=checked]:bg-white"
          />
          <Label htmlFor="ready" className="ml-2 text-sm font-normal">
            {t("ready")}
          </Label>
        </div>
      </RadioGroup>
    </div>
  );
}
