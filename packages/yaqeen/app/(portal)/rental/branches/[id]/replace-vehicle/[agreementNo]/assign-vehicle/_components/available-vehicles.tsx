"use client";
import { startTransition, useEffect, useMemo, useRef, useState } from "react";
import { useAtom } from "jotai";
import { groupBy } from "lodash-es";
import { ExternalLink, Loader2 } from "lucide-react";
import { Car } from "@phosphor-icons/react/dist/ssr";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { type Route } from "next";
import { useProgressBar } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { type Vehicle, VehicleCard } from "../../../../bookings/[bookingId]/assign-a-vehicle/_components/vehicle-card";
import { calculatePriceActionWalkin, lockVehicle } from "@/lib/actions";
import ClientPaginationControls from "@/components/ui/data-table/client-pagination-controls";
import { useReactTable } from "@tanstack/react-table";
import { getCoreRowModel } from "@tanstack/react-table";
import { getPaginationRowModel } from "@tanstack/react-table";
import { DataFilter } from "@/components/ui/data-filter";
import NoData from "@/components/ui/data-table/no-data";
import SelectedVehicle from "./selected-vehicle";
import { useToast } from "@/lib/hooks/use-toast";
import { useTranslations } from "next-intl";
import { buildGroupsFilters } from "../../../../bookings/utils";
import VehicleSkeleton from "../../../../bookings/[bookingId]/assign-a-vehicle/_components/vehicle-skeleton";
import { selectedCreatedVehicleAtom } from "../../../../bookings/create/atoms";

interface SuggestedVehiclesProps {
  vehicles: Vehicle[];
  preSelectedVehicle?: Vehicle | null;
}

interface Entity {
  totalCount: number;
  vehicle: Vehicle;
  vehicles: Vehicle[];
}

// Create a ReadyVehicles component for the side sheet
function ReadyVehicles({
  data,
  filters,
  onVehicleSelect,
  isLocking,
}: {
  data: Vehicle[];
  filters?: {
    groups: string[];
  };
  onVehicleSelect: (vehicle: Vehicle) => void;
  isLocking: boolean;
}) {
  const t = useTranslations("vehicles");
  const { groups } = useMemo(() => buildGroupsFilters(data), [data]);
  const [groupFilter, setGroupFilter] = useState<string[]>(filters?.groups ?? []);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  // Filter vehicles based on selected groups
  const filteredVehicles = useMemo(() => {
    if (groupFilter.length === 0) return data;
    return data.filter((vehicle) => groupFilter.includes(vehicle.model?.groupResponse?.id?.toString() ?? ""));
  }, [data, groupFilter]);

  const table = useReactTable<Vehicle>({
    data: filteredVehicles,
    columns: [],
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleGroupChange = (selectedGroups: string[]) => {
    setGroupFilter(selectedGroups);
    table.setPageIndex(0);
  };

  return (
    <div>
      <section className="flex h-full flex-col">
        <header className="flex items-center gap-2 border p-4">
          <DataFilter
            isMultiSelect
            filterKey="vehicleGroup"
            title={t("allGroups")}
            options={groups}
            value={groupFilter}
            onFilterChange={handleGroupChange}
          />
        </header>
        <section className="h-[calc(100vh-247px)] overflow-y-auto">
          {table.getRowModel().rows.map((row) => (
            <div className="border-b" key={row.original.plateNo}>
              <VehicleCard
                vehicleDetails={row.original}
                renderActionButton={(vehicleDetails) => (
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-lg"
                    disabled={isLocking}
                    onClick={() => onVehicleSelect(vehicleDetails)}
                  >
                    {isLocking ? (
                      <>
                        {t("selecting")} <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      t("select")
                    )}
                  </Button>
                )}
              />
            </div>
          ))}
          {!filteredVehicles.length && (
            <NoData
              icon={() => <Car className="h-12 w-12 text-slate-300" />}
              title={
                <>
                  <h1 className="text-3xl font-medium">{t("noResults")}</h1>
                  <p className="my-4 text-base font-normal">{t("modifyFilters")}</p>
                </>
              }
              callback={() => (
                <section className="justify-content-center flex items-center gap-2">
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      console.log("View all branch vehicles");
                    }}
                  >
                    {t("viewAllBranchVehicles")} <ExternalLink className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      setGroupFilter([]);
                    }}
                  >
                    {t("resetFilters")}
                  </Button>
                </section>
              )}
            />
          )}
        </section>
        <section className="border-t p-2">
          <ClientPaginationControls
            total={filteredVehicles.length}
            table={table}
            start={pagination.pageIndex * pagination.pageSize + 1}
            end={Math.min((pagination.pageIndex + 1) * pagination.pageSize, filteredVehicles.length)}
          />
        </section>
      </section>
    </div>
  );
}

export default function AvailableVehicles({ vehicles, preSelectedVehicle }: SuggestedVehiclesProps) {
  // Initialize translations
  const t = useTranslations("vehicles");
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const progress = useProgressBar();
  const { toast } = useToast();

  // Track the plate number of the vehicle being locked instead of a global loading state
  const [lockingPlateNo, setLockingPlateNo] = useState<string | null>(null);
  const [readyVehicleFilters, setReadyVehicleFilters] = useState<{ groups: string[] }>({ groups: [] });
  const [selectedVehicle, setSelectedVehicle] = useAtom(selectedCreatedVehicleAtom);
  const [isloading, setIsLoading] = useState(false);

  // Track the previous plateNo from URL to detect changes
  const prevPlateNoRef = useRef<string | null>(null);

  // State to control the visibility of available vehicles list
  const [showAvailableVehicles, setShowAvailableVehicles] = useState<boolean>(true);

  // State for the side sheet
  const [showReadyVehicleSheet, setShowReadyVehicleSheet] = useState(false);
  const [readyVehicles, setReadyVehicles] = useState<Vehicle[]>([]);

  useEffect(() => {
    // Get current plateNo from search params
    const currentPlateNo = searchParams.get("plateNo");

    // If the plate number has changed, turn off loading
    if (prevPlateNoRef.current !== currentPlateNo) {
      if (isloading) {
        setIsLoading(false);
      }
      // Update the reference
      prevPlateNoRef.current = currentPlateNo;
    }

    if (preSelectedVehicle) {
      if (preSelectedVehicle) {
        setSelectedVehicle(preSelectedVehicle);
      }
    }
  }, [searchParams, vehicles, selectedVehicle, setSelectedVehicle, isloading]);

  const representativeVehicles = useMemo(() => {
    const groupedVehicles = groupBy(vehicles, (v) => v.model!.id);
    return Object.values(groupedVehicles).map((vehicles) => ({
      vehicle: vehicles[0]!,
      vehicles: vehicles.slice(1),
      totalCount: vehicles.length - 1,
    }));
  }, [vehicles]);

  const { groups } = useMemo(() => buildGroupsFilters(vehicles), [vehicles]);
  const [groupFilter, setGroupFilter] = useState<string[]>([]);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  // Filter data based on selected groups
  const filteredVehicles = useMemo(() => {
    if (groupFilter.length === 0)
      return representativeVehicles.filter((v) => selectedVehicle?.plateNo !== v.vehicle.plateNo);
    return representativeVehicles.filter(
      (entity: Entity) =>
        groupFilter.includes(entity.vehicle.model?.groupResponse?.id?.toString() ?? "") &&
        selectedVehicle?.plateNo !== entity.vehicle.plateNo
    );
  }, [representativeVehicles, groupFilter, selectedVehicle]);

  // Create TanStack Table instance
  const table = useReactTable<Entity>({
    data: filteredVehicles,
    columns: [],
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleGroupChange = (selectedGroups: string[]) => {
    setGroupFilter(selectedGroups);
    table.setPageIndex(0);
  };

  // Handle opening and closing the ready vehicles sheet
  const onReadyVehicleSheetOpen = (open: boolean) => {
    setShowReadyVehicleSheet(open);
    if (!open) setReadyVehicleFilters({ ...readyVehicleFilters, groups: [] });
  };

  const handleViewMore = (vehicle: Vehicle, vehicles: Vehicle[]) => {
    const uniqueGroupIds = Array.from(
      new Set([vehicle, ...vehicles].map((v) => v.model?.groupResponse?.id?.toString() ?? ""))
    );

    setReadyVehicleFilters({ ...readyVehicleFilters, groups: uniqueGroupIds });
    setReadyVehicles(vehicles);
    setShowReadyVehicleSheet(true);
  };

  const generateQuote = async (vehicle: Vehicle, lockRef: string) => {
    setIsLoading(true);
    try {
      const currentPickupBranchId = Number(searchParams.get("pickupBranchId"));
      const currentDropOffBranchId = Number(searchParams.get("dropOffBranchId"));
      const currentPickupTimestamp = Number(searchParams.get("pickupTimestamp") || 0);
      const currentDropOffTimestamp = Number(searchParams.get("dropOffTimestamp") || 0);

      // Check if required parameters are present
      if (!currentDropOffBranchId || !currentDropOffTimestamp) {
        console.log("Skip quote generation - missing required parameters");
        return { quoteId: "", offerId: "" };
      }

      const vehiclePlateNo = vehicle.plateNo;
      const vehicleGroupId = vehicle.model?.groupResponse?.id?.toString() ?? "";

      const bodyData = {
        body: {
          pickupBranchId: currentPickupBranchId,
          dropOffBranchId: currentDropOffBranchId,
          pickupDateTime: currentPickupTimestamp,
          dropOffDateTime: currentDropOffTimestamp,
          ...(vehiclePlateNo ? { vehiclePlateNo } : {}),
          ...(vehicleGroupId ? { vehicleGroupId: Number(vehicleGroupId) } : {}),
          ...(lockRef ? { vehicleLockRef: String(lockRef) } : {}),
        },
      };

      const priceResponse = await calculatePriceActionWalkin(bodyData);

      if ("error" in priceResponse || priceResponse?.status !== 200) {
        const errorMessage =
          "error" in priceResponse
            ? t("errors.unexpectedError")
            : priceResponse?.status === 400
              ? priceResponse.body.desc || t("errors.unexpectedError")
              : t("errors.pricingFailure");

        toast({
          variant: "destructive",
          title: t("errors.pricingFailure"),
          description: errorMessage,
        });

        return { quoteId: "", offerId: "" };
      }

      if (priceResponse?.body?.quoteId) {
        return {
          quoteId: priceResponse.body.quoteId,
          offerId: priceResponse.body.offerId,
        };
      } else {
        toast({
          variant: "destructive",
          title: t("errors.pricingFailure"),
          description: t("errors.missingQuoteId"),
        });

        return { quoteId: "", offerId: "" };
      }
    } catch (error) {
      console.error("Error generating quote:", error);
      toast({
        variant: "destructive",
        title: t("errors.pricingFailure"),
        description: t("errors.unexpectedError"),
      });

      return { quoteId: "", offerId: "" };
    }
  };

  const handleVehicleSelect = async (vehicle: Vehicle) => {
    try {
      // Set loading state for the specific vehicle
      setLockingPlateNo(vehicle.plateNo);
      progress.start();

      // Step 1: Try to lock the vehicle
      const lockResponse = await lockVehicle(vehicle.plateNo);

      if (lockResponse.status !== 200 || !lockResponse.body.locked) {
        const errorMessage = t("errors.vehicleLockFailed");
        throw new Error(errorMessage);
      }

      // Vehicle locked successfully - prepare URL parameters
      const current = new URLSearchParams(Array.from(searchParams.entries()));

      // Step 2: Update URL params with vehicle information
      current.set("plateNo", vehicle.plateNo);
      current.set("model", `${vehicle.model?.make?.name?.en} ${vehicle.model?.name.en}`);
      current.set("modelAr", `${vehicle.model?.make?.name?.ar} ${vehicle.model?.name.ar}`);
      current.set("vehicleGroupId", vehicle.model?.groupResponse?.id?.toString() ?? "1");
      current.set("vehicleLockRef", lockResponse.body.reference);
      current.set("vehiclePrice", String(vehicle?.dailyPrice) ?? "0");
      current.set("plateNoAr", vehicle.plateNoAr ?? "");

      // Step 3: Generate quote only if drop-off details are available
      const hasDropOffDetails = Boolean(searchParams.get("dropOffBranchId") && searchParams.get("dropOffTimestamp"));

      if (hasDropOffDetails) {
        const { quoteId, offerId } = await generateQuote(vehicle, lockResponse.body.reference);

        if (quoteId && offerId) {
          current.set("quoteId", quoteId);
          current.set("offerId", offerId);
          console.log("Quote generated successfully:", { quoteId, offerId });
        }
      } else {
        console.log("Skipping quote generation - drop-off details not provided");
      }

      // Step 4: Update URL and state
      const query = `?${current.toString()}`;

      startTransition(() => {
        router.replace((pathname + query) as Route);
        setSelectedVehicle?.(vehicle);
        setShowReadyVehicleSheet(false);
        setShowAvailableVehicles(false);
        progress.done();

        toast({
          variant: "success",
          title: t("success.vehicleSelected"),
          description: vehicle.model?.name.en + " " + vehicle.plateNo + " Locked",
        });
      });
    } catch (error) {
      console.error("Error selecting vehicle:", error);

      // Extract error message, fallback to default if not available
      let errorMessage: string;

      if (error instanceof Error) {
        try {
          // Try to parse the error message as JSON
          const parsedError = JSON.parse(error.message);
          errorMessage = parsedError.desc || t("errors.defaultMessage");
        } catch {
          // If parsing fails, use the error message directly
          errorMessage = error.message || t("errors.defaultMessage");
        }
      } else {
        errorMessage = t("errors.defaultMessage");
      }

      toast({
        title: t("errors.title"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLockingPlateNo(null);
      progress.done();
    }
  };

  // Handle "Change Vehicle" button click
  const handleChangeVehicle = () => {
    setShowAvailableVehicles(true);
  };

  useEffect(() => {
    // This is to reset the selected vehicle when the component mounts and you come from the booking page
    // TODO: check if plateNo param is not available then setSelectedVehicle null
    return () => {
      setSelectedVehicle(null);
    };
  }, []);

  // Hide available vehicles if a vehicle is already selected on component mount
  useEffect(() => {
    if (selectedVehicle) {
      setShowAvailableVehicles(false);
    }
  }, [selectedVehicle]);

  if (isloading) {
    return (
      <>
        <VehicleSkeleton />
        <VehicleSkeleton />
        <VehicleSkeleton />
      </>
    );
  }

  return (
    <>
      {selectedVehicle && (
        <SelectedVehicle
          className="max-w-3xl overflow-hidden"
          preSelectedVehicle={selectedVehicle ?? null}
          customerPreference={""}
          groupCode={""}
          isEmpty={!selectedVehicle}
          isOnlyDowngrade={false}
          onChangeVehicle={handleChangeVehicle}
        />
      )}

      {showAvailableVehicles && (
        <Card className="max-w-3xl overflow-hidden !p-0">
          <CardHeader className="flex flex-row items-center justify-between border-b p-4">
            <section>
              <h2 className="text-lg font-semibold">{t("availableVehicles")}</h2>
              <p className="text-sm text-slate-500">
                {vehicles?.length} {t("readyVehicles")}
              </p>
            </section>
            <section className="flex h-full flex-col gap-2">
              <Sheet open={showReadyVehicleSheet} onOpenChange={onReadyVehicleSheetOpen}>
                <SheetContent side="right" className="w-full p-0 pt-6 ring-0 sm:w-[786px] sm:max-w-full">
                  <SheetHeader className=" px-4">
                    <SheetDescription>
                      <Button variant="outline" size="sm" disabled>
                        <ExternalLink className="mr-2 hidden h-4 w-4" />
                        {t("viewAllBranchVehicles")}
                      </Button>
                    </SheetDescription>
                    <SheetTitle className="py-4">
                      {t("readyVehicles")} {readyVehicles.length}
                    </SheetTitle>
                  </SheetHeader>
                  <ReadyVehicles
                    data={readyVehicles}
                    filters={readyVehicleFilters}
                    onVehicleSelect={handleVehicleSelect}
                    isLocking={!!lockingPlateNo}
                  />
                </SheetContent>
              </Sheet>

              <DataFilter
                isMultiSelect
                filterKey="vehicleGroup"
                title={t("allGroups")}
                options={groups}
                value={groupFilter}
                onFilterChange={handleGroupChange}
              />
            </section>
          </CardHeader>
          {table.getRowModel().rows.map((row) => {
            const entity = row.original;
            const vehicle = entity.vehicle;
            const isLocking = lockingPlateNo === vehicle.plateNo;
            return (
              <div className="border-b" key={vehicle.plateNo}>
                <VehicleCard
                  vehicleDetails={vehicle}
                  groupCount={entity.totalCount}
                  renderActionButton={(vehicleDetails) =>
                    vehicleDetails.preferenceType === "UPGRADE" ? (
                      <Button
                        variant="outline"
                        onClick={() => console.log("")}
                        className="flex h-auto items-center gap-2 rounded-md py-0"
                      >
                        {t("upgradeOptions")}
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-lg"
                        disabled={lockingPlateNo !== null}
                        onClick={async () => {
                          await handleVehicleSelect(vehicle);
                        }}
                      >
                        {isLocking ? (
                          <>
                            {t("selecting")} <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                          </>
                        ) : (
                          t("select")
                        )}
                      </Button>
                    )
                  }
                  onViewMore={() => handleViewMore(vehicle, entity.vehicles)}
                />
              </div>
            );
          })}
          {!representativeVehicles.length && (
            <NoData
              icon={() => <Car className="h-12 w-12 text-slate-300" />}
              title={
                <>
                  <h1 className="text-3xl font-medium">{t("noResults")}</h1>
                  <p className="my-4 text-base font-normal">{t("modifyFilters")}</p>
                </>
              }
              callback={() => (
                <section className="justify-content-center flex items-center gap-2">
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      console.log("View all branch vehicles");
                    }}
                  >
                    {t("viewAllBranchVehicles")} <ExternalLink className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      setGroupFilter([]);
                    }}
                  >
                    {t("resetFilters")}
                  </Button>
                </section>
              )}
            />
          )}
          <section className="border-t p-2">
            <ClientPaginationControls
              total={filteredVehicles.length}
              table={table}
              start={pagination.pageIndex * pagination.pageSize + 1}
              end={Math.min((pagination.pageIndex + 1) * pagination.pageSize, filteredVehicles.length)}
            />
          </section>
        </Card>
      )}
    </>
  );
}
