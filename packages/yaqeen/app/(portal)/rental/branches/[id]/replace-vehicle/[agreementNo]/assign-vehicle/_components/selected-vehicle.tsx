"use client";

import { cn } from "@/lib/utils";

import { Card, CardHeader } from "@/components/ui/card";
import { useAtom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { useSearchParams, usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";
import { type Route } from "next";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { selectedCreatedVehicleAtom } from "../../../../bookings/create/atoms";
import { Vehicle, VehicleCard } from "../../../../bookings/[bookingId]/assign-a-vehicle/_components/vehicle-card";
import { VehiclesEmptyState } from "../../../../bookings/[bookingId]/assign-a-vehicle/_components/vehicle-not-found";

interface SelectedVehicleProps {
  preSelectedVehicle: Vehicle | null;
  customerPreference: string;
  groupCode: string;
  isEmpty: boolean;
  isOnlyDowngrade: boolean;
  className?: string;
  onChangeVehicle?: () => void;
}

export default function SelectedVehicle({
  className,
  preSelectedVehicle,
  customerPreference,
  groupCode,
  isEmpty,
  isOnlyDowngrade,
  onChangeVehicle,
}: SelectedVehicleProps) {
  useHydrateAtoms([[selectedCreatedVehicleAtom, preSelectedVehicle]]);
  const t = useTranslations("vehicles");
  const [selectedVehicle] = useAtom(selectedCreatedVehicleAtom);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (!selectedVehicle) return;

    const params = new URLSearchParams(searchParams);
    const plateNo = params.get("plateNo");
    const vehicleGroupId = params.get("vehicleGroupId");

    if (!plateNo || !vehicleGroupId) {
      params.set("plateNo", selectedVehicle.plateNo);
      params.set("model", `${selectedVehicle.model?.make?.name?.en} ${selectedVehicle.model?.name.en}`);
      params.set("vehicleGroupId", "1");
      router.replace(`${pathname}?${params.toString()}` as Route);
    }
  }, [selectedVehicle, searchParams, pathname, router]);

  const vehicleCard = selectedVehicle ? (
    <VehicleCard
      vehicleDetails={selectedVehicle}
      isSelected
      renderActionButton={() => (
        <Button variant="outline" size="sm" className="rounded-lg" onClick={onChangeVehicle}>
          {t("changeVehicle")}
        </Button>
      )}
    />
  ) : (
    <VehiclesEmptyState
      customerPreference={customerPreference}
      groupCode={groupCode}
      hasRecommendedVehicle={!!preSelectedVehicle}
      isEmpty={isEmpty}
      isOnlyDowngrade={isOnlyDowngrade}
    />
  );

  return (
    <Card className={cn("!p-0", className)}>
      <CardHeader className="border-b p-4">
        <h2 className="text-lg font-semibold">{t("selectedVehicle")}</h2>
      </CardHeader>

      {vehicleCard}
    </Card>
  );
}
