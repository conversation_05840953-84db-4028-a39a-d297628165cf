
  // Generate 5 mock vehicles based on vehicle-card.tsx structure
  const now = Date.now();
  export const mockVehicles = [
    {
      plateNo: "ABC 123",
      plateNoAr: "١٢٣ ABC",
      odometerReading: 10000,
      fuelLevel: 6,
      model: {
        groupResponse: { code: "GRP1" },
        primaryImageUrl: "/static/<EMAIL>",
        make: { name: { en: "Toyota" } },
        name: { en: "Corolla" },
      },
      modelYear: 2020,
      preferenceType: "EXACT_MATCH",
      lastCleaned: now,
      lastInspected: now - 3600000,
      offer: { bookingPriceDifference: 0 },
      tags: ["RECOMMENDED"],
    },
    {
      plateNo: "DEF 456",
      plateNoAr: "٤٥٦ DEF",
      odometerReading: 20000,
      fuelLevel: 5,
      model: {
        groupResponse: { code: "GRP2" },
        primaryImageUrl: "/static/<EMAIL>",
        make: { name: { en: "Honda" } },
        name: { en: "Civic" },
      },
      modelYear: 2019,
      preferenceType: "SIMILAR",
      lastCleaned: now - 7200000,
      lastInspected: now - 3600000,
      offer: { bookingPriceDifference: 50 },
      tags: [],
    },
    {
      plateNo: "GHI 789",
      plateNoAr: "٧٨٩ GHI",
      odometerReading: 30000,
      fuelLevel: 4,
      model: {
        groupResponse: { code: "GRP1" },
        primaryImageUrl: "/static/<EMAIL>",
        make: { name: { en: "Toyota" } },
        name: { en: "Camry" },
      },
      modelYear: 2021,
      preferenceType: "UPGRADE",
      lastCleaned: now - 3600000,
      lastInspected: now - 1800000,
      offer: { bookingPriceDifference: 150 },
      tags: [],
    },
    {
      plateNo: "JKL 012",
      plateNoAr: "٠١٢ JKL",
      odometerReading: 40000,
      fuelLevel: 3,
      model: {
        groupResponse: { code: "GRP3" },
        primaryImageUrl: "/static/<EMAIL>",
        make: { name: { en: "Ford" } },
        name: { en: "Focus" },
      },
      modelYear: 2018,
      preferenceType: "DOWNGRADE",
      lastCleaned: now - 10800000,
      lastInspected: now - 5400000,
      offer: { bookingPriceDifference: -20 },
      tags: [],
    },
    {
      plateNo: "MNO 345",
      plateNoAr: "٣٤٥ MNO",
      odometerReading: 50000,
      fuelLevel: 7,
      model: {
        groupResponse: { code: "GRP2" },
        primaryImageUrl: "/static/<EMAIL>",
        make: { name: { en: "Honda" } },
        name: { en: "Accord" },
      },
      modelYear: 2022,
      preferenceType: "SIMILAR",
      lastCleaned: now - 1800000,
      lastInspected: now - 900000,
      offer: { bookingPriceDifference: 75 },
      tags: [],
    },
  ];