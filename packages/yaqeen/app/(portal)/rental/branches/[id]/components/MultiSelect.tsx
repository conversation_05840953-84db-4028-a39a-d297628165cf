"use client";

// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */

import React, { useState, useRef, useEffect } from "react";
import ReactSelect, { components, type OptionProps, type Props as ReactSelectProps } from "react-select";
import { X } from "@phosphor-icons/react/dist/ssr";
import { Separator } from "@/components/ui/separator";

interface SelectedOption {
  value: number | string;
  label: string;
}
export interface SelectOptionProps {
  id: string;
  label: string;
  selected: SelectedOption[];
}

const customStyles = {
  multiValueLabel: (def: any) => ({
    ...def,
    backgroundColor: "lightgray",
  }),
  multiValueRemove: (def: any) => ({
    ...def,
    backgroundColor: "lightgray",
  }),
  valueContainer: (base: any) => ({
    ...base,
    maxHeight: "65px",
    overflow: "auto",
  }),
  option: (styles: any, props: any) => {
    const { isSelected, isFocused } = props;
    return {
      ...styles,
      padding: 0,
      backgroundColor:
        isSelected && !isFocused
          ? null
          : isFocused && !isSelected
            ? styles.backgroundColor
            : isFocused && isSelected
              ? "#DEEBFF"
              : null,
      color: isSelected ? null : null,
      ":first-child": { marginBottom: "5px" },
    };
  },
  menu: (def: any) => ({ ...def, zIndex: 9999, width: 220 }),
  menuList: (def: any) => ({ ...def, maxHeight: 220 }),
  control: (def: any) => ({ ...def, width: 140, fontSize: "12px" }),
};

const customFilterOption = ({ value, label }: SelectedOption, input: string) => {
  // hide select all on search
  if (value === "*" && input !== "") {
    return false;
  }
  return label.toLowerCase().includes(input.toLowerCase());
};

const onKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
  if (e.key === " " || e.key === "Enter") {
    e.preventDefault();
  }
};

const MultiSelect = (
  props: Omit<ReactSelectProps, "onChange"> & {
    options: SelectOptionProps["selected"];
    value: SelectOptionProps["selected"];
    defaultLabel: string;
    groupId: string;
    onChange: (selected: SelectOptionProps) => void;
  }
) => {
  const { isDisabled, defaultLabel, groupId, options: parentOptions, onChange: parentOnChange } = props;
  const refSelect = useRef<HTMLDivElement>(null);
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const isAllSelected = useRef<boolean>(false);
  const selectAllLabel = useRef<string>("Select all");
  const allOption = { value: "*", label: selectAllLabel.current };

  const filteredOptions = parentOptions;
  const filteredSelectedOptions = props.value;
  const hideDropDown = filteredOptions.length === 1;

  useEffect(() => {
    const elem = refSelect.current;
    const triggerMenu = refSelect?.current?.querySelector(".trigger");

    const domClick = (event: any) => {
      if (elem?.contains(event.target) && !hideDropDown) {
        if (event.target.classList.contains("trigger-checkbox")) {
          return;
        }
        if (triggerMenu?.contains(event.target)) {
          setIsMenuOpen((prev) => !prev);
        }
      } else {
        setIsMenuOpen(false);
      }
    };
    document.addEventListener("mousedown", domClick);

    return () => {
      document.removeEventListener("mousedown", domClick);
    };
  }, []);

  const CustomPlaceholder = (props: any) => {
    const isSemi = !isAllSelected.current && filteredSelectedOptions?.length > 0;

    const handleCheckboxChange = () => {
      if (isAllSelected.current) {
        isAllSelected.current = false;
        parentOnChange({
          id: groupId,
          label: defaultLabel,
          selected: [],
        });
        return;
      }

      isAllSelected.current = true;

      const options = props.options.map((el: SelectedOption) => {
        return {
          label: el.label,
          value: el.value,
        };
      });

      parentOnChange({
        id: groupId,
        label: defaultLabel,
        selected: [
          ...options
            .filter((el: SelectedOption) => el.value !== "*")
            .filter(
              ({ value }: SelectedOption) =>
                (props.value ?? []).filter((opt: SelectedOption) => opt.value === value).length === 0
            ),
        ],
      });
      if (!hideDropDown) {
        setIsMenuOpen(true);
      }
    };

    return (
      <components.Placeholder {...props}>
        <div className={`${props.isSelected ? "bg-[#E6EDFF]" : ""} ms-1 p-1`}>
          <input
            key={props.value}
            className="trigger-checkbox"
            type="checkbox"
            disabled={isDisabled}
            checked={isAllSelected.current}
            onChange={handleCheckboxChange}
            ref={(input: HTMLInputElement) => {
              const theElement = input;
              if (input && isSemi) theElement.indeterminate = true;
            }}
          />

          <label className="ms-3 text-sm text-[#292929]">{defaultLabel}</label>
        </div>
      </components.Placeholder>
    );
  };

  const CustomValueContainer = (props: any) => {
    return (
      <components.ValueContainer {...props}>
        <CustomPlaceholder {...props} />
      </components.ValueContainer>
    );
  };

  const CustomOption = (
    props: OptionProps & {
      value?: SelectedOption["value"];
    }
  ) => {
    return (
      <components.Option {...props}>
        <div className={`${props.isSelected ? "bg-[#E6EDFF]" : ""} ms-1 p-1`}>
          {/* Semi checked(Indeterminate) */}
          {props.value === "*" && !isAllSelected.current && filteredSelectedOptions?.length > 0 ? (
            <input
              key={props.value}
              type="checkbox"
              ref={(input: HTMLInputElement) => {
                const theElement = input;
                if (input) theElement.indeterminate = true;
              }}
            />
          ) : (
            <input
              key={props.value}
              type="checkbox"
              checked={props.isSelected || isAllSelected.current}
              onChange={() => {}}
            />
          )}
          <label className="ms-3 text-sm">{props.label}</label>
        </div>
      </components.Option>
    );
  };

  const handleChange = (selectedOptions: SelectOptionProps["selected"]) => {
    const selected = selectedOptions.map((el) => {
      return { label: el?.label, value: el?.value };
    });
    // Handle different select/unselect cases
    // 1. select 1 item
    // 2. select all
    // 3. select all after few items
    // 4. unselect item
    // 5. clear all
    // 6. unselect all

    // (2,3) on select all or all items selected
    if (
      selected.length > 0 &&
      !isAllSelected.current &&
      (selected[selected.length - 1]?.value === allOption.value || filteredOptions?.length === selected?.length)
    ) {
      parentOnChange({
        id: groupId,
        label: defaultLabel,
        selected: [
          ...(props.value ?? []),
          ...props.options.filter(
            ({ value }: SelectedOption) =>
              (props.value ?? []).filter((opt: SelectedOption) => opt.value === value).length === 0
          ),
        ],
      });
      return;
    }

    // (1,4) if 1 item select/remove
    if (
      selected.length > 0 &&
      selected[selected.length - 1]?.value !== allOption.value &&
      filteredOptions?.length !== selected?.length
    ) {
      parentOnChange({ id: groupId, label: defaultLabel, selected });
      return;
    }

    // (5,6) if all unselect or clear
    parentOnChange({
      id: groupId,
      label: defaultLabel,
      selected: [],
    });
  };

  const CustomMenuList = (props: any) => {
    const { selectProps } = props;
    const { onInputChange, inputValue, onMenuInputFocus } = selectProps;

    const ariaAttributes = {
      "aria-label": selectProps["aria-label"],
      "aria-labelledby": selectProps["aria-labelledby"],
    };

    const clearSearch = () => {
      onInputChange("", {
        action: "input-change",
      });
    };

    return (
      <components.Menu {...props} className="menu-list">
        <div className="relative">
          <input
            style={{
              width: "100%",
              boxSizing: "border-box",
              padding: 10,
              border: "none",
              borderBottom: "1px solid lightgrey",
            }}
            autoCorrect="off"
            autoComplete="off"
            spellCheck="false"
            type="text"
            value={inputValue}
            onChange={(e) => {
              onInputChange(e.currentTarget.value, {
                action: "input-change",
              });
            }}
            onMouseDown={(e: any) => {
              e.stopPropagation();
              e.target.focus();
            }}
            onTouchEnd={(e: any) => {
              e.stopPropagation();
              e.target.focus();
            }}
            onFocus={onMenuInputFocus}
            placeholder="Available models"
            {...ariaAttributes}
          />
          {inputValue && (
            <button type="button" className="absolute right-2 top-2" onClick={clearSearch}>
              <X />
            </button>
          )}
        </div>

        <components.MenuList {...props} />
        <Separator />
        <div className="-mt-3 flex justify-end p-3">
          <button
            type="button"
            className="font-bold text-[#0275FF]"
            onClick={() => {
              setIsMenuOpen(false);
            }}
          >
            Done
          </button>
        </div>
      </components.Menu>
    );
  };

  isAllSelected.current = filteredOptions?.length === filteredSelectedOptions?.length;

  if (filteredSelectedOptions?.length > 0) {
    if (filteredSelectedOptions?.length === filteredOptions?.length) {
      selectAllLabel.current = `All (${filteredOptions.length}) selected`;
    } else {
      selectAllLabel.current = `${filteredSelectedOptions?.length} / ${filteredOptions.length} selected`;
    }
  } else selectAllLabel.current = "Select all";

  allOption.label = selectAllLabel.current;

  return (
    <div ref={refSelect}>
      <ReactSelect
        {...props}
        isDisabled={isDisabled}
        menuIsOpen={isMenuOpen}
        onKeyDown={onKeyDown}
        options={[allOption, ...props.options]}
        onChange={handleChange as ReactSelectProps["onChange"]}
        classNames={{
          control: () => "trigger",
        }}
        components={{
          ...props.components,
          Option: CustomOption,
          Placeholder: CustomPlaceholder,
          Menu: CustomMenuList,
          ValueContainer: CustomValueContainer,
          IndicatorSeparator: () => null,
          ...(hideDropDown && { IndicatorsContainer: () => null }),
        }}
        filterOption={customFilterOption}
        menuPlacement={props.menuPlacement ?? "bottom"}
        styles={customStyles}
        isMulti
        closeMenuOnSelect={false}
        tabSelectsValue={false}
        backspaceRemovesValue={false}
        hideSelectedOptions={false}
        blurInputOnSelect={false}
        isSearchable={false}
        isClearable={false}
        controlShouldRenderValue={false}
      />
    </div>
  );
};

export default MultiSelect;
