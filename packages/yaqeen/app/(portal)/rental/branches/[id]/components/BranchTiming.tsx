"use client";

// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */

import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useToast } from "@/lib/hooks/use-toast";
import Select from "react-select";
import React, { useState } from "react";
import { defaultTimings } from "./constants";

const hourOptions = Array.from({ length: 24 }, (_, i) => {
  const hour = i.toString().padStart(2, "0");
  return {
    value: hour,
    label: hour,
  };
});

const minuteOptions = [
  {
    value: "00",
    label: "00",
  },
  {
    value: "30",
    label: "30",
  },
];

interface TimeRangeProps {
  start: string;
  end: string;
}

export interface TimingsProps {
  day: string;
  open: boolean;
  start: string;
  end: string;
  timeRanges: TimeRangeProps[];
}

export default function BranchTiming({
  branchId,
  branchDetail,
  setBranchDetail,
  is24By7Open,
  set24By7Open,
  timingState,
  setTimingState,
}: {
  branchId: string;
  branchDetail: any;
  setBranchDetail: React.Dispatch<React.SetStateAction<any>>;
  is24By7Open: boolean;
  set24By7Open: React.Dispatch<React.SetStateAction<boolean>>;
  timingState: TimingsProps[];
  setTimingState: React.Dispatch<React.SetStateAction<TimingsProps[]>>;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleAddTiming = (currentItem: TimingsProps) => {
    const copyState = timingState;
    const emptyRange = {
      start: "00:00",
      end: "00:00",
    };
    const formattedState = copyState.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          timeRanges: [...(currentItem?.timeRanges ?? []), emptyRange],
        };
      }
      return item;
    });

    setTimingState(formattedState);
  };

  const handleRemoveTiming = (currentItem: TimingsProps, index: number) => {
    const copyState = timingState;
    const formattedState = copyState.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          timeRanges: currentItem?.timeRanges.filter((item, childIndex) => childIndex !== index),
        };
      }
      return item;
    });

    setTimingState(formattedState);
  };

  const handleSelectHour = (
    selectedTime: any,
    timeType = "start",
    selectType = "hour",
    currentItem: any,
    index: number,
  ) => {
    const copyState = timingState;
    const formattedState = copyState.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          timeRanges: currentItem?.timeRanges.map((item: any, childIndex: number) => {
            if (childIndex === index) {
              const isHour = selectType === "hour";
              const timeIndex = isHour ? 1 : 0;
              const hour = `${selectedTime.value}:${item[timeType].split(":")[timeIndex]}`;
              const minute = `${item[timeType].split(":")[timeIndex]}:${selectedTime.value}`;
              return { ...item, [timeType]: isHour ? hour : minute };
            }
            return item;
          }),
        };
      }
      return item;
    });

    setTimingState(formattedState);
  };

  const handleCheckbox = (currentItem: TimingsProps) => {
    const copyState = timingState;
    const formattedState = copyState.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          open: !item?.open,
        };
      }
      return item;
    });
    const isOpen24By7 = formattedState?.every((details) => {
      const time = details.timeRanges.every((el) => el.start === "00:00" && el.end === "00:00");
      return time && details.open;
    });
    set24By7Open(isOpen24By7);

    setTimingState(formattedState);
  };

  const onUpdate = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    const formData = new FormData(event.currentTarget);

    const {
      timings,
    }: {
      timings: TimingsProps[];
    } = JSON.parse(formData.get("stateFields") as string);

    const formValues = {
      name: {
        en: branchDetail?.name?.en,
        ar: branchDetail?.name?.ar,
      },
      latitude: branchDetail?.latitude,
      longitude: branchDetail?.longitude,
      phoneNumber: branchDetail?.phoneNumber,
      email: branchDetail?.email,
      code: branchDetail?.code,
      tags: branchDetail?.tags,
      directions: branchDetail?.directions,
      leadTimeMinutes: branchDetail?.leadTimeMinutes,
      branchOpen: branchDetail?.branchOpen,
      type: branchDetail?.type,
      cityId: branchDetail?.city.id,
      timings: timings,
    };

    try {
      const response = await fetch(`/routes/branchSettings/${branchId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...formValues }),
      });

      if (!response.ok) {
        throw new Error("Failed to update branch timings");
      }

      const data = await response.json();

      if (data?.status !== 200) {
        toast({
          variant: "destructive",
          title: "Failed",
          description: data?.body?.desc ?? "Failed to update branch settings",
          duration: 3000,
        });
        return;
      }
      setBranchDetail(data.body);

      toast({
        variant: "success",
        title: "Success",
        description: "Branch timings updated successfully",
        duration: 3000,
      });
    } catch (error) {
      console.error("Error updating branch timings:", error);
      toast({
        variant: "destructive",
        title: "Failed",
        description: "Failed to update branch timings",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={onUpdate}>
      <div className="mb-6 grid  gap-2">
        {timingState.map((item, i) => {
          const timeRange = item?.timeRanges;
          return (
            <div key={i} className="mb-4 flex items-center  gap-28">
              <div className=" flex h-full w-64 pt-8">
                <Label>{item.day}</Label>
              </div>
              <div className="flex w-96 flex-col">
                <div className="flex pb-2">
                  <Label className="w-1/2">Open</Label>
                  <Label className="w-1/2">Close</Label>
                </div>
                {timeRange.map((time, index) => {
                  const startTime = time?.start;
                  const endTime = time?.end;
                  const defaultHourMinute = {
                    label: "00",
                    value: "00",
                  };
                  const openHour = startTime.split(":")[0];
                  const openMinute = startTime.split(":")[1];
                  const closeHour = endTime.split(":")[0];
                  const closeMinute = endTime.split(":")[1];

                  const selectedOpenHour = hourOptions.find((item) => item.value === openHour);
                  const selectedCloseHour = hourOptions.find((item) => item.value === closeHour);
                  const selectedOpenMinute = minuteOptions.find((item) => item.value === openMinute);
                  const selectedCloseMinute = minuteOptions.find((item) => item.value === closeMinute);

                  return (
                    <div key={index} className="mb-2 flex items-center gap-2">
                      <div className="flex flex-col">
                        <div className="flex gap-x-4">
                          <div className="flex-col gap-2">
                            <div className="flex gap-2">
                              <Select
                                name="openHour"
                                value={selectedOpenHour || defaultHourMinute}
                                options={hourOptions}
                                instanceId="openHour"
                                data-testid="openHour"
                                className="w-20"
                                onChange={(time: any) => {
                                  handleSelectHour(time, "start", "hour", item, index);
                                }}
                                isDisabled={is24By7Open ? is24By7Open : !item.open}
                                components={{
                                  IndicatorSeparator: () => null,
                                }}
                              />

                              <Select
                                name="openMinute"
                                value={selectedOpenMinute || defaultHourMinute}
                                options={minuteOptions}
                                instanceId="openMinute"
                                data-testid="openMinute"
                                className="w-20"
                                onChange={(time: any) => {
                                  handleSelectHour(time, "start", "minute", item, index);
                                }}
                                isDisabled={is24By7Open ? is24By7Open : !item.open}
                                components={{
                                  IndicatorSeparator: () => null,
                                }}
                              />
                            </div>
                          </div>

                          <div className="flex-col gap-2">
                            <div className="flex gap-2">
                              <Select
                                name="closeHour"
                                value={selectedCloseHour || defaultHourMinute}
                                options={hourOptions}
                                instanceId="closeHour"
                                data-testid="closeHour"
                                className="w-20"
                                onChange={(time: any) => {
                                  handleSelectHour(time, "end", "hour", item, index);
                                }}
                                isDisabled={is24By7Open ? is24By7Open : !item.open}
                                components={{
                                  IndicatorSeparator: () => null,
                                }}
                              />
                              <Select
                                name="closeMinute"
                                value={selectedCloseMinute || defaultHourMinute}
                                options={minuteOptions}
                                instanceId="closeMinute"
                                data-testid="closeMinute"
                                className="w-20"
                                onChange={(time: any) => {
                                  handleSelectHour(time, "end", "minute", item, index);
                                }}
                                isDisabled={is24By7Open ? is24By7Open : !item.open}
                                components={{
                                  IndicatorSeparator: () => null,
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {index > 0 && (
                        <button
                          type="button"
                          name="removeTimings"
                          className="text-xs text-blue-500 hover:underline"
                          onClick={() => {
                            handleRemoveTiming(item, index);
                          }}
                        >
                          remove
                        </button>
                      )}
                    </div>
                  );
                })}
                <button
                  type="button"
                  name="addMoreTimings"
                  className="flex justify-self-start text-xs text-blue-500"
                  onClick={() => {
                    handleAddTiming(item);
                  }}
                >
                  +Add more timings
                </button>
              </div>
              <div className=" ml-[-6rem] flex h-full items-center justify-center">
                <input
                  name="openBranch"
                  className="checkbox checkbox-primary flex p-4"
                  title="openBranch"
                  type="checkbox"
                  value={`${item.open}`}
                  checked={item.open}
                  onChange={() => {
                    handleCheckbox(item);
                  }}
                  data-testid="openBranch"
                />
                <span className="mx-2">open</span>
              </div>
            </div>
          );
        })}
      </div>

      <input
        hidden
        type="hidden"
        name="stateFields"
        defaultValue={JSON.stringify({
          timings: timingState,
        })}
      />
      <Button disabled={isLoading} type="submit" variant="default" className="max-w-fit px-4 py-2 font-normal">
        Update {isLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : <></>}
      </Button>
    </form>
  );
}
