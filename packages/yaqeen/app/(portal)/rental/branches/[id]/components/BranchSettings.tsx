"use client";

// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */

import React, { useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import ChipInput from "@/components/ui/ChipInput";
import { Button } from "@/components/ui/button";
import { branchTypeOptions, defaultTimings, openBranchOptions } from "./constants";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useToast } from "@/lib/hooks/use-toast";
import { SelectComp } from "@/components/ui/MySelect/select";

interface BranchSettingsProps {
  branchDetail: any;
  setBranchDetail: any;
  selectedRegion: any;
  selectedCity: any;
  regionOption: any;
  cityOption: any;
  selectedBranchType: any;
  setIsEditForm: any;
  is24By7Open: boolean;
  set24By7Open: (value: boolean) => void;
  setTimingState: any;
  timings: any;
}

interface InputFieldProps {
  name: string;
  label: string;
  placeholder: string;
  defaultValue: string;
  type?: string;
  isDisabled?: boolean;
}

const InputField = ({ name, label, placeholder, defaultValue, type = "text", isDisabled }: InputFieldProps) => {
  return (
    <div className="mb-6 flex w-[418px] flex-col gap-2 ">
      <Label>{label}</Label>
      <Input name={name} placeholder={placeholder} defaultValue={defaultValue} type={type} disabled={isDisabled} />
    </div>
  );
};

interface FormValues {
  name: {
    en: string;
    ar: string;
  };
  latitude: string;
  longitude: string;
  phoneNumber: string;
  email: string;
  code: string;
  tags: any;
  directions: string;
  leadTimeMinutes: string;
  branchOpen: boolean;
  type?: string;
  cityId?: string;
  timings: any;
}

const BranchSettings: React.FC<BranchSettingsProps> = ({
  branchDetail,
  setBranchDetail,
  selectedRegion,
  selectedCity,
  cityOption,
  regionOption,
  selectedBranchType,
  setIsEditForm,
  is24By7Open,
  set24By7Open,
  setTimingState,
  timings,
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [chips, setChips] = React.useState<string[]>(branchDetail.tags);
  const { toast } = useToast();
  const [regionId, setRegionId] = React.useState(selectedRegion);
  const [cityId, setCityId] = React.useState(selectedCity);
  const [branchTypeOption, setBranchTypeOption] = React.useState(selectedBranchType);
  const [branchOpenOption, setBranchOpenOption] = React.useState(
    is24By7Open ? openBranchOptions[0] : openBranchOptions[1],
  );

  useEffect(() => {
    setBranchOpenOption(is24By7Open ? openBranchOptions[0] : openBranchOptions[1]);
  }, [is24By7Open]);
  useEffect(() => {
    setRegionId(selectedRegion);
  }, [selectedRegion]);

  const handleSet24By7Open = (option: any) => {
    const checked = option.value === "Yes";
    set24By7Open(checked);
    setTimingState(checked ? defaultTimings : timings);
    setBranchOpenOption(option);
  };

  const onUpdate = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    const formData = new FormData(event.currentTarget);
    const formValues: FormValues = {
      name: {
        en: formData.get("name.en") as string,
        ar: formData.get("name.ar") as string,
      },
      latitude: formData.get("latitude") as string,
      longitude: formData.get("longitude") as string,
      phoneNumber: formData.get("phoneNumber") as string,
      email: formData.get("email") as string,
      code: formData.get("code") as string,
      tags: chips,
      directions: formData.get("directions") as string,
      leadTimeMinutes: formData.get("leadTimeMinutes") as string,
      branchOpen: formData.get("branchOpen") === "true",
      type: formData.get("branchType") as string,
      cityId: formData.get("cityId") as string,
      timings: is24By7Open ? defaultTimings : timings,
    };

    try {
      const response = await fetch(`/routes/branchSettings/${branchDetail.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...formValues }),
      });

      if (!response.ok) {
        throw new Error("Failed to update branch settings");
      }
      const data = await response.json();

      if (data.status !== 200) {
        toast({
          variant: "destructive",
          title: "Failed",
          description: data?.body?.desc ?? "Failed to update branch settings",
          duration: 3000,
        });
        return;
      }

      setBranchDetail(data.body);
      toast({
        variant: "success",
        title: "Success",
        description: "Branch settings updated successfully",
        duration: 3000,
      });
      setIsEditForm(false);
    } catch (error) {
      console.error("Error updating branch settings:", error);
      toast({
        variant: "destructive",
        title: "Failed",
        description: "Failed to update branch settings",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form
      className="flex max-w-[892px] flex-wrap gap-4 rounded-lg border border-solid border-slate-200 p-4"
      onSubmit={onUpdate}
    >
      <div className=" mb-4 flex flex-wrap gap-4 gap-y-6 ">
        <div className=" flex w-[418px] flex-col gap-2 ">
          <Label>Region</Label>
          <SelectComp
            selectedOption={regionId}
            setSelectedOption={setRegionId}
            name="regionId"
            defaultValue={selectedRegion}
            options={regionOption}
          />
        </div>
        <div className=" flex w-[418px] flex-col gap-2 ">
          <Label>City</Label>
          <SelectComp
            selectedOption={cityId}
            setSelectedOption={setCityId}
            name="cityId"
            defaultValue={selectedCity}
            options={cityOption}
          />
        </div>
      </div>
      <InputField name="name.en" label="Name" placeholder="Name" defaultValue={branchDetail.name.en} />
      <InputField
        name="name.ar"
        label="Name (Arabic)"
        placeholder="Name (Arabic)"
        defaultValue={branchDetail.name.ar}
      />
      <InputField
        name="latitude"
        label="Latitude"
        placeholder="Latitude"
        defaultValue={branchDetail.latitude}
        type="number"
      />
      <InputField
        name="longitude"
        label="Longitude"
        placeholder="Longitude"
        defaultValue={branchDetail.longitude}
        type="number"
      />
      <InputField
        type="number"
        name="phoneNumber"
        label="Contact No"
        placeholder="Contact No"
        defaultValue={branchDetail?.phoneNumber}
      />
      <InputField name="email" label="Email" placeholder="Email" defaultValue={branchDetail.email} />
      <div className="flex w-[418px] flex-col gap-2 ">
        <div>
          <Label>Branch Type</Label>
          <SelectComp
            setSelectedOption={setBranchTypeOption}
            selectedOption={branchTypeOption}
            name="branchType"
            defaultValue={selectedBranchType}
            options={branchTypeOptions}
          />
        </div>
      </div>
      <InputField name="tempCode" label="Code" placeholder="Code" defaultValue={branchDetail.code} isDisabled={true} />
      <input name="code" defaultValue={branchDetail.code} hidden />

      <div className="flex w-[418px] flex-col gap-2 ">
        <Label>Tags</Label>
        <ChipInput name="tags" label="Tags" defaultValue={chips} placeHolder="Tags" chips={chips} setChips={setChips} />
      </div>
      <InputField
        name="directions"
        label="Directions"
        placeholder="Directions"
        defaultValue={branchDetail.directions}
      />
      <InputField
        name="leadTimeMinutes"
        label="Lead Time (minutes)"
        placeholder="Lead Time (minutes)"
        defaultValue={branchDetail.leadTimeMinutes}
        type="number"
      />
      <div className=" flex w-[418px] flex-col gap-2 ">
        <Label>24/7</Label>
        <SelectComp
          // @ts-ignore
          selectedOption={branchOpenOption}
          setSelectedOption={handleSet24By7Open}
          name="branchOpen"
          // @ts-ignore
          defaultValue={branchOpenOption}
          options={openBranchOptions}
        />
        <input name="branchOpen" type="checkbox" value={`${is24By7Open}`} hidden />
      </div>

      <Button disabled={isLoading} type="submit" variant="default" className="px-4 py-2 font-normal ">
        Update {isLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : <></>}
      </Button>
      <Button variant="outline" className="px-4 py-2 font-normal " onClick={() => setIsEditForm(false)}>
        Cancel
      </Button>
    </form>
  );
};

export default BranchSettings;
