import { useState } from "react";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";

export const PAUSE_ACTION = "pause";

export const ReasonForPauseModal = ({
  open,
  onChange,
  onClose,
  onConfirm,
}: {
  open: boolean;
  onChange: (prevState: boolean) => void;
  onClose: () => void;
  onConfirm: (reason: string) => void;
}) => {
  const [reason, setReason] = useState("");
  const [requiredError, setRequiredError] = useState(false);

  const handleConfirm = () => {
    if (!reason) {
      setRequiredError(true);
      return;
    }
    setRequiredError(false);
    onConfirm(reason);
  };

  return (
    <Dialog open={open} onOpenChange={onChange}>
      <DialogContent className="p-4">
        <div className="grid h-full grid-rows-[auto_1fr_auto] gap-3">
          <div>
            <h1 className="text-xl font-bold">Reason for availability pause</h1>
            <h2>Add the reason for pausing the extended availability</h2>
          </div>
          <Textarea
            name="pause-reason"
            required
            placeholder="Due to a public holiday"
            value={reason}
            onChange={(e) => {
              setReason(e.target.value);
            }}
          />
          {requiredError && <span className="text-red-400">Required Field</span>}

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose} type="button" name="cancelBtn" data-testid="btn-cancel">
              Cancel
            </Button>
            <Button type="button" data-testid="btn-confirm" onClick={handleConfirm}>
              Activate Pause
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
