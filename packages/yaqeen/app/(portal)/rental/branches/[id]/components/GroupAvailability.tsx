"use client";

// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */
import React, { type FormEvent, useState } from "react";
import { CaretRight } from "@phosphor-icons/react/dist/ssr";
import Link from "next/link";
import MultiSelect, { SelectOptionProps } from "./MultiSelect";
import { PauseAvailability } from "./PauseAvailability";
import { Button } from "@/components/ui/button";

type SelectedGroups = Record<string, SelectOptionProps>;

interface BranchAvailabilityDetails {
  groups: Array<{
    id: string;
    label: string;
    subGroups: Array<{
      label: string;
      value: number;
      selected: boolean;
    }>;
  }>;
  formattedAudit: string;
}

const CurrentAvailabilitySection = ({
  availability,
  submitRequest,
}: {
  availability: BranchAvailabilityDetails;
  submitRequest: () => void;
}) => {
  const [isDirty, setIsDirty] = useState<boolean>(false);
  const [isAllUncheckedModal, setIsAllUncheckedModal] = useState<boolean>(false);

  const originalHashMap = availability.groups.reduce<SelectedGroups>((acc, group) => {
    acc[group.id] = {
      id: group.id,
      label: group.label,
      selected: group.subGroups.filter((sub) => sub.selected).map((sub) => ({ label: sub.label, value: sub.value })),
    };

    return acc;
  }, {});

  const [optionsSelected, setOptionsSelected] = useState<SelectedGroups>(originalHashMap);

  const isCurrentChanged = (options?: SelectedGroups) =>
    JSON.stringify(options ?? optionsSelected) !== JSON.stringify(originalHashMap);

  const validateAndSubmitRequest = (selectedOptions: Array<SelectOptionProps["selected"][number]["value"]>) => {
    const isSomeChecked = selectedOptions.length;
    if (!isSomeChecked) {
      setIsAllUncheckedModal(true);
      return;
    }

    setIsDirty(false);
  };

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const selectedIds = Object.values(optionsSelected)
      .filter((group) => !!group.selected.length)
      .flatMap((group) => {
        const virtualIds = group.selected.map((el) => el.value);
        return virtualIds;
      });

    validateAndSubmitRequest(selectedIds);
  };

  const handleChange = (selected: SelectOptionProps) => {
    const newOptions = { ...optionsSelected, [selected.id]: selected };
    setOptionsSelected(newOptions);
    setIsDirty(isCurrentChanged(newOptions));
  };

  const onNoVehicleConfirm = () => {
    setIsAllUncheckedModal(false);
  };

  return (
    <>
      <h2 className="text-2xl font-medium leading-none tracking-normal text-slate-900">Current Availability</h2>
      <p className="mt-2 text-sm text-slate-700">
        <span className="text-neutral-grey300">For bookings within 48 hours</span>
        <span className="text-neutral-grey300">{` - ${availability.formattedAudit}`}</span>
      </p>
      <section className="my-4 flex flex-col gap-12 self-stretch rounded-lg border border-solid border-slate-200 p-4">
        <div className="flex flex-wrap gap-3">
          {availability.groups.map((group) => {
            return (
              <MultiSelect
                key={group.id}
                defaultLabel={group.label}
                groupId={group.id}
                options={group.subGroups}
                onChange={handleChange}
                value={optionsSelected[group.id]?.selected || []}
              />
            );
          })}
        </div>
      </section>
      <Button disabled>Update</Button>
    </>
  );
};

const ExtendedAvailabilitySection = ({
  availability,
  pausedDetails,
  submitRequest,
}: {
  availability: BranchAvailabilityDetails;
  pausedDetails?: any;
  submitRequest: () => void;
}) => {
  const isPaused = pausedDetails?.isValid;
  const pickerStartingDateTime = pausedDetails?.endDateTime ? pausedDetails.endDateTime * 1000 : undefined;

  const [isDirty, setIsDirty] = useState(false);
  const [isAllUncheckedModal, setIsAllUncheckedModal] = useState<boolean>(false);

  const originalHashMap = availability.groups.reduce<SelectedGroups>((acc, group) => {
    acc[group.id] = {
      id: group.id,
      label: group.label,
      selected: group.subGroups.filter((sub) => sub.selected).map((sub) => ({ label: sub.label, value: sub.value })),
    };

    return acc;
  }, {});

  const [optionsSelected, setOptionsSelected] = useState<SelectedGroups>(originalHashMap);

  const validateAndSubmitRequest = (selectedOptions: Array<SelectOptionProps["selected"][number]["value"]>) => {
    const isSomeChecked = selectedOptions.length;
    if (!isSomeChecked) {
      setIsAllUncheckedModal(true);
      return;
    }

    setIsDirty(false);
  };

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const selectedIds = Object.values(optionsSelected)
      .filter((group) => !!group.selected.length)
      .flatMap((group) => {
        const virtualIds = group.selected.map((el) => el.value);
        return virtualIds;
      });

    validateAndSubmitRequest(selectedIds);
  };

  const isCurrentChanged = (options?: SelectedGroups) =>
    JSON.stringify(options ?? optionsSelected) !== JSON.stringify(originalHashMap);

  const handleChange = (selected: SelectOptionProps) => {
    const newOptions = { ...optionsSelected, [selected.id]: selected };
    setOptionsSelected(newOptions);
    setIsDirty(isCurrentChanged(newOptions));
  };

  return (
    <>
      <h2 className="text-2xl font-medium leading-none tracking-normal text-slate-900">Extended Availability</h2>
      <p className="mt-2 text-sm text-slate-700">
        <span className="text-neutral-grey300">For bookings within 48 hours</span>
        <span className="text-neutral-grey300">{` - ${availability.formattedAudit}`}</span>
      </p>
      <section className="my-4 flex flex-col gap-12 self-stretch rounded-lg border border-solid border-slate-200 p-4">
        <div className="flex flex-wrap gap-3">
          {availability.groups.map((group) => {
            return (
              <MultiSelect
                key={group.id}
                defaultLabel={group.label}
                groupId={group.id}
                options={group.subGroups}
                onChange={handleChange}
                value={optionsSelected[group.id]?.selected || []}
              />
            );
          })}
        </div>
      </section>
      <div className="flex gap-4">
        <Button disabled>Update</Button>
        <PauseAvailability pausedDetails={pausedDetails} />
      </div>
    </>
  );
};

interface GroupAvailabilityProps {
  branch: any;
  currentAvailability: any;
  extendedAvailability: any;
  pauseAvailability: any;
}

export default function GroupAvailability({
  branch,
  currentAvailability,
  extendedAvailability,
  pauseAvailability,
}: GroupAvailabilityProps) {
  return (
    <>
      <section className="flex flex-col self-stretch bg-slate-50 px-6 py-4 max-md:px-5">
        <div className="flex w-full flex-wrap items-start gap-1 text-xs leading-relaxed max-md:max-w-full">
          <div className="flex items-center gap-2 whitespace-nowrap text-slate-700">
            <Link href={`/rental/branches/${branch.id}`}>Branch</Link>
            <CaretRight className="h-4 w-4" />
          </div>
          <div className="flex items-center gap-2 whitespace-nowrap text-slate-700">
            <Link href={`/rental/branches/${branch.id}/settings`}>{branch.name.en}</Link>
            <CaretRight className="h-4 w-4" />
          </div>
          <div className="gap-2 self-stretch text-slate-500">Group availability</div>
        </div>
        <div className="mt-6 flex w-full flex-wrap items-start gap-4 font-medium text-slate-900 max-md:max-w-full">
          <div className="flex min-w-[240px] flex-col justify-center max-md:max-w-full">
            <div className="flex items-center gap-x-2 ">
              <h2 className="text-3xl tracking-tight max-md:max-w-full">Group availability</h2>
            </div>
          </div>
        </div>
      </section>
      <div className="p-4">
        <CurrentAvailabilitySection availability={currentAvailability} submitRequest={() => {}} />
      </div>

      <div className="p-4">
        <ExtendedAvailabilitySection
          availability={extendedAvailability}
          pausedDetails={pauseAvailability}
          submitRequest={() => {}}
        />
      </div>
    </>
  );
}
