import { useState } from "react";

import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
// import { formatDateTime } from "@/lib/utils";
import { ReasonForPauseModal } from "./ReasonForPauseModal";

export interface PauseAvailability {
  updatedOn: number;
  branchId: number;
  id: number;
  isValid: boolean;
  reason: string;
  startDateTime: number;
  endDateTime: number;
  createdOn: number;
  createdBy: string;
}

export const PauseAvailability = ({ pausedDetails: _pausedDetails }: { pausedDetails?: PauseAvailability }) => {
  const [openDatePopUp, setOpenDatePopUp] = useState(false);
  const [openReasonPopUp, setOpenReasonPopUp] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  // const isPaused = pausedDetails?.isValid;

  const handleCloseDatePopUp = () => {
    setOpenDatePopUp(false);
  };

  const handleOpenReasonPopUp = () => {
    setOpenReasonPopUp(true);
  };
  const handleCloseReasonPopUp = () => {
    setOpenReasonPopUp(false);
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  return (
    <>
      <DropdownMenu open={openDatePopUp} onOpenChange={setOpenDatePopUp}>
        <DropdownMenuTrigger disabled>
          <Button variant="outline">Pause Availability</Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <div className="p-4">
            <div className="mb-4 flex justify-between">
              <h3>Pause Until:</h3>
            </div>
            <DatePicker
              date={selectedDate}
              setDate={handleDateChange}
              name="endDateTime"
              test-id="pauseDate"
              disablePastDates={true}
            />

            <div className="flex justify-between">
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleCloseDatePopUp}>
                  Cancel
                </Button>
                <Button onClick={handleOpenReasonPopUp}>Pause availability</Button>
              </div>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
      <ReasonForPauseModal
        open={openReasonPopUp}
        onChange={setOpenReasonPopUp}
        onClose={handleCloseReasonPopUp}
        onConfirm={handleCloseReasonPopUp}
      />
    </>
  );
};
