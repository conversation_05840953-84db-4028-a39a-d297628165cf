"use client";

// IGNORING FOR TECH DEBT TICKET LATER
/* eslint-disable */
import React, { useState } from "react";
import { ArrowSquareOut, CaretRight, PencilSimple } from "@phosphor-icons/react/dist/ssr";
import { useParams } from "next/navigation";

import { ProgressBarLink } from "@/components/progress-bar";
import { Label } from "@/components/ui/label";
import { branchTypeOptions } from "./constants";
import BranchTiming from "./BranchTiming";
import BranchSettings from "./BranchSettings";
import Link from "next/link";
import { type Route } from "next";
interface BranchInfoItemProps {
  label: string;
  value: string;
}

const BranchInfoItem: React.FC<BranchInfoItemProps> = ({ label, value }) => (
  <div className="flex flex-col whitespace-nowrap">
    <div className="text-sm font-medium leading-none text-slate-500">{label}</div>
    <div className="mt-2 text-base text-slate-800">{value ?? "N/A"}</div>
  </div>
);

interface TagListProps {
  tags: string[];
}

const TagList: React.FC<TagListProps> = ({ tags }) => (
  <div className="flex min-w-[240px] flex-col font-medium max-md:max-w-full">
    <div className="text-sm leading-none text-slate-500">Tags</div>
    <div className="mt-2 flex flex-wrap items-start gap-2 text-xs leading-relaxed text-slate-900 max-md:max-w-full">
      {tags.map((tag, index) => (
        <div key={index} className="gap-1 self-stretch whitespace-nowrap rounded-[100px] bg-slate-300 px-3">
          {tag}
        </div>
      ))}
    </div>
  </div>
);

interface DirectionsButtonProps {
  directions: string;
}
const DirectionsButton: React.FC<DirectionsButtonProps> = ({ directions }) => (
  <div className="flex flex-col text-sm leading-none">
    <div className="font-medium text-slate-500">Directions</div>
    <a
      href={directions}
      target="_blank"
      className="mt-2 flex min-h-[32px] items-center justify-center gap-2 rounded-md border border-solid border-slate-300 bg-white p-2 text-slate-900"
    >
      <ArrowSquareOut className="h-4 w-4" />
      <span className="my-auto self-stretch">Get directions</span>
    </a>
  </div>
);

interface BranchAvailabilityProps {
  currentAvailabilityGroupsCode: string[];
  extendedAvailabilityGroupsCode: string[];
}

const BranchAvailability: React.FC<BranchAvailabilityProps> = ({
  currentAvailabilityGroupsCode,
  extendedAvailabilityGroupsCode,
}) => {
  return (
    <div className="flex max-w-md justify-between gap-3">
      <div className="w-2/4">
        <Label>Current availability</Label>
        <div>
          {currentAvailabilityGroupsCode?.length ? (
            <span className="text-sm font-bold text-black">{currentAvailabilityGroupsCode.join(", ")}</span>
          ) : (
            "-"
          )}
        </div>
      </div>
      <div className="w-2/4">
        <Label>Extended availability</Label>
        <div>
          {extendedAvailabilityGroupsCode?.length ? (
            <span className="text-sm font-bold text-black">{extendedAvailabilityGroupsCode.join(", ")}</span>
          ) : (
            "-"
          )}
        </div>
      </div>
    </div>
  );
};

interface DetailPageProps {
  branch: any;
  regions: any;
  cities: any;
  currentAvailabilityGroupsCode: string[];
  extendedAvailabilityGroupsCode: string[];
}

export default function DetailPage({
  branch,
  regions,
  cities,
  currentAvailabilityGroupsCode,
  extendedAvailabilityGroupsCode,
}: DetailPageProps) {
  const { id } = useParams();

  const [timingState, setTimingState] = useState(branch?.timings);

  const isOpen24By7 = timingState?.every((details: any) => {
    const time = details.timeRanges.every((el: any) => el.start === "00:00" && el.end === "00:00");
    return time && details.open;
  });

  const [is24By7Open, set24By7Open] = useState(isOpen24By7);
  const [isEditForm, setIsEditForm] = useState(false);
  const [branchDetail, setBranchDetail] = useState(branch);

  const regionOption = regions?.map((region: any, index: number) => ({
    id: index,
    value: region.id,
    label: region.name.en,
  }));

  const selectedRegion = regionOption.find((region: any) => region?.value === branchDetail?.city.region.id);

  const cityOption = cities?.map((city: any, index: number) => ({
    id: index,
    value: city.id,
    label: city.name.en,
  }));

  const selectedCity = cityOption.find((city: any) => city?.value === branchDetail?.city.id);

  const selectedBranchType = branchTypeOptions?.find((branch) => branch?.value === branchDetail?.type);

  const branchInfoItems = [
    { label: "City", value: selectedCity?.label },
    { label: "Region", value: selectedRegion?.label },
    { label: "Longitude", value: branchDetail.longitude },
    { label: "Latitude", value: branchDetail.latitude },
    { label: "Contact Number", value: branchDetail.phoneNumber },
    { label: "Email", value: branchDetail.email },
    { label: "Branch Type", value: selectedBranchType?.label },
    { label: "Lead Time (minutes)", value: branchDetail.leadTimeMinutes },
    { label: "Code", value: branchDetail.code },
    { label: "24/7", value: is24By7Open ? "Yes" : "No" },
  ];

  return (
    <>
      <section className="flex flex-col self-stretch bg-slate-50 px-6 py-4 max-md:px-5">
        <div className="flex w-full flex-wrap items-start gap-1 text-xs leading-relaxed max-md:max-w-full">
          <div className="flex items-center gap-2 whitespace-nowrap text-slate-700">
            <Link href={`/rental/branches/${branch.id}`}>Branch</Link>
            <CaretRight className="h-4 w-4" />
          </div>
          <div className="gap-2 self-stretch text-slate-500">{branch.name.en}</div>
        </div>
        <div className="mt-6 flex w-full flex-wrap items-start gap-4 font-medium text-slate-900 max-md:max-w-full">
          <div className="flex min-w-[240px] flex-col justify-center max-md:max-w-full">
            <div className="flex items-center gap-x-2 ">
              <h2 className="text-3xl tracking-tight max-md:max-w-full">{branch.name.en}</h2>
              <PencilSimple className="h-6 w-6 cursor-pointer text-slate-500" onClick={() => setIsEditForm(true)} />
            </div>
            <p className="mt-2 text-lg leading-loose">{branch.name.ar}</p>
          </div>
        </div>
      </section>
      <div className="p-4">
        {isEditForm ? (
          <BranchSettings
            setIsEditForm={setIsEditForm}
            branchDetail={branchDetail}
            setBranchDetail={setBranchDetail}
            selectedBranchType={selectedBranchType}
            selectedRegion={selectedRegion}
            regionOption={regionOption}
            is24By7Open={is24By7Open}
            set24By7Open={set24By7Open}
            selectedCity={selectedCity}
            cityOption={cityOption}
            timings={timingState}
            setTimingState={setTimingState}
          />
        ) : branchDetail ? (
          <section className="my-4 flex flex-wrap content-start items-start gap-12 self-stretch rounded-lg border border-solid border-slate-200 p-4">
            {branchInfoItems.map((item, index) => (
              <BranchInfoItem key={index} label={item.label} value={item.value} />
            ))}
            <DirectionsButton directions={branchDetail.directions} />
            <TagList tags={branchDetail.tags} />

            <input hidden name="tags" defaultValue={JSON.stringify(branchDetail.tags)} />
          </section>
        ) : (
          ""
        )}

        <div className="mt-6">
          <div className="flex items-center gap-x-2 ">
            <h2 className="text-2xl font-medium leading-none tracking-normal text-slate-900">Availability</h2>

            <ProgressBarLink href={`/rental/branches/${id}/availability` as Route}>
              <PencilSimple className="h-6 w-6 cursor-pointer text-slate-500" />
            </ProgressBarLink>
          </div>
          <section className="my-4 flex flex-col gap-12 self-stretch rounded-lg border border-solid border-slate-200 p-4">
            <BranchAvailability
              currentAvailabilityGroupsCode={currentAvailabilityGroupsCode}
              extendedAvailabilityGroupsCode={extendedAvailabilityGroupsCode}
            />
          </section>
        </div>

        <div className="mt-6">
          <h2 className="text-2xl font-medium leading-none tracking-normal text-slate-900">Branch timings</h2>
          <p className="mt-2 text-sm text-slate-700">This is a 24/7 branch, but still you will set the timings</p>
          <section className="my-4 flex flex-col gap-12 self-stretch rounded-lg border border-solid border-slate-200 p-4">
            <BranchTiming
              branchId={branch.id}
              branchDetail={branchDetail}
              setBranchDetail={setBranchDetail}
              is24By7Open={is24By7Open}
              set24By7Open={set24By7Open}
              timingState={timingState}
              setTimingState={setTimingState}
            />
          </section>
        </div>
      </div>
    </>
  );
}
