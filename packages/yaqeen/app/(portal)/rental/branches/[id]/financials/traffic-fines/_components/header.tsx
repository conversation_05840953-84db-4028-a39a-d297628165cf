"use client";
import { useTranslations, useLocale } from "next-intl";
import { File, CaretRight, CaretLeft } from "@phosphor-icons/react";
import { ProgressBarLink } from "@/components/progress-bar";
import { useState } from "react";
import CreateFineDialog from "./create-traffic-fine/traffic-fine-dialog";

export default function Header() {
  const t = useTranslations("TrafficFines");
  const locale = useLocale();
  const [openCreateFineDialog, setOpenCreateFineDialog] = useState(false);

  return (
    <>
      {openCreateFineDialog && <CreateFineDialog open={openCreateFineDialog} onOpenChange={setOpenCreateFineDialog} />}
      <div className="flex flex-col gap-6 bg-slate-50 px-6 py-4">
        <div className="flex items-center gap-2 text-xs">
          <ProgressBarLink href="/" className="text-slate-700">
            {t("home")}
          </ProgressBarLink>
          {locale === "en" ? <CaretRight size={12} fill="#64748B" /> : <CaretLeft size={12} fill="#64748B" />}
          <span className="text-slate-500">{t("trafficFines")}</span>
        </div>
        <div className="flex items-center justify-between pb-6">
          <h1 className="text-3xl font-medium text-slate-900">{t("trafficFines")}</h1>
          <button
            className="flex items-center gap-2 rounded-md bg-lumi-500 px-4 py-2 text-sm text-slate-900 hover:bg-lumi-400"
            onClick={() => setOpenCreateFineDialog(true)}
          >
            <File size={18} fill="#0F172A" />
            {t("createFine")}
          </button>
        </div>
      </div>
    </>
  );
}
