"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { amountFormatter } from "@/lib/utils";
import { Confetti } from "@phosphor-icons/react";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { CopySimple, Check } from "@phosphor-icons/react";
import { useState, startTransition } from "react";
import { useProgressBar } from "@/components/progress-bar";
import { useTranslations } from "next-intl";

interface PaymentCardProps {
  paymentLink: string;
  paymentAmount: number;
  branchId: string;
}
export default function PaymentCard({ paymentLink, paymentAmount, branchId }: PaymentCardProps) {
  const router = useRouter();
  const [copied, setCopied] = useState(false);
  const progress = useProgressBar();
  const t = useTranslations("TrafficFines.paymentLink");

  const handleGoToInvoices = () => {
    progress.start();
    startTransition(() => {
      router.push(`/rental/branches/${branchId}/financials/traffic-fines`);
      progress.done();
    });
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(paymentLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  return (
    <Card className="w-[468px] max-w-full">
      <CardHeader className="px-4 pt-12">
        <CardTitle className="flex flex-col items-center gap-1">
          <Confetti size={56} weight="duotone" className="text-lumi-700" />
          <h1 className="text-2xl font-bold text-lumi-800">{t("paymentRequested")}</h1>
          <p className="text-center text-md font-normal text-slate-600">{t("customerWillReceiveLink")}</p>
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-6">
        <div className="rounded-[6px] bg-slate-100">
          <div className="px-4 py-6 text-center text-2xl font-medium text-slate-900">
            {t("paymentTotal")}: {t("sar")} {amountFormatter(paymentAmount)}
          </div>
          <Separator />
          <div className="p-4">
            <span className="text-sm font-medium text-slate-500">{t("quickPayLink")}</span>
            <div className="flex items-center gap-2">
              <a
                href={paymentLink}
                target="_blank"
                rel="noopener noreferrer"
                className="truncate text-sm font-normal text-blue-600 underline"
                aria-label="Copy payment link"
              >
                {paymentLink}
              </a>
              <Button
                variant="ghost"
                title={copied ? "Copied!" : "Copy link"}
                aria-label={copied ? "Copied!" : "Copy link"}
                className="flex justify-center p-0 text-blue-600 hover:bg-muted"
                onClick={handleCopyLink}
              >
                {copied ? <Check size={20} /> : <CopySimple size={20} />}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      <Separator />
      <CardFooter className="p-4">
        <Button onClick={handleGoToInvoices} className="w-full">
          {t("backToInvoices")}
        </Button>
      </CardFooter>
    </Card>
  );
}
