export interface OptionProps {
  label: string;
  value: string;
}

export const REWARD_PROVIDERS = [
  { value: "QITAF", label: "QITAF" },
  { value: "ALFURSAN", label: "ALFURSAN" },
  { value: "<PERSON><PERSON><PERSON><PERSON>", label: "<PERSON><PERSON><PERSON><PERSON>" },
] as OptionProps[];

export const LOYALTY_STATUS = [
  { value: "INITIATED", label: "INITIATED" },
  { value: "SUCCESS", label: "SUCCESS" },
  { value: "FAILED", label: "FAILED" },
  { value: "CANCELED", label: "CANCELED" },
] as OptionProps[];
