import { useLocale, useTranslations } from "next-intl";
import { type ColumnDef } from "@tanstack/react-table";
import { type TrafficFine } from "@/api/contracts/rental/traffic-fine-contract";
import { type ChargeStatus, type PaymentStatus } from "../constants";
import { amountFormatter } from "@/lib/utils";
import { Info } from "@phosphor-icons/react";
import TooltipComponent from "@/components/tooltip-component";
import { formatEpochToDateTime, getChargeStatusColor, getPaymentStatusColor } from "../utils";
import { ProgressBarLink } from "@/components/progress-bar";
import ActionsMenu from "./actions-menu";
import { type Branch } from "@/api/contracts/branch-contract";
import { getBranchName } from "../utils";
import { PosItem } from "./table";
import { convertPlateToArabic } from "@/lib/utils";

export function useTrafficFineColumns(branches: Branch[], posData: PosItem[]): ColumnDef<TrafficFine>[] {
  const t = useTranslations("TrafficFines");
  const locale = useLocale();

  return [
    {
      accessorKey: "ticketNumber",
      header: () => {
        return t("columns.violationNo");
      },
      cell: ({ row }) => {
        const ticketNumber = row.getValue<number>("ticketNumber");
        const invoiceNo = row.original.invoiceNumber;

        return (
          <div className="flex items-center gap-x-2">
            <span>{ticketNumber}</span>
            {invoiceNo && (
              <TooltipComponent content={<span className="text-sm text-blue-600">{invoiceNo}</span>} withArrow={false}>
                <Info size={16} weight="regular" className="cursor-pointer text-slate-900" />
              </TooltipComponent>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "plateNo",
      header: () => {
        return t("columns.plateNo");
      },
      cell: ({ row }) => {
        const plateNo = row.getValue<string>("plateNo");
        return locale === "ar" ? convertPlateToArabic(plateNo) : plateNo;
      },
    },
    {
      accessorKey: "fineDate",
      header: () => {
        return t("columns.fineDate");
      },
      cell: ({ row }) => {
        const fineDateEpochTime = row.getValue<number>("fineDate");
        const { date, time } = formatEpochToDateTime(fineDateEpochTime);
        return (
          <div className="flex flex-col items-start">
            <span className="font-medium">{date}</span>
            <span>{time}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "chargeStatus",
      header: () => {
        return t("columns.chargeStatus");
      },
      cell: ({ row }) => {
        const chargeStatus = row.getValue<ChargeStatus>("chargeStatus");
        return (
          <span
            className={`inline-flex h-6 items-center rounded-full px-2 text-xs ${getChargeStatusColor(chargeStatus)}`}
          >
            {t(`statuses.${chargeStatus}`)}
          </span>
        );
      },
    },
    {
      accessorKey: "paymentStatus",
      header: () => {
        return t("columns.paymentStatus");
      },
      cell: ({ row }) => {
        const paymentStatus = row.getValue<PaymentStatus>("paymentStatus");
        return (
          <span
            className={`inline-flex h-6 items-center rounded-full px-2 text-xs ${getPaymentStatusColor(paymentStatus)}`}
          >
            {t(`statuses.${paymentStatus}`)}
          </span>
        );
      },
    },
    {
      accessorKey: "bookingNo",
      header: () => {
        return t("columns.bookingNo");
      },
      cell: ({ row }) => {
        const { issuedBranchId, bookingId } = row.original;

        return (
          <ProgressBarLink
            href={`/rental/branches/${issuedBranchId}/bookings/${bookingId}`}
            className="font-medium text-blue-600"
          >
            {row.getValue<string>("bookingNo")}
          </ProgressBarLink>
        );
      },
    },
    {
      accessorKey: "agreementNo",
      header: () => {
        return t("columns.agreementNo");
      },
      cell: ({ row }) => {
        return <span className="font-medium">{row.getValue<string>("agreementNo")}</span>;
      },
    },
    {
      accessorKey: "issuedBranchId",
      header: () => {
        return t("columns.issuedBranch");
      },
      cell: ({ row }) => {
        const branchId = row.getValue<number>("issuedBranchId");
        const branchName = getBranchName(branches, branchId, locale);
        return branchName;
      },
    },
    {
      accessorKey: "amount",
      header: () => {
        return t("columns.amount");
      },
      cell: ({ row }) => {
        const amount = row.getValue<number>("amount");
        return amountFormatter(Number(amount));
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return <ActionsMenu row={row.original} posData={posData} />;
      },
    },
  ];
}
