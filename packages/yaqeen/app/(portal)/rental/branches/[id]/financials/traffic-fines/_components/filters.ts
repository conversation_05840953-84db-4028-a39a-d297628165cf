import { type Branch } from "@/api/contracts/branch-contract";
import { useLocale, useTranslations } from "next-intl";

export function useTrafficFineFilters(branches: Branch[]) {
  const t = useTranslations("TrafficFines");
  const locale = useLocale();

  const searchInputFilters = [
    {
      label: t("filters.bookingNo"),
      value: "bookingNos",
    },
    {
      label: t("filters.plateNo"),
      value: "plateNos",
    },
    {
      label: t("filters.ticketNo"),
      value: "ticketNumbers",
    },
    {
      label: t("filters.agreementNo"),
      value: "agreementNumbers",
    },
  ];

  const selectFilters = [
    {
      filterKey: "branchIds",
      filterName: t("filters.location"),
      columnKey: "issuedBranchId",
      options: [
        ...branches.map((branch) => ({
          label: locale === "en" ? branch.name?.en : branch.name?.ar,
          value: String(branch.id),
        })),
      ],
      isMultiSelect: true,
    },
    {
      filterKey: "chargeStatuses",
      filterName: t("filters.chargeStatus"),
      columnKey: "chargeStatus",
      options: [
        {
          label: t("statuses.DRIVER"),
          value: "DRIVER",
        },
        {
          label: t("statuses.DEBTOR"),
          value: "DEBTOR",
        },
      ],
      isMultiSelect: true,
    },
    {
      filterKey: "paymentStatuses",
      filterName: t("filters.paymentStatus"),
      columnKey: "paymentStatus",
      options: [
        {
          label: t("statuses.UNPAID"),
          value: "UNPAID",
        },
        {
          label: t("statuses.PAID"),
          value: "PAID",
        },
      ],
      isMultiSelect: true,
    },
    {
      filterType: "daterange",
      filterKey: "fineDate",
      filterName: t("filters.dateRange"),
      columnKey: "fineDate",
      isMultiSelect: false,
    },
  ];

  return {
    searchInputFilters,
    selectFilters,
  };
}
