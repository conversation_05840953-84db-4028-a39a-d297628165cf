import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock } from "@phosphor-icons/react";
import { QUARTER_MINUTE_OPTIONS, getNearest15MinTime } from "../../utils";
import { DialogFooter } from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { type InitialFormState } from "./traffic-fine-dialog";

type SearchAgreementDialogProps = {
  state: InitialFormState;
  formAction: (formData: FormData) => void;
  pending: boolean;
  onCancel: () => void;
};

export default function SearchAgreementDialog({ state, formAction, pending, onCancel }: SearchAgreementDialogProps) {
  const t = useTranslations("TrafficFines.createTrafficFine");
  const [date, setDate] = useState<Date>(new Date());

  return (
    <form action={formAction} className="space-y-4 p-4">
      <h3 className="text-base font-bold">{t("trafficTicketDetails")}</h3>

      <div className="grid grid-cols-2 gap-4">
        <div className="flex flex-col gap-[6px]">
          <Label htmlFor="ticketNumber">{t("ticketNo")}</Label>
          <Input id="ticketNumber" name="ticketNumber" placeholder={t("enterTicketNo")} />
          {state.errors?.ticketNumber && <p className="text-sm text-red-500">{t("errors.ticketNumberRequired")}</p>}
          {state.errors?.duplicateFine && (
            <p className="text-sm text-red-500">{t("errors.fineCreatedForThisTicket")}</p>
          )}
        </div>

        <div className="flex flex-col gap-[6px]">
          <Label htmlFor="plateNo">{t("licenseNo")}</Label>
          <Input id="plateNo" name="plateNo" placeholder={t("enterLicenseNo")} />
          {state.errors?.plateNo && <p className="text-sm text-red-500">{t("errors.plateNoRequired")}</p>}
        </div>

        <div className="flex flex-col gap-[6px]">
          <Label htmlFor="violationDate">{t("violationDate")}</Label>
          <DatePicker disableNextYearDates={true} disableFutureDates={true} date={date} setDate={setDate} />
          <input type="hidden" name="violationDate" value={date.toISOString()} />
          {/* <Input id="violationDate" name="violationDate" type="date" placeholder={t("violationDate")} /> */}
          {state.errors?.violationDate && <p className="text-sm text-red-500">{t("errors.violationDateRequired")}</p>}
        </div>

        <div className="flex flex-col gap-[6px]">
          <Label htmlFor="violationTime">{t("time")}</Label>
          <Input id="violationTime" name="violationTime" placeholder={t("time")} type="time" />
          {/* <Select name="violationTime" defaultValue={getNearest15MinTime()}>
            <SelectTrigger id="violationTime">
              <div className="flex items-center gap-2">
                <Clock size={18} className="text-slate-400" />
                <SelectValue placeholder={t("time")} />
              </div>
            </SelectTrigger>
            <SelectContent>
              {QUARTER_MINUTE_OPTIONS.map((t) => (
                <SelectItem key={t.value} value={t.value}>
                  {t.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select> */}
          {state.errors?.violationTime && <p className="text-sm text-red-500">{t("errors.timeRequired")}</p>}
        </div>

        <div>
          <Label htmlFor="amount">{t("amount")}</Label>
          <Input id="amount" name="amount" type="number" placeholder={t("enterAmount")} />
          {state.errors?.amount && <p className="text-sm text-red-500">{t("errors.amountRequired")}</p>}
        </div>
      </div>

      {state.errors?.general && <p className="text-sm text-red-500">{state.errors.general}</p>}

      <DialogFooter className="gap-3 p-4">
        <Button variant="outline" onClick={onCancel}>
          {t("cancel")}
        </Button>
        <Button type="submit" className="!m-0" disabled={pending}>
          {pending && <Loader2 className="me-2 h-4 w-4 animate-spin" />}
          {t("getAgreementDetails")}
        </Button>
      </DialogFooter>
    </form>
  );
}
