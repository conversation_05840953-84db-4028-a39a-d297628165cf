"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

import { ReconciliationReportDialog } from "./reconciliation-report-dialog";

export function DownloadReport() {
  const [isReconciliationReportDialogOpen, setIsReconciliationReportDialogOpen] = useState<boolean>(false);

  const handleOpenReconciliationReportDialog = () => {
    setIsReconciliationReportDialogOpen(true);
  };

  return (
    <>
      <Button variant="outline" onClick={handleOpenReconciliationReportDialog}>
        Download Report
      </Button>
      <ReconciliationReportDialog
        open={isReconciliationReportDialogOpen}
        onOpenChange={setIsReconciliationReportDialogOpen}
      />
    </>
  );
}
