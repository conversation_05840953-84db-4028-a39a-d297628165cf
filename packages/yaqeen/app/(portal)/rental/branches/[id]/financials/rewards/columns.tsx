"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

type Reward = {
  displayId: string;
  trackId: string;
  customerIdentifier: string;
  provider: string;
  bookingDate: number;
  rewardedOn: number;
  value: string;
  valueType: string;
  status: string;
  netAmount: string;
};

export const columns: ColumnDef<Reward>[] = [
  {
    accessorKey: "displayId",
    header: "Carpro's Display ID",
  },
  {
    accessorKey: "trackId",
    header: "Track Id",
  },
  {
    accessorKey: "customerIdentifier",
    header: "Customer Identifier",
  },
  {
    accessorKey: "provider",
    header: "Reward Provider",
  },
  {
    accessorKey: "bookingDate",
    header: "Booking Date",
    cell: ({ row }) => <div>{format(new Date(row.getValue<number>("bookingDate") * 1000), "dd/MM/yyyy")}</div>,
  },
  {
    accessorKey: "rewardedOn",
    header: "Rewarded Date",
    cell: ({ row }) => (
      <div>
        {row.getValue<number>("rewardedOn")
          ? format(new Date(row.getValue<number>("rewardedOn") * 1000), "dd/MM/yyyy")
          : "N.A"}
      </div>
    ),
  },
  {
    accessorKey: "value",
    header: "Reward Value",
    cell: ({ row }) => (
      <div>{row.getValue<number>("value").toLocaleString("en-US", { minimumFractionDigits: 2 })} SAR</div>
    ),
  },
  {
    accessorKey: "valueType",
    header: "Reward Value Type",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "netAmount",
    header: "Transaction Amount",
    cell: ({ row }) => (
      <div>
        {row.getValue<number>("netAmount")
          ? `${row.getValue<number>("netAmount").toLocaleString("en-US", { minimumFractionDigits: 2 })} SAR`
          : "-"}
      </div>
    ),
  },
];
