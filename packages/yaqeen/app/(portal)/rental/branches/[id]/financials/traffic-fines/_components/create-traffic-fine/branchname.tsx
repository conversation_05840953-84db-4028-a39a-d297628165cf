import { type Branch } from "@/api/contracts/branch-contract";
import { useLocale } from "next-intl";
import { useCustomQuery } from "@/lib/hooks/use-query";

export const BranchName = ({ branchId }: { branchId: number }) => {
  const locale = useLocale() as "en" | "ar";

  const {
    data: branchesData,
    isLoading: isLoadingBranches,
    isError,
  } = useCustomQuery<{ data: Branch[] }>(["branches"], "/next-api/branches", {
    staleTime: 24 * 60 * 60 * 1000, // 1 day
  });

  const branches = branchesData?.data || [];
  if (isLoadingBranches) return <div>Loading...</div>;
  if (isError) return <div>Error loading branches</div>;
  if (!branches || branches.length === 0) return <div>No branches found</div>;
  const branch = branches.find((branch) => branch.id === branchId);
  return <div>{branch?.name?.[locale]}</div>;
};
