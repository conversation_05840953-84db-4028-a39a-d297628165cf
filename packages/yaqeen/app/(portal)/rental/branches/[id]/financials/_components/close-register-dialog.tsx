import { But<PERSON> } from "@/components/ui/button";
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useState } from "react";
import { closeCashRegister, depositCashRegister } from "@/lib/actions/cash-register-actions";
import { ArrowLeft } from "@phosphor-icons/react";
import { useToast } from "@/lib/hooks/use-toast";
import { ContinueButton } from "@/components/ContinueButton";
import { useTranslations } from "next-intl";
interface CloseRegisterDialogProps {
  onOpenChange: (open: boolean) => void;
  totalBalance: number;
  registerId: string;
  displayRegisterNo: number;
}

export function CloseRegisterDialog({
  onOpenChange,
  totalBalance,
  registerId,
  displayRegisterNo,
}: CloseRegisterDialogProps) {
  const tCommon = useTranslations("common");
  const t = useTranslations("CashRegister");
  const [showDepositView, setShowDepositView] = useState(false);
  const { toast } = useToast();
  const [, depositAction] = useActionState(async () => {
    const response = await depositCashRegister(Number(registerId));
    if (response.status === 200) {
      toast({
        title: t("toast.deposit.success.title"),
        description: t("toast.deposit.success.description", { displayRegisterNo }),
        variant: "success",
      });
    } else {
      toast({
        title: t("toast.deposit.error.title"),
        description: t("toast.deposit.error.description", { displayRegisterNo }),
        variant: "destructive",
      });
    }

    onOpenChange(false);
  }, null);
  const [, closeAction] = useActionState(async () => {
    const response = await closeCashRegister(Number(registerId));
    if (response.status === 200) {
      toast({
        title: t("toast.close.success.title"),
        description: t("toast.close.success.description", { displayRegisterNo }),
        variant: "success",
      });
    } else {
      toast({
        title: t("toast.close.error.title"),
        description: t("toast.close.error.description", { displayRegisterNo }),
        variant: "destructive",
      });
    }

    onOpenChange(false);
  }, null);

  // Round down to nearest whole number for deposit amount
  const amountToDeposit = Math.floor(totalBalance);
  const remainingBalance = totalBalance - amountToDeposit;

  if (showDepositView) {
    return (
      <DialogContent>
        <DialogHeader className="space-y-6">
          <DialogTitle>{t("actions.createDepositRecord")}</DialogTitle>
          <DialogDescription>
            {t("cashBalancePresent")} SAR {totalBalance.toFixed(2)}
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4 space-y-2 text-muted-foreground">
          <div>
            <div className="text-base">{t("amountToDeposit")}</div>
            <div className="text-xl">SAR {amountToDeposit.toFixed(2)}</div>
          </div>
          <div>
            <div className="text-base">{t("remainingBalance")}</div>
            <div className="text-xl">SAR {remainingBalance.toFixed(2)}</div>
          </div>
        </div>
        <form action={depositAction} className="space-y-2">
          <ContinueButton className="mt-6 w-full">{t("actions.confirmDepositAndCloseRegister")}</ContinueButton>
          <Button
            variant="outline"
            className="mt-6 flex w-full items-center gap-2 ltr:flex-row rtl:flex-row-reverse"
            onClick={() => setShowDepositView(false)}
          >
            <ArrowLeft size={18} />
            {tCommon("actions.goBack")}
          </Button>
        </form>
      </DialogContent>
    );
  }

  if (totalBalance >= 0 && totalBalance < 1) {
    return (
      <DialogContent>
        <DialogHeader className="space-y-4">
          <DialogTitle>{t("closeCashRegister")}</DialogTitle>
          <DialogDescription>{t("closeCashRegisterDescription")}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-4">
          <form action={closeAction} className="flex flex-row gap-2">
            <DialogClose asChild>
              <Button variant="outline">{t("actions.cancel")}</Button>
            </DialogClose>
            <ContinueButton>{t("actions.confirm")}</ContinueButton>
          </form>
        </DialogFooter>
      </DialogContent>
    );
  }

  return (
    <DialogContent>
      <DialogHeader className="space-y-4">
        <DialogTitle>{t("cashBalancePresent")}</DialogTitle>
        <DialogDescription>
          <span className="text-base">
            {t("cashBalancePresentDescription", { totalBalance: totalBalance.toFixed(2) })}
          </span>
        </DialogDescription>
      </DialogHeader>
      <DialogFooter className="mt-4 flex !flex-col gap-2">
        <form action={closeAction} className="space-y-2">
          <ContinueButton className="w-full">{t("actions.moveFullBalanceToNextDayAndCloseRegister")}</ContinueButton>
        </form>
        {totalBalance > 0 && (
          <Button variant="outline" className="w-full" onClick={() => setShowDepositView(true)}>
            {t("actions.createDepositRecord")}
          </Button>
        )}
      </DialogFooter>
    </DialogContent>
  );
}
