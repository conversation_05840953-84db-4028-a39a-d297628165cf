"use client";

import { useLocale, useTranslations } from "next-intl";
import { type Invoice } from "@/api/contracts/invoices-contract";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { format } from "date-fns";
import { CheckCircle2, XCircle, Info, Loader2Icon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { fetchInvoiceStatus, createInvoiceNote, retryInvocieZatca, revalidateClientPath } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useState, useMemo } from "react";
import { type InvoiceStatusResponse } from "@/api/contracts/booking/invoice-contract";
import { type InvoiceNoteFormData, InvoiceNoteModal } from "../_components/invoice-note-modal";
import TooltipComponent from "@/components/tooltip-component";
import { useParams } from "next/navigation";

type ColumnContentKey =
  | "invoiceNumber"
  | "issueDate"
  | "invoiceConfigType"
  | "payStatus"
  | "bookingNumber"
  | "agreementNumber"
  | "branchName"
  | "totalInvoiceAfterVat"
  | "totalAmountPaid"
  | "invoiceStatus";

const Content = ({ contentKey }: { contentKey: ColumnContentKey }) => {
  const t = useTranslations("invoice");
  return <div className="text-start">{t(`list.columns.${contentKey}`)}</div>;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CellContent = ({ contentKey }: any) => {
  const t = useTranslations("invoice");
  return t(contentKey);
};

const LocalCellContent = ({ name }: { name: { en: string; ar: string } }) => {
  const locale = useLocale();
  return <>{locale === "ar" ? name.ar : name.en}</>;
};

function ActionsCell({ row }: { row: Row<Invoice> }) {
  const t = useTranslations("invoice");
  const { toast } = useToast();
  const params = useParams();
  const branchId = params.id as string;
  const [isLoading, setIsLoading] = useState(false);
  const [isPrintPending, setIsPrintPending] = useState(false);
  const [isCreditNoteModalOpen, setIsCreditNoteModalOpen] = useState(false);
  const [isFromCancellation, setIsFromCancellation] = useState(false);
  const invoiceStatus = row.getValue<string>("invoiceStatus");
  const invoiceConfigType = row.getValue<string>("invoiceConfigType");
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | undefined>(undefined);

  const openCreditNoteModal = (fromCancellation: boolean, invoice?: Invoice) => {
    setIsFromCancellation(fromCancellation);

    if (invoice) {
      setSelectedInvoice(invoice);
    } else {
      setSelectedInvoice(undefined);
    }

    setIsCreditNoteModalOpen(true);
  };

  const actions = useMemo(() => {
    const handlePrintInvoice = async () => {
      try {
        setIsPrintPending(true);
        const agreementNumber = String(row.getValue("agreementNumber"));
        const externalId = String(row.getValue("invoiceNumber"));
        if (!agreementNumber || !externalId) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Agreement number or external ID is missing",
            duration: 3000,
          });
          return;
        }
        const response = await fetchInvoiceStatus(agreementNumber, externalId);
        const invoiceStatus: InvoiceStatusResponse = response.body as InvoiceStatusResponse;

        if (response.status === 200 && invoiceStatus.downloadUrl) {
          window.open(invoiceStatus.downloadUrl, "_blank");
        } else {
          toast({
            variant: "destructive",
            title: t("results.toast.view.failed.title"),
            description: t("results.toast.view.failed.description"),
            duration: 3000,
          });
        }
      } catch (error) {
        console.error("Error fetching invoice status:", error);
        const errorMessage = (error as { desc?: string }).desc || t("results.toast.view.error.title");
        toast({
          title: t("results.toast.view.error.title"),
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsPrintPending(false);
      }
    };

    const actionsList = [];
    if (invoiceStatus === "ERROR") {
      actionsList.push({
        key: "resend",
        label: <CellContent contentKey="list.actions.resend" />, // t("list.actions.resend"),
        handler: async () => {
          try {
            setIsLoading(true);
            const invoiceNumber = String(row.getValue("invoiceNumber"));
            const invoiceConfigType = String(row.getValue("invoiceConfigType"));
            if (!invoiceNumber || !invoiceConfigType) {
              toast({
                variant: "destructive",
                title: "Error",
                description: "Invoice number or invoice Type is missing",
                duration: 3000,
              });
              return;
            }
            const response = await retryInvocieZatca(invoiceNumber, invoiceConfigType);
            if (response.status === 200 || response.status === 201) {
              toast({
                title: t("results.toast.resend.success.title"),
                description: t("results.toast.resend.success.description"),
                variant: "success",
              });
              void revalidateClientPath(`/rental/branches/${branchId}/financials/invoices`);
            } else {
              toast({
                variant: "destructive",
                title: t("results.toast.resend.failed.title"),
                description: t("results.toast.resend.failed.description"),
                duration: 3000,
              });
            }
          } catch (error) {
            console.error("Error resending invoice:", error);
            const errorMessage = (error as { desc?: string }).desc || "An error occurred";
            toast({
              title: "Error",
              description: errorMessage,
              variant: "destructive",
            });
          } finally {
            setIsLoading(false);
          }
        },
      });
    } else if (invoiceStatus === "PENDING" || invoiceStatus === "SUCCESS") {
      actionsList.push(
        { key: "print", label: <CellContent contentKey="list.actions.print" />, handler: handlePrintInvoice },
        ...(invoiceConfigType === "DRIVER_INVOICE"
          ? [
              {
                key: "cancel",
                label: <CellContent contentKey="list.actions.cancel" />,
                handler: () => openCreditNoteModal(true, row.original),
              },
            ]
          : [])
      );
    } else if (invoiceStatus === "CANCELLED") {
      actionsList.push({
        key: "print",
        label: <CellContent contentKey="list.actions.print" />,
        handler: handlePrintInvoice,
      });
    }

    return actionsList;
  }, [invoiceStatus, row, toast]);

  // TODO: replace any with appriate type
  const handleCreateCreditNote = async (data: InvoiceNoteFormData) => {
    const invoiceCategory = data.invoiceConfigType;
    try {
      const response = await createInvoiceNote({
        ...data,
        cancelInvoice: true,
        invoiceCategory: data.invoiceConfigType,
      });
      if (response.success) {
        toast({
          title:
            invoiceCategory === "CREDIT_NOTE"
              ? t("results.toast.credit_note.success.title")
              : t("results.toast.debit_note.success.title"),
          description:
            invoiceCategory === "CREDIT_NOTE"
              ? t("results.toast.credit_note.success.description")
              : t("results.toast.debit_note.success.description"),
          variant: "success",
        });
      } else {
        toast({
          title:
            invoiceCategory === "CREDIT_NOTE"
              ? t("results.toast.credit_note.failed.title")
              : t("results.toast.debit_note.failed.title"),
          description:
            response.message ||
            (invoiceCategory === "CREDIT_NOTE"
              ? t("results.toast.credit_note.failed.description")
              : t("results.toast.debit_note.failed.description")),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error creating credit note:", error);
      toast({
        title:
          invoiceCategory === "CREDIT_NOTE"
            ? t("results.toast.credit_note.error.title")
            : t("results.toast.debit_note.error.title"),
        description:
          invoiceCategory === "CREDIT_NOTE"
            ? t("results.toast.credit_note.error.description")
            : t("results.toast.debit_note.error.description"),
        variant: "destructive",
      });
    } finally {
      setIsCreditNoteModalOpen(false);
    }
  };

  if (invoiceStatus.toLowerCase() === "pending") return;

  return (
    <>
      {isCreditNoteModalOpen && (
        <InvoiceNoteModal
          isOpen={isCreditNoteModalOpen}
          onClose={() => setIsCreditNoteModalOpen(false)}
          invoiceData={selectedInvoice}
          isFromCancellation={isFromCancellation}
          noteType="CREDIT_NOTE"
          onCreateInvoiceNote={handleCreateCreditNote}
        />
      )}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="rounded-full p-2 hover:bg-gray-200">
            <span className="sr-only">Open actions</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="h-5 w-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 6.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12 17.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
              />
            </svg>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48">
          {actions.map((action) => (
            <DropdownMenuItem
              key={action.key}
              onSelect={(e) => {
                e.preventDefault();
                action.handler();
              }}
              disabled={isLoading}
            >
              {action.label}{" "}
              {(isLoading || (isPrintPending && action.key === "print")) && (
                <Loader2Icon className="ml-2 h-4 w-4 animate-spin" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}

export const columns: ColumnDef<Invoice>[] = [
  {
    accessorKey: "invoiceNumber",
    header: () => <Content contentKey="invoiceNumber" />,
  },
  {
    accessorKey: "issueDate",
    header: () => <Content contentKey="issueDate" />,
    cell: ({ row }) => (
      <div className="flex flex-col">
        <label>{format(new Date(row.getValue<number>("issueDate") * 1000), "dd MMM yyyy")}</label>
        <label>{format(new Date(row.getValue<number>("issueDate") * 1000), "HH:mm:ss")}</label>
      </div>
    ),
  },
  {
    accessorKey: "invoiceConfigType",
    header: () => <Content contentKey="invoiceConfigType" />,
    cell: ({ row }) => {
      const invoiceConfigType = row.getValue<string>("invoiceConfigType");
      return (
        <div className="flex items-center gap-2">
          <CellContent
            contentKey={
              `list.filters.invoiceConfigTypes.${invoiceConfigType.toLowerCase()}` as keyof ReturnType<
                typeof useTranslations
              >
            }
          />
          {row.original.noteReason && (
            <TooltipComponent content={row.original.noteReason}>
              <Info className="h-4 w-4 text-blue-500" />
            </TooltipComponent>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "payStatus",
    header: () => <Content contentKey="payStatus" />,
    cell: ({ row }) => {
      const payStatus = row.getValue<string>("payStatus").toLowerCase();
      const isPaid = payStatus === "paid";
      return (
        <span className={`rounded rounded-full px-2 py-1 capitalize ${isPaid ? "bg-blue-100" : "bg-red-100"}`}>
          <CellContent
            contentKey={`list.filters.paymentStatus.${payStatus}` as keyof ReturnType<typeof useTranslations>}
          />
        </span>
      );
    },
  },
  {
    accessorKey: "bookingNumber",
    header: () => <Content contentKey="bookingNumber" />,
  },
  {
    accessorKey: "agreementNumber",
    header: () => <Content contentKey="agreementNumber" />,
  },
  {
    accessorKey: "branch",
    header: () => <Content contentKey="branchName" />,
    cell: ({ row }) => {
      const branch = row.original?.branch;
      return <LocalCellContent name={branch?.name || { en: "N/A", ar: "غير متوفر" }} />;
    },
  },
  {
    accessorKey: "totalInvoiceAfterVat",
    header: () => <Content contentKey="totalInvoiceAfterVat" />,
    cell: ({ row }) => (
      <div>{row.getValue<number>("totalInvoiceAfterVat").toLocaleString("en-US", { minimumFractionDigits: 2 })}</div>
    ),
  },
  {
    accessorKey: "totalAmountPaid",
    header: () => <Content contentKey="totalAmountPaid" />,
    cell: ({ row }) => (
      <div>
        {(row.getValue<number>("totalInvoiceAfterVat") - row.getValue<number>("totalAmountPaid")).toLocaleString(
          "en-US",
          { minimumFractionDigits: 2 }
        )}{" "}
      </div>
    ),
  },
  {
    accessorKey: "invoiceStatus",
    header: () => <Content contentKey="invoiceStatus" />,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <label className="capitalize">
          <CellContent
            contentKey={
              `list.filters.invoiceStatuses.${row.getValue<string>("invoiceStatus").toLowerCase()}` as keyof ReturnType<
                typeof useTranslations
              >
            }
          />
        </label>
        {row.getValue<string>("invoiceStatus") === "SUCCESS" && (
          <CheckCircle2 className="size-4 fill-green-600 stroke-white" />
        )}
        {row.getValue<string>("invoiceStatus") === "ERROR" && (
          <TooltipComponent content={row.original.errorMessage}>
            <XCircle className="size-4 fill-red-600 stroke-white" />
          </TooltipComponent>
        )}
        {row.getValue<string>("invoiceStatus") === "PENDING" && (
          <Info className="size-4 fill-orange-600 stroke-white" />
        )}
      </div>
    ),
  },
  {
    accessorKey: "actions",
    header: "",
    cell: ActionsCell,
  },
];
