"use client";

import { ProgressBarLink, useProgressBar } from "@/components/progress-bar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { type Route } from "next";
import { useParams } from "next/navigation";
import { useQueryState } from "nuqs";
import { startTransition, useMemo, useState } from "react";
import Select from "react-select";
import { REWARD_PROVIDERS, LOYALTY_STATUS, type OptionProps } from "./constants";
import { DownloadReport } from "../_components/download-report";

export default function RewardFilter() {
  const params = useParams();
  const id = Number(params.id);
  const progress = useProgressBar();

  const today = format(new Date(), "yyyy-MM-dd'T'HH:mm");

  const [queryStartDate, setQueryStartDate] = useQueryState("startDateTime", { shallow: false });
  const [queryEndDate, setQueryEndDate] = useQueryState("endDateTime", { shallow: false });
  const [queryRewardProviders, setQueryProviders] = useQueryState("rewardProviders", { shallow: false });
  const [queryStatus, setQueryStatus] = useQueryState("status", { shallow: false });

  const [startDate, setStartDate] = useState(queryStartDate || today);
  const [endDate, setEndDate] = useState(queryEndDate || today);
  const [selectProvider, setSelectProvider] = useState<OptionProps[]>(() => {
    if (queryRewardProviders) {
      return queryRewardProviders
        .split(",")
        .map((item) => REWARD_PROVIDERS.find((sub) => sub?.value.toLocaleLowerCase() === item.toLocaleLowerCase()))
        .filter(Boolean) as OptionProps[];
    }
    return [];
  });
  const [selectStatus, setSelectStatus] = useState<OptionProps[]>(() => {
    if (queryStatus) {
      return queryStatus
        .split(",")
        .map((item) => LOYALTY_STATUS.find((sub) => sub?.value.toLocaleLowerCase() === item.toLocaleLowerCase()))
        .filter(Boolean) as OptionProps[];
    }
    return [];
  });

  const isValidate = useMemo(() => {
    return startDate && endDate && selectProvider.length > 0 && selectStatus.length > 0;
  }, [startDate, endDate, selectProvider, selectStatus]);

  const handleApplyFilters = () => {
    progress.start();
    startTransition(() => {
      if (startDate) {
        void setQueryStartDate(format(startDate, "yyyy-MM-dd'T'HH:mm"));
      }
      if (endDate) {
        void setQueryEndDate(format(endDate, "yyyy-MM-dd'T'HH:mm"));
      }
      if (selectProvider.length > 0) {
        void setQueryProviders(selectProvider.map((item) => item?.value).join(","));
      }

      if (selectStatus.length > 0) {
        void setQueryStatus(selectStatus.map((item) => item?.value).join(","));
      }
      progress.done();
    });
  };

  const clearFilters = () => {
    progress.start();
    startTransition(() => {
      // reset local state
      setStartDate(today);
      setEndDate(today);
      setSelectProvider([]);
      setSelectStatus([]);

      // reset query state
      void setQueryStartDate(null);
      void setQueryEndDate(null);
      void setQueryProviders(null);
      void setQueryStatus(null);
      progress.done();
    });
  };

  return (
    <section className="border-b bg-slate-50">
      <div className="flex w-full flex-col self-stretch px-4">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${id}` as Route}>Home</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">Rewards</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full justify-between py-6">
          <div className="w-full max-w-2xl space-y-4">
            <h1 className="text-3xl font-medium tracking-tight">Rewards</h1>
            <div className="mb-4 flex gap-4">
              <div className="flex-1">
                <Label htmlFor="rewardStartDate" className="w-full text-sm font-medium text-slate-900">
                  Start Date
                </Label>
                <Input
                  type="datetime-local"
                  id="rewardStartDate"
                  name="rewardStartDate"
                  value={startDate}
                  onChange={(e) => {
                    setStartDate(e.target.value);
                  }}
                  max={today}
                />
              </div>
              <div className="flex-1">
                <Label htmlFor="rewardEndDate" className="w-full text-sm font-medium text-slate-900">
                  End Date
                </Label>
                <Input
                  type="datetime-local"
                  id="rewardEndDate"
                  name="rewardEndDate"
                  value={endDate}
                  onChange={(e) => {
                    setEndDate(e.target.value);
                  }}
                  max={today}
                />
              </div>
            </div>

            <div className="mb-4 flex gap-4">
              <div className="flex-1">
                <Label>Providers</Label>
                <Select
                  name="rewardProviders"
                  value={selectProvider}
                  options={REWARD_PROVIDERS}
                  instanceId="rewardProviders"
                  isMulti
                  onChange={(selected) => {
                    setSelectProvider(selected as OptionProps[]);
                  }}
                />
              </div>
              <div className="flex-1">
                <Label>Status</Label>
                <Select
                  name="rewardStatus"
                  value={selectStatus}
                  options={LOYALTY_STATUS}
                  instanceId="status"
                  isMulti
                  onChange={(selected) => {
                    setSelectStatus(selected as OptionProps[]);
                  }}
                />
              </div>
            </div>

            <div className="mb-4 mt-2 flex gap-4">
              <Button onClick={handleApplyFilters} disabled={!isValidate}>
                Search
              </Button>
              <Button variant="outline" onClick={clearFilters}>
                Clear
              </Button>
            </div>
          </div>
          <div className="space-y-4">
            <DownloadReport />
          </div>
        </div>
      </div>
    </section>
  );
}
