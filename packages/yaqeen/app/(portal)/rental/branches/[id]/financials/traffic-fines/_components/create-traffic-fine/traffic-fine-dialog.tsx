import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { DialogDescription } from "@radix-ui/react-dialog";
import { Separator } from "@/components/ui/separator";
import { useState, useActionState, useEffect } from "react";
import Loading from "./loading-indicator";
import SearchAgreementDialog from "./search-agreement-form";
import { searchAgreementForFine } from "../../actions";
import InvoiceFormDialog from "./invoice-form";
import type { Agreement } from "@/api/contracts/booking/schema";

type CreateFineDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export type InitialFormState = {
  errors: {
    ticketNumber?: string;
    duplicateFine?: string;
    plateNo?: string;
    violationDate?: string;
    violationTime?: string;
    amount?: string;
    general?: string;
  };
  success: boolean;
  formValues?: {
    ticketNumber: string;
    plateNo: string;
    violationDate: string;
    violationTime: string;
    fineDate: number;
    amount: string;
    agreement: Agreement;
  };
};

export default function CreateFineDialog({ open, onOpenChange }: CreateFineDialogProps) {
  const t = useTranslations("TrafficFines.createTrafficFine");
  const [step, setStep] = useState<"form" | "loading" | "result">("form");
  const initialState: InitialFormState = {
    errors: {},
    success: false,
  };

  const [state, formAction, pending] = useActionState(searchAgreementForFine, initialState);

  useEffect(() => {
    if (state.success && state.formValues?.agreement) {
      setStep("result");
    }
  }, [state.success]);

  return step === "loading" ? (
    <Loading />
  ) : (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[887px] max-w-full p-0">
        <DialogHeader className="p-4">
          <DialogTitle className="text-md font-bold">{t("recordFine")}</DialogTitle>
          <DialogDescription className="text-sm text-slate-600">{t("recordFineDescription")}</DialogDescription>
        </DialogHeader>
        <Separator />
        {step === "form" && (
          <SearchAgreementDialog
            state={state}
            formAction={formAction}
            pending={pending}
            onCancel={() => {
              setStep("form");
              onOpenChange(false);
            }}
          />
        )}
        {step === "result" && state?.formValues?.agreement && (
          <InvoiceFormDialog
            agreementFormState={state}
            agreementData={state.formValues.agreement}
            onCancel={() => {
              setStep("form");
              onOpenChange(false);
            }}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
