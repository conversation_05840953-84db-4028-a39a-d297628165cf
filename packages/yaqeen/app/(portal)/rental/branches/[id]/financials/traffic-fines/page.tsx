import { Suspense } from "react";
import { api } from "@/api";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import TrafficFinesTable from "./_components/table";
import { formatDateToEpoch } from "./utils";
import Header from "./_components/header";

type PageProps = {
  searchParams: Promise<Record<string, string | undefined>>;
  params: Promise<{ id: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const params = await props.params;
  const branchId = params.id;

  const {
    pageNumber,
    pageSize,
    ticketNumbers,
    agreementNumbers,
    trafficFineIds,
    bookingNos,
    plateNos,
    chargeStatuses,
    paymentStatuses,
    fineDate,
    branchIds,
    sort,
    order,
  } = searchParams;

  const fineDateRangeStart = fineDate?.split(",")[0];
  const fineDateRangeEnd = fineDate?.split(",")[1];

  const [trafficFines, branches, posData] = await Promise.all([
    api.trafficFine.getTrafficFineList({
      query: {
        ...(pageNumber && { page: Number(pageNumber) }),
        ...(pageSize && { size: Number(pageSize) }),
        ...(ticketNumbers && { ticketNumbers }),
        ...(agreementNumbers && { agreementNumbers }),
        ...(trafficFineIds && { trafficFineIds }),
        ...(bookingNos && { bookingNos }),
        ...(plateNos && { plateNos }),
        ...(paymentStatuses && { paymentStatuses }),
        ...(chargeStatuses && { chargeStatuses }),
        ...(fineDateRangeStart && { "fineDateRange.start": formatDateToEpoch(fineDateRangeStart) }),
        ...(fineDateRangeEnd && { "fineDateRange.end": formatDateToEpoch(fineDateRangeEnd) }),
        ...(branchIds && { branchIds }),
        sort: sort ? sort : "id",
        order: order ? order : "desc",
      },
    }),
    api.branch.getDetailedBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.branch.getAllPos({
      params: {
        id: branchId,
      },
    }),
  ]);

  if (trafficFines.status !== 200) {
    throw new Error("Error occurred while fetching traffic fines.");
  }

  if (branches.status !== 200) {
    throw new Error("Error occurred while fetching branches.");
  }

  if (posData.status !== 200) {
    throw new Error("Error occurred while fetching POS data.");
  }

  const posDataResponse = posData.body;
  return (
    <>
      <Header />
      <div className="px-6">
        <Suspense fallback={<TableSkeleton showPagination filterCount={5} />}>
          <TrafficFinesTable
            tableData={{
              total: trafficFines.body.totalElements,
              data: trafficFines.body.content,
            }}
            branches={branches.body.data}
            posData={posDataResponse.data}
          />
        </Suspense>
      </div>
    </>
  );
}
