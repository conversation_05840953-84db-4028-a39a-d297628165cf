"use client";

import { useState, useMemo } from "react";
import { format, addMonths } from "date-fns";
import Select from "react-select";

import { <PERSON><PERSON>, DialogContent, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { toast } from "@/lib/hooks/use-toast";

import { REWARD_PROVIDERS, LOYALTY_STATUS, type OptionProps } from "../rewards/constants";

const DEFAULT_LOYALTY_STATUS = [{ value: "SUCCESS", label: "SUCCESS" }];

export interface ReconciliationReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ReconciliationReportDialog({ open, onOpenChange }: ReconciliationReportDialogProps) {
  const today = format(new Date(), "yyyy-MM-dd'T'HH:mm");

  const [startDate, setStartDate] = useState<string>(today);
  const [endDate, setEndDate] = useState<string>(today);
  const [selectProvider, setSelectProvider] = useState<OptionProps[]>([]);
  const [selectStatus, setSelectStatus] = useState<OptionProps[]>(DEFAULT_LOYALTY_STATUS);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const nextMonthThresholdDate = format(addMonths(new Date(startDate), 1), "yyyy-MM-dd'T'HH:mm");
  const selectProviderStr = selectProvider.map((item) => item?.value).join(",");
  const selectStatusStr = selectStatus.map((item) => item?.value).join(",");

  const isValidate = useMemo(() => {
    return startDate && endDate && selectProvider.length > 0;
  }, [startDate, endDate, selectProvider]);

  const clearForm = () => {
    setStartDate(today);
    setEndDate(today);
    setSelectProvider([]);
    setSelectStatus(DEFAULT_LOYALTY_STATUS);
  };

  const downloadExcel = async () => {
    const queryObj = {
      startDateTime: startDate,
      endDateTime: endDate,
      rewardProviders: selectProviderStr,
      status: selectStatusStr,
    };

    const queryParams = new URLSearchParams(queryObj);

    setIsLoading(true);
    const res = await fetch(`/next-api/download/reconciliation-report?${queryParams}`);

    if (!res.ok) {
      setIsLoading(false);
      onOpenChange(false);
      clearForm();

      toast({
        title: "Error while download report ",
        variant: "destructive",
      });

      throw new Error("Failed to download report");
    }

    const blob = await res.blob();
    const url = window.URL.createObjectURL(blob);

    const contentDisposition = res.headers.get("content-disposition");
    const fileName = contentDisposition?.split("filename=")[1]?.replace(/"/g, "") || "";

    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);

    setIsLoading(false);
    onOpenChange(false);
    clearForm();

    toast({
      title: "Reconciliation Report download successfully",
      variant: "success",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[855px]">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-xl">Reconciliation Report</DialogTitle>
          <Separator className="my-4" />
          <h6 className="font-medium">Please only select max date a months (31 days)</h6>
        </DialogHeader>
        <div className="mb-4 flex gap-4">
          <div className="flex-1">
            <Label htmlFor="rewardStartDate" className="w-full text-sm font-medium text-slate-900">
              Start Date
            </Label>
            <Input
              type="datetime-local"
              id="rewardStartDate"
              name="rewardStartDate"
              defaultValue={startDate}
              onChange={(e) => {
                setStartDate(e.target.value);
              }}
              max={today}
            />
          </div>
          <div className="flex-1">
            <Label htmlFor="rewardEndDate" className="w-full text-sm font-medium text-slate-900">
              End Date
            </Label>
            <Input
              type="datetime-local"
              id="rewardEndDate"
              name="rewardEndDate"
              defaultValue={endDate}
              onChange={(e) => {
                setEndDate(e.target.value);
              }}
              min={startDate}
              max={nextMonthThresholdDate}
            />
          </div>
        </div>
        <div className="mb-4 flex gap-4">
          <div className="flex-1">
            <Label>Providers</Label>
            <Select
              name="providers"
              value={selectProvider}
              options={REWARD_PROVIDERS}
              instanceId="providers"
              required
              isMulti
              onChange={(selected) => {
                setSelectProvider(selected as OptionProps[]);
              }}
            />
          </div>
          <div className="flex-1">
            <Label>Status</Label>
            <Select
              name="status"
              value={selectStatus}
              options={LOYALTY_STATUS}
              instanceId="status"
              required
              isDisabled
            />
          </div>
        </div>
        <DialogFooter className="mt-6">
          <Button disabled={!isValidate} onClick={downloadExcel}>
            Download Now
            {isLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : <></>}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
