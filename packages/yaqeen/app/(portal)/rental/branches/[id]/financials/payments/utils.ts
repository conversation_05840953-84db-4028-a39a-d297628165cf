import { api } from "@/api";
import { getLocale } from "next-intl/server";
import type { IBranch } from "@/api/contracts/branch-contract";

export const getBranchName = async (
  params: Promise<{
    id: string;
  }>
) => {
  const pParams = await params;
  const { id } = pParams;
  const locale = (await getLocale()) as "en" | "ar";
  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse.find((branch) => branch.id === Number(id));
  const branchName = branch?.name?.[locale] ?? `N/A`;
  return branchName;
};
