import { ContinueButton } from "@/components/ContinueButton";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { approveCashRegister } from "@/lib/actions/cash-register-actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useActionState } from "react";
import { useTranslations } from "next-intl";
interface ConfirmDepositDialogProps {
  onConfirm: () => void;
  children: React.ReactNode;
  registerId: number;
  bankId: number | null;
  displayRegisterNo: number;
}

export function ConfirmDepositDialog({
  onConfirm,
  children,
  registerId,
  bankId,
  displayRegisterNo,
}: ConfirmDepositDialogProps) {
  const t = useTranslations("CashRegister");
  const { toast } = useToast();

  const [, approveAction, isPending] = useActionState(async () => {
    if (!bankId) return;

    try {
      const response = await approveCashRegister(registerId, bankId);
      if (response.status === 200) {
        toast({
          title: t("toast.deposit.confirm.title"),
          description: t("toast.deposit.confirm.description", { displayRegisterNo }),
          variant: "success",
        });
        onConfirm();
      } else {
        toast({
          title: t("toast.close.error.title"),
          description: t("toast.close.error.description", { displayRegisterNo }),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to approve cash register:", error);
    }
  }, null);

  return (
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>{t("confirmDeposit")}</DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        {children}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-full">
                <form action={approveAction}>
                  <ContinueButton className="w-full" disabled={bankId === null || isPending}>
                    {t("confirmDepositAndCloseRegister")}
                  </ContinueButton>
                </form>
              </div>
            </TooltipTrigger>
            {!bankId && (
              <TooltipContent>
                <p>{t("pleaseSelectABankFirst")}</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>
    </DialogContent>
  );
}
