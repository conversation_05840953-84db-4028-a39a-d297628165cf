import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { DotsThree, File, XCircle } from "@phosphor-icons/react";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { useToast } from "@/lib/hooks/use-toast";
import { useState, startTransition } from "react";
import WithdrawFromDepositDialog from "./withdraw-from-deposit-dialog";
import { useQuery } from "@tanstack/react-query";
import { type PosItem } from "./table";
import { Loader2 } from "lucide-react";
import { downloadInvoiceHtml } from "../actions";
import { type TrafficFine } from "@/api/contracts/rental/traffic-fine-contract";
import { useRouter } from "next/navigation";
import { useProgressBar } from "@/components/progress-bar";

interface ActionsMenuProps {
  row: TrafficFine;
  posData: PosItem[];
}

export default function ActionsMenu({ row, posData }: ActionsMenuProps) {
  const {
    id: trafficFineId,
    bookingId,
    issuedBranchId,
    agreementNo,
    invoiceNumber,
    paymentStatus,
    paymentLinkExpiryDate,
  } = row;
  const t = useTranslations("TrafficFines");
  const { toast } = useToast();
  const [menuOpen, setMenuOpen] = useState(false);
  const [openWithdrawFromDepositDialog, setOpenWithdrawFromDepositDialog] = useState(false);
  const [downloadInvoiceLoading, setDownloadInvoiceLoading] = useState(false);
  const router = useRouter();
  const nowSec = Date.now() / 1000;
  const paymentLinkExpired = !paymentLinkExpiryDate || paymentLinkExpiryDate < nowSec;
  const progress = useProgressBar();

  const {
    data: eligibilityData,
    isFetching: fetchingEligibilityData,
    isFetched,
    refetch: refetchEligibilityData,
    isError,
    error,
  } = useQuery({
    queryKey: ["traffic-fine-security-deposit-eligibility", trafficFineId],
    queryFn: async () => {
      const res = await fetch(`/next-api/traffic-fine/check-security-deposit?trafficFineId=${trafficFineId}`);
      if (res.status === 400) {
        throw new Error("Ineligible");
      }
      if (!res.ok) {
        throw new Error("Unexpected Error");
      }
      return res.json();
    },
    enabled: false,
    staleTime: 3 * 60 * 60 * 1000, // 3 hours
  });

  const handlePayFromDeposit = async () => {
    let result;

    // Only refetch if not already fetched
    if (!isFetched) {
      result = await refetchEligibilityData();
    } else {
      result = { isError, error };
    }

    if (result.isError) {
      const message = result.error?.message;
      if (message === "Ineligible") {
        toast({
          title: (
            <div className="flex items-start gap-2">
              <XCircle weight="fill" className="h-6 w-6 flex-shrink-0 text-red-600" />
              <div className="space-y-1">
                <p className="text-base font-bold text-red-700">{t("securityDeposit.toastTitle")}</p>
                <p className="text-sm font-normal text-slate-600">{t("securityDeposit.toastDescription")}</p>
              </div>
            </div>
          ),
        });
      } else throw new Error(message);
      return;
    }

    setOpenWithdrawFromDepositDialog(true);
  };

  const handlePrintInvoice = async () => {
    if (!invoiceNumber) {
      toast({
        title: t("invoice.notFound"),
        description: t("invoice.checkInvoiceNo"),
        variant: "destructive",
      });
      return;
    }

    setDownloadInvoiceLoading(true);
    try {
      const res = await downloadInvoiceHtml(agreementNo, invoiceNumber);

      if (res.status !== 200) {
        toast({
          title: t("invoice.downloadFailed"),
          description: t("invoice.failedToDownloadInvoice"),
          variant: "destructive",
        });
        return;
      }

      const data = res.body.htmlContent;

      const printWindow = window.open(
        "",
        "_blank",
        "width=" + window.screen.availWidth + ",height=" + window.screen.availHeight
      );
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.title = `Traffic Invoice - ${invoiceNumber}`;
        printWindow.document.write(data.html);
        printWindow.document.close();

        printWindow.onload = () => {
          printWindow.focus();
          printWindow.print();
        };
      } else {
        toast({
          title: t("invoice.printFailed"),
          description: t("invoice.failedToOpenPrintWindow"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error downloading invoice:", error);
      toast({
        title: t("invoice.downloadFailed"),
        description: t("invoice.failedToDownloadInvoice"),
        variant: "destructive",
      });
    } finally {
      setDownloadInvoiceLoading(false);
    }
  };

  const handlePaymentLink = () => {
    progress.start();

    startTransition(() => {
      const url = paymentLinkExpired
        ? `/rental/branches/${issuedBranchId}/financials/traffic-fines/quick-pay?trafficFineId=${trafficFineId}`
        : `/rental/branches/${issuedBranchId}/bookings/${bookingId}`;

      router.push(url);
      progress.done();
    });
  };

  const actions =
    paymentStatus === "PAID" && invoiceNumber
      ? [
          {
            label: t("actions.printInvoice"),
            icon: downloadInvoiceLoading ? (
              <Loader2 size={16} className="me-2 animate-spin text-slate-900" />
            ) : (
              <File size={16} className="me-2 text-slate-900" />
            ),
            onClick: handlePrintInvoice,
            disabled: downloadInvoiceLoading,
          },
        ]
      : [
          {
            label: paymentLinkExpired ? t("actions.createPaymentLink") : t("actions.viewPaymentLink"),
            icon: <File size={16} className="me-2 text-slate-900" />,
            onClick: handlePaymentLink,
            disabled: false,
          },
          {
            label: t("actions.payFromDeposit"),
            icon: fetchingEligibilityData ? (
              <Loader2 size={16} className="me-2 animate-spin text-slate-900" />
            ) : (
              <File size={16} className="me-2 text-slate-900" />
            ),
            onClick: handlePayFromDeposit,
            disabled: fetchingEligibilityData,
          },
        ];

  return (
    <>
      {openWithdrawFromDepositDialog && eligibilityData && (
        <WithdrawFromDepositDialog
          open={openWithdrawFromDepositDialog}
          onOpenChange={setOpenWithdrawFromDepositDialog}
          trafficFineId={trafficFineId}
          bookingId={bookingId}
          issuedBranchId={issuedBranchId}
          posData={posData}
          depositAmount={eligibilityData.depositAmount}
          fineAmount={eligibilityData.fineAmount}
        />
      )}
      <DropdownMenu open={menuOpen} onOpenChange={setMenuOpen}>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center justify-end gap-x-1">
            <Button variant="outline" className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
              <DotsThree className="h-4 w-4" />
              <span className="sr-only">Open Menu</span>
            </Button>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[190px]">
          <DropdownMenuLabel className="font-bold">{t("actions.actions")}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {actions.map((action, index) => (
            <DropdownMenuItem
              key={index}
              onSelect={async (e) => {
                e.preventDefault();
                if (!action.disabled) {
                  await action.onClick();
                  setMenuOpen(false);
                }
              }}
              disabled={action.disabled}
              className="cursor-pointer font-medium"
            >
              {action.icon}
              {action.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
