import Image from "next/image";
import { useTranslations } from "use-intl";

export default function Loading() {
  const t = useTranslations("TrafficFines.createTrafficFine");
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-slate-900 bg-opacity-90">
      <div className="flex flex-col items-center justify-center gap-3 text-white">
        <Image src="/static/lumi-lemon.gif" alt="Loading..." width={128} height={128} className="mb-5" />
        <div className="text-3xl font-bold">{t("loadingDetails")}</div>
        <div className="mt-3 text-xl font-medium">{t("dontCloseOrRefresh")}</div>
      </div>
    </div>
  );
}
