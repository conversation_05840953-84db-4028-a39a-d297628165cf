"use client";

import { DataTable } from "@/components/ui/data-table/data-table";
import { type MessageKeys, useLocale, useTranslations } from "next-intl";
import { invoiceCategoryLabels } from "../../../types";
import { columns } from "../invoices/columns";
import { type Invoice } from "@/api/contracts/invoices-contract";
import { Branch, BranchesListRes } from "@/api/contracts/branch-contract";

interface InvoicesTableProps {
  data: {
    data: Invoice[];
    total: number;
  };
  pageSize: number;
  invoiceStats: {
    totalAmount: number;
    totalAmountDue: number;
  };
  brances: BranchesListRes;
}

export default function InvoicesTable({ data, pageSize, invoiceStats, brances }: InvoicesTableProps) {
  const t = useTranslations("invoice");
  const locale = useLocale();

  return (
    <>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-500">{t("list.stats.totalAmount")}:</span>
          <span className="text-sm text-slate-500">
            (SAR{" "}
            {(invoiceStats as { totalAmount: number }).totalAmount.toLocaleString("en-US", {
              minimumFractionDigits: 2,
            })}
            )
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-500">{t("list.stats.totalAmountDue")}:</span>
          <span className="text-sm text-slate-500">
            (SAR{" "}
            {(invoiceStats as { totalAmountDue: number }).totalAmountDue.toLocaleString("en-US", {
              minimumFractionDigits: 2,
            })}
            )
          </span>
        </div>
      </div>
      <DataTable
        searchFilters={[
          {
            label: t("list.filters.search.bookingNumbers"),
            value: "bookingNumbers",
          },
          {
            label: t("list.filters.search.invoiceNumbers"),
            value: "invoiceNumbers",
          },
          {
            label: t("list.filters.search.agreementNumbers"),
            value: "agreementNumbers",
          },
        ]}
        filters={[
          {
            filterKey: "invoiceConfigTypes",
            filterName: t("list.filters.invoiceConfigTypes.title"),
            columnKey: "invoiceConfigType",
            isMultiSelect: true,
            options: [
              ...Object.entries(invoiceCategoryLabels).map(([value]) => {
                const labelKey = `list.filters.invoiceConfigTypes.${value.toLowerCase()}`;
                return {
                  label: t(
                    labelKey as MessageKeys<
                      { list: { filters: { invoiceConfigTypes: Record<string, string> } } },
                      "list.filters.invoiceConfigTypes"
                    >
                  ),
                  value,
                };
              }),
            ],
          },
          {
            filterKey: "invoiceStatuses",
            filterName: t("list.filters.invoiceStatuses.title"),
            columnKey: "invoiceStatus",
            isMultiSelect: true,
            options: [
              { label: t("list.filters.invoiceStatuses.success"), value: "SUCCESS" },
              { label: t("list.filters.invoiceStatuses.pending"), value: "PENDING" },
              { label: t("list.filters.invoiceStatuses.error"), value: "ERROR" },
              // { label: t("list.filters.invoiceStatuses.no_zatca"), value: "NO_ZATCA" },
            ],
          },
          {
            filterType: "daterange",
            filterKey: "issueDate",
            filterName: "Invoice date",
            columnKey: "issueDate",
            isMultiSelect: false,
          },
          {
            filterKey: "paymentStatus",
            filterName: t("list.filters.paymentStatus.title"),
            columnKey: "payStatus",
            isMultiSelect: false,
            options: [
              { label: t("list.filters.paymentStatus.paid"), value: "paid" },
              { label: t("list.filters.paymentStatus.unpaid"), value: "unpaid" },
            ],
          },
          {
            filterKey: "branchIds",
            filterName: t("list.filters.branch.title"),
            columnKey: "branch",
            isMultiSelect: true,
            options: brances.map((branch: Branch) => ({
              label: branch.name[locale as "en" | "ar"],
              value: branch.id.toString(),
            })),
          },
        ]}
        searchPlaceholder={t("list.searchPlaceholder")}
        columns={columns}
        data={data}
        emptyMessage={t("list.emptyMessage")}
        pageSize={pageSize}
        styleClasses={{
          wrapper: "mt-4",
        }}
      />
    </>
  );
}
