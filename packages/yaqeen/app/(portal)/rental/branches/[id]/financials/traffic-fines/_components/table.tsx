"use client";

import { type TrafficFine } from "@/api/contracts/rental/traffic-fine-contract";
import { useTrafficFineColumns } from "./columns";
import { useTranslations } from "next-intl";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type Branch } from "@/api/contracts/branch-contract";
import { useTrafficFineFilters } from "./filters";

export type PosItem = {
  posId: string;
};

interface TrafficFinesTableProps {
  tableData: {
    total: number;
    data: TrafficFine[];
  };
  branches: Branch[];
  posData: PosItem[];
}

export default function TrafficFinesTable({ tableData, branches, posData }: TrafficFinesTableProps) {
  const t = useTranslations("TrafficFines");
  const columns = useTrafficFineColumns(branches, posData);
  const { searchInputFilters, selectFilters } = useTrafficFineFilters(branches);

  return (
    <DataTable
      searchPlaceholder={t("searchPlaceholder")}
      columns={columns}
      columnVisibility={{
        id: true,
        paymentStatus: true,
        invoiceNumber: false,
      }}
      searchFilters={searchInputFilters}
      filters={selectFilters}
      data={{
        total: tableData.total,
        data: tableData.data,
      }}
      emptyMessage={t("noFines")}
      paginationEnabled={true}
      leftPinnedColumn={false}
      styleClasses={{
        wrapper: "mt-4",
      }}
    />
  );
}
