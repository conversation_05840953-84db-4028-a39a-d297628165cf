import { Suspense } from "react";
import { CardSkeleton } from "@/components/ui/skeleton";
import { generatePaymentLink } from "../actions";
import PaymentCard from "./_components/payment-card";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{
    trafficFineId?: string;
  }>;
}) {
  const _params = await params;
  const branchId = _params.id;
  const _sParams = await searchParams;
  const { trafficFineId } = _sParams;

  const trafficFineResponse = await generatePaymentLink(trafficFineId ?? "");

  if (trafficFineResponse.status !== 200) {
    throw new Error("Error occurred while fetching traffic fine.");
  }

  return (
    <div className="flex min-h-screen w-full items-center justify-center p-6">
      <Suspense fallback={<CardSkeleton />}>
        <PaymentCard
          paymentLink={trafficFineResponse.body.paymentLink}
          paymentAmount={trafficFineResponse.body.amount}
          branchId={branchId}
        />
      </Suspense>
    </div>
  );
}
