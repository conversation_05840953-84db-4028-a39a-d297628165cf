import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getBranchName } from "../utils";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
  }>;
  params: Promise<{
    id: string;
  }>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;

  const branchName = await getBranchName(params);

  const refunds = await api.payment.getRefunds({
    query: {
      page: pageNumber,
      size: pageSize,
    },
  });

  if (refunds.status !== 200) {
    throw new Error("Failed to fetch refunds");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <div className="flex flex-col px-6 py-6">
        <h1 className="mb-6 text-2xl font-semibold">Refunds</h1>
        <h2>{branchName}</h2>
        <DataTable
          legacyPage
          searchPlaceholder="Search refunds"
          columns={columns}
          data={{
            data: refunds.body.data,
            total: refunds.body.total,
          }}
          emptyMessage="There are no refunds."
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
