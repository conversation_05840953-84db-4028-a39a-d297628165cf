import { format, parse, addMinutes, startOfDay } from "date-fns";
import { type ChargeStatus, type PaymentStatus } from "./constants";
import { type Branch } from "@/api/contracts/branch-contract";

/**
 * Converts UTC epoch seconds to a formatted date and time.
 * @param epochSeconds - UTC timestamp in seconds
 * @returns An object with `date` (DD-MM-YYYY) and `time` (HH:mm:ss)
 */
export function formatEpochToDateTime(epochSeconds: number): { date: string; time: string } {
  const date = new Date(epochSeconds * 1000);

  return {
    date: format(date, "dd/MM/yyyy"),
    time: format(date, "HH:mm:ss"),
  };
}

/**
 * Converts a date string to epoch timestamp.
 *
 * @param date - The date string to be converted.
 * @returns The epoch timestamp of the provided date.
 */
export function formatDateToEpoch(date: string): number {
  const parsedDate = parse(date, "yyyy-MM-dd", new Date());

  parsedDate.setHours(0, 0, 0, 0);

  return Math.floor(parsedDate.getTime() / 1000);
}

export function getChargeStatusColor(chargeStatus: ChargeStatus): string {
  const colorMap: Record<ChargeStatus, string> = {
    DRIVER: "bg-lumi-200",
    DEBTOR: "bg-[#FEC7AA]",
  };

  return colorMap[chargeStatus] ?? "bg-lumi-200";
}

export function getPaymentStatusColor(paymentStatus: PaymentStatus): string {
  const colorMap: Record<PaymentStatus, string> = {
    UNPAID: "bg-[#FEE2E2]",
    PAID: "bg-[#E2E8F0]",
  };

  return colorMap[paymentStatus] ?? "bg-[#FEE2E2]";
}

export function getBranchName(branches: Branch[], branchId: number, locale: string): string {
  const branch = branches.find((branch) => branch.id === branchId);
  if (!branch) return `#${branchId}`;
  return locale === "ar" ? branch.name.ar : branch.name.en;
}

/**
 * Generates an array of time strings in HH:mm format for every 15 minutes in a day.
 * @returns An array of time strings.
 */
export const QUARTER_MINUTE_OPTIONS = Array.from({ length: 96 }).map((_, i) => {
  const time = format(addMinutes(startOfDay(new Date()), i * 15), "HH:mm");
  return {
    value: time,
    label: time,
  };
});

/**
 * Calculates the nearest 15-minute interval from the current time.
 *
 * @returns {string} The formatted time string representing the nearest 15-minute interval
 * in "HH:mm" format.
 */
export const getNearest15MinTime = () => {
  const now = new Date();
  const rounded = new Date(Math.round(+now / (15 * 60 * 1000)) * (15 * 60 * 1000));
  return format(rounded, "HH:mm");
};

export const combineDateAndTime = (date: Date, time: string): number => {
  const [hours, minutes] = time.split(":").map(Number);
  const combined = new Date(date);
  combined.setHours(hours ?? 0, minutes ?? 0, 0, 0);
  return Math.floor(combined.getTime() / 1000);
};
