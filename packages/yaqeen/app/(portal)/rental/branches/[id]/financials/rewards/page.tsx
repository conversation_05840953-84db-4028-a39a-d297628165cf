import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";

import RewardFilter from "./reward-filter";

type PageProps = {
  searchParams: Promise<{
    startDateTime?: string;
    endDateTime?: string;
    rewardProviders?: string;
    status?: string;
    displayId?: string;
    customerIdentifier?: string;
    pageNumber?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.displayId ?? sParams.customerIdentifier ?? "";

  const query = {
    startDateTime: sParams.startDateTime,
    endDateTime: sParams.endDateTime,
    rewardProviders: sParams.rewardProviders,
    status: sParams.status,
  };

  const rewards = await api.payment.getRewards({
    query: {
      ...query,
      page: pageNumber,
      size: pageSize,
      sort: "id",
      order: "desc",
      query: searchQuery,
    },
  });

  if (rewards.status !== 200) {
    throw new Error("Failed to fetch Rewards");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <div className="flex flex-col px-6 py-6">
        <RewardFilter />

        <DataTable
          legacyPage
          searchFilters={[
            {
              label: "Carpro's Display ID",
              value: "displayId",
            },

            {
              label: "Customer Identifier",
              value: "customerIdentifier",
            },
          ]}
          searchPlaceholder="Search by Display Id or Customer Identifier"
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: rewards.body.data,
            total: rewards.body.total,
          }}
          emptyMessage="There are no rewards."
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
