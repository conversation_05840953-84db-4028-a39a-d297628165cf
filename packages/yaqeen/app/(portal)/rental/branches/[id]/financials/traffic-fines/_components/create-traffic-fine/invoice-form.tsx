import { type InitialFormState as AgreementFormState } from "./traffic-fine-dialog";
import { useActionState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { createTrafficFine } from "../../actions";
import { Label } from "@/components/ui/label";
import { DialogFooter } from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { CheckCircleIcon } from "@phosphor-icons/react";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLocale } from "next-intl";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { convertPlateToArabic, toNormal } from "@/lib/utils";
import type { IAgreement } from "@/api/contracts/booking/booking-contract";
import { BranchName } from "./branchname";

type InvoiceFormDialogProps = {
  agreementFormState: AgreementFormState;
  agreementData: IAgreement;
  onCancel: () => void;
};

type InitialFormState = {
  errors: {
    ticketNumber?: string;
    plateNo?: string;
    agreementNo?: string;
    fineDate?: string;
    amount?: string;
    chargeStatus?: string;
    issuedBranchId?: string;
    muncipality?: string;
    violationCode?: string;
    general?: string;
  };
  success: boolean;
};

export default function InvoiceFormDialog({ agreementFormState, agreementData, onCancel }: InvoiceFormDialogProps) {
  const driver = agreementData.driverResponse;
  const t = useTranslations("TrafficFines.createTrafficFine");
  const initialState: InitialFormState = {
    errors: {},
    success: false,
  };
  const { toast } = useToast();
  const [state, formAction, pending] = useActionState(createTrafficFine, initialState);
  const locale = useLocale();
  const params = useParams();
  const { id: issuedBranchId } = params;

  const { data: municipalities, isLoading: isMunicipalitiesLoading } = useQuery({
    queryKey: ["municipalities"],
    queryFn: async () => {
      const res = await fetch("/next-api/traffic-fine/municipalities");
      if (!res.ok) throw new Error("Failed to fetch municipalities");
      return res.json();
    },
    staleTime: 1000 * 60 * 60 * 24,
  });

  const { data: violationCodes, isLoading: isViolationCodesLoading } = useQuery({
    queryKey: ["violationCodes"],
    queryFn: async () => {
      const res = await fetch("/next-api/traffic-fine/violation-codes");
      if (!res.ok) throw new Error("Failed to fetch violation codes");
      return res.json();
    },
    staleTime: 1000 * 60 * 60 * 24,
  });

  useEffect(() => {
    if (state.errors?.general) {
      toast({
        title: "Error",
        description: state.errors.general,
        variant: "destructive",
      });
    }
  }, [state.errors?.general]);

  useEffect(() => {
    if (state.success) {
      onCancel();
      toast({
        title: (
          <div className="flex items-start gap-2">
            <CheckCircleIcon weight="fill" className="h-6 w-6 flex-shrink-0 text-lumi-600" />
            <div className="space-y-1">
              <p className="text-base font-bold text-slate-900">{t("Traffic Invoice created successfully")}</p>
              <p className="text-sm font-normal text-slate-600">
                {t("A copy of the invoice will be sent to the customer’s email address")}
              </p>
            </div>
          </div>
        ),
      });
    }
  }, [state.success]);

  return (
    <form action={formAction}>
      <h3 className="px-4 pt-8 text-base font-bold ">{t("trafficTicketDetails")}</h3>
      <input type="hidden" name="issuedBranchId" value={Number(issuedBranchId)} />
      <div className="flex gap-6 p-4">
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="ticketNumber" className="text-sm font-medium text-slate-500">
            {t("ticketNo")}
          </Label>
          <div className="text-sm font-medium">{agreementFormState.formValues?.ticketNumber}</div>
          <input type="hidden" name="ticketNumber" value={agreementFormState.formValues?.ticketNumber} />
        </div>
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="plateNo" className="text-sm font-medium text-slate-500">
            {t("licenseNo")}
          </Label>
          <div className="text-sm font-medium">
            {locale === "ar"
              ? convertPlateToArabic(agreementFormState.formValues?.plateNo ?? "")
              : agreementFormState.formValues?.plateNo}
          </div>
          <input type="hidden" name="plateNo" value={agreementFormState.formValues?.plateNo} />
        </div>
      </div>
      <Separator />
      <div className="flex gap-6 p-4">
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="fineDate" className="text-sm font-medium text-slate-500">
            {t("violationDateAndTime")}
          </Label>
          <div className="text-sm font-medium">
            {agreementFormState.formValues?.violationDate
              ? format(new Date(agreementFormState.formValues.violationDate), "dd/MM/yyyy")
              : ""}{" "}
            - {agreementFormState.formValues?.violationTime}
          </div>
          <input type="hidden" name="fineDate" value={agreementFormState.formValues?.fineDate} />
        </div>
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="amount" className="text-sm font-medium text-slate-500">
            {t("amount")}
          </Label>
          <div className="text-sm font-medium">{agreementFormState.formValues?.amount}</div>
          <input type="hidden" name="amount" value={agreementFormState.formValues?.amount} />
        </div>
      </div>
      <Separator />
      <div className="flex flex-col gap-2 p-4">
        <Label htmlFor="chargeStatus" className="text-sm font-medium text-slate-500">
          {t("chargeStatus")}
        </Label>
        <div className="text-sm font-medium">{t("invoiceToDriver")}</div>
        <input type="hidden" name="chargeStatus" value="DRIVER" />
      </div>
      <Separator />
      <div className="flex gap-6 p-4">
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="municipality" className="text-sm font-medium text-slate-500">
            {t("municipality")}
          </Label>
          <Select name="municipality">
            <SelectTrigger>
              <SelectValue placeholder={t("selectMunicipality")} />
            </SelectTrigger>
            <SelectContent>
              {isMunicipalitiesLoading ? (
                <span className="ms-2">{t("loading")}</span>
              ) : (
                municipalities?.map((m: { id: number; name: string }) => (
                  <SelectItem key={m.id} value={m.name}>
                    {m.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          {state.errors?.municipality && <p className="text-sm text-red-500">{t("errors.municipalityRequired")}</p>}
        </div>
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="violationCode" className="text-sm font-medium text-slate-500">
            {t("violationCode")}
          </Label>
          <Select name="violationCode">
            <SelectTrigger>
              <SelectValue placeholder={t("selectViolationCode")} />
            </SelectTrigger>
            <SelectContent>
              {isViolationCodesLoading ? (
                <span className="ms-2">{t("loading")}</span>
              ) : (
                violationCodes?.map((v: { id: number; nameEn: string; nameAr: string }) => (
                  <SelectItem key={v.id} value={v.id.toString()}>
                    {locale === "ar" ? v.nameAr : v.nameEn}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          {state.errors?.violationCode && <p className="text-sm text-red-500">{t("errors.violationCodeRequired")}</p>}
        </div>
      </div>
      <Separator />
      <h4 className="px-4 pt-8 text-base font-bold ">{t("agreementDetails")}</h4>
      <div className="flex gap-6 p-4">
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="agreementNo" className="text-sm font-medium text-slate-500">
            {t("agreementNo")}
          </Label>
          <div className="text-sm font-medium text-blue-600">{agreementData.agreementNo}</div>
          <input type="hidden" name="agreementNo" value={agreementData.agreementNo} />
        </div>
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="status" className="text-sm font-medium text-slate-500">
            {t("status")}
          </Label>
          <div className="text-sm font-medium">{toNormal(agreementData.status)}</div>
        </div>
      </div>
      <Separator />
      <div className="flex gap-6 p-4">
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="driver" className="text-sm font-medium text-slate-500">
            {t("driver")}
          </Label>
          <div className="text-sm font-medium">{driver.fullName}</div>
        </div>
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="mobileNo" className="text-sm font-medium text-slate-500">
            {t("mobileNo")}
          </Label>
          <div className="text-sm font-medium">{driver?.mobileNumber ?? "-"}</div>
        </div>
      </div>
      <Separator />
      <div className="flex gap-6 p-4">
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="address" className="text-sm font-medium text-slate-500">
            {t("address")}
          </Label>
          <div className="text-sm font-medium">{driver?.address?.street ?? "-"}</div>
        </div>
        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="vehicleType" className="text-sm font-medium text-slate-500">
            {t("checkInBranch")}
          </Label>
          <div className="text-sm font-medium">
            <BranchName branchId={agreementData.checkinBranch} />
          </div>
        </div>

        {agreementData.status === "ONGOING" && (
          <div className="flex flex-1 flex-col gap-2">
            <Label htmlFor="vehicleType" className="text-sm font-medium text-slate-500">
              {t("dropoffDateTime")}
            </Label>
            <div className="text-sm font-medium">
              {format(new Date(agreementData.dropOffDateTime * 1000), "dd/MM/yyyy - HH:mm")}
            </div>
          </div>
        )}
      </div>
      <DialogFooter className="gap-3 p-4">
        <Button variant="outline" onClick={onCancel}>
          {t("cancel")}
        </Button>
        <Button type="submit" className="!m-0" disabled={pending}>
          {pending && <Loader2 className="me-2 h-4 w-4 animate-spin" />}
          {t("createTrafficInvoice")}
        </Button>
      </DialogFooter>
    </form>
  );
}
