"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { type Route } from "next";

type Refund = {
  id: number;
  createdOn: number;
  amount: number;
  currency: string;
  customerEmail: string;
  customerName: string;
  referenceId: string;
  paymentId: string;
  trackId: string;
  refundStatus: string;
  initiatedFor: string;
  bookingId: string;
};

export const columns: ColumnDef<Refund>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "paymentId",
    header: "Payment ID",
    cell: ({ row }) => {
      const paymentId = row.getValue<string>("paymentId");
      return (
        <ProgressBarLink
          href={`/rental/financials/payments/${paymentId}` as Route}
          className="text-blue-600 hover:text-blue-800 hover:underline"
        >
          {paymentId}
        </ProgressBarLink>
      );
    },
  },
  {
    accessorKey: "referenceId",
    header: "Reference ID",
  },
  {
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => (
      <div>
        {row.getValue<number>("amount").toLocaleString("en-US", { minimumFractionDigits: 2 })} {row.original.currency}
      </div>
    ),
  },
  {
    accessorKey: "createdOn",
    header: "Created On",
    cell: ({ row }) => <div>{format(new Date(row.getValue<number>("createdOn") * 1000), "dd MMM yyyy HH:mm")}</div>,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue<string>("status");
      return (
        <span
          className={`inline-block rounded px-2 py-1 text-sm ${
            status === "SUCCESS" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
          }`}
        >
          {status}
        </span>
      );
    },
  },
];
