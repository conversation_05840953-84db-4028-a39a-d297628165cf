"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Separator } from "@/components/ui/separator";
import { fetchInvoiceByNumber, fetchNoteReasons } from "@/lib/actions";
import { type InvoiceSearchResponse } from "@/api/contracts/booking/invoice-contract";
import { type Invoice } from "@/api/contracts/invoices-contract";
import { debounce, throttle } from "lodash-es";
import { useTranslations } from "next-intl";

// Define the schema for credit note validation
const invoiceNoteSchema = z
  .object({
    issueDate: z.date({
      required_error: "Invoice issue date is required",
    }),
    originalInvoiceNumber: z.string().min(1, "Original invoice number is required"),
    agreementNumber: z.string().optional(),
    reason: z.string().min(1, "Reason is required"),
    remarks: z.string().optional(),
    creditedAmountBeforeVAT: z.coerce.number().positive("Amount must be positive"),
    vatPercentage: z.enum(["0", "15"], {
      errorMap: () => ({ message: "VAT percentage must be either 0% or 15%" }),
    }),
    vatAmount: z.coerce.number().min(0, "VAT amount cannot be negative"),
    totalCredit: z.coerce.number().positive("Total credit must be positive"),
  })
  .refine(
    (data) => {
      // Calculate VAT amount based on percentage
      const vatAmount = data.creditedAmountBeforeVAT * (Number.parseInt(data.vatPercentage) / 100);
      return Math.abs(data.vatAmount - vatAmount) < 0.01; // Allow for small rounding differences
    },
    {
      message: "VAT amount doesn't match the VAT percentage",
      path: ["vatAmount"],
    }
  );

// Define a specific type for the credit note schema
export type InvoiceNoteFormData = {
  issueDate: Date;
  invoiceType?: string;
  invoiceCategory?: string;
  invoiceConfigType?: string;
  originalInvoiceNumber: string;
  agreementNumber?: string;
  reason: string;
  remarks?: string;
  creditedAmountBeforeVAT: number;
  vatPercentage: "0" | "15";
  vatAmount: number;
  totalCredit: number;
  cancelInvoice?: boolean;
};

// Types for the component props
interface InvoiceNoteModalProps {
  isOpen: boolean;
  noteType: string;
  onClose: () => void;
  invoiceData?: Invoice;
  isFromCancellation?: boolean;
  onCreateInvoiceNote: (data: InvoiceNoteFormData) => Promise<void>;
}

export function InvoiceNoteModal({
  isOpen,
  onClose,
  invoiceData,
  isFromCancellation = false,
  noteType,
  onCreateInvoiceNote,
}: InvoiceNoteModalProps) {
  const t = useTranslations("invoice");
  const [isLoading, setIsLoading] = useState(false);
  const [reasonLoading, setReasonLoading] = useState(false);
  const [reasons, setReasons] = useState<{ value: string; label: string }[]>([]);
  const [originalInvoiceDetails, setOriginalInvoiceDetails] = useState<InvoiceSearchResponse | null>(null);
  // Initialize the form
  const form = useForm<InvoiceNoteFormData>({
    resolver: zodResolver(invoiceNoteSchema),
    defaultValues: {
      issueDate: new Date(),
      originalInvoiceNumber: invoiceData?.invoiceNumber || "",
      invoiceType: invoiceData?.invoiceType || "",
      agreementNumber: invoiceData?.agreementNumber || "",
      reason: isFromCancellation ? "Invoice Cancellation" : "",
      remarks: "",
      creditedAmountBeforeVAT: invoiceData?.totalInvoiceBeforeVat || 0,
      vatPercentage: "15",
      vatAmount: invoiceData?.totalVatAmount || 0,
      totalCredit: invoiceData?.totalInvoiceAfterVat || 0,
    },
  });

  // Watch for changes in form values
  const watchedOriginalInvoiceNumber = form.watch("originalInvoiceNumber");
  const watchedCreditedAmount = form.watch("creditedAmountBeforeVAT");
  const watchedVatPercentage = form.watch("vatPercentage");

  // Effect to fetch agreement number when original invoice number changes
  useEffect(() => {
    if (!isFromCancellation && watchedOriginalInvoiceNumber && watchedOriginalInvoiceNumber.length > 0) {
      const debouncedFetchDetails = debounce(async () => {
        setIsLoading(true);
        try {
          const detailsResp = await fetchInvoiceByNumber(watchedOriginalInvoiceNumber);
          if (detailsResp.status === 200 || detailsResp.status === 201) {
            const details = detailsResp.body as unknown as InvoiceSearchResponse;
            setOriginalInvoiceDetails(details);
            form.setValue("agreementNumber", details.agreementNumber.toString());
            form.setValue("originalInvoiceNumber", details.invoiceNumber);
            form.setValue("invoiceType", details.invoiceType);

            form.clearErrors("originalInvoiceNumber");
          } else {
            form.setError("originalInvoiceNumber", {
              type: "manual",
              message: (detailsResp.body as { desc?: string }).desc ?? "Invalid invoice number",
            });
            setOriginalInvoiceDetails(null);
            form.setValue("agreementNumber", "");
          }
        } catch (error) {
          console.error("Error fetching invoice details:", error);
          form.setError("originalInvoiceNumber", {
            type: "manual",
            message: "Error fetching invoice details",
          });
        } finally {
          setIsLoading(false);
        }
      }, 300); // 300ms debounce

      const throttledFetchDetails = throttle(debouncedFetchDetails, 1000); // 1000ms throttle

      void throttledFetchDetails();

      return () => {
        debouncedFetchDetails.cancel();
        throttledFetchDetails.cancel();
      };
    }
  }, [watchedOriginalInvoiceNumber, isFromCancellation, form]);

  // Effect to update VAT amount and total when credited amount or VAT percentage changes
  useEffect(() => {
    if (watchedCreditedAmount && watchedVatPercentage) {
      const vatAmount = watchedCreditedAmount * (Number.parseInt(watchedVatPercentage) / 100);
      const totalCredit = Number(watchedCreditedAmount) + Number(vatAmount);

      form.setValue("vatAmount", Math.round(vatAmount * 100) / 100);
      form.setValue("totalCredit", Math.round(totalCredit * 100) / 100);
    }
  }, [watchedCreditedAmount, watchedVatPercentage, form]);

  useEffect(() => {
    const fetchReasons = async () => {
      setReasonLoading(true);
      try {
        const noteReasons = await fetchNoteReasons(noteType);
        if (noteReasons.status !== 200) {
          throw new Error("Failed to fetch note reasons");
        }
        const reasonsData = noteReasons.body as { value: string; label: string }[];
        setReasons(reasonsData);
        form.setValue("reason", reasonsData[0]?.value || ""); // Set default reason
        form.clearErrors("reason");
      } catch (error) {
        console.error("Error fetching note reasons:", error);
      } finally {
        setReasonLoading(false);
      }
    };

    void fetchReasons();
  }, [noteType]);
  // Handle form submission
  const onSubmit = async (data: InvoiceNoteFormData) => {
    // Validate that total credit is not more than original invoice amount
    if (originalInvoiceDetails && data.totalCredit > originalInvoiceDetails.totalInvoiceAfterVat) {
      form.setError("totalCredit", {
        type: "manual",
        message: "Total credit cannot exceed the original invoice amount",
      });
      return;
    }

    setIsLoading(true);
    try {
      await onCreateInvoiceNote({
        ...data,
        invoiceConfigType: noteType,
        cancelInvoice: isFromCancellation,
      });
      // TODO: in case of error should not close onClose();
    } catch (error) {
      console.error("Error creating credit note:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open || onClose()} modal={true}>
      <DialogContent className="p-0 sm:max-w-[887px]">
        <DialogHeader className="p-4">
          <DialogTitle>
            {noteType === "DEBIT_NOTE" ? t("creditDebitNote.debit_note") : t("creditDebitNote.credit_note")}
          </DialogTitle>
          <DialogDescription>
            {isFromCancellation
              ? noteType === "DEBIT_NOTE"
                ? t("creditDebitNote.cancel_debit_description")
                : t("creditDebitNote.cancel_credit_description")
              : noteType === "DEBIT_NOTE"
                ? t("creditDebitNote.new_debit_note")
                : t("creditDebitNote.new_credit_note")}
          </DialogDescription>
        </DialogHeader>

        <Separator />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <section className="px-4 pb-4 pt-0">
                <h3 className="my-4 font-medium">
                  {noteType === "DEBIT_NOTE"
                    ? t("creditDebitNote.debit_note_details")
                    : t("creditDebitNote.credit_note_details")}
                </h3>

                {/* Invoice Issue Date */}
                <FormField
                  control={form.control}
                  name="issueDate"
                  render={({ field }) => (
                    <FormItem className="mb-4 flex flex-col">
                      <FormLabel>{t("creditDebitNote.issueDate")}</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? format(field.value, "dd/MM/yyyy") : <span>Select date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="mb-4 grid grid-cols-2 gap-4">
                  {/* Original Invoice Number */}
                  <FormField
                    control={form.control}
                    name="originalInvoiceNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.originalInvoiceNumber.label")}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={isFromCancellation}
                            placeholder={t("creditDebitNote.originalInvoiceNumber.placeholder")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Agreement Number */}
                  <FormField
                    control={form.control}
                    name="agreementNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.agreementNumber.label")}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            disabled={true}
                            placeholder={t("creditDebitNote.agreementNumber.placeholder")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Reason */}
                  <FormField
                    control={form.control}
                    name="reason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex">
                          Reason {reasonLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
                        </FormLabel>
                        <Select
                          disabled={isFromCancellation || reasonLoading}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("creditDebitNote.remarks.placeholder")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {reasons.map((reason) => (
                              <SelectItem key={reason.value} value={reason.value}>
                                {reason.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Remarks */}
                  <FormField
                    control={form.control}
                    name="remarks"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.remarks.label")}</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder={t("creditDebitNote.remarks.placeholder")} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </section>
              <Separator />

              <section className="px-4 pb-4 pt-0">
                <h3 className="mb-4 font-medium">{t("creditDebitNote.financial_details")}</h3>

                <div className="grid grid-cols-4 gap-4">
                  {/* Credited Amount Before VAT */}
                  <FormField
                    control={form.control}
                    name="creditedAmountBeforeVAT"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.creditedAmountBeforeVAT.label")}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            step="0.01"
                            disabled={isFromCancellation}
                            placeholder={t("creditDebitNote.creditedAmountBeforeVAT.placeholder")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* VAT Percentage */}
                  <FormField
                    control={form.control}
                    name="vatPercentage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.vatPercentage")}</FormLabel>
                        <Select disabled={isFromCancellation} onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="VAT %" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="0">0%</SelectItem>
                            <SelectItem value="15">15%</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* VAT Amount */}
                  <FormField
                    control={form.control}
                    name="vatAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.vatAmount")}</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" step="0.01" readOnly={true} placeholder="VAT amount" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Total Credit */}
                  <FormField
                    control={form.control}
                    name="totalCredit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("creditDebitNote.totalCredit")}</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" step="0.01" readOnly={true} placeholder="Total credit" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </section>
            </div>

            <Separator />
            <DialogFooter className="gap-2 px-4 pb-4 pt-0">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                {t("creditDebitNote.cancel_cta")}
              </Button>
              <Button type="submit" disabled={isLoading}>
                <Loader2 className={cn("mr-2 h-4 w-4 animate-spin", isLoading ? "block" : "hidden")} />
                {noteType === "DEBIT_NOTE"
                  ? t("creditDebitNote.success_debit_cta")
                  : t("creditDebitNote.success_credit_cta")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
