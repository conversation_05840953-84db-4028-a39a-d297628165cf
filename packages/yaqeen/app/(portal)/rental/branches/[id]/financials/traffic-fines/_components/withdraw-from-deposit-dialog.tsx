import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { amountFormatter } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useActionState, useEffect } from "react";
import { withdrawFromDeposit } from "../actions";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { type PosItem } from "./table";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";
import { useProgressBar } from "@/components/progress-bar";
import { startTransition } from "react";

type WithdrawFromDepositDialogProps = {
  open: boolean;
  onO<PERSON><PERSON>hange: (open: boolean) => void;
  trafficFineId: number;
  bookingId: number;
  issuedBranchId: number;
  posData: PosItem[];
  depositAmount: number;
  fineAmount: number;
};

type AmountDisplayProps = {
  label: string;
  amount: number;
};

type InitialFormState = {
  errors: {
    trafficFineId?: string;
    approvalCode?: string;
    posMachine?: string;
    cardLast4Digit?: string;
    general?: string;
  };
  success: boolean;
};

const AmountDisplay = ({ label, amount }: AmountDisplayProps) => {
  return (
    <div className="p-6">
      <div className="flex flex-col items-center gap-3 rounded bg-slate-100 px-8 py-6">
        <span className="text-slate-600">{label}</span>
        <span className="text-3xl text-slate-900">SAR {amountFormatter(amount)}</span>
      </div>
    </div>
  );
};

export default function WithdrawFromDepositDialog({
  open,
  onOpenChange,
  trafficFineId,
  bookingId,
  issuedBranchId,
  posData,
  depositAmount,
  fineAmount,
}: WithdrawFromDepositDialogProps) {
  const t = useTranslations("TrafficFines.securityDeposit");
  const initialState: InitialFormState = { errors: {}, success: false };
  const [state, formAction, pending] = useActionState(withdrawFromDeposit, initialState);
  const router = useRouter();
  const progress = useProgressBar();

  useEffect(() => {
    if (state.success) {
      onOpenChange(false);
      progress.start();

      startTransition(() => {
        router.push(`/rental/branches/${issuedBranchId}/bookings/${bookingId}`);
        progress.done();
      });
    }
  }, [state.success]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[600px] p-0">
        <DialogHeader className="p-4">
          <DialogTitle className="text-lg text-slate-900">{t("withdrawFromDeposit")}</DialogTitle>
        </DialogHeader>
        <Separator />
        <AmountDisplay label={t("amountInDeposit")} amount={depositAmount} />
        <Separator />
        <AmountDisplay label={t("fineAmount")} amount={fineAmount} />
        <Separator />
        <form action={formAction} className="flex flex-col">
          <div className="flex flex-col gap-4 p-6">
            <input type="hidden" name="trafficFineId" value={trafficFineId} />
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-2">
                <Label htmlFor="approvalCode" className="text-xs">
                  {t("approvalCode")}
                </Label>
                <Input name="approvalCode" placeholder={t("approvalCode")} className="text-sm text-slate-400" />
                {state.errors?.approvalCode && <p className="text-sm text-red-500">{t("approvalCodeRequired")}</p>}
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="posMachine" className="text-xs">
                  {t("pos")}
                </Label>
                <Select name="posMachine">
                  <SelectTrigger>
                    <SelectValue placeholder={t("select")} />
                  </SelectTrigger>
                  <SelectContent>
                    {posData?.map((machine) => (
                      <SelectItem key={machine.posId} value={machine.posId}>
                        {machine.posId}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state.errors?.posMachine && <p className="text-sm text-red-500">{t("posMachineRequired")}</p>}
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="cardLast4Digit" className="text-xs">
                  {t("cardLast4Digits")}
                </Label>
                <Input name="cardLast4Digit" placeholder={t("last4Digits")} className="text-sm text-slate-400" />
                {state.errors?.cardLast4Digit && <p className="text-sm text-red-500">{t("cardLast4DigitsRequired")}</p>}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Checkbox id="customerConsent" name="customerConsent" value="true" />
              <Label htmlFor="customerConsent" className="text-base font-medium text-slate-700">
                {t("customerConsent")}
              </Label>
            </div>
          </div>

          {state.errors?.general && <p className="px-6 text-sm text-red-500">{state.errors.general}</p>}

          <DialogFooter className="flex justify-end gap-3 p-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t("cancel")}
            </Button>
            <Button type="submit" disabled={pending} className="m-0">
              {pending && <Loader2 className="me-2 h-4 w-4 animate-spin" />}
              {t("withdrawFromDeposit")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
