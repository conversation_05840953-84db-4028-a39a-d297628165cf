import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Button } from "@/components/ui/button";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";

type PageProps = {
  searchParams: Promise<{
    referenceNo?: string;
    customerIdentifier?: string;
    pageNumber?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.customerIdentifier ?? sParams.referenceNo ?? "";

  const quickPayLinksResponse = await api.payment.getQuickPayLinks({
    query: {
      page: pageNumber,
      size: pageSize,
      sort: "updatedOn",
      order: "desc",
      query: searchQuery,
    },
  });

  if (quickPayLinksResponse.status !== 200) {
    throw new Error("Failed to fetch Quick Pay Links");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <div className="flex flex-col px-6 py-6">
        <div className="flex w-full justify-between">
          <h1 className="mb-6 text-2xl font-semibold">Quick Pay</h1>
          <Button disabled>Add New</Button>
        </div>

        <DataTable
          legacyPage
          searchFilters={[
            {
              label: "Customer Identifier",
              value: "customerIdentifier",
            },
            {
              label: "Agreement",
              value: "referenceNo",
            },
          ]}
          searchPlaceholder="Search by Customer Identifier or Agreement"
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: quickPayLinksResponse.body.data,
            total: quickPayLinksResponse.body.total,
          }}
          emptyMessage="There are no Quickpay Links."
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
