'use client';

import { SmileyXEyes } from "@phosphor-icons/react/dist/ssr";
import Link from "next/link";
import { useParams } from "next/navigation";

export default function NotFound() {
  const params = useParams();
  const { id: branchId } = params;
  return (
    <section className="flex h-full flex-col items-center justify-center gap-2">
      <SmileyXEyes className="h-10 w-10 text-lumi-600" />
      <h2 className="text-xl font-semibold">404 Not Found</h2>
      <p>Could not find the requested booking.</p>
      <Link
        href={`/rental/branches/${branchId as string}/bookings`}
        className="mt-4 rounded-md bg-lumi-500 px-4 py-2 text-sm text-white transition-colors hover:bg-lumi-400"
      >
        Go to Bookings list
      </Link>
    </section>
  );
}
