import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";

export function LoyaltySkeleton() {
  return (
    <Card className="w-full space-y-6 p-6">
      {/* Header */}
      <Skeleton className="h-8 w-40" />

      {/* Content Grid */}
      <div className="grid grid-cols-3 gap-4">
        {/* Loyalty Program Column */}
        <div className="space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-6 w-24" />
        </div>

        {/* Account Number Column */}
        <div className="space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-6 w-28" />
        </div>

        {/* Points/Miles Column */}
        <div className="space-y-2">
          <Skeleton className="h-5 w-28" />
          <Skeleton className="h-6 w-16" />
        </div>
      </div>
    </Card>
  );
}
