import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export function BookingDetailAndRentalRateSkeleton() {
  return (
    <Card className="w-full space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <Skeleton className="h-8 w-40" /> {/* Booking details */}
        <Skeleton className="h-6 w-64" /> {/* Booked on timestamp */}
      </div>

      <Separator className="my-6" />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-[2fr,1fr] md:gap-0">
        {/* Left Column */}
        <div className="space-y-6 pr-6">
          {/* Pickup Section */}
          <div className="space-y-4">
            <Skeleton className="h-7 w-24" /> {/* Pickup text */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-full" /> {/* Location icon */}
              <Skeleton className="h-5 w-[100px]" /> {/* Airport name */}
            </div>
            {/* Date and Time Row */}
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5" /> {/* Calendar icon */}
                  <Skeleton className="h-5 w-[150px]" /> {/* Today's date */}
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5" /> {/* Clock icon */}
                  <Skeleton className="h-5 w-16" /> {/* Now text */}
                </div>
                <Skeleton className="h-6 w-28 rounded-full bg-muted/50" /> {/* Hours early badge */}
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Drop-off Section */}
          <div className="space-y-4">
            <Skeleton className="h-7 w-28" /> {/* Drop-off text */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 w-full rounded-lg" /> {/* Location dropdown */}
            </div>
            {/* Date and Time Selectors */}
            <div className="flex gap-4">
              <Skeleton className="h-10 w-[150px] rounded-lg" /> {/* Date picker */}
              <Skeleton className="h-10 w-[70px] rounded-lg" /> {/* Time picker */}
            </div>
          </div>
        </div>

        <div className="border-l border-border pl-6">
          {/* Right Column */}
          <div className="space-y-4">
            <Skeleton className="h-7 w-48" /> {/* Customer preferences */}
            <Skeleton className="h-5 w-64" /> {/* Edit text */}
            <div className="mt-6 space-y-2">
              {/* Car preference */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5" /> {/* Car icon */}
                <Skeleton className="h-5 w-[100px]" /> {/* Car model text */}
              </div>

              {/* N/A section */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5" /> {/* Shield icon */}
                <Skeleton className="h-5 w-12" /> {/* N/A text */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
