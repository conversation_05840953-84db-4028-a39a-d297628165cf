import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function InsuranceExtrasSkeleton() {
  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-7 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* First Insurance Option */}
        <div className="flex items-start justify-between">
          <div className="flex gap-3">
            <Skeleton className="h-5 w-5 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-5 w-96" />
            </div>
          </div>
          <Skeleton className="h-6 w-16" />
        </div>
      </CardContent>
    </Card>
  );
}
