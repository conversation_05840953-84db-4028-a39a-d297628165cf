import { api } from "@/api";
import PageHeader from "./page-header";
import { type NavItem } from "./constants";
import { CancelBooking } from "./cancel-booking";
import { type TajeerAgreement } from "@/api/contracts/schema";

interface PageTitleProps {
  pageName: string;
  bookingType: string;
  bookingId?: string;
  branchId: number;
  navItemsArray: NavItem[];
}

export default async function PageTitle({ branchId, bookingType, bookingId, navItemsArray, pageName }: PageTitleProps) {
  const booking = await api.bookingDetails.getBookingById({
    params: {
      id: Number(bookingId),
    },
  });

  if (booking.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const bookingNumber = booking.body.bookingNo;
  const referenceNo = booking.body.referenceNo;
  const status = booking.body.status;

  const getTajeerContracts = await api.tajeer.getAllTajeerAgreements({
    query: {
      referenceNumber: bookingNumber ?? "",
    },
  });
  let tajeerContracts: TajeerAgreement[] = [];
  if (getTajeerContracts.status === 200) {
    tajeerContracts = getTajeerContracts?.body?.data ?? [];
  }

  const isAuthorized = tajeerContracts.some((contract) => contract.status === "SUCCESS" && contract.type === "TAJEER");
  const isTammAuthorized = tajeerContracts.some(
    (contract) => contract.status === "SUCCESS" && contract.type === "TAMM"
  );

  const bookingBody = booking.body;

  const isAuthorizedForTajeer = isAuthorized || isTammAuthorized;

  return (
    <>
      <PageHeader
        pageName={pageName}
        source={bookingBody.source ?? ""}
        bookingType={bookingBody.bookingType ?? bookingType}
        booking={bookingBody}
        bookingId={bookingId}
        branchId={branchId}
        bookingNumber={bookingNumber}
        navItemsArray={navItemsArray}
        isAuthorizedForTajeer={isAuthorizedForTajeer}
        dropdownActions={() => (
          <>
            {status === "UPCOMING" && referenceNo && (
              <CancelBooking bookingRef={referenceNo} disabled={isAuthorizedForTajeer} />
            )}
          </>
        )}
      />
    </>
  );
}
