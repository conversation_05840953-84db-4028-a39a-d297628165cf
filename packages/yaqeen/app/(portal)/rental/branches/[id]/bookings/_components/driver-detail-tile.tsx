"use client";

import { useTranslations } from "next-intl";

const DriverDetailTile = ({
  title,
  value,
  isDate,
  hijri,
  gregorean,
}: {
  title: string;
  value?: string;
  isDate?: boolean;
  hijri?: string;
  gregorean?: string;
}) => {
  const t = useTranslations("drivers");

  return (
    <>
      <div className="flex w-full flex-col gap-y-2">
        <span className="capitalize text-slate-500">{title || t("values.na")}</span>
        {!isDate ? (
          <>
            <span className={`text-slate-900 ${!value ? "!text-red-500" : ""}`}>
              {value ? value : t("values.fillIt")}
            </span>
          </>
        ) : (
          <>
            <span className={`text-slate-900 ${!hijri ? "!text-red-500" : ""}`}>
              {hijri ? hijri : t("values.fillIt")} AH
            </span>
            <span className={`text-slate-900 ${!gregorean ? "!text-red-500" : ""}`}>
              {gregorean ? gregorean : t("values.fillIt")} CE
            </span>
          </>
        )}
      </div>
    </>
  );
};

export default DriverDetailTile;
