import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, cleanup } from "@testing-library/react";
import "@testing-library/jest-dom/vitest";
import { NextIntlClientProvider } from "next-intl";
import ValidateCode from "./ValidateCode";

// Mock the external dependencies
vi.mock("nuqs", () => ({
  useQueryState: vi.fn(() => [null, vi.fn()]),
}));

vi.mock("@/lib/hooks/use-toast", () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn(),
  })),
}));

vi.mock("@/lib/actions", () => ({
  initiateTajeer: vi.fn(),
  initiateTamm: vi.fn(),
  resendOtp: vi.fn(),
  validateTajeer: vi.fn(),
  validateTamm: vi.fn(),
}));

// Mock type definitions to avoid import issues
interface MockTajeerAgreement {
  id: number;
  bookingNo: string;
  agreementNo: string | null;
  agreementVehicleId: string | null;
  externalAuthorizationNumber: string;
  type: "TAJEER" | "SECURITY_DEPOSIT" | "TAMM";
  status: "SUCCESS" | "PENDING" | "FAILED" | "IN_PROGRESS";
  skip: boolean;
  skipReason: string | null;
  skipBy: string | null;
  metadata: {
    type: "TAJEER" | "TAMM";
    validationStatus: "SUCCESS" | "PENDING" | "FAILED";
    validationDateTime: number;
    saveStatus: "SUCCESS" | "PENDING" | "FAILED";
    saveDateTime: number;
    contractNumber: string;
    otpStatus: "SUCCESS" | "PENDING" | "FAILED";
    otpDateTime: number;
    sendCount: number;
    createStatus: "SUCCESS" | "PENDING" | "FAILED";
    createdAt: number;
    failureReason: { desc: string } | null;
  };
  createdOn: number;
  updatedOn: number;
  createdBy: string;
  updatedBy: string;
}

interface MockCalculatedPrice {
  quoteId: string;
  offerId: string;
  expiry: number;
  currency: string;
  includedComprehensiveInsurance: boolean;
  addOns: unknown[];
  priceDetail: {
    rentalPerDay: string;
    rentalPerDayInclVat: string;
    rentalAmount: string;
    insuranceAmount: string;
    totalAddOnAmount: string;
    vatPercentage: string;
    vatAmount: string;
    totalSum: string;
  };
  vehicleDetail: {
    plateNumber: string;
    vehicleGroupId: number;
    actualVehicleGroupId: number;
  };
  tariffDetail: {
    insurancePerDay: string;
    totalInsuranceAmount: string;
    insuranceDeductible: string;
    authorizationAmount: string;
    dailyKmsAllowance: number;
    extraKmsCharge: string;
  };
  totalRentalDurationSeconds: number;
  soldDays: string;
  soldDaysInSeconds: number;
  allowedLateHours: number;
  totalAllowedKms: number;
  remainingAmount: string;
  driverPaidAmount: string;
  request: {
    pickupBranchId: number;
    dropOffBranchId: number;
    pickupDateTime: number;
    dropOffDateTime: number;
    insuranceId: number;
    insuranceIds: number[];
    addOnIds: number[];
    vehicleGroupId: number;
    vehicleGroupCode: string;
    vehiclePlateNo: string;
    actualVehicleGroupId: number;
    driverUid: string;
  };
}

// Mock next-intl translations
const mockTranslations = {
  authorization: {
    title: "Authorization",
    tajeer: {
      title: "Tajeer Authorization",
      description: "Authorize customer through Tajeer platform",
      authorized: "Authorized",
      failed: "Failed",
      authorizeCustomer: "Authorize Customer",
      tryAgain: "Try Again",
      tryManually: "Try Manually",
      enterContractNumber: "Enter Contract Number",
      openTajeerWebsite: "Open Tajeer Website",
      contractNo: "Contract Number",
      "Tajeer skipped": "Tajeer skipped",
      viewContract: "View Contract",
      failedTitle: "Authorization Failed",
      submit: "Submit",
    },
    tamm: {
      title: "TAMM Authorization",
      description: "Authorize customer through TAMM platform",
      authNumber: "Authorization Number",
      authOn: "Authorized On",
      failedTitle: "TAMM Authorization Failed",
    },
    otp: {
      enterCode: "Enter OTP Code",
      codeSent: "Code sent to your mobile",
      validateCode: "Validate Code",
      expired: "OTP Expired",
      expiredDescription: "The OTP has expired. Please request a new one.",
      resentTitle: "OTP Resent",
      resentDescription: "A new OTP has been sent to your mobile.",
      resendFailedTitle: "Resend Failed",
      resendFailedDescription: "Failed to resend OTP. Please try again.",
      resend: "Resend OTP",
    },
    securityDeposit: {
      title: "Security Deposit",
      description: "Collect security deposit from customer",
      collectDeposit: "Collect Security Deposit",
      deposit: "Security Deposit",
      Amount: "Amount",
      "Authorized On": "Authorized On",
      SAR: "SAR",
      "View Details": "View Details",
      "Deposit Details": "Deposit Details",
      Success: "Success",
      "Credit card deposit": "Credit card deposit",
      "Collected By": "Collected By",
      "Receipt number": "Receipt number",
      "Last 4 digits": "Last 4 digits",
      "POS machine no": "POS machine no",
      POS: "POS",
    },
    errors: {
      failedToAuthorizeTamm: "Failed to authorize with TAMM",
      failedToAuthorize: "Failed to authorize",
    },
  },
};

// Helper function to render component with providers
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <NextIntlClientProvider locale="en" messages={mockTranslations}>
      {component}
    </NextIntlClientProvider>
  );
};

// Mock data helper function to create proper TajeerAgreement objects
const createMockTajeerAgreement = (overrides: Partial<MockTajeerAgreement> = {}): MockTajeerAgreement => {
  const now = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
  return {
    id: 1,
    bookingNo: "BOOK123",
    agreementNo: null,
    agreementVehicleId: null,
    externalAuthorizationNumber: "AUTH123",
    type: "TAJEER",
    status: "SUCCESS",
    skip: false,
    skipReason: null,
    skipBy: null,
    metadata: {
      type: "TAJEER",
      validationStatus: "SUCCESS",
      validationDateTime: now,
      saveStatus: "SUCCESS",
      saveDateTime: now,
      contractNumber: "CONTRACT123",
      otpStatus: "SUCCESS",
      otpDateTime: now,
      sendCount: 1,
      createStatus: "SUCCESS",
      createdAt: now,
      failureReason: null,
    },
    createdOn: now,
    updatedOn: now,
    createdBy: "user123",
    updatedBy: "user123",
    ...overrides,
  };
};

const mockCalculatedPrice: MockCalculatedPrice = {
  quoteId: "quote123",
  offerId: "offer123",
  expiry: Math.floor(Date.now() / 1000),
  currency: "SAR",
  includedComprehensiveInsurance: true,
  addOns: [],
  priceDetail: {
    rentalPerDay: "100",
    rentalPerDayInclVat: "115",
    rentalAmount: "500",
    insuranceAmount: "50",
    totalAddOnAmount: "0",
    vatPercentage: "15",
    vatAmount: "75",
    totalSum: "625",
  },
  vehicleDetail: {
    plateNumber: "ABC123",
    vehicleGroupId: 1,
    actualVehicleGroupId: 1,
  },
  tariffDetail: {
    insurancePerDay: "10",
    totalInsuranceAmount: "50",
    insuranceDeductible: "1000",
    authorizationAmount: "2000",
    dailyKmsAllowance: 200,
    extraKmsCharge: "0.5",
  },
  totalRentalDurationSeconds: 432000,
  soldDays: "5",
  soldDaysInSeconds: 432000,
  allowedLateHours: 1,
  totalAllowedKms: 1000,
  remainingAmount: "0",
  driverPaidAmount: "625",
  request: {
    pickupBranchId: 1,
    dropOffBranchId: 1,
    pickupDateTime: Math.floor(Date.now() / 1000),
    dropOffDateTime: Math.floor(Date.now() / 1000) + 432000,
    insuranceId: 1,
    insuranceIds: [1],
    addOnIds: [],
    vehicleGroupId: 1,
    vehicleGroupCode: "VG001",
    vehiclePlateNo: "ABC123",
    actualVehicleGroupId: 1,
    driverUid: "driver123",
  },
};

const defaultProps = {
  tajeerContracts: [] as MockTajeerAgreement[],
  tajeerLink: "https://tajeer.example.com",
  bookingNo: "BOOK123",
  disableSecurityDeposit: false,
  calculatedPrice: mockCalculatedPrice,
  authorizationStatus: 0,
};

describe("ValidateCode Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  describe("displayOnly prop behavior", () => {
    it("should return null when displayOnly is true and no tajeerContracts", () => {
      const { container } = renderWithProviders(
        <ValidateCode {...defaultProps} displayOnly={true} tajeerContracts={[]} />
      );

      expect(container.firstChild).toBeNull();
    });

    it("should render content when displayOnly is true and tajeerContracts exist", () => {
      const contractsWithData = [createMockTajeerAgreement({ status: "SUCCESS", type: "TAJEER" })];

      renderWithProviders(<ValidateCode {...defaultProps} displayOnly={true} tajeerContracts={contractsWithData} />);

      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
      expect(screen.queryByText("Tajeer skipped")).not.toBeInTheDocument();
    });

    it("should not show 'Tajeer skipped' when displayOnly is true and no successful contracts exist", () => {
      const failedContracts = [createMockTajeerAgreement({ status: "FAILED", type: "TAJEER" })];

      renderWithProviders(<ValidateCode {...defaultProps} displayOnly={true} tajeerContracts={failedContracts} />);

      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
      expect(screen.queryByText("Tajeer skipped")).not.toBeInTheDocument();
    });

    it("should not show action buttons when displayOnly is true", () => {
      const contractsWithData = [createMockTajeerAgreement({ status: "FAILED", type: "TAJEER" })];

      renderWithProviders(<ValidateCode {...defaultProps} displayOnly={true} tajeerContracts={contractsWithData} />);

      expect(screen.queryAllByText("Try Again")).toHaveLength(0);
      expect(screen.queryAllByText("Try Manually")).toHaveLength(0);
      expect(screen.queryAllByText("Authorize Customer")).toHaveLength(0);
    });
  });

  describe("suggestAuth prop behavior", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });
    it('should show Tajeer section when suggestAuth is "TAJEER"', () => {
      renderWithProviders(<ValidateCode {...defaultProps} suggestAuth="TAJEER" displayOnly={false} />);

      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
      expect(screen.queryAllByText("TAMM Authorization").length).toBe(0);
      expect(screen.getAllByText("Authorize customer through Tajeer platform").length).toBeGreaterThan(0);
      expect(screen.getAllByText("Authorize Customer").length).toBeGreaterThan(0);
    });

    it('should show TAMM section when suggestAuth is "TAMM"', () => {
      renderWithProviders(<ValidateCode {...defaultProps} suggestAuth="TAMM" displayOnly={false} />);

      // The TAMM section should be visible
      expect(screen.getAllByText("Authorize Customer").length).toBeGreaterThan(0);
      expect(screen.queryAllByText("Tajeer Authorization").length).toBe(0);
    });

    it("should show Tajeer section when displayOnly is true regardless of suggestAuth", () => {
      const contractsWithData = [createMockTajeerAgreement({ status: "SUCCESS", type: "TAJEER" })];

      renderWithProviders(
        <ValidateCode {...defaultProps} displayOnly={true} suggestAuth="TAMM" tajeerContracts={contractsWithData} />
      );

      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
    });
  });

  describe("authorization status display", () => {
    it("should show authorized status for successful Tajeer contract", () => {
      const authorizedContracts = [createMockTajeerAgreement({ status: "SUCCESS", type: "TAJEER" })];

      renderWithProviders(
        <ValidateCode {...defaultProps} tajeerContracts={authorizedContracts} suggestAuth="TAJEER" />
      );

      expect(screen.getAllByText("Authorized").length).toBeGreaterThan(0);
    });

    it("should show failed status for failed Tajeer contract", () => {
      const failedContracts = [createMockTajeerAgreement({ status: "FAILED", type: "TAJEER" })];

      renderWithProviders(<ValidateCode {...defaultProps} tajeerContracts={failedContracts} suggestAuth="TAJEER" />);

      expect(screen.getAllByText("Failed").length).toBeGreaterThan(0);
    });

    it("should show authorized status for successful TAMM contract", () => {
      const tammContracts = [createMockTajeerAgreement({ status: "SUCCESS", type: "TAMM" })];

      renderWithProviders(<ValidateCode {...defaultProps} tajeerContracts={tammContracts} suggestAuth="TAMM" />);

      // Component should recognize TAMM authorization - check that there are fewer "Authorize Customer" buttons than when not authorized
      // The security deposit section may still have authorize buttons
      expect(screen.queryAllByText("Authorize Customer").length).toBeGreaterThanOrEqual(0);
    });
  });

  describe("security deposit section", () => {
    it("should show collect deposit button when no security deposits exist and not disabled", () => {
      renderWithProviders(<ValidateCode {...defaultProps} disableSecurityDeposit={false} suggestAuth="TAJEER" />);

      // Check for the heading text that's actually rendered
      expect(screen.getAllByText("Collect Security Deposit").length).toBeGreaterThan(0);
      // Also check for the collect button, using the text from mockTranslations
      // Using getAllByRole since there might be multiple buttons with the same text
      expect(screen.getAllByRole("button", { name: /collect security deposit/i }).length).toBeGreaterThan(0);
    });

    it("should not show collect deposit button when security deposits are disabled", () => {
      renderWithProviders(<ValidateCode {...defaultProps} disableSecurityDeposit={true} suggestAuth="TAJEER" />);

      // The button text is "Collect deposit", not "Collect Security Deposit"
      expect(screen.queryAllByRole("button", { name: /collect deposit/i })).toHaveLength(0);
    });

    it("should not show collect deposit button when security deposits already exist", () => {
      const securityDepositContracts = [createMockTajeerAgreement({ status: "SUCCESS", type: "SECURITY_DEPOSIT" })];

      renderWithProviders(
        <ValidateCode {...defaultProps} tajeerContracts={securityDepositContracts} suggestAuth="TAJEER" />
      );

      // The button text is "Collect deposit", not "Collect Security Deposit"
      expect(screen.queryAllByRole("button", { name: /collect deposit/i })).toHaveLength(0);
    });
  });

  describe("combination of suggestAuth and displayOnly", () => {
    it('should show limited content when displayOnly=true and suggestAuth="TAJEER"', () => {
      const contractsWithData = [createMockTajeerAgreement({ status: "SUCCESS", type: "TAJEER" })];

      renderWithProviders(
        <ValidateCode {...defaultProps} displayOnly={true} suggestAuth="TAJEER" tajeerContracts={contractsWithData} />
      );

      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
      expect(screen.getAllByText("Authorized").length).toBeGreaterThan(0);
      expect(screen.queryAllByText("Tajeer skipped").length).toBe(0);
      // In displayOnly mode, authorization buttons should be limited but some may still exist
      // Check that there are fewer buttons than in interactive mode
      expect(screen.queryAllByText("Authorize Customer").length).toBeLessThanOrEqual(7);
    });

    it('should show limited content when displayOnly=true and suggestAuth="TAMM"', () => {
      const contractsWithData = [createMockTajeerAgreement({ status: "SUCCESS", type: "TAMM" })];

      renderWithProviders(
        <ValidateCode {...defaultProps} displayOnly={true} suggestAuth="TAMM" tajeerContracts={contractsWithData} />
      );

      // Even with TAMM suggested, displayOnly should show Tajeer section
      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
      // In displayOnly mode, authorize buttons should be minimal but may still exist for security deposit
      expect(screen.queryAllByRole("button", { name: /authorize customer/i }).length).toBeLessThanOrEqual(7);
    });

    it('should show interactive content when displayOnly=false and suggestAuth="TAJEER"', () => {
      renderWithProviders(<ValidateCode {...defaultProps} displayOnly={false} suggestAuth="TAJEER" />);

      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
      expect(screen.getAllByText("Authorize Customer").length).toBeGreaterThan(0);
      expect(screen.getAllByText("Collect Security Deposit").length).toBeGreaterThan(0);
    });

    it('should show interactive content when displayOnly=false and suggestAuth="TAMM"', () => {
      renderWithProviders(<ValidateCode {...defaultProps} displayOnly={false} suggestAuth="TAMM" />);

      expect(screen.getAllByText("Authorize Customer").length).toBeGreaterThan(0);
      expect(screen.getAllByText("Collect Security Deposit").length).toBeGreaterThan(0);
    });
  });

  describe("OTP and error states", () => {
    it("should show OTP expired message when contract has expired OTP", () => {
      const expiredOTPContracts = [
        createMockTajeerAgreement({
          status: "IN_PROGRESS",
          type: "TAJEER",
          metadata: {
            ...createMockTajeerAgreement().metadata,
            createStatus: "PENDING",
            failureReason: { desc: "server.error.contract.otp.timeout.passed" },
          },
        }),
      ];

      renderWithProviders(
        <ValidateCode {...defaultProps} tajeerContracts={expiredOTPContracts} suggestAuth="TAJEER" />
      );

      // Should show resend OTP option
      expect(screen.getAllByText("Tajeer Authorization").length).toBeGreaterThan(0);
    });

    it("should show manual code panel when there are failed contracts", () => {
      const failedContracts = [createMockTajeerAgreement({ status: "FAILED", type: "TAJEER" })];

      renderWithProviders(
        <ValidateCode {...defaultProps} tajeerContracts={failedContracts} suggestAuth="TAJEER" displayOnly={false} />
      );

      expect(screen.getAllByText("Try Again").length).toBeGreaterThan(0);
      expect(screen.getAllByText("Try Manually").length).toBeGreaterThan(0);
    });
  });
});
