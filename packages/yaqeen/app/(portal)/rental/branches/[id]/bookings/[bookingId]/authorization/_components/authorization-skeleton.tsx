import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

export default function AuthorizationSkeleton() {
  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-8 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Tajeer Authorization Section */}
        <div className="flex items-start justify-between py-4 ">
          <div className="space-y-2">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-5 w-96" />
          </div>
          <Skeleton className="h-10 w-40 rounded-lg" />
        </div>

        <Separator />

        {/* Security Deposit Section */}
        <div className="flex items-start justify-between py-4">
          <div className="space-y-2">
            <Skeleton className="h-6 w-36" />
            <Skeleton className="h-5 w-72" />
          </div>
          <Skeleton className="h-10 w-36 rounded-lg" />
        </div>
      </CardContent>
    </Card>
  );
}
