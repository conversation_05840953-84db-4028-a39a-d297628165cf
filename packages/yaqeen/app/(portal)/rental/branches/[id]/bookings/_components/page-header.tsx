import { type JSX } from "react";
import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Globe, User } from "@phosphor-icons/react/dist/ssr";
import { BookingNav } from "../[bookingId]/booking-nav";
import { type NavItem } from "./constants";
import type { Route } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import { mapBookingSource, mapBookingType } from "@/lib/maper";
import { type Booking } from "@/api/contracts/booking/schema";
import { BookingNavWalkin } from "../create/_components/booking-nav-walkin";

interface PageTitleProps {
  pageName: string;
  source: string;
  bookingType: string;
  bookingId?: string;
  booking?: Booking; // Replace with the actual type of booking
  bookingNumber?: string;
  branchId: number;
  navItemsArray: NavItem[];
  isAuthorizedForTajeer?: boolean;
  dropdownActions?: () => JSX.Element;
}

export default async function PageHeader({
  pageName,
  source,
  bookingType,
  bookingId,
  booking,
  bookingNumber,
  branchId,
  navItemsArray,
  dropdownActions,
  isAuthorizedForTajeer = false,
}: PageTitleProps) {
  const t = await getTranslations("common");
  const locale = await getLocale();

  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}` as Route}>
                  {t("breadcrumbs.home")}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}/bookings` as Route}>
                  {t("breadcrumbs.myBookings")}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{pageName}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-medium tracking-tight">{pageName}</h1>
            <div className="flex items-center gap-2">
              {bookingNumber && <span className="text-slate-700">Booking {bookingNumber ?? "N/A"}</span>}
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                <Globe className="size-3" />

                {mapBookingType(
                  bookingType,
                  booking?.priceDetail?.discountDetail?.promoCode,
                  booking?.debtorName,
                  locale
                )}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                <User className="size-3" />
                {mapBookingSource(source, booking?.aggregatorName ?? "", locale)}
              </Badge>
            </div>
          </div>

          {dropdownActions ? dropdownActions() : null}
        </div>

        {bookingId ? (
          <BookingNav
            isAuthorizedForTajeer={isAuthorizedForTajeer}
            bookingId={bookingId}
            navItemsArray={navItemsArray}
          />
        ) : (
          <BookingNavWalkin isAuthorizedForTajeer={isAuthorizedForTajeer} navItemsArray={navItemsArray} />
        )}
      </div>
    </section>
  );
}
