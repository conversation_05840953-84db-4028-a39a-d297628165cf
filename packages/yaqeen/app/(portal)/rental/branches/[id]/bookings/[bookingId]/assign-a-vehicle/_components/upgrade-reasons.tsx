"use client";

import { getVehicleUpgradeReaons } from "@/lib/actions";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Skeleton } from "@/components/ui/skeleton";
import { useEffect, useReducer } from "react";
import { useLocale } from "next-intl";

export interface VehicleUpgradeReason {
  id: string;
  label: { en: string; ar: string };
  description: { en: string; ar: string };
  textBox: boolean;
}

const VehicleUpgradeReason = ({ data }: { data: VehicleUpgradeReason }) => {
  const locale = useLocale();
  const { id, label, description, textBox } = data;
  return (
    <fieldset className="mb-2 flex items-start space-x-2">
      <RadioGroupItem value={id} id={id} />
      <section className="flex w-full flex-col justify-center">
        <Label className="text-medium font-medium" htmlFor={id}>
          {locale === "ar" ? label.ar : label.en}
        </Label>
        {description && (
          <p className="mt-1.5 text-sm font-normal text-slate-600">
            {locale === "ar" ? description.ar : description.en}
          </p>
        )}
        {textBox && <Input type="text" name="reasonText" className="mt-1.5" />}
      </section>
    </fieldset>
  );
};

const VehicleUpgradeReasonsSkeleton = () => {
  return (
    <>
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="mb-4 flex flex-col px-4">
          <Skeleton className="mb-1 h-4" />
          <Skeleton className="mb-1 h-2" />
          <Skeleton className="h-2" />
        </div>
      ))}
    </>
  );
};

const initState = {
  loading: true,
  reasons: [],
};

export const VehicleUpgradeReasons = () => {
  const locale = useLocale();
  const [{ loading, reasons }, dispatch] = useReducer((state, action) => {
    switch (action.type) {
      case "REASONS":
        return { ...state, reasons: action.value, loading: false };
      case "RESET":
        return { ...initState };
      case "LOADING":
        return { ...state, loading: action.value };
      default:
        return state;
    }
  }, initState);

  useEffect(() => {
    const fetchReasons = async () => {
      try {
        const resp = await getVehicleUpgradeReaons();
        const options = (resp as { body: { options: VehicleUpgradeReason[] } })?.body?.options || [];
        dispatch({ type: "REASONS", value: options });
      } catch (error) {
        console.error("Failed to fetch vehicle upgrade reasons:", error);
      } finally {
        dispatch({ type: "LOADING", value: false });
      }
    };

    void fetchReasons();
  }, []);

  return (
    <>
      {loading ? (
        <VehicleUpgradeReasonsSkeleton />
      ) : (
        <RadioGroup className="px-4" name="identifier" dir={locale === "ar" ? "rtl" : "ltr"} required>
          {reasons.map((reason: VehicleUpgradeReason) => (
            <VehicleUpgradeReason key={reason.id} data={reason} />
          ))}
        </RadioGroup>
      )}
    </>
  );
};
