import { api } from "@/api";
import { notFound, redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";

import BookingDetailAndRentalRate from "./booking-detail-and-rental";
import { bookingSearchParamsCache } from "./bookingSearchParams";
import LoyaltyProgram from "./loyalty-program";
import Payments from "./payments";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import PricingBreakdown from "../pricing-breakdown";
import { BookingDetailAndRentalRateSkeleton } from "../../_components/skeletons/booking-detail-rental-skeleton";
import { LoyaltySkeleton } from "../../_components/skeletons/loyalty-program-skeleton";
import { NAV_ITEMS } from "../constants";

import SidesheetWrapper from "../_components/sidesheet-wrapper";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const { dropOffBranchId, dropOffTimestamp } = await bookingSearchParamsCache.parse(searchParams);
  const { bookingId } = await params;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  if (!dropOffBranchId && !dropOffTimestamp) {
    const defaultSearchParams = new URLSearchParams({
      dropOffBranchId: bookingResponse.body.dropOffBranchId?.toString() ?? "",
      dropOffTimestamp: bookingResponse.body.dropOffDateTime?.toString() ?? "",
    });

    redirect(`?${defaultSearchParams.toString()}`, RedirectType.replace);
  }

  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;
  const PAYMENTS_KEY = `${suspenseKey}_payments`;
  const LOYALTY_PROGRAM_KEY = `${suspenseKey}_loyalty_program`;
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        {" "}
        <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
          <BookingDetailAndRentalRate booking={bookingResponse.body} />
        </Suspense>
        <Suspense key={PAYMENTS_KEY} fallback={<LoyaltySkeleton />}>
          <Payments bookingId={bookingId} />
        </Suspense>
        <Suspense key={LOYALTY_PROGRAM_KEY} fallback={<LoyaltySkeleton />}>
          <LoyaltyProgram bookingId={bookingId} />
        </Suspense>
        <ActionsBar bookingNo={bookingResponse.body.bookingNo} className="w-full" navItemsArray={NAV_ITEMS} />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={bookingResponse.body} _searchParams={searchParams}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
