import { api } from "@/api";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Separator } from "@/components/ui/separator";
import { getTimeDifference } from "@/lib/utils";
import { CalendarCheck, Clock, MapPin } from "@phosphor-icons/react/dist/ssr";
import { format } from "date-fns";
import { capitalize } from "lodash-es";
import { Suspense } from "react";
import { type Booking } from "@/api/contracts/booking/schema";
import Preferences from "./components/preferences";
import BranchTimeSelector from "../../_components/branch-time-selector";
import { getTranslations } from "next-intl/server";
import { getLocale } from "next-intl/server";
import { enUS, arSA } from "date-fns/locale";

export default async function BookingDetailAndRentalRate({
  displayOnly = false,
  booking,
}: {
  displayOnly?: boolean;
  booking: Booking;
}) {
  const t = await getTranslations("createAgreement");
  const discountT = await getTranslations("discount");
  const locale = (await getLocale()) as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const ON_TIME = "On time";
  const picupBranchName = booking.pickupBranch?.name[locale] || booking.pickupBranch?.name.en || "";

  const branches = await api.branch.getDetailedBranchList({
    query: { page: 0, size: 1000 },
  });

  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const now: Date = new Date();
  const currentDateTime = format(now, "dd/MM/yyyy");
  const bookingDate = booking.bookingDateTime
    ? format(booking.bookingDateTime * 1000, "dd/MM/yyyy - HH:mm:ss", { locale: nLocale })
    : "";
  const timeDiff = booking.pickupDateTime ? getTimeDifference(booking.pickupDateTime, locale) : "";
  const pickupDateTime = booking.pickupDateTime
    ? format(booking.pickupDateTime * 1000, "dd/MM/yyyy - HH:mm", { locale: nLocale })
    : "";

  const { priceDetail } = booking;

  const discount = {
    discount_code: priceDetail.discountDetail?.promoCode ?? "",
    percentage: priceDetail.discountDetail?.totalDiscount
      ? `SAR ${priceDetail.discountDetail?.totalDiscount} (${priceDetail.discountDetail?.discountPercentage}%)`
      : "",
  };

  return (
    <section className="flex flex-col gap-y-6">
      <Card className="flex flex-col shadow">
        <CardHeader className=" flex w-full flex-row justify-between gap-2 p-4 ">
          <CardTitle className="text-lg font-bold">{t("bookingDetails.pageTitle")}</CardTitle>
          <span className="text-sm text-slate-500">
            {t("bookingDetails.bookingOn")}: {bookingDate}
          </span>
        </CardHeader>
        <Separator />
        <CardContent className="flex w-full p-0 max-md:flex-wrap ">
          <div className="flex min-w-[450px] flex-col text-sm font-medium">
            <p className="w-full px-4 pt-4 text-base font-bold text-slate-900 ">{t("bookingDetails.pickup")}</p>
            <div className="flex w-full items-start gap-1 p-4 text-sm font-medium text-slate-900 ">
              <div className="flex w-full items-center gap-1.5">
                <MapPin className=" size-5" />
                <span className=" w-full capitalize">{picupBranchName}</span>
              </div>
            </div>
            <Separator />
            <div className="flex w-full flex-col justify-center p-4 font-medium text-slate-900 ">
              <div className="flex w-full items-start justify-between gap-4">
                <div className="flex w-2/3 items-center justify-start gap-1.5 gap-x-4">
                  <div className="flex min-w-fit  flex-row items-center gap-x-1.5">
                    <CalendarCheck className="size-5" />
                    <span>
                      {t("bookingDetails.today")}, {currentDateTime}
                    </span>
                  </div>
                  <div className="flex w-full items-center gap-1.5">
                    <Clock className=" size-5" />
                    <p className="my-auto self-stretch">{t("bookingDetails.now")}</p>
                  </div>
                </div>
                <div className="flex w-1/3  justify-end">
                  <Badge
                    variant="outline"
                    className={`flex w-fit items-center gap-1 ${timeDiff === ON_TIME ? "bg-lumi-200" : "bg-slate-300"} font-normal text-slate-900`}
                  >
                    {timeDiff}
                  </Badge>
                </div>
              </div>
              {timeDiff !== ON_TIME ? (
                <span className="px-7 pt-1 text-xs font-normal leading-5 text-slate-600">
                  {t("bookingDetails.orignalPickupTime")}: {pickupDateTime}
                </span>
              ) : (
                <></>
              )}
            </div>
            <Separator className="h-1" />
            <div className="">
              <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900 ">
                {t("bookingDetails.dropoff")}
              </p>
              <div className="p-4">
                <BranchTimeSelector
                  branchId={booking.dropOffBranchId?.toString() ?? ""}
                  branchOptions={branches.body.data}
                  branchDateTime={booking.dropOffDateTime ?? 0}
                  branchType="dropOff"
                  disablePastDates={true}
                  displayOnly={displayOnly}
                  isSubmitButton={false}
                  disableNextYearDates={true}
                />
              </div>
            </div>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <Preferences booking={booking} />
          </Suspense>
        </CardContent>
      </Card>
      {/* Discount Card */}
      {discount.discount_code !== "" ? (
        <Card className="flex flex-col shadow">
          <CardHeader className="px-4">
            <CardTitle className="text-lg font-bold">{t("bookingDetails.discount")}</CardTitle>
          </CardHeader>
          <Separator />
          <CardContent className="flex w-full flex-col items-center p-0 ">
            <div className="flex w-full p-4 text-sm">
              {Object.entries(discount).map(([key, value], index) => {
                // @ts-expect-error TODO: Fix this
                const _discount = discountT(capitalize(key.replace(/_/g, " ")));
                return (
                  <div key={index} className="flex w-1/3 flex-col gap-y-2">
                    <span className="text-slate-500">{_discount}</span>
                    <span className=" text-slate-900 ">{value || "N/A"}</span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      ) : null}
    </section>
  );
}
