"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { useParams } from "next/navigation";
import { ProgressBarLink } from "@/components/progress-bar";
import { type Route } from "next";

export const AgreementButton = ({
  children,
  rowId,
  buttonTitle,
  ...props
}: {
  children: React.ReactNode;
  rowId?: string;
  buttonTitle: string;
}) => {
  const params = useParams();
  const { id: branchId } = params;

  if (buttonTitle === "endAgreement" && rowId) {
    return (
      <Button variant={"outline"} className="rounded-md p-4 text-sm transition-colors  max-lg:hidden" {...props}>
        <ProgressBarLink href={`/rental/branches/${branchId as string}/replace-vehicle/${rowId}/inspection-details`}>
          {children}
        </ProgressBarLink>
      </Button>
    );
  }
  return (
    <ProgressBarLink href={`/rental/branches/${branchId as string}/bookings/${rowId}/booking-details` as Route}>
      <Button variant={"outline"} className="rounded-md p-4 text-sm transition-colors  max-lg:hidden " {...props}>
        {children}
      </Button>
    </ProgressBarLink>
  );
};
