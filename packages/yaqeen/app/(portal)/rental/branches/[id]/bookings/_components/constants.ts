import type { Route } from "next";

export const timeFilterOptions = [
  { label: "Next 2 hours", value: "NEXT_2_HOURS", translationKey: "next2Hours" },
  { label: "Next 6 hours", value: "NEXT_6_HOURS", translationKey: "next6Hours" },
  { label: "Today", value: "TODAY", translationKey: "today" },
  { label: "Next 48 hours", value: "NEXT_48_HOURS", translationKey: "next48Hours" },
  { label: "This week", value: "THIS_WEEK", translationKey: "thisWeek" },
  { label: "This month", value: "THIS_MONTH", translationKey: "thisMonth" },
];

export const navItems: (branchId: number) => Array<{
  label: string;
  href: Route;
  translationKey: string;
}> = (branchId: number) => [
  {
    label: "All bookings",
    href: `/rental/branches/${branchId}/bookings` as Route,
    translationKey: "allBookings",
  },
  {
    label: "Upcoming",
    href: `/rental/branches/${branchId}/bookings/upcoming` as Route,
    translationKey: "upcoming",
  },
  {
    label: "Ongoing",
    href: `/rental/branches/${branchId}/bookings/ongoing` as Route,
    translationKey: "ongoing",
  },
  {
    label: "Needs action",
    href: `/rental/branches/${branchId}/bookings/needs-action` as Route,
    translationKey: "needsAction",
  },
  {
    label: "Completed",
    href: `/rental/branches/${branchId}/bookings/completed` as Route,
    translationKey: "completed",
  },
];

export const filters = [
  {
    filterKey: "dropOffDateRangeStart",
    filterName: "Drop-off time",
    translationKey: "dropOffTime",
    columnKey: "dropOffDateTime",
    isMultiSelect: false,
    options: timeFilterOptions,
  },
];

export const searchFilters = [
  {
    label: "Booking No",
    value: "bookingNo",
    translationKey: "bookingNo",
  },
  {
    label: "Mobile No",
    value: "mobileNumber",
    translationKey: "mobileNumber",
  },
  {
    label: "Driver Name",
    value: "driverName",
    translationKey: "driverName",
  },
];

export const STATUSES = ["UPCOMING", "NOW_SHOW", "ONGOING", "DELAYED", "SUSPENDED", "CANCELLED", "COMPLETED"];

export const getColor = (status = "text-slate-900") => {
  switch (status) {
    case "text-slate-500":
      return "text-slate-500";
    case "text-slate-900":
      return "text-slate-900";
    case "text-red-700":
      return "text-red-700";
    case "text-green-700":
      return "text-green-700";
    default:
      return "text-slate-900";
  }
};

export const DROPOFF_DATE_TIME = "dropOffDateTime";
export const BOOKING_DATE_TIME = "bookingDateTime";
export const CHECKIN_DATE = "checkinDate";

export const PAYMENTS_DATA = [
  {
    amount: 100,
    transactionTime: 1632556800000,
    type: "Cash",
    status: "Success",
  },
  {
    amount: 200,
    transactionTime: 1632556800000,
    type: "Card",
    status: "Success",
  },
  {
    amount: 300,
    transactionTime: 1632556800000,
    type: "Cash",
    status: "Success",
  },
];

export type NavItemUrls =
  | "/booking-details"
  | "/driver-details"
  | "/assign-a-vehicle"
  | "/insurance-and-extras"
  | "/payment"
  | "/authorization";

export interface NavItem {
  label: string;
  href: NavItemUrls;
  completed: boolean;
  isEnabled?: boolean;
  translationKey: string;
}

export const getNextTabUrl = (pathName: string, branchId: number, bookingId: number | "", navItemsArray: NavItem[]) => {
  const currentStep = navItemsArray.findIndex((item) => pathName.includes(item.href));
  return `/rental/branches/${branchId}/bookings/${bookingId ? bookingId : "create"}${navItemsArray[currentStep + 1]?.href}`;
};

export const ID_TYPES_DROPDOWN = [
  {
    label: "Saudi National ID",
    value: "SAUDI_NATIONAL",
    translationKey: "saudiNational",
  },
  {
    label: "GCC ID",
    value: "GCC",
    translationKey: "gcc",
  },
  {
    label: "Iqama",
    value: "RESIDENT",
    translationKey: "iqama",
  },
  {
    label: "Passport",
    value: "VISITOR",
    translationKey: "passport",
  },
];

export type ID_TYPES_DROPDOWN_TYPE = "saudiNational" | "gcc" | "resident" | "visitor" | "iqama" | "passport";

export enum ID_TYPES_ENUM {
  SAUDI_NATIONAL = "SAUDI_NATIONAL",
  GCC = "GCC",
  RESIDENT = "RESIDENT",
  VISITOR = "VISITOR",
  ID = "ID",
  NATIONAL_ID = "NATIONAL_ID",
  IQAMA = "IQAMA",
  PASSPORT = "PASSPORT",
}
