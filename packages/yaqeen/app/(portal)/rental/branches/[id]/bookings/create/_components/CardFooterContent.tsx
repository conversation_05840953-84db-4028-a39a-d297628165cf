import { CardFooter } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { SaveRemainingAmount } from "../../_components/SaveRemainingAmount";
import { getTranslations } from "next-intl/server";

/**
 * Card footer component showing totals
 */
export const CardFooterContent = async ({
  totalSum,
  driverPaidAmount,
  remainingAmount,
}: {
  totalSum: number;
  driverPaidAmount: number;
  remainingAmount: number;
}) => {
  const t = await getTranslations("pricing");
  return (
    <CardFooter className="flex flex-col border-t p-0">
      <div className="w-full space-y-3 p-4">
        <div className="flex items-center justify-between text-base font-medium text-slate-900">
          <span>{t("total")}</span>
          <span>{isNaN(totalSum) ? "0.00" : Number(totalSum).toFixed(2)}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span>{t("paidAmount")}</span>
          <span>{isNaN(driverPaidAmount) ? "0.00" : Number(driverPaidAmount).toFixed(2)}</span>
        </div>
      </div>
      <Separator />
      <div className="flex w-full justify-between bg-lumi-50 p-4 text-base font-medium text-slate-900">
        <span>{t("remaining")}</span>
        <SaveRemainingAmount amount={isNaN(remainingAmount) ? "0.00" : Number(remainingAmount).toFixed(2)} />
        <span>{isNaN(remainingAmount) ? "0.00" : Number(remainingAmount).toFixed(2)}</span>
      </div>
    </CardFooter>
  );
};
