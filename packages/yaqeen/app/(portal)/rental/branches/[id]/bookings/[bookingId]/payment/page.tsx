import { Suspense } from "react";
import PricingBreakdown from "../pricing-breakdown";
import { api } from "@/api";
import type { Pos, Booking } from "@/api/contracts/booking/schema";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { type Promotion } from "@/api/contracts/schema";
import TableSkeleton from "./_components/table-skeleton";
import { NAV_ITEMS } from "../constants";
import PaymentsInterface from "./_components/payments-interface";
export default async function Payment({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string; id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;
  const { bookingId, id } = await params;
  const _searchParams = await searchParams;

  // Check if we have a quoteId in search params
  let quoteId = _searchParams.quoteId as string | undefined;

  // If no quoteId, we need to fetch booking details and calculate price
  if (!quoteId) {
    console.log("No quoteId found in search params, fetching booking details and calculating price...");

    // Fetch booking details first
    const bookingDetailsResponse = await api.bookingDetails.getBookingById({
      params: { id: Number(bookingId) },
    });

    if (bookingDetailsResponse.status !== 200) {
      throw new Error(`Failed to fetch booking details: ${bookingDetailsResponse.status}`);
    }

    const bookingDetails = bookingDetailsResponse.body;

    // Extract parameters needed for price calculation
    const pickupBranchId = bookingDetails.pickupBranchId;
    const dropOffBranchId = bookingDetails.dropOffBranchId;
    const dropOffTimeStamp = bookingDetails.dropOffDateTime;
    const vehiclePlateNo = bookingDetails.assignedVehicle?.plateNo;
    const vehicleGroupId = bookingDetails.preferredVehicle?.vehicleGroupId;

    // Calculate price using parameters from booking details
    const calculatePriceResponse = await api.bookingDetails.calculatePrice({
      params: { bookingId: String(bookingId) },
      body: {
        pickupBranchId,
        dropOffBranchId,
        dropOffTime: dropOffTimeStamp,
        vehiclePlateNo,
        vehicleGroupId,
      },
    });

    if (calculatePriceResponse.status !== 200) {
      throw new Error(
        `Failed to calculate price: ${calculatePriceResponse.body?.desc || calculatePriceResponse.status}`
      );
    }

    // Get the quoteId from the calculation response
    quoteId = calculatePriceResponse.body.quoteId;

    if (!quoteId) {
      throw new Error("Failed to get quoteId from price calculation");
    }
  }

  // Now fetch all required data in parallel using the quoteId
  const [
    bookingResponse,
    transactionsResponse,
    posResponse,
    promotionsResponse,
    corporatePromotionsResponse,
    calculatedPriceQuote,
  ] = await Promise.all([
    api.bookingDetails.getBookingById({
      params: { id: Number(bookingId) },
    }),
    api.payment.getBookingTransactions({
      params: {
        bookingId: String(bookingId),
      },
      query: {
        notInitiatedFor: "SECURITY_DEPOSIT_AUTHORIZATION",
      },
    }),
    api.branch.getAllPos({
      params: {
        id,
      },
    }),
    api.payment.getPromotions({
      query: {
        quoteId: String(quoteId),
      },
    }),
    api.payment.getCorporatePromotions({
      query: {
        quoteId: String(quoteId),
      },
    }),
    api.booking.getCalculatePriceForQuote({
      params: {
        quoteId: String(quoteId),
      },
    }),
  ]);

  if (transactionsResponse?.status !== 200) {
    throw new Error(`Error fetching transactions: ${transactionsResponse.status}`);
  }

  if (promotionsResponse?.status !== 200) {
    throw new Error(`Error fetching promotions: ${promotionsResponse.status}`);
  }

  if (corporatePromotionsResponse?.status !== 200) {
    throw new Error(`Error fetching corporate promotions: ${corporatePromotionsResponse.status}`);
  }

  if (calculatedPriceQuote?.status !== 200) {
    throw new Error(`Error fetching calculated price: ${calculatedPriceQuote.status}`);
  }

  // Extract data from responses
  const booking: Booking = bookingResponse.body as Booking;
  const { priceDetail } = booking;
  const transactions = transactionsResponse.body;
  const posResponseData = posResponse?.body as Pos;

  if (!posResponseData?.data) {
    throw new Error(`Error: POS data not available`);
  }

  const promotions: Promotion[] = promotionsResponse.body.data ?? [];
  const corporatePromotions: Promotion[] = corporatePromotionsResponse.body.data ?? [];

  const hasRefunds = transactions.data.some((transaction) => transaction.type.toLowerCase().includes("refund"));

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Suspense fallback={<TableSkeleton />}>
          <PaymentsInterface
            posResponse={posResponseData}
            priceDetail={priceDetail}
            payments={transactions.data}
            promotions={promotions}
            bookingNo={booking.bookingNo}
            corporatePromotions={corporatePromotions}
            bookingId={Number(bookingId)}
            vehicleGroupId={booking.preferredVehicle?.vehicleGroupId}
            vehiclePlateNo={booking.assignedVehicle?.plateNo}
            dropOffBranchId={booking.dropOffBranchId}
            dropOffTimestamp={booking.dropOffDateTime}
            hasRefunds={hasRefunds}
            pageName="createAgreement"
          />
        </Suspense>
        <ActionsBar bookingNo={booking.bookingNo} className="w-full" navItemsArray={NAV_ITEMS} />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={booking} _searchParams={searchParams} path="payment" />
        </Suspense>
      </div>
    </section>
  );
}
