"use client";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { HIJRI_MONTHS } from "@/lib/constants";
import { useTranslations } from "next-intl";

const FormSchema = z.object({
  day: z.coerce.number().refine((val) => val > 0 && val <= 31, "Invalid day"),
  month: z.string(),
  year: z.coerce.number().refine((val) => /^\d{4}$/.test(String(val)), "Invalid year"),
});

type FormData = z.infer<typeof FormSchema>;

interface IDatePicker {
  label: string;
  months: string[];
  date: string;
  onChange: (date: string) => void;
  yearPlaceholder: string;
}

export default function CustomDatePicker({
  months = HIJRI_MONTHS,
  onChange,
  date,
  label,
  yearPlaceholder,
}: IDatePicker) {
  const t = useTranslations("datePicker");
  const [day = "", month = "", year = ""] = date.split("-");
  const _month = months[parseInt(month) - 1];

  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      day: parseInt(day) ?? 0,
      month: _month ?? "",
      year: parseInt(year) ?? 0,
    },
  });

  // if date is changed, update the form values
  useEffect(() => {
    const [day = "", month = "", year = ""] = date.split("-");
    // set month with month name
    const _month = months[parseInt(month) - 1];
    form.setValue("day", parseInt(day) ?? 0);
    form.setValue("month", _month ?? "");
    form.setValue("year", parseInt(year) ?? 0);
  }, [date, form, months]);

  const handleFormChange = (data: FormData) => {
    const { day, month, year } = data;
    // send value back to parent component, as date format, not month readable name
    const _month = month.split("-")[0];
    const newDate = `${day}-${_month}-${year}`;
    onChange(newDate);
  };

  return (
    <Form {...form}>
      <form className="w-full space-y-4">
        <FormLabel>{label}</FormLabel>
        <div className="!mt-2 flex items-center gap-x-4">
          <FormField
            control={form.control}
            name="day"
            render={({ field }) => (
              <FormItem className="w-1/5">
                <FormControl>
                  <Input
                    id="day"
                    type="number"
                    placeholder="01"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      handleFormChange(form.getValues());
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="month"
            render={({ field }) => (
              <FormItem className="w-3/5">
                <FormControl>
                  <Select
                    value={field.value}
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleFormChange(form.getValues());
                    }}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectMonth")} />
                    </SelectTrigger>
                    <SelectContent>
                      {months.map((month) => (
                        <SelectItem key={month} value={month}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="year"
            render={({ field }) => (
              <FormItem className="w-1/5">
                <FormControl>
                  <Input
                    type="number"
                    placeholder={yearPlaceholder}
                    {...field}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      handleFormChange(form.getValues());
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
}
