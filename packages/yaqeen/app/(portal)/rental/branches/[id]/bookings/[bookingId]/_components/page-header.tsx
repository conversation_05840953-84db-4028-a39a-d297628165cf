import { type JSX } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import { mapBookingSource, mapBookingType } from "@/lib/maper";
import { Globe, User } from "@phosphor-icons/react/dist/ssr";
import type { Route } from "next";
import type { Booking } from "@/api/contracts/booking/schema";
import { getLocale, getTranslations } from "next-intl/server";

interface PageTitleProps {
  pageName: string;
  source: string;
  bookingType: string;
  bookingId?: string;
  booking: Booking;
  bookingNumber?: string;
  branchId: number;
  children: JSX.Element;
}

async function PageHeader({ pageName, source, bookingType, booking, branchId, children }: PageTitleProps) {
  const t = await getTranslations("bookings");
  const filtersT = await getTranslations("bookings.filters");

  const locale = await getLocale();
  const mybookings = t("myBookings");
  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24 ">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}` as Route}>{t("home")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}/bookings` as Route}>{mybookings}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{pageName}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-medium tracking-tight">{pageName}</h1>
              {booking.status === "UPCOMING" && (
                <div className="rounded-full bg-blue-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    booking.status.toLowerCase()
                  )}
                </div>
              )}
              {booking.status === "NO_SHOW" && (
                <div className="rounded-full bg-blue-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    (booking?.status ?? "").replace("_", " ").toLowerCase()
                  )}
                </div>
              )}
              {booking.status === "ONGOING" && (
                <div className="rounded-full bg-green-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    booking.status.toLowerCase()
                  )}
                </div>
              )}
              {booking.status === "COMPLETED" && (
                <div className="rounded-full bg-slate-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    booking.status.toLowerCase()
                  )}
                </div>
              )}
              {["CANCELLED", "LATE_RETURN"].includes(booking?.status ?? "") && (
                <div className="rounded-full bg-red-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    (booking?.status ?? "").replace("_", " ").toLowerCase()
                  )}
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              {booking.agreementNo && ["ONGOING", "COMPLETED", "LATE_RETURN"].includes(booking?.status ?? "") && (
                <span className="text-sm text-slate-900">
                  {t("agreement")} {booking.agreementNo}
                </span>
              )}
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                <User className="size-3" />
                {mapBookingType(
                  bookingType,
                  booking?.priceDetail?.discountDetail?.promoCode,
                  booking?.debtorName,
                  locale
                )}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                <Globe className="size-3" />
                {mapBookingSource(source, booking?.aggregatorName, locale)}
              </Badge>
            </div>
          </div>

          {children}
        </div>
      </div>
    </section>
  );
}

export default PageHeader;
