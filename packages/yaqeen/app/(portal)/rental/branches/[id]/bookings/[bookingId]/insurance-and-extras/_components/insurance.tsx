"use client";
import React, { startTransition, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { type z } from "zod";
import { type SingleInsuranceSchema } from "@/api/contracts/booking/schema";
import { useLocale, useTranslations } from "next-intl";

export interface InsuranceProps {
  insurances: z.infer<typeof SingleInsuranceSchema>[];
}

/**
 * Insurance component displaying available insurance options
 */
export default function Insurance({ insurances }: InsuranceProps) {
  const t = useTranslations("insurance");
  const locale = useLocale();
  const [_insurances, setInsurances] = useState(insurances);
  const [, setInsuranceIds] = useQueryState("insuranceIds", { shallow: false });
  const progress = useProgressBar();

  /**
   * Handle insurance option selection
   */
  const handleInsuranceChange = (id: number) => {
    // Update insurances state with selected option
    const updatedInsurances = _insurances.map((insurance) => {
      return {
        ...insurance,
        isEnabled: insurance.id === id,
      };
    });

    setInsurances(updatedInsurances);
    progress.start();

    // Update URL query parameter
    startTransition(() => {
      void setInsuranceIds(String(id));
      progress.done();
    });
  };

  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="flex w-full p-0 max-md:flex-wrap">
        <div className="flex w-full flex-col text-sm font-medium">
          <RadioGroup>
            {_insurances.map((insurance) => (
              <div className="flex items-start gap-2 p-4" key={insurance.id} dir={locale === "ar" ? "rtl" : "ltr"}>
                <RadioGroupItem
                  checked={insurance.isEnabled}
                  onClick={() => handleInsuranceChange(insurance.id)}
                  value={String(insurance.id) ?? ""}
                  id={insurance.name[locale as keyof typeof insurance.name]}
                  className="mt-1 gap-x-2 text-blue-600"
                />
                <Label
                  htmlFor={insurance.name[locale as keyof typeof insurance.name]}
                  className="flex w-full flex-row items-end justify-between"
                >
                  <div>
                    <p className="text-base font-medium">{insurance.name[locale as keyof typeof insurance.name]}</p>
                    <p className="text-sm text-slate-600 ">
                      {insurance.description[locale as keyof typeof insurance.description]}
                    </p>
                  </div>
                  <div className="flex flex-col items-end">
                    <p className="text-base font-medium">
                      {insurance.deductible
                        ? t("pricing.withDeductible", { amount: insurance.deductible?.toFixed(2) })
                        : t("pricing.free")}
                    </p>
                    {insurance.perday ? (
                      <p className="text-xs text-slate-500">
                        {t("pricing.perDay", { amount: insurance.perday?.toFixed(2) })}
                      </p>
                    ) : null}
                  </div>
                </Label>
              </div>
            ))}
            <Separator />
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );
}
