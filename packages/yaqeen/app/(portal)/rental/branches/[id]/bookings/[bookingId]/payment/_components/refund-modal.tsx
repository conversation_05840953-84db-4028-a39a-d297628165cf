"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { createRefund } from "@/lib/actions/refund-actions";
import { useParams } from "next/navigation";
import { Bank, Info, Money } from "@phosphor-icons/react/dist/ssr";
import { useActionState, useState } from "react";
import { toast } from "@/lib/hooks/use-toast";
import { ContinueButton } from "@/components/ContinueButton";
import { useTranslations } from "next-intl";
interface RefundModalProps {
  isOpen: boolean;
  onClose: (cancel?: boolean) => void;
  amount: number;
  bookingId: string;
  sourceRoute: string;
}

export default function RefundModal({ isOpen, onClose, amount, bookingId, sourceRoute }: RefundModalProps) {
  const [refundMethod, setRefundMethod] = useState<"CASH" | "BANK_TRANSFER">("CASH");
  const params = useParams();
  const branchId = Number(params.id);
  const t = useTranslations("Refund");
  const [, formAction] = useActionState(
    async (state: unknown, formData: FormData) => {
      const result = await createRefund(formData, sourceRoute);

      if (result.success) {
        toast({
          variant: "success",
          title: t("success"),
          description: refundMethod === "BANK_TRANSFER" ? t("refundRequestCreated") : t("refundProcessed"),
        });
        onClose();
      } else if (result.message) {
        toast({
          title: t("error"),
          description: result.message,
          variant: "destructive",
        });
      }

      return result;
    },
    {
      success: false,
      message: "",
    }
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="p-0 sm:w-[600px]">
        <DialogHeader className="relative p-6">
          <DialogTitle className="text-lg font-semibold">{t("issueRefund")}</DialogTitle>
        </DialogHeader>
        <Separator />

        <form action={formAction} className="content">
          <input type="hidden" name="bookingId" value={bookingId} />
          <input type="hidden" name="amount" value={amount} />
          <input type="hidden" name="branchId" value={branchId} />
          <input type="hidden" name="refundThrough" value={refundMethod} />

          <div className="space-y-6 p-6">
            <div className="space-y-2">
              <div className="text-sm font-medium">{t("amountToRefund")}</div>
              <div className="flex flex-col items-center justify-center rounded-md bg-slate-50 py-8">
                <div className="text-4xl font-medium">SAR {amount.toFixed(2)}</div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">{t("refundMethod")}</div>
              <RadioGroup
                value={refundMethod}
                onValueChange={(value) => setRefundMethod(value as "CASH" | "BANK_TRANSFER")}
                className="grid grid-cols-2 gap-4"
              >
                <div className="relative flex-1">
                  <RadioGroupItem value="CASH" id="cash" className="peer sr-only" />
                  <Label
                    htmlFor="cash"
                    className="flex cursor-pointer items-center justify-center gap-2 rounded-md border-2 border-muted bg-background p-4 hover:bg-accent peer-data-[state=checked]:border-blue-600 peer-data-[state=checked]:bg-blue-50"
                  >
                    <Money size={24} weight="regular" />
                    <span className="text-base font-medium">{t("cash")}</span>
                  </Label>
                </div>

                <div className="relative flex-1">
                  <RadioGroupItem value="BANK_TRANSFER" id="bank" className="peer sr-only" />
                  <Label
                    htmlFor="bank"
                    className="flex cursor-pointer items-center justify-center gap-2 rounded-md border-2 border-muted bg-background p-4 hover:bg-accent peer-data-[state=checked]:border-blue-600 peer-data-[state=checked]:bg-blue-50"
                  >
                    <Bank size={24} weight="regular" />
                    <span className="text-base font-medium">{t("bankTransfer")}</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {refundMethod === "BANK_TRANSFER" && (
              <Alert>
                <Info size={20} />
                <AlertTitle className="text-slate-900">{t("ibanLinkSentToCustomer")}</AlertTitle>
                <AlertDescription className="text-slate-500">{t("customerWillReceiveLink")}</AlertDescription>
              </Alert>
            )}
          </div>

          <Separator />
          <div className="flex justify-end gap-2 p-4">
            <Button type="button" variant="outline" onClick={() => onClose(true)}>
              {t("cancel")}
            </Button>
            <ContinueButton>{refundMethod === "CASH" ? t("processRefund") : t("requestRefund")}</ContinueButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
