import { type ReactNode } from "react";
import PageTitle from "../../_components/page-title";
import { BookingProvider } from "../booking-provider";
import { NAV_ITEMS } from "../constants";
import { getTranslations } from "next-intl/server";

interface LayoutProps {
  children: ReactNode;
  params: Promise<{
    bookingId: string;
    id: number;
  }>;
}

export default async function Layout({ children, params }: LayoutProps) {
  const { bookingId, id } = await params;

  const t = await getTranslations("createAgreement");
  return (
    <BookingProvider>
      <div className="flex flex-col pb-16 lg:min-h-screen">
        <PageTitle
          branchId={id}
          bookingType="Local"
          bookingId={bookingId}
          navItemsArray={NAV_ITEMS}
          pageName={t("Create rental agreement")}
        />
        <div className="container my-6 px-24">{children}</div>
      </div>
    </BookingProvider>
  );
}
