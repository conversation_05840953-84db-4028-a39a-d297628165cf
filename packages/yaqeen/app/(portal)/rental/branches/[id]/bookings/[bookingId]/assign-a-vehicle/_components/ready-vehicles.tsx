"use client";

import { Button } from "@/components/ui/button";
import { DataFilter } from "@/components/ui/data-filter";
import ClientPaginationControls from "@/components/ui/data-table/client-pagination-controls";
import NoData from "@/components/ui/data-table/no-data";
import { Car } from "@phosphor-icons/react/dist/ssr";
import { getCoreRowModel, getPaginationRowModel, useReactTable } from "@tanstack/react-table";
import { ExternalLink } from "lucide-react";
import { useMemo, useState } from "react";
import { buildGroupsFilters } from "../../../utils";
import { type Vehicle, VehicleCard } from "./vehicle-card";
import { useTranslations } from "next-intl";

export default function ReadyVehicles({
  data,
  filters,
  onVehicleSelect,
}: {
  data: Vehicle[];
  filters?: {
    groups: string[];
  };
  onVehicleSelect: (action: string, vehicle: Vehicle) => void;
}) {
  const t = useTranslations("createAgreement");
  const { groups } = useMemo(() => buildGroupsFilters(data), [data]);

  const [groupFilter, setGroupFilter] = useState<string[]>(filters?.groups ?? []);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  const filteredVehicles = useMemo(() => {
    if (groupFilter.length === 0) return data;
    return data.filter((vehicle) => groupFilter.includes(vehicle.model?.groupResponse?.id?.toString() ?? ""));
  }, [data, groupFilter]);

  const table = useReactTable<Vehicle>({
    data: filteredVehicles,
    columns: [],
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleGroupChange = (selectedGroups: string[]) => {
    setGroupFilter(selectedGroups);
    table.setPageIndex(0);
  };

  return (
    <div>
      <section className="flex h-full flex-col">
        <header className="flex items-center gap-2 border p-4">
          <DataFilter
            isMultiSelect
            filterKey="vehicleGroup"
            title="All groups"
            options={groups}
            value={groupFilter}
            onFilterChange={handleGroupChange}
          />
        </header>
        <section className="h-[calc(100vh-247px)] overflow-y-auto">
          {table.getRowModel().rows.map((row) => (
            <div className="border-b" key={row.original.plateNo}>
              <VehicleCard
                vehicleDetails={row.original}
                renderActionButton={(vehicleDetails) =>
                  vehicleDetails.preferenceType === "UPGRADE" ? (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => onVehicleSelect("upgrade", vehicleDetails)}
                        className="flex h-auto items-center gap-2 rounded-md py-0"
                      >
                        {t("assignVehicle.cta.upgrade")}
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      className="rounded-lg"
                      onClick={() => onVehicleSelect("select", vehicleDetails)}
                    >
                      {t("assignVehicle.cta.select")}
                    </Button>
                  )
                }
              />
            </div>
          ))}
          {!filteredVehicles.length && (
            <NoData
              icon={() => <Car className="h-12 w-12 text-slate-300" />}
              title={
                <>
                  <h1 className="text-3xl font-medium">{t("assignVehicle.noResults")}</h1>
                  <p className="my-4 text-base font-normal">{t("assignVehicle.noResultsDesc")}</p>
                </>
              }
              callback={() => (
                <section className="justify-content-center flex items-center gap-2">
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      console.log("Reset filters");
                    }}
                    disabled
                  >
                    {t("assignVehicle.cta.viewAll")} <ExternalLink className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      console.log("Reset filters");
                    }}
                  >
                    {t("assignVehicle.cta.resetFilters")}
                  </Button>
                </section>
              )}
            />
          )}
        </section>
        <section className="border-t p-2">
          <ClientPaginationControls
            total={filteredVehicles.length}
            table={table}
            start={pagination.pageIndex * pagination.pageSize + 1}
            end={Math.min((pagination.pageIndex + 1) * pagination.pageSize, filteredVehicles.length)}
          />
        </section>
      </section>
    </div>
  );
}
