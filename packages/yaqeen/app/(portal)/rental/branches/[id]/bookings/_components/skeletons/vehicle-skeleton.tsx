import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export default function VehicleSkeleton() {
  return (
    <Card className="w-full max-w-3xl border">
      <CardHeader className="p-4">
        <CardTitle>
          <Skeleton className="h-8 w-48" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Separator className="mb-4" />
        <div className="flex gap-4">
          {/* Image skeleton */}
          <Skeleton className="h-20 w-32 rounded-md" />

          {/* Content skeleton */}
          <div className="flex-1 space-y-4">
            {/* Tags */}
            <div className="flex gap-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-24" />
            </div>

            {/* Title */}
            <Skeleton className="h-8 w-3/4" />

            {/* Info row */}
            <div className="flex items-center gap-4">
              <Skeleton className="h-4 w-20" />
              <Separator orientation="vertical" className="h-4" />
              <Skeleton className="h-4 w-16" />
              <Separator orientation="vertical" className="h-4" />
              <Skeleton className="h-4 w-24" />
              <Separator orientation="vertical" className="h-4" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
