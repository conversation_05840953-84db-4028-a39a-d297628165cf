import { ActionsBar } from "../../_components/actions-bar";
import { WALK_IN_NAV_ITEMS } from "../constants";

import PricingBreakdown from "../_components/pricing-breakdown";
import { api } from "@/api";
import { DriverDetails } from "@/app/(portal)/rental/branches/[id]/bookings/create/driver-details/_components/driver-details";
import { countries } from "country-data-list";
import { getPricingServerInfo } from "../utils";
import SidesheetWrapper from "../../[bookingId]/_components/sidesheet-wrapper";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _searchParams = await searchParams;

  const quoteId: string | undefined = _searchParams.quoteId as string | undefined;

  // Fetch drivers using the searchDrivers API
  const driversResponse = await api.driverDetails.searchDrivers({
    query: {
      sort: "id",
      order: "desc",
      query: "", // Empty string to get all drivers or can be filled with a search query
      page: 0,
      size: 10,
    },
  });

  const countriesResponse = await api.branch.getCountries({
    requiresAuth: false,
  });

  if (countriesResponse?.status !== 200) {
    throw new Error(`Error: ${countriesResponse.status}`);
  }

  const getFilteredCountries = () => {
    return countries.all
      .filter((country) => {
        return countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`);
      })
      .map((country) => ({
        ...country,
        id: countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`)?.id,
      }));
  };

  const filterdCountries = getFilteredCountries();

  // Handle the response and extract drivers data
  const drivers = driversResponse.status === 200 ? driversResponse.body.data : [];

  const driverName = _searchParams?.driverName as string;
  const driverUid = _searchParams?.driverUid as string;

  // Driver information
  const driver = {
    title: "Mr",
    name: driverName,
    driverUId: driverUid,
  };

  const isEnableContinue = !driverName || !driverUid;

  const { branchesResponse, quoteResponse } = await getPricingServerInfo({ quoteId });
  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <div className="max-w-3xl overflow-hidden !p-0">
          <DriverDetails drivers={drivers} searchParams={_searchParams} countries={filterdCountries} />
        </div>
        <ActionsBar
          bookingNo="1234"
          className="w-full"
          navItemsArray={WALK_IN_NAV_ITEMS}
          successCtaDisabled={isEnableContinue}
        />
      </div>
      <div className="col-span-4">
        <PricingBreakdown
          _searchParams={_searchParams}
          branchesResponse={branchesResponse.body.data}
          quoteResponse={quoteResponse}
          quoteId={quoteId ? String(quoteId) : undefined}
        >
          {driver.driverUId && <SidesheetWrapper driverUId={driver.driverUId ?? ""} />}
        </PricingBreakdown>
      </div>
    </section>
  );
}
