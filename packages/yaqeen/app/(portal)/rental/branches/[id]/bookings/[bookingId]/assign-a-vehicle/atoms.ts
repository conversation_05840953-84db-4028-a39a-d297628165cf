import { atom } from "jotai";
import { type Vehicle } from "./_components/vehicle-card";

export type SelectedVehicleState = Record<number | string, Vehicle>

export const showSuggestedVehiclesAtom = atom(false);
export const selectedVehicleAtom = atom<SelectedVehicleState | null>({});

export const freeUpgradeDataAtom = atom<{
    reason: string,
    reasonText?: string;
} | null>(null);

export type UpgradignVehicleAtom  = {
    vehicle: Vehicle, 
    type?: string,
} | null;
export const upgradingVehicleAtom = atom<UpgradignVehicleAtom>(null);
