import React from "react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

export default function AlertDialog({
  children,
  selectHandler,
}: {
  children: React.ReactNode;
  selectHandler: () => void;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="p-0 sm:max-w-md">
        <DialogHeader className="p-4">
          <DialogTitle>Replace the current discount?</DialogTitle>
          <DialogDescription className=" text-sm text-slate-600">
            Applying this discount will replace the current discount with the selected one
          </DialogDescription>
        </DialogHeader>
        <Separator className="" />
        <DialogFooter className="p-4">
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button variant="default" className="bg-lumi-500" onClick={selectHandler}>
              Apply discount
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
