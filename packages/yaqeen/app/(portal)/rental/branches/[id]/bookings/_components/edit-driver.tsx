"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { type Country, CountryDropdown } from "@/components/ui/country-dropdown";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { PhoneInput } from "@/components/ui/phone-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/lib/hooks/use-toast";
import { driverAttachOrCreate, updateDriver } from "@/lib/actions";
import { GREGORIAN_MONTHS, HIJRI_MONTHS, HIJ<PERSON>_MONTHS_ARABIC } from "@/lib/constants";
import { getFullDate, isValidDate, makeDateValid } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { capitalize } from "lodash-es";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { startTransition, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { type z } from "zod";
import { IFRAME_URL } from "../[bookingId]/constants";
import CustomDatePicker from "../[bookingId]/driver-details/_components/date-picker";
import { driverFormSchema } from "../[bookingId]/driver-details/schema";
import { type IDriverFormValues } from "../[bookingId]/driver-details/types";
import { type IDriverDetails } from "../[bookingId]/types";
import { type Route } from "next";
import { useProgressBar } from "@/components/progress-bar";
import { ID_TYPES_DROPDOWN, type ID_TYPES_DROPDOWN_TYPE } from "./constants";
import { useLocale, useTranslations } from "next-intl";
import { useQueryState } from "nuqs";
import clsx from "clsx";

type DriverFormValues = z.infer<typeof driverFormSchema>;
interface IEditDriverProps {
  driver: IDriverDetails;
  countries: Country[];
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  nationality: string;
  countryCode: string;
  mobileNumber: string;
  idType: string;
  dob: string;
  hijrahDob: string;
  address: string;
  documentNo: string;
  idIssuedCountry: string;
  documentExpiry: string;
  documentHijrahExpiry: string;
  // license details
  licenseNo: string;
  licenseCountry: string;
  licenseExpiry: string;
  licenseHijrahExpiry: string;
}

export default function EditDriver(props: IEditDriverProps) {
  const router = useRouter();
  const { toast } = useToast();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [, setIdTypeState] = useState(props.idType);
  const [filteredIdTypes, setFilteredIdTypes] = useState<typeof ID_TYPES_DROPDOWN>(ID_TYPES_DROPDOWN);
  const [_idtype, setIdType] = useState(props.idType);
  const progress = useProgressBar();
  const locale = useLocale();
  const t = useTranslations("drivers");
  const [, setDriverName] = useQueryState("driverName", {
    shallow: false,
  });
  const [, setDriverMode] = useQueryState("driverMode", {
    shallow: false,
  });

  const isWalkIn = pathname.includes("/create/driver-details");
  const quoteId = searchParams.get("quoteId");

  const findCountryId = (countryName: string) => {
    return props.countries.find((country) => country.name === countryName)?.id ?? 2;
  };

  const findCountry = (countryName: string) => {
    return props.countries.find((country) => country.name === countryName) ?? props.countries[0]!;
  };

  useEffect(() => {
    setFilteredIdTypes(updateIdType(findCountry(props.nationality)));
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const defaultValues: Partial<DriverFormValues> = {
    title: props.title,
    firstName: props.firstName,
    lastName: props.lastName,
    email: props.email,
    nationality: props.nationality,
    countryCode: `+${props.countryCode}`,
    mobileNumber: `+${props.countryCode}${props.mobileNumber}`,
    idType: props.idType,
    dob: props.dob,
    hijrahDob: props.hijrahDob,
    address: props.address,
    documentNo: props.documentNo,
    idIssuedCountry: props.idType === "SAUDI_NATIONAL" ? props.nationality : props.idIssuedCountry,
    documentExpiry: props.documentExpiry,
    documentHijrahExpiry: props.documentHijrahExpiry,
    // license details
    licenseNo: props.licenseNo,
    licenseCountry: props.licenseCountry,
    licenseExpiry: props.licenseExpiry,
    licenseHijrahExpiry: props.licenseHijrahExpiry,
    borderNumber: "",
  };

  const form = useForm<DriverFormValues>({
    resolver: zodResolver(driverFormSchema),
    defaultValues,
    mode: "onChange",
  });

  // Add wrapper function for setValue
  const setFormValue = (name: keyof DriverFormValues, value: DriverFormValues[keyof DriverFormValues]) => {
    form.setValue(name, value, { shouldDirty: true });
  };

  const getIdStatus = (countryCode: number) => {
    // 966, then idstaus isSaudi: true
    // 965, 968, 971, 973, 974, then idstatus isGCC: true
    const gccCountries = [965, 968, 971, 973, 974];
    const idStatus = {
      isSaudi: false,
      isGCC: false,
      isTourist: false,
    };
    if (countryCode === 966) {
      idStatus.isSaudi = true;
    } else if (gccCountries.includes(countryCode)) {
      idStatus.isGCC = true;
    } else {
      idStatus.isTourist = true;
    }
    return idStatus;
  };

  async function onSubmit(data: DriverFormValues) {
    data.mobileNumber = data.mobileNumber.replace(`${data.countryCode}`, "");
    const countryCode = data.countryCode.toString().replace("+", "");
    const nationalityCountryId = findCountryId(data.nationality);
    const idIssuedCountryId = findCountryId(data.idIssuedCountry);
    const licenseCountryId = findCountryId(data.licenseCountry);
    const isVisitor = data.idType === "VISITOR";
    const _data: IDriverFormValues = {
      driverUid: props.driver.driverUId,
      title: data.title,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber ?? "",
      countryCode: Number(countryCode),
      email: data.email,
      dob: makeDateValid(data.dob),
      hijrahDob: makeDateValid(data.hijrahDob),
      documents: [],
      nationality: {
        id: nationalityCountryId,
      },
      address: {
        street: data.address,
      },
      idType: data.idType,
      metadata: {
        borderNumber: data.borderNumber ?? "",
      },
    };

    const _idType =
      data.idType === "SAUDI_NATIONAL" || data.idType === "GCC"
        ? "ID"
        : data.idType === "RESIDENT"
          ? "IQAMA"
          : data.idType === "VISITOR"
            ? "PASSPORT"
            : "PASSPORT";

    // if idtype is other than visitor, then dont add border number
    if (!isVisitor) {
      delete _data.metadata;
    }

    // if id type is gcc, or passport, then set the version:1, otherwise dont add it
    // const shouldAddVersion = data.idType === "GCC" || data.idType === "VISITOR";
    const document = {
      ...props.driver.documents.find((doc) => doc.type === _idType),
      documentNo: data.documentNo,
      expiry: makeDateValid(data.documentExpiry),
      hijrahExpiry: makeDateValid(data.documentHijrahExpiry),
      issuedPlace: {
        id: idIssuedCountryId,
      },
      type: _idType,
      version: 1,
    };

    const license = {
      ...props.driver.documents.find((doc) => doc.type === "LICENSE"),
      documentNo: data.licenseNo,
      expiry: makeDateValid(data.licenseExpiry),
      hijrahExpiry: makeDateValid(data.licenseHijrahExpiry),
      issuedPlace: {
        id: licenseCountryId,
      },
      type: "LICENSE",
    };

    _data.documents = [document, license];

    // NOTE: keep the log
    console.log("data", JSON.stringify(_data));

    try {
      let response = null;
      if (isWalkIn) {
        if ("driverUid" in _data) {
          delete (_data as { driverUid?: string }).driverUid;
        }

        if (!quoteId) {
          toast({
            variant: "destructive",
            title: t("errors.failed"),
            description: "Quote id missing",
            duration: 3000,
          });
          return;
        }

        const params = new URLSearchParams(searchParams);
        // prepare a url with all the existing params

        const url = `${pathname}?${params.toString()}` as Route;

        response = await driverAttachOrCreate(_data, quoteId, url);
        if (response.status === 200) {
          progress.start();
          startTransition(() => {
            void setDriverName(`${data.firstName} ${data.lastName}`);
            void setDriverMode("viewDetail");
            setTimeout(() => {
              window.location.reload();
            }, 500);

            progress.done();
          });
        }
      } else {
        response = await updateDriver(_data);
      }

      if (response.status === 500) {
        toast({
          variant: "destructive",
          title: t("errors.serverFailure"),
          duration: 3000,
        });
        return;
      }

      if (response.status !== 200) {
        toast({
          variant: "destructive",
          title: t("errors.failed"),
          description: capitalize(response.body?.desc ?? t("errors.failedToUpdateDriver")),
          duration: 3000,
        });
        return;
      }

      if (response.status === 200) {
        toast({
          variant: "success",
          title: t("success.driverUpdated"),
          description: "",
          duration: 3000,
        });
        if (!isWalkIn) {
          const _sp = new URLSearchParams(searchParams);
          _sp.delete("edit");
          router.push(`${pathname}?${_sp.toString()}`);
        }
      }
    } catch (error) {
      console.error("Error updating driver:", JSON.stringify(error));
    }
  }

  const updateIdType = (country: Country) => {
    const code = country.countryCallingCodes[0]?.replace("+", "") ?? "966";
    const idStatus = getIdStatus(Number(code));
    const filteredIdTypes = idStatus.isGCC
      ? ID_TYPES_DROPDOWN.filter((type) => type.value !== "SAUDI_NATIONAL")
      : idStatus.isTourist
        ? ID_TYPES_DROPDOWN.filter((type) => type.value !== "SAUDI_NATIONAL" && type.value !== "GCC")
        : ID_TYPES_DROPDOWN;
    return filteredIdTypes;
  };

  // Get localized ID type label
  const getLocalizedIdType = (idType: string) => {
    const matchingType = ID_TYPES_DROPDOWN.find((type) => type.value === idType);

    if (matchingType) {
      if (matchingType.translationKey) {
        return t(`idTypes.${matchingType.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
          defaultValue: matchingType.label,
        });
      }
      return matchingType.label;
    }

    return capitalize(idType.split("_").join(" "));
  };

  return (
    <div className="m-4 flex flex-col gap-x-3 rounded-md bg-slate-100">
      <p className="p-4 text-base font-bold text-slate-900 ">{t("sections.driverDetails")}</p>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("fields.firstName")}</FormLabel>
                    <div className="flex items-center overflow-hidden rounded-md border">
                      <Select
                        onValueChange={(value) => setFormValue("title", capitalize(value))}
                        defaultValue={capitalize(form.getValues("title")) ?? "Mr"}
                      >
                        <SelectTrigger className="w-16 rounded-r-none border-none focus:outline-none">
                          <SelectValue placeholder="Mr" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Mr">Mr</SelectItem>
                          <SelectItem value="Mrs">Mrs</SelectItem>
                        </SelectContent>
                      </Select>
                      <Input
                        {...field}
                        placeholder={t("fields.firstName")}
                        className="flex-1 rounded-l-none border-none !pl-0 focus:outline-none"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="">{t("fields.lastName")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.lastName")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel className="">{t("fields.mobile")}</FormLabel>
                      <FormControl>
                        <PhoneInput
                          value={field.value}
                          onCountryChange={(value) => {
                            setFormValue("mobileNumber", value ?? "");
                            setFormValue("countryCode", value ?? "");
                          }}
                          onChange={(value: { countryCode: string; phoneNumber: string }) => {
                            setFormValue("mobileNumber", `${value?.countryCode}${value?.phoneNumber}`);
                            setFormValue("countryCode", value?.countryCode);
                          }}
                          placeholder="+966 50 123 4567"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="">{t("fields.email")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.email")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="nationality"
                render={({ field }) => (
                  <FormItem className="w-full ">
                    <FormLabel>{t("fields.nationality")}</FormLabel>
                    <CountryDropdown
                      placeholder={t("fields.nationality")}
                      options={props.countries}
                      defaultValue={field.value}
                      onChange={(country) => {
                        if (!country) return;
                        // if country is saudi arabia, then set id type to saudi_national
                        if (country.name === "Saudi Arabia") {
                          setFormValue("idType", "SAUDI_NATIONAL");
                          setFormValue("idIssuedCountry", country.name);
                        }
                        const filteredIdTypes = updateIdType(country);
                        field.onChange(country.name);
                        setFormValue("idType", filteredIdTypes[0]?.value ?? "SAUDI_NATIONAL");
                        setFilteredIdTypes(filteredIdTypes);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="idType"
                render={() => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel>{t("fields.idType")}</FormLabel>
                      <div className="flex items-center overflow-hidden rounded-md border">
                        <Select
                          onValueChange={(value) => {
                            if (form.getValues("nationality") === "Saudi Arabia") {
                              setFormValue("idType", "SAUDI_NATIONAL");
                              setIdType(value);
                              return;
                            }
                            setFormValue("idType", value);
                            setIdType(value);
                            setIdTypeState(value);
                          }}
                          defaultValue={form.getValues("idType")}
                          value={form.getValues("idType")}
                          // disable it if idtype is saudi_national
                          disabled={form.getValues("nationality") === "Saudi Arabia"}
                        >
                          <SelectContent>
                            {filteredIdTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.translationKey
                                  ? t(`idTypes.${type.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
                                      defaultValue: type.label,
                                    })
                                  : type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                          <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                            <SelectValue placeholder={t("idTypes.saudiNational2")} />
                          </SelectTrigger>
                        </Select>
                      </div>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="idIssuedCountry"
                render={({ field }) => (
                  <FormItem className="w-full ">
                    <FormLabel>{t("fields.idIssuedCountry")}</FormLabel>
                    <CountryDropdown
                      disabled={form.getValues("idType") === "SAUDI_NATIONAL"}
                      options={props.countries}
                      placeholder={t("fields.idIssuedCountry")}
                      defaultValue={field.value}
                      onChange={(country) => {
                        field.onChange(country.name);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="documentNo"
                render={({ field }) => {
                  // if id type is saudi_national, then udpate the license number same as of document number and vice versa
                  const idtype = form.getValues("idType");
                  if (idtype === "SAUDI_NATIONAL") {
                    setFormValue("licenseNo", field.value);
                  }
                  const idTypeLabel = idtype ? getLocalizedIdType(idtype) : null;
                  return (
                    <FormItem className="w-full">
                      <FormLabel>
                        {idTypeLabel ? (
                          <div
                            className={clsx(
                              "flex gap-x-1 pb-2",
                              locale === "ar" ? "flex-row-reverse justify-end" : "flex-row"
                            )}
                          >
                            <div>{idTypeLabel}</div>
                            <div>{t("fields.number")}</div>
                          </div>
                        ) : (
                          t("fields.idNumber")
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder={t("fields.idNumber")} {...field} value={field.value} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="hijrahDob"
                render={({ field }) => (
                  <FormItem>
                    <CustomDatePicker
                      label={t("fields.dateOfBirthHijri")}
                      date={field.value}
                      onChange={(date) => {
                        setFormValue("hijrahDob", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate("", date);
                          setFormValue("dob", dates.gregorean);
                        }
                      }}
                      months={locale === "ar" ? HIJRI_MONTHS_ARABIC : HIJRI_MONTHS}
                      yearPlaceholder="1395"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <CustomDatePicker
                      label={t("fields.dateOfBirthGregorian")}
                      date={field.value}
                      onChange={(date) => {
                        setFormValue("dob", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate(date);
                          setFormValue("hijrahDob", dates.hijri);
                        }
                      }}
                      months={GREGORIAN_MONTHS}
                      yearPlaceholder="1975"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="documentHijrahExpiry"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <CustomDatePicker
                      label={t("fields.idExpiryDateHijri")}
                      date={field.value}
                      onChange={(date) => {
                        setFormValue("documentHijrahExpiry", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate("", date);
                          setFormValue("documentExpiry", dates.gregorean);
                        }
                      }}
                      months={locale === "ar" ? HIJRI_MONTHS_ARABIC : HIJRI_MONTHS}
                      yearPlaceholder="1450"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="documentExpiry"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <CustomDatePicker
                      label={t("fields.idExpiryDateGregorian")}
                      date={field.value}
                      onChange={(date) => {
                        setFormValue("documentExpiry", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate(date);
                          setFormValue("documentHijrahExpiry", dates.hijri);
                        }
                      }}
                      months={GREGORIAN_MONTHS}
                      yearPlaceholder="2028"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="">{t("fields.address")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.address")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* only show for the visitors otherwise hidden */}
            {_idtype === "VISITOR" ? (
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="borderNumber"
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <div className="flex flex-row items-center justify-between">
                          <FormLabel>{t("fields.borderNumber")}</FormLabel>
                          <Link href={IFRAME_URL} target="_blank" className="h-auto p-0 font-normal text-blue-600">
                            {t("actions.getBorderNumber")}
                          </Link>
                        </div>
                        <FormControl>
                          <Input placeholder={t("fields.borderNumber")} {...field} value={field.value} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
            ) : (
              <></>
            )}
          </div>
          <Separator />
          {/* License details */}
          <div className="flex w-full flex-col">
            <span className="p-4 text-base font-bold text-slate-900 ">{t("sections.drivingLicense")}</span>
            <div className="flex w-full flex-row gap-x-6 p-4">
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseCountry"
                  render={({ field }) => (
                    <FormItem className="w-full ">
                      <FormLabel>{t("fields.country")}</FormLabel>
                      <CountryDropdown
                        placeholder={t("fields.country")}
                        options={props.countries}
                        defaultValue={field.value}
                        onChange={(country) => {
                          field.onChange(country.name);
                        }}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseNo"
                  render={({ field }) => {
                    if (form.getValues("idType") === "SAUDI_NATIONAL") {
                      setFormValue("documentNo", field.value);
                    }
                    return (
                      <FormItem className="w-full">
                        <FormLabel className="">{t("fields.licenseNumber")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("fields.licenseNumber")} {...field} value={field.value} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
            </div>
            <Separator />
            <div className="flex w-full flex-row gap-x-6 p-4">
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseHijrahExpiry"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <CustomDatePicker
                        label={t("fields.licenseExpiryDateHijri")}
                        date={field.value}
                        onChange={(date) => {
                          setFormValue("licenseHijrahExpiry", date);
                          if (isValidDate(date)) {
                            const dates = getFullDate("", date);
                            setFormValue("licenseExpiry", dates.gregorean);
                          }
                        }}
                        months={locale === "ar" ? HIJRI_MONTHS_ARABIC : HIJRI_MONTHS}
                        yearPlaceholder="1445"
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseExpiry"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <CustomDatePicker
                        label={t("fields.licenseExpiryDateGregorian")}
                        date={field.value}
                        onChange={(date) => {
                          setFormValue("licenseExpiry", date);
                          if (isValidDate(date)) {
                            const dates = getFullDate(date);
                            setFormValue("licenseHijrahExpiry", dates.hijri);
                          }
                        }}
                        months={GREGORIAN_MONTHS}
                        yearPlaceholder="2028"
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="flex w-full justify-end gap-x-2 p-4">
            <Dialog open={isDialogOpen}>
              <DialogTrigger
                asChild
                onClick={(e) => {
                  if (form.formState.isDirty) {
                    setIsDialogOpen(true);
                  } else {
                    e.preventDefault();
                    router.back();
                  }
                }}
              >
                <Button variant="outline">{t("actions.cancel")}</Button>
              </DialogTrigger>
              <DialogContent className="p-0 sm:max-w-md">
                <DialogHeader className="p-4">
                  <DialogTitle>{t("dialogs.cancelEdit.title")}</DialogTitle>
                  <DialogDescription>{t("dialogs.cancelEdit.description")}</DialogDescription>
                </DialogHeader>
                <Separator className="" />
                <DialogFooter className="p-4 sm:justify-end">
                  <Button
                    onClick={() => {
                      setIsDialogOpen(false);
                      router.back();
                    }}
                    variant="outline"
                  >
                    {t("dialogs.cancelEdit.confirm")}
                  </Button>
                  <Button
                    onClick={() => {
                      setIsDialogOpen(false);
                    }}
                    type="button"
                  >
                    {t("dialogs.cancelEdit.continue")}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <LoadingSpinner className="ml-1 text-slate-800" />
              ) : (
                t("actions.saveChanges")
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
