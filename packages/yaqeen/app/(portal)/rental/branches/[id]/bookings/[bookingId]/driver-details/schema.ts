import { checkLicenseExpiry, dateEqGr<PERSON>han } from "@/lib/utils";
import { z } from "zod";

export const driverFormSchema = z.object({
  title: z.string(),
  firstName: z
    .string()
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(30, {
      message: "Name must not be longer than 15 characters.",
    }),
  lastName: z
    .string()
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(30, {
      message: "Name must not be longer than 15 characters.",
    }),
  countryCode: z.string().min(2, {
    message: "Please select a country code.",
  }),
  mobileNumber: z
    .string()
    .refine((value) => value.startsWith("+"), {
      message: "Number must start with a '+' sign.",
    })

    .superRefine((value, ctx) => {
      if (value.startsWith("+966") && !/^\+9665\d{8}$/.test(value)) {
        ctx.addIssue({
          code: "custom",
          message: "Valid format: +9665XXXXXXXX (12 digits).",
        });
      } else if (!value.startsWith("+966") && !/^\+\d{7,15}$/.test(value)) {
        ctx.addIssue({
          code: "custom",
          message: "Valid format: +Code with 7-15 digits.",
        });
      }
    }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  nationality: z.string().min(2, {
    message: "Please select",
  }),
  idType: z.string().min(2, {
    message: "Please select an ID type.",
  }),
  idIssuedCountry: z.string(),
  documentNo: z.string().min(5, {
    message: "Document number must be at least 5 characters.",
  }),
  documentExpiry: z.string().refine((_value) => {
    return checkLicenseExpiry(_value, 6, "gregorean");
  }, "Document must be valid for 6 months or more."),
  documentHijrahExpiry: z.string().refine((_value) => {
    return checkLicenseExpiry(_value, 6, "hijri");
  }, "Document must be valid for 6 months or more."),
  dob: z.string().refine((_value) => {
    return dateEqGrThan(_value, 21, "gregorean");
  }, "You must be 21 years or older."),
  hijrahDob: z.string().refine((_value) => {
    return dateEqGrThan(_value, 21, "hijri");
  }, "You must be 21 years or older."),
  address: z
    .string()
    .min(5, {
      message: "Address must be at least 5 characters.",
    })
    .max(50, {
      message: "Address must not be longer than 50 characters.",
    }),

  /** License details */

  licenseNo: z.string().min(5, {
    message: "Liscence number must be at least 5 characters.",
  }),
  licenseCountry: z.string().min(2, {
    message: "Please select a country.",
  }),
  // should be valid for 6 months and more
  licenseExpiry: z.string().refine((_value) => {
    return checkLicenseExpiry(_value, 6, "gregorean");
  }, "License must be valid for 6 months or more."),
  licenseHijrahExpiry: z.string().refine((_value) => {
    return checkLicenseExpiry(_value, 6, "hijri");
  }, "License must be valid for 6 months or more."),
  borderNumber: z.string().optional(),
});

export const driverFormSchemaOptionalBorder = driverFormSchema.omit({ borderNumber: true });
