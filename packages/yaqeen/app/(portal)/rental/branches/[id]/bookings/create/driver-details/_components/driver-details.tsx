"use client";
import { type Route } from "next";
import React, { startTransition, useState, useCallback, useEffect } from "react";
import { DriverSearch } from "./driver-search";
import { type Driver } from "@/api/contracts/booking/driver-details-contract";
import DriverDetailsCard from "./driver-detail-card";
import { type Country } from "@/components/ui/country-dropdown";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useProgressBar } from "@/components/progress-bar";
import CreateDriver from "./create-driver";
import { useToast } from "@/lib/hooks/use-toast";
import { driverAttachOrCreate, getDriverById } from "@/lib/actions";
import { useTranslations } from "next-intl";
import DriverDetailsSkeleton from "../../../[bookingId]/driver-details/_components/driver-details-skeleton";

interface DriverDetailsProps {
  drivers: Driver[];
  searchParams: Record<string, string | string[] | undefined>;
  countries: Country[];
}

/**
 * Driver details component handling driver selection, creation, and management
 */
export function DriverDetails({ drivers = [], countries }: DriverDetailsProps) {
  // Get translations
  const t = useTranslations("drivers");
  const commonT = useTranslations("common");
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const progress = useProgressBar();
  const { toast } = useToast();
  const driverMode = searchParams.get("driverMode");
  const quoteId = searchParams.get("quoteId");
  const driverUid = searchParams.get("driverUid");

  // Fetch driver details if driverUid exists but selectedDriver doesn't
  useEffect(() => {
    const fetchDriverDetails = async () => {
      if (driverUid && !selectedDriver) {
        try {
          setIsLoading(true);
          const response = await getDriverById(driverUid);

          if (response.status === 200 && response.body) {
            setSelectedDriver(response.body);
          } else {
            console.error("Failed to fetch driver details:", response);
            toast({
              title: "Error",
              description: "Driver not found",
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Error fetching driver details:", error);
          toast({
            title: commonT("errors.unexpectedError"),
            description: t("errors.unexpectedError"),
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    // Only fetch if we have a driverUid but no selectedDriver
    if (driverMode === "viewDetail" && driverUid && !selectedDriver) {
      void fetchDriverDetails();
    }
  }, [driverUid, selectedDriver, t, commonT, toast, driverMode]);

  /**
   * Update URL parameters and navigate
   */
  const updateUrlParams = useCallback(
    (paramUpdates: Record<string, string>) => {
      progress.start();
      const params = new URLSearchParams(searchParams);
      Object.entries(paramUpdates).forEach(([key, value]) => {
        params.set(key, value);
      });
      startTransition(() => {
        router.replace(`${pathname}?${params.toString()}` as Route);
        progress.done();
      });
    },
    [progress, router, pathname, searchParams]
  );

  /**
   * Transform driver object to match expected API format
   */
  const transformDriverData = (driver: Driver) => {
    return {
      ...driver,
      documents: driver.documents.map((doc) => ({
        ...doc,
        issuedPlace: doc.issuedPlace
          ? {
              id: doc.issuedPlace.code,
              name: doc.issuedPlace.name,
            }
          : undefined,
      })),
    };
  };

  /**
   * Handle driver selection and attachment with improved progress indicators
   */
  const onSelectDriver = async (driver: Driver) => {
    // Start progress indicator immediately
    progress.start();
    try {
      // Transform driver object to match the expected format
      const transformedDriver = transformDriverData(driver);
      if (!quoteId) {
        toast({
          title: t("errors.missingQuoteId"),
          description: t("errors.missingQuoteId"),
          variant: "destructive",
        });
        progress.done();
        return;
      }
      // Attach or create driver
      const res = await driverAttachOrCreate(transformedDriver, quoteId);
      if (res?.status !== 200) {
        toast({
          title: t("errors.failedToAssignDriver"),
          description: res.body?.desc || t("errors.failedToAssignDriver"),
          variant: "destructive",
        });
        progress.done();
        return;
      }
      // Update local state
      setSelectedDriver(driver);
      // Update URL params with startTransition for smoother UI
      startTransition(() => {
        const params = new URLSearchParams(searchParams);
        params.set("driverUid", driver.driverUId);
        params.set("driverName", `${driver.firstName} ${driver.lastName}`);
        params.set("driverMode", `viewDetail`);
        router.replace(`${pathname}?${params.toString()}` as Route);
        progress.done();
      });
    } catch (error) {
      console.error("Error selecting driver:", error);
      toast({
        title: commonT("errors.unexpectedError"),
        description: t("errors.unexpectedError"),
        variant: "destructive",
      });
      progress.done();
    }
  };

  /**
   * Handle driver deselection
   */
  const deleteHandler = () => {
    setIsLoading(true);
    startTransition(() => {
      setSelectedDriver(null);
      updateUrlParams({
        driverUid: "",
        driverName: "",
        driverMode: "",
      });
      setIsLoading(false);
    });
  };

  /**
   * Switch to create driver mode
   */
  const handleCreateNew = () => {
    setSelectedDriver(null);
    updateUrlParams({
      driverUid: "",
      driverName: "",
      driverMode: "create",
    });
  };

  if (isLoading) {
    return <DriverDetailsSkeleton />;
  }

  /**
   * Render the appropriate component based on state
   */
  const renderDriverComponent = () => {
    if (driverMode === "create") {
      return <CreateDriver countries={countries} />;
    }

    if (selectedDriver && driverUid) {
      return (
        <DriverDetailsCard
          driver={selectedDriver}
          deleteHandler={deleteHandler}
          countries={countries}
          isLoading={isLoading}
        />
      );
    }

    return (
      <DriverSearch onSelectDriver={onSelectDriver} onCreateNew={handleCreateNew} className="mb-3" drivers={drivers} />
    );
  };

  return <div className="w-full">{renderDriverComponent()}</div>;
}
