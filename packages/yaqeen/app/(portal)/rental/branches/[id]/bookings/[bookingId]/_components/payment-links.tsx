import { api } from "@/api";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { enUS, arSA } from "date-fns/locale";
import { getUserLocale } from "@/services/locale";
import { getTranslations } from "next-intl/server";
import { cn } from "@/lib/utils";
import { CopyLinkButton } from "./CopyLinkButton";

interface PaymentLinksProps {
  bookingNo: string;
}

export default async function PaymentLinks({ bookingNo }: PaymentLinksProps) {
  const t = await getTranslations("bookingDetail");
  const locale = await getUserLocale();
  const nLocale = locale === "ar" ? arSA : enUS;

  const response = await api.payment.quickPaySearch({
    query: {
      payTypes: "YAQEEN_AGREEMENT_EXTENSION,YAQEEN_TRAFFIC_FINE",
      order: "desc",
      sort: "id",
      size: 100,
      referenceNo: bookingNo,
    },
  });

  if (response.status !== 200 || !response.body?.data?.length) {
    return null; // Don't render the component if no payment links
  }

  const paymentLinks = response.body.data;

  const getPaymentStatus = (expireAt: number) => {
    const now = Math.floor(Date.now() / 1000);
    if (now > expireAt) {
      return "expired";
    }
    return "active";
  };

  const getPaymentTypeLabel = (payType: string) => {
    switch (payType) {
      case "YAQEEN_TRAFFIC_FINE":
        return "trafficFine";
      case "YAQEEN_AGREEMENT_EXTENSION":
        return "agreementExtension";
      default:
        return payType;
    }
  };

  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="px-4">
        <CardTitle className="text-lg font-bold">{t("paymentLinks")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        <div className="text grid grid-cols-5 bg-slate-50 p-4 text-slate-500">
          <div>
            {t("amount")} ({t("currency")})
          </div>
          <div>{t("createdOn")}</div>
          <div>{t("type")}</div>
          <div>{t("link")}</div>
          <div>{t("status")}</div>
        </div>

        {paymentLinks.map((link) => {
          const status = getPaymentStatus(link.expireAt);
          const type = getPaymentTypeLabel(link.payType);
          const isPaid = !!link.paymentDetails;

          return (
            <div key={link.id} className="grid grid-cols-5 border-t p-4 text-slate-900">
              <div>
                {link.currency} {link.amount}
              </div>
              <div>
                <div>
                  {format(new Date(link.createdOn * 1000), "dd/MM/yyyy", {
                    locale: nLocale,
                  })}
                </div>
                <div className="text-sm text-slate-500">
                  {format(new Date(link.createdOn * 1000), "HH:mm:ss", {
                    locale: nLocale,
                  })}
                </div>
              </div>
              <div>
                {t(
                  // @ts-expect-error TODO useTranslations is not typed correctly
                  type
                )}
              </div>
              <div className="flex items-center gap-2">
                <Link href={link.paymentLink} target="_blank" className="text-blue-600 underline">
                  {t("link")}
                </Link>
                <div>
                  <CopyLinkButton variant="ghost" label="" link={link.paymentLink} />
                </div>
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={cn(
                      "rounded-full",
                      isPaid
                        ? "bg-green-100 text-green-800"
                        : status === "expired"
                          ? "bg-red-100 text-red-800"
                          : "bg-blue-100 text-blue-800"
                    )}
                  >
                    {isPaid ? t("Paid") : t(status)}
                  </Badge>
                </div>
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
