"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  DialogContent,
  <PERSON>alogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { startTransition, useState } from "react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { type Promotion } from "@/api/contracts/schema";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { CheckCircle, TrashSimple } from "@phosphor-icons/react/dist/ssr";
import { type Booking } from "@/api/contracts/booking/schema";
import { useToast } from "@/lib/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import TooltipComponent from "@/components/tooltip-component";
import AlertDialog from "./alert-dialog";
import { useTranslations } from "next-intl";

interface SearchDiscountProps {
  setDiscountDetail: (item: Booking["priceDetail"]["discountDetail"]) => void;
  promotions: Promotion[];
  corporatePromotions: Promotion[];
  discountDetail: Booking["priceDetail"]["discountDetail"];
}

const DiscountList = ({
  detail,
  onRemove,
}: {
  detail: Booking["priceDetail"]["discountDetail"];
  onRemove: () => void;
}) => {
  const t = useTranslations("discount");
  return (
    <div>
      <div className="py-4">
        <div className=" flex w-full p-4 py-4 text-sm ">
          {detail?.isCorporate && (
            <div className="flex w-full flex-col gap-y-1">
              <span className="text-slate-500 	">{t("table.company")}</span>
              <span className=" text-slate-900 ">{detail?.name?.en ?? "N/A"}</span>
            </div>
          )}
          <div className="flex w-full flex-col gap-y-1">
            <span className="text-slate-500 	">{t("table.code")}</span>
            <span className=" text-slate-900 ">{detail?.promoCode ?? ""}</span>
          </div>
          <div className="flex w-full flex-col gap-y-1">
            <span className="text-slate-500 	">{t("table.amount")}</span>
            <span className=" text-slate-900 ">{detail?.discountPercentage ?? "N/A"}%</span>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <button className="flex items-center justify-center rounded-lg border border-slate-300  hover:bg-muted">
                <TrashSimple size={16} className="mx-3 my-1" />
              </button>
            </DialogTrigger>
            <DialogContent className="p-0 sm:max-w-md">
              <DialogHeader className="p-4">
                <DialogTitle>{t("remove_discount_title")}?</DialogTitle>
                <DialogDescription>{t("remove_discount_desc")}</DialogDescription>
              </DialogHeader>
              <Separator />
              <DialogFooter className="p-4">
                <DialogClose asChild>
                  <Button variant="outline">{t("cta.negative")}</Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button variant="destructive" className="bg-red-700" onClick={onRemove}>
                    {t("cta.submit")}
                  </Button>
                </DialogClose>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {detail?.isCorporate && (
          <div className="p-4">
            <Separator />
            <div className="flex w-full flex-col gap-y-1">
              <span className="text-slate-500">Company email</span>
              <div className=" flex items-center text-slate-900">
                <span><EMAIL></span>
                <TooltipComponent content="Verified">
                  <CheckCircle weight="fill" className="fill-lumi-600" />
                </TooltipComponent>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export function SearchDiscount({
  setDiscountDetail,
  promotions,
  corporatePromotions,
  discountDetail,
}: SearchDiscountProps) {
  const t = useTranslations("discount");
  const progress = useProgressBar();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  let discountPromoDetail: Booking["priceDetail"]["discountDetail"] | null = discountDetail;
  const [discountCode, setDiscountCode] = useQueryState("discountCode", { shallow: false });

  if (!discountDetail?.promoCode && discountCode) {
    let corporatePromo = corporatePromotions.find((prom) => prom.code === discountCode);
    if (!corporatePromo) {
      corporatePromo = promotions.find((prom) => prom.code === discountCode);
    }
    if (corporatePromo) {
      discountPromoDetail = {
        promoCode: corporatePromo.code,
        discountPercentage: String(corporatePromo.percentageDiscount),
        name: {
          en: corporatePromo?.name?.en,
        },
      };
    }
  } else {
    discountPromoDetail = null;
  }

  const selectHandler = async (item: Promotion) => {
    progress.start();
    startTransition(() => {
      setDiscountDetail({
        promoCode: item.code,
        discountPercentage: String(item.percentageDiscount),
        name: {
          en: item?.name?.en,
        },
      });
      void setDiscountCode(item.code);
      setIsOpen(false);
      setSearchQuery("");
      progress.done();
      toast({
        title: t("toast.select.success.title"),
        variant: "success",
      });
    });
  };

  const removeDiscountHandler = async () => {
    progress.start();
    startTransition(() => {
      setDiscountDetail({
        promoCode: "",
        discountPercentage: "",
        name: {
          en: "",
        },
      });
      void setDiscountCode("");
      progress.done();
      toast({
        title: t("toast.remove.success.title"),
        variant: "success",
      });
    });
  };

  return (
    <>
      <div className="relative">
        <Command className=" rounded-lg border shadow-sm">
          <CommandInput
            placeholder={t("search_placehodler")}
            value={searchQuery}
            onValueChange={(value) => {
              setSearchQuery(value);
              setIsOpen(value.length > 0);
            }}
            className="border-none focus:ring-0"
          />
          {isOpen && searchQuery && (
            <div className="absolute top-12 z-50 mt-1 w-full rounded-lg border bg-popover shadow-md">
              <CommandList className="p-2">
                <CommandEmpty>{t("no_search_results")}</CommandEmpty>
                <CommandGroup>
                  {promotions.map((item) => {
                    const DiscountBar = () => (
                      <div className="flex w-full cursor-pointer justify-between">
                        <span>{item.code}</span>
                        <span className="text-xs text-slate-500">{item.percentageDiscount}%</span>
                      </div>
                    );

                    return (
                      <CommandItem className=" py-2" key={item.id}>
                        {discountDetail?.promoCode ? (
                          <AlertDialog
                            selectHandler={() => {
                              setSearchQuery("");
                              return selectHandler(item);
                            }}
                          >
                            <div className=" w-full">
                              <DiscountBar />
                            </div>
                          </AlertDialog>
                        ) : (
                          <div className=" w-full" onClick={() => selectHandler(item)}>
                            <DiscountBar />
                          </div>
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </CommandList>
            </div>
          )}
        </Command>
      </div>
      {!!discountDetail?.promoCode && discountCode !== "" ? (
        <DiscountList detail={discountDetail} onRemove={removeDiscountHandler} />
      ) : discountPromoDetail && discountCode !== "" ? (
        <DiscountList detail={discountPromoDetail} onRemove={removeDiscountHandler} />
      ) : null}
    </>
  );
}
