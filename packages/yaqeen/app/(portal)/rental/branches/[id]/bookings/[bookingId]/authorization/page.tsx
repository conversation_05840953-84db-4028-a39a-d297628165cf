import { Suspense } from "react";
import PricingBreakdown from "../pricing-breakdown";
import { api } from "@/api";
import { notFound } from "next/navigation";
import ValidateCode from "./_components/ValidateCode";
import { type TajeerAgreement } from "@/api/contracts/schema";
import type { Pos, Booking, CalculatedPrice } from "@/api/contracts/booking/schema";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { NAV_ITEMS } from "../constants";
import SidesheetWrapper from "../_components/sidesheet-wrapper";

export default async function Authorization({
  params,
  searchParams,
}: {
  params: Promise<{ id: string; bookingId: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _params = await params;
  const suspenseKey = Object.entries(_params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(_params.bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  // Get driver UID for the DriverProfileSheet component
  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const posResponse = await api.branch.getAllPos({
    params: {
      id: String(_params.id),
    },
  });

  const posResponseData = posResponse?.body as Pos;
  if (!posResponseData?.data) {
    throw new Error(`Error: ${posResponseData.total}`);
  }

  const suggestedAuthR = await api.booking.getSuggestedAuthorization({
    params: {
      bookingNo: bookingResponse.body.bookingNo!,
    },
  });

  if (suggestedAuthR.status !== 200) {
    throw new Error(`Error: ${suggestedAuthR.status}`);
  }
  const suggestAuth = suggestedAuthR?.body?.suggestedAuths?.[0]?.name ?? "";

  const booking: Booking = bookingResponse.body;
  const getTajeerContracts = await api.tajeer.getAllTajeerAgreements({
    query: {
      referenceNumber: booking?.bookingNo ?? "",
    },
  });
  let tajeerContracts: TajeerAgreement[] = [];
  if (getTajeerContracts.status === 200) {
    tajeerContracts = getTajeerContracts?.body?.data ?? [];
  }

  let authorizationStatus;
  const authorizationStatusResp = await api.tajeer.getDriverAuthStatus({
    query: {
      referenceNumber: booking.bookingNo!,
      page: 1,
      size: 1,
    },
  });

  if (authorizationStatusResp.status === 200) {
    authorizationStatus = authorizationStatusResp?.body?.total ?? 0;
  }

  const { quoteId } = await searchParams;
  const getCalculatedPrice = await api.booking.getCalculatePriceForQuote({
    params: {
      quoteId: String(quoteId),
    },
  });

  const calculatedPrice = getCalculatedPrice.body as CalculatedPrice;
  if (getCalculatedPrice.status !== 200) {
    throw new Error(`Error: ${getCalculatedPrice.status}`);
  }

  const tajeerLink =
    process.env.NODE_ENV === "production" ? "https://tajeer.tga.gov.sa/" : "https://tajeerstg.tga.gov.sa/#/";
  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <ValidateCode
          posResponseData={posResponseData}
          tajeerLink={tajeerLink}
          bookingNo={booking.bookingNo!}
          tajeerContracts={tajeerContracts}
          calculatedPrice={calculatedPrice}
          authorizationStatus={authorizationStatus}
          suggestAuth={suggestAuth}
        />
        <ActionsBar bookingNo={booking.bookingNo} className="w-full" navItemsArray={NAV_ITEMS} />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={bookingResponse.body} _searchParams={searchParams}>
            {driverUId && <SidesheetWrapper driverUId={driverUId} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
