"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Separator } from "@/components/ui/separator";
import { CalendarCheck, Clock, MapPin } from "@phosphor-icons/react/dist/ssr";
import { format } from "date-fns";
import { Suspense } from "react";
import { type Booking } from "@/api/contracts/booking/schema";
import Preferences from "./components/preferences";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";

export default function StaticBookingDetailAndRentalRate({ booking }: { booking: Booking }) {
  const nLocale = useLocale() as "en" | "ar";
  const picupBranchName = booking.pickupBranch?.name[nLocale] ?? "";
  const dropoffBranchName = booking.dropOffBranch?.name[nLocale] ?? "";

  const locale = nLocale === "en" ? enUS : arSA;
  const bookingDate = booking.bookingDateTime
    ? format(booking.bookingDateTime * 1000, "dd/MM/yyyy - HH:mm:ss", { locale })
    : "";

  const t = useTranslations("closeAgreement");
  return (
    <section className="flex flex-col gap-y-6">
      <Card className="flex flex-col shadow">
        <CardHeader className=" flex w-full flex-row justify-between gap-2 p-4 ">
          <CardTitle className="text-lg font-bold  ">{t("Booking details")}</CardTitle>
          <span className="text-sm text-slate-500">
            {t("Booked on")}: {bookingDate}
          </span>
        </CardHeader>
        <Separator />
        <CardContent className="flex w-full p-0 max-md:flex-wrap ">
          <div className="flex min-w-[450px] flex-col text-sm font-medium">
            <p className="w-full px-4 pt-4 text-base font-bold text-slate-900 ">{t("Pickup")}</p>
            <div className="flex w-full items-start gap-1 p-4 text-sm font-medium text-slate-900 ">
              <div className="flex w-full items-center gap-1.5">
                <MapPin className=" size-5" />
                <span className=" w-full capitalize">{picupBranchName}</span>
              </div>
            </div>
            <Separator />
            <div className="flex w-full flex-col justify-center p-4 font-medium text-slate-900 ">
              <div className="flex w-full items-start justify-between gap-4">
                <div className="flex w-2/3 items-center justify-start gap-1.5 gap-x-4">
                  <div className="flex min-w-fit  flex-row items-center gap-x-1.5">
                    <CalendarCheck className="size-5" />
                    <span>{format(booking.pickupDateTime! * 1000, "eeee, dd/MM/yyyy", { locale })}</span>
                  </div>
                  <div className="flex w-full items-center gap-1.5">
                    <Clock className=" size-5" />
                    <p className="my-auto self-stretch">
                      {format(booking.pickupDateTime! * 1000, "HH:mm", { locale })}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <Separator className="h-1" />
            <p className="w-full px-4 pt-4 text-base font-bold text-slate-900 ">{t("Drop-off")}</p>
            <div className="flex w-full items-start gap-1 p-4 text-sm font-medium text-slate-900 ">
              <div className="flex w-full items-center gap-1.5">
                <MapPin className=" size-5" />
                <span className=" w-full capitalize">{dropoffBranchName}</span>
              </div>
            </div>
            <Separator />
            <div className="flex w-full flex-col justify-center p-4 font-medium text-slate-900 ">
              <div className="flex w-full items-start justify-between gap-4">
                <div className="flex w-2/3 items-center justify-start gap-1.5 gap-x-4">
                  <div className="flex min-w-fit  flex-row items-center gap-x-1.5">
                    <CalendarCheck className="size-5" />
                    <span>{format(booking.dropOffDateTime! * 1000, "eeee, dd/MM/yyyy", { locale })}</span>
                  </div>
                  <div className="flex w-full items-center gap-1.5">
                    <Clock className=" size-5" />
                    <p className="my-auto self-stretch">
                      {format(booking.dropOffDateTime! * 1000, "HH:mm", { locale })}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <Preferences booking={booking} />
          </Suspense>
        </CardContent>
      </Card>
    </section>
  );
}
