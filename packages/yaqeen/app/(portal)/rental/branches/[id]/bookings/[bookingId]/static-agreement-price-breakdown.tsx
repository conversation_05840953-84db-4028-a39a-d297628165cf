"use client";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { differenceInDays, differenceInHours } from "date-fns";
import { format } from "date-fns-tz";
import { SaveRemainingAmount } from "../_components/SaveRemainingAmount";
import { useSearchParams } from "next/navigation";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { useLocale, useTranslations } from "next-intl";
import type { ReactElement } from "react";
import { VehicleDetails } from "../_components/vehicle-details";
import { arSA, enUS } from "date-fns/locale";

export default function StaticAgreementPricingBreakdown({
  booking,
  children,
}: {
  booking: AgreementInvoice;
  children: ReactElement | null;
}) {
  const t = useTranslations("pricing");
  const locale = useLocale() as "en" | "ar";

  const nLocale = locale === "en" ? enUS : arSA;
  const tRefund = useTranslations("Refund");
  const _searchParams = useSearchParams();
  const searchParams = Object.fromEntries(_searchParams.entries());
  const { driver } = booking;
  const dropOffTimeStamp = Number(booking.dropOffDateTime);
  const pickupTimeStamp = Number(booking.pickupDateTime);

  const insuranceIds = [];
  const isInsuranceExist = typeof searchParams.insuranceIds === "string";
  if (isInsuranceExist && searchParams?.insuranceIds?.length) {
    insuranceIds.push(Number(searchParams.insuranceIds));
  }

  const { priceDetail } = booking;
  const { discountDetail } = priceDetail;
  const { includedComprehensiveInsurance, addOns } = priceDetail;
  const {
    rentalAmount,
    insuranceAmount,
    dropOffAmount,
    vatPercentage,
    vatAmount,
    totalSum,
    totalAddOnAmount,
    extraKmsChargeSum,
    extraFuelChargeSum,
    trafficFineSum,
  } = priceDetail.priceBreakdown ?? {};
  const { remainingAmount, driverPaidAmount, refundApprovedAmount, refundRequestedAmount } = booking ?? {};

  const { pickupBranch, dropOffBranch } = booking;

  if (!pickupBranch || !dropOffBranch) {
    throw new Error("Branch not found");
  }

  if (!dropOffTimeStamp) {
    throw new Error("Timestamps not found");
  }

  const pickupDate = format(new Date(pickupTimeStamp * 1000), "EEEE, dd/MM/yyyy, HH:mm", {
    locale: nLocale,
  });
  const dropOffDate = format(new Date(dropOffTimeStamp * 1000), "EEEE, dd/MM/yyyy, HH:mm", {
    locale: nLocale,
  });

  const days = differenceInDays(new Date(dropOffTimeStamp * 1000), new Date(pickupTimeStamp * 1000));
  const remainingHours = differenceInHours(new Date(dropOffTimeStamp * 1000), new Date(pickupTimeStamp * 1000)) % 24;

  const bookingSummary = t("duration", { days, hours: remainingHours });

  const pickupDetails = {
    date: pickupDate,
    location: pickupBranch.name[locale] || pickupBranch.name.en,
    city: pickupBranch.city.name[locale] || pickupBranch.city.name.en,
  };

  const dropOffDetails = {
    date: dropOffDate,
    location: dropOffBranch.name[locale] || dropOffBranch.name.en,
    city: dropOffBranch.city.name[locale] || dropOffBranch.city.name.en,
  };

  const { assignedVehicle } = booking;
  const vehicle = assignedVehicle?.vehiclePlateInfo;

  return (
    <Card className="w-full overflow-hidden text-sm text-slate-600">
      <div className="flex items-center justify-between">
        <CardHeader className="px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg capitalize text-slate-900">{`${driver?.title}. ${driver?.name}`}</CardTitle>
            </div>
          </div>
        </CardHeader>
        {children}
      </div>
      <Separator />

      <div className="flex items-center justify-between p-4">
        <h4 className="text-base font-bold text-slate-900">{t("Booking summary")}</h4>
        <span>{bookingSummary}</span>
      </div>

      <Separator />

      <CardContent className="p-0">
        <section className="space-y-4 p-4">
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">{t("Pickup")}</h5>
            <p className="font-medium text-slate-900">{pickupDetails.date}</p>
            <p>
              {pickupDetails.location}, {pickupDetails.city}
            </p>
          </div>
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">{t("Drop-off")}</h5>
            <p className="font-medium text-slate-900">{dropOffDetails.date}</p>
            <p>
              {dropOffDetails.location}, {dropOffDetails.city}
            </p>
          </div>
        </section>

        <Separator />

        {vehicle ? <VehicleDetails vehicle={vehicle} /> : null}

        <Separator />

        <section>
          <div className="flex items-center justify-between p-4 text-slate-900">
            <h5 className="text-base font-bold">{t("Price breakdown")}</h5>
            <span>{t("SAR")}</span>
          </div>

          <Separator />

          <div className="space-y-2 p-4">
            <div className="flex items-center justify-between">
              <span className="overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-relaxed tracking-normal">
                {t("Rental")}{" "}
                {days > 0 &&
                  `for ${days} day${days !== 1 ? "s" : ""} ${remainingHours > 0 ? `& ${remainingHours} hour${remainingHours !== 1 ? "s" : ""}` : ""}`}
              </span>
              <span>{Number(rentalAmount).toFixed(2)}</span>
            </div>

            {insuranceAmount && (
              <div className="flex items-center justify-between">
                <span>{includedComprehensiveInsurance ? t("comprehensiveInsurance") : t("insurance")}</span>
                <span>{Number(insuranceAmount).toFixed(2)}</span>
              </div>
            )}

            {totalAddOnAmount && (
              <div className="flex items-center justify-between">
                <span>{t("addOns")}</span>
                <span>{Number(totalAddOnAmount).toFixed(2)}</span>
              </div>
            )}
            {addOns?.map((addon) => (
              <div key={addon.id} className="flex items-center justify-between pl-4">
                <span>{addon?.name?.[locale] || t("addonFallback", { number: addon?.id })}</span>
              </div>
            ))}

            {extraKmsChargeSum && (
              <div className="flex items-center justify-between">
                <span>{t("extraKmCharges")}</span>
                <span>{Number(extraKmsChargeSum).toFixed(2)}</span>
              </div>
            )}

            {extraFuelChargeSum && (
              <div className="flex items-center justify-between">
                <span>{t("extraFuelCharges")}</span>
                <span>{Number(extraFuelChargeSum).toFixed(2)}</span>
              </div>
            )}

            {dropOffAmount && (
              <div className="flex items-center justify-between">
                <span>{t("dropOffFee")}</span>
                <span>{Number(dropOffAmount).toFixed(2)}</span>
              </div>
            )}
            {discountDetail?.promoCode && (
              <div className="flex items-center justify-between text-green-600">
                <span>
                  {t("Discount")} {Number(discountDetail.discountPercentage)}% ({discountDetail?.promoCode})
                </span>
                <span>-{discountDetail.totalDiscount}</span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <span>
                {t("vat")}
                {vatPercentage ? `${parseInt(vatPercentage)}%` : ""}
              </span>
              <span>{Number(vatAmount).toFixed(2)}</span>
            </div>

            {trafficFineSum !== "" && (
              <div className="flex items-center justify-between">
                <span>{t("trafficFine")}</span>
                <span>{Number(trafficFineSum).toFixed(2)}</span>
              </div>
            )}
          </div>
        </section>
      </CardContent>
      <CardFooter className="flex flex-col border-t p-0">
        <div className="w-full space-y-3 p-4">
          <div className="flex items-center justify-between text-base font-medium text-slate-900">
            <span>{t("total")}</span>
            <span>{Number(totalSum).toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>{t("paidAmount")}</span>
            <span>{Number(driverPaidAmount).toFixed(2)}</span>
          </div>
          {refundApprovedAmount && Number(refundApprovedAmount) > 0 && (
            <div className="flex w-full justify-between">
              <span>{tRefund("refunded")}</span>
              <span>-{Number(refundApprovedAmount).toFixed(2)}</span>
            </div>
          )}
        </div>
        <Separator />
        <div
          className={`flex w-full justify-between p-4 text-base font-medium 
            ${Number(remainingAmount) > 0 ? "bg-red-50 text-red-700" : "bg-lumi-50"}
          `}
        >
          <span className="text-slate-900">{t("remaining")}</span>
          <SaveRemainingAmount amount={Number(remainingAmount).toFixed(2)} />
          <span>{Number(remainingAmount).toFixed(2)}</span>
        </div>
        {refundRequestedAmount && Number(refundRequestedAmount) > 0 && (
          <div className="flex w-full justify-between p-4">
            <span>{tRefund("toBeRefunded")}</span>
            <span>-{Number(refundRequestedAmount).toFixed(2)}</span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}
