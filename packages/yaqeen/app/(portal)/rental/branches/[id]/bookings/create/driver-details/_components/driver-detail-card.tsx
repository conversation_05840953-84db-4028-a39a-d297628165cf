"use client";

import React from "react";

import { Fragment } from "react";

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { User } from "lucide-react";
import { countries as countriesDataList } from "country-data-list";
import { type Country } from "@/components/ui/country-dropdown";
import { type Driver } from "@/api/contracts/booking/driver-details-contract";
import { Card, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ProgressBarLink } from "@/components/progress-bar";
import { PencilSimple, TrashSimple } from "@phosphor-icons/react/dist/ssr";
import DriverDetailTile from "../../../_components/driver-detail-tile";
import { getFullDate, getLocalizedName } from "@/lib/utils";
import { type IdType } from "../../../[bookingId]/constants";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { type IDocument } from "../../../[bookingId]/types";
import { useSearchParams } from "next/navigation";
import EditDriver from "../../../_components/edit-driver";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { type Route } from "next";
import { ID_TYPES_ENUM } from "../../../_components/constants";

interface IDetails {
  title: string;
  isDate?: boolean;
  value?: string;
  hijri?: string;
  gregorean?: string;
}

interface DriverDetailsCardProps {
  driver: Driver | null;
  deleteHandler: () => void;
  countries: Country[];
  isLoading?: boolean;
}

export default function DriverDetailsCard({ driver, deleteHandler, countries }: DriverDetailsCardProps) {
  // Get translations
  const t = useTranslations("drivers");
  const locale = useLocale();
  const searchParams = useSearchParams();
  const driverMode = searchParams.get("driverMode");

  // Return early if driver is null
  if (!driver) {
    return (
      <Card className="flex flex-col items-center justify-center p-8 shadow">
        <p className="text-slate-600">{t("search.noDriverFound")}</p>
      </Card>
    );
  }

  const {
    firstName = "",
    lastName = "",
    countryCode = 966,
    mobileNumber = "",
    email = "",
    dob = "",
    hijrahDob = "",
    idType = "SAUDI_NATIONAL",
    nationality = { code: 0, name: { en: "N/A" } },
  } = driver;
  console.log("driver", driver);

  const dobDate = getFullDate(dob, hijrahDob);

  const license = driver?.documents?.find((doc) => doc.type === "LICENSE") ?? {
    code: 0,
    type: "LICENSE",
    documentNo: "XXX",
    expiry: "",
    hijrahExpiry: "",
    issuedPlace: { code: 0, name: { en: "" } },
  };

  const licenseExpiryDate = getFullDate(license.expiry, license.hijrahExpiry);

  let document: IDocument = {} as IDocument;
  switch (idType) {
    case "SAUDI_NATIONAL":
    case "GCC":
      document = driver.documents?.find((doc) => doc.type === "ID" || doc.type === "NATIONAL_ID") ?? document;
      break;
    case "RESIDENT":
      document = driver.documents?.find((doc) => doc.type === "IQAMA") ?? document;
      break;
    case "VISITOR":
      document = driver.documents?.find((doc) => doc.type === "PASSPORT") ?? document;
      break;
  }

  const documentExpiryDate = getFullDate(document.expiry, document.hijrahExpiry);

  const driverDetailsData: IDetails[][] = [
    [
      { title: t("fields.mobile"), value: mobileNumber ? `+${countryCode} ${mobileNumber}` : t("values.na") },
      { title: t("fields.email"), value: email || t("values.na") },
    ],
  ];

  const _nationality = getLocalizedName(nationality.name, locale, nationality.name?.en);

  // add data on base of idType for driver
  const _idType: IdType = idType as IdType;
  if (_idType === "SAUDI_NATIONAL") {
    driverDetailsData.push(
      [
        { title: t("fields.nationality"), value: _nationality },
        { title: t("fields.idNumber"), value: document.documentNo },
      ],
      [
        { title: t("fields.dateOfBirth"), isDate: true, hijri: dobDate.hijri, gregorean: dobDate.gregorean },
        {
          title: t("fields.idExpiryDate"),
          isDate: true,
          hijri: documentExpiryDate.hijri,
          gregorean: documentExpiryDate.gregorean,
        },
      ],
      [{ title: t("fields.address"), value: driver.address?.street }]
    );
  } else if (_idType === "GCC") {
    driverDetailsData.push(
      [
        { title: t("fields.nationality"), value: _nationality },
        { title: t("fields.idType"), value: idType },
      ],
      [
        { title: t("fields.idNumber"), value: document.documentNo },
        { title: t("fields.dateOfBirth"), isDate: true, hijri: dobDate.hijri, gregorean: dobDate.gregorean },
      ],
      [
        {
          title: t("fields.idExpiryDate"),
          isDate: true,
          hijri: documentExpiryDate.hijri,
          gregorean: documentExpiryDate.gregorean,
        },
        { title: t("fields.address"), value: driver.address?.street },
      ]
    );
  } else if (_idType === "RESIDENT") {
    driverDetailsData.push(
      [
        { title: t("fields.nationality"), value: _nationality },
        { title: t("fields.idType"), value: ID_TYPES_ENUM.IQAMA },
      ],
      [
        { title: t("fields.iqamaNumber"), value: document.documentNo },
        { title: t("fields.dateOfBirth"), isDate: true, hijri: dobDate.hijri, gregorean: dobDate.gregorean },
      ],
      [
        {
          title: t("fields.iqamaExpiryDate"),
          isDate: true,
          hijri: documentExpiryDate.hijri,
          gregorean: documentExpiryDate.gregorean,
        },
        { title: t("fields.address"), value: driver.address?.street },
      ]
    );
  } else if (_idType === "VISITOR") {
    driverDetailsData.push(
      [
        { title: t("fields.nationality"), value: _nationality },
        { title: t("fields.idType"), value: ID_TYPES_ENUM.PASSPORT },
      ],
      [
        {
          title: t("fields.passportIssueCountry"),
          value: getLocalizedName(document?.issuedPlace?.name, locale, document?.issuedPlace?.name?.en ?? ""),
        },
        { title: t("fields.passportNumber"), value: document.documentNo },
      ],
      [
        { title: t("fields.dateOfBirth"), isDate: true, hijri: dobDate.hijri, gregorean: dobDate.gregorean },
        {
          title: t("fields.passportExpiryDate"),
          isDate: true,
          hijri: documentExpiryDate.hijri,
          gregorean: documentExpiryDate.gregorean,
        },
      ],
      [
        { title: t("fields.address"), value: driver.address?.street },
        { title: t("fields.borderNumber"), value: driver?.metadata?.borderNumber },
      ]
    );
  } else {
    driverDetailsData.push([
      { title: t("fields.dateOfBirth"), isDate: true, hijri: dobDate.hijri, gregorean: dobDate.gregorean },
      { title: t("fields.address"), value: driver?.address?.street },
    ]);
  }

  // Get filtered countries, and add id to the country object

  const getCountryNameByCode = (code: string) => {
    return countriesDataList.all.find((country) => country.countryCallingCodes[0] === "+" + code)?.name;
  };

  // make function to update search params with driverMode either create or edit
  const updateSearchParams = (mode: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("driverMode", mode);
    return `?${params.toString()}`;
  };

  return (
    <Accordion type="single" collapsible className="w-full rounded-lg border bg-white shadow-sm" defaultValue="item-1">
      <AccordionItem value="item-1">
        <Card className="flex flex-col shadow">
          {driverMode === "edit" ? (
            <>
              <EditDriver
                driver={driver}
                countries={countries ?? []}
                title={driver.title}
                firstName={firstName}
                lastName={lastName}
                email={email}
                nationality={getCountryNameByCode(String(driver?.nationality?.code)) ?? ""}
                mobileNumber={mobileNumber}
                idType={driver.idType}
                dob={dobDate.gregorean}
                hijrahDob={dobDate.hijri}
                address={driver.address.street}
                countryCode={String(driver.countryCode)}
                documentNo={document.documentNo}
                idIssuedCountry={
                  getCountryNameByCode(String(document.issuedPlace?.code ?? driver?.nationality?.code)) ?? ""
                }
                documentExpiry={documentExpiryDate.gregorean}
                documentHijrahExpiry={documentExpiryDate.hijri}
                licenseNo={license.documentNo}
                licenseCountry={
                  getCountryNameByCode(String(license?.issuedPlace?.code ?? driver?.nationality?.code)) ?? ""
                }
                licenseExpiry={licenseExpiryDate.gregorean}
                licenseHijrahExpiry={licenseExpiryDate.hijri}
              />
            </>
          ) : (
            <>
              <AccordionTrigger className="!w-full rounded-md p-4 no-underline transition-all duration-300 hover:bg-slate-100 [&>div]:w-full ">
                <CardHeader className="flex w-full flex-row items-center justify-between gap-x-3 p-0">
                  <div className="flex items-center gap-x-3">
                    <span className="rounded-lg  bg-slate-100 p-2">
                      <User className="size-6" />
                    </span>
                    <span
                      className={`!m-0 text-lg font-bold capitalize ${firstName || lastName ? "" : "text-red-500"}`}
                    >
                      {driver.title || t("values.fillIt")} {firstName || t("values.fillIt")}{" "}
                      {lastName || t("values.fillIt")}
                    </span>
                    <Badge
                      variant="outline"
                      className="!m-0 flex items-center gap-1 bg-slate-300 font-normal text-slate-900"
                    >
                      {t("labels.mainDriver")}
                    </Badge>
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger
                      asChild
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      <span className="mr-2 flex cursor-pointer items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted">
                        <TrashSimple className="size-5" />
                      </span>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>{t("actions.removeDriver")}</AlertDialogTitle>
                        <AlertDialogDescription>{t("actions.removeDriverDescription")}</AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          {t("actions.cancel")}
                        </AlertDialogCancel>
                        <AlertDialogAction onClick={deleteHandler} className="bg-red-600 hover:bg-red-700">
                          {t("actions.remove")}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </CardHeader>
              </AccordionTrigger>
              <Separator />
              <AccordionContent>
                <section className="">
                  <div className="flex items-center justify-start gap-x-4 p-4">
                    <span className="text-base font-bold text-slate-900 ">{t("sections.driverDetails")}</span>
                    <ProgressBarLink href={updateSearchParams("edit") as Route}>
                      <p className=" flex size-8 cursor-pointer items-center justify-center  rounded-md border  border-slate-300 hover:bg-slate-100">
                        <PencilSimple className=" size-4 text-slate-900 " />
                      </p>
                    </ProgressBarLink>
                  </div>
                  {driverDetailsData?.map((details, index) => (
                    <Fragment key={index}>
                      <div className="flex flex-row p-4">
                        {details.map((detail, index) => (
                          <div key={index} className="flex w-1/2">
                            <DriverDetailTile
                              title={detail.title}
                              isDate={detail.isDate}
                              value={detail.value}
                              hijri={detail.hijri}
                              gregorean={detail.gregorean}
                            />
                          </div>
                        ))}
                      </div>
                      <Separator />
                    </Fragment>
                  ))}
                </section>
                <Separator className="h-1" />
                {/* Driving License */}
                <section>
                  <p className="p-4 pb-0 text-base font-bold text-slate-900 ">{t("sections.drivingLicense")}</p>
                  <section>
                    <div className="flex flex-row p-4 ">
                      <div className="flex w-1/3">
                        <DriverDetailTile
                          title={t("fields.country")}
                          value={getLocalizedName(
                            license?.issuedPlace?.name,
                            locale,
                            license?.issuedPlace?.name?.en ?? ""
                          )}
                        />
                      </div>
                      <div className="flex w-1/3">
                        <DriverDetailTile title={t("fields.licenseNumber")} value={license.documentNo} />
                      </div>
                      <div className="flex w-1/3">
                        <DriverDetailTile
                          title={t("fields.expiryDate")}
                          isDate
                          hijri={licenseExpiryDate.hijri}
                          gregorean={licenseExpiryDate.gregorean}
                        />
                      </div>
                    </div>
                  </section>
                </section>
              </AccordionContent>
            </>
          )}
        </Card>
      </AccordionItem>
    </Accordion>
  );
}
