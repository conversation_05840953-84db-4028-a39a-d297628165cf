import { type NavItem } from "../_components/constants";

export const NAV_ITEMS: Array<NavItem> = [
  { label: "Booking details", href: "/booking-details", completed: true, translationKey: "bookingdetails" },
  { label: "Driver details", href: "/driver-details", completed: true, translationKey: "driverdetails" },
  { label: "Assign a vehicle", href: "/assign-a-vehicle", completed: false, translationKey: "assignavehicle" },
  {
    label: "Insurance & extras",
    href: "/insurance-and-extras",
    completed: false,
    translationKey: "insuranceandextras",
  },
  { label: "Payment", href: "/payment", completed: false, translationKey: "payment" },
  { label: "Authorization", href: "/authorization", completed: false, translationKey: "authorization" },
] as const;

// SAUDI_NATIONAL, RESIDENT, GCC, VISITOR
export const ID_TYPES = ["SAUDI_NATIONAL", "RESIDENT", "GCC", "VISITOR"] as const;

export type IdType = (typeof ID_TYPES)[number];

export const IFRAME_URL =
  "https://www.absher.sa/wps/portal/individuals/Home/myservices/einquiries/passports/qbn/!ut/p/z1/pZLPb4IwFIDv_hV64Mp7lMqa3fjhAMWBM1PWi0HDkASoQTay_36d20ESw8j2Ds1r832v7WuBj8bjkQyIZSLT62HgBHiVvOdZ0uSiSgqI4YUbO4MEtuYxsmAzMsXVkjkWCSiiS2F7AdCn1NOoBDaag-Ymeribz1wdfQ34f3zEgX4nTLSeiCVtNyR_8a8rDfN7AN5ffv77BvyCTO3I9tlS18JHz0EfF44VhCGhK-MH6HuDXuCryTeO2e3igHtkhdh_fxmz2ussA16nr2md1upbLZePTXM63yuoYNu2aiZEVqTqQZQK3lKO4txA3CVhndRwKp9lxB9rP4_KLWuYOZl8Ajrkglw!/dz/d5/L0lHSkovd0RNQURrQUVnQSEhLzROVkUvZW4!/";
