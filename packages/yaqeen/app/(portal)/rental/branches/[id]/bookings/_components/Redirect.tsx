"use client";

import { useQueryStates, parseAsString } from "nuqs";
import { useEffect } from "react";

const getLastSegment = () => {
  const pathName = window.location.pathname;
  const segments = pathName.split("/");
  return segments.pop();
};

export default function Redirect({ newQuoteId }: { newQuoteId: string }) {
  const [{ quoteId, actualQuoteId }, setQueryParams] = useQueryStates({
    quoteId: parseAsString,
    actualQuoteId: parseAsString,
  });

  useEffect(() => {
    if (quoteId !== newQuoteId) {
      const lastSegment = getLastSegment();
      if (lastSegment === "assign-a-vehicle" && !actualQuoteId) {
        void setQueryParams({ actualQuoteId: newQuoteId, quoteId: newQuoteId }, { shallow: true });
      } else {
        void setQueryParams({ quoteId: newQuoteId }, { shallow: true });
      }
    }
  }, [newQuoteId, quoteId, actualQuoteId, setQueryParams]);

  return null;
}
