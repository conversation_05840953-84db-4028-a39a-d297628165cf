"use client";

import { type Row, type ColumnDef } from "@tanstack/react-table";

import { DataTableRowActions } from "@/components/ui/data-table/data-table-row-actions";
import { CaretUpDown, CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";
import { amountFormatter, convertPlateToArabic, formattedPickupTime } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import TooltipComponent from "@/components/tooltip-component";
import { type Booking } from "../../_components/types";
import { AgreementButton } from "../../_components/AgreementButton";
import { getColor } from "../../_components/constants";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";

const LocalizeText = ({ message }: { message: string }) => {
  const t = useTranslations("bookings.columns");
  // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'message' implicitly has an 'any' type.
  return <div>{t(message)}</div>;
};

export const columns: ColumnDef<Booking>[] = [
  {
    id: "id",
    accessorKey: "id",
    enableHiding: true,
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => <LocalizeText message="bookingNo" />,
    cell: ({ row }) => {
      return row.getValue<Booking["bookingNo"]>("bookingNo");
    },
  },
  {
    accessorKey: "pickupDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="pickupTime" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <FormattedPickupTime row={row} />;
    },
  },
  {
    accessorKey: "driver",
    header: () => <LocalizeText message="driver" />,
    cell: ({ row }) => {
      return (
        <span className="flex items-center gap-x-1">
          <span className=" text-blue-600">
            {row.getValue<Booking["driver"]>("driver")?.firstName} {row.getValue<Booking["driver"]>("driver")?.lastName}
          </span>
          <TooltipComponent content="Yaqeen verified">
            <CheckCircle weight="fill" className="size-4 fill-green-600" />
          </TooltipComponent>
        </span>
      );
    },
  },
  {
    accessorKey: "preferredVehicleGroup",
    enableHiding: true,
  },
  {
    accessorKey: "assignedVehicle",
    header: () => <LocalizeText message="vehicle" />,
    cell: ({ row }) => {
      const assigned = row.getValue<Booking["assignedVehicle"]>("assignedVehicle");
      const preferred = row.getValue<Booking["preferredVehicleGroup"]>("preferredVehicleGroup");

      const vehicle = assigned || preferred;

      const plateNo = vehicle?.plateNo || "";
      const [plateNumber = "", plateLetters = ""] = plateNo.split(" ");
      const arabicLetters = convertPlateToArabic(plateLetters.split("").reverse().join(" "));
      const arabicNumber = plateNumber.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[+d] ?? d);
      const arabicPlateNo = `${arabicLetters} ${arabicNumber}`;

      return (
        <div className="flex flex-col">
          <TranslatedText>
            {(_t, locale) => (
              <>
                <span className="font-medium text-slate-900">
                  {vehicle?.model?.make?.name?.[locale]} {vehicle?.model?.name?.[locale]}
                </span>
                <span>{locale === "ar" ? arabicPlateNo : plateNo}</span>
              </>
            )}
          </TranslatedText>
        </div>
      );
    },
  },
  {
    accessorKey: "group",
    header: () => <LocalizeText message="group" />,
    cell: ({ row }) => {
      return (
        <p>
          {row.getValue<Booking["preferredVehicleGroup"]>("preferredVehicleGroup")?.vehicleGroupCode ??
            row.getValue<Booking["assignedVehicle"]>("assignedVehicle")?.carGroup ??
            "N/A"}
        </p>
      );
    },
  },
  {
    accessorKey: "source",
    header: () => <LocalizeText message="source" />,
    cell: ({ row }) => {
      return row.getValue<Booking["source"]>("source") ?? "N/A";
    },
  },
  {
    accessorKey: "paymentStatus",
    enableHiding: true,
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="total" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t) => (
            <span className="flex items-center gap-x-1">
              {amountFormatter(Number(row.getValue<Booking["totalPrice"]>("totalPrice")))}
              {row.getValue<Booking["paymentStatus"]>("paymentStatus")?.toLowerCase() === "paid" ? (
                <TooltipComponent content={t("Paid")}>
                  <CheckCircle weight="fill" className=" size-4 fill-green-600" />
                </TooltipComponent>
              ) : (
                <TooltipComponent content={t("Unpaid")}>
                  <XCircle weight="fill" className="size-4 fill-slate-400" />
                </TooltipComponent>
              )}
            </span>
          )}
        </TranslatedText>
      );
    },
  },
  {
    accessorKey: "flightNo",
    header: () => <LocalizeText message="flight" />,
    cell: ({ row }) => {
      return row.getValue<Booking["flightNo"]>("flightNo") ?? "N/A";
    },
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <DataTableRowActions row={row}>
        <AgreementButton buttonTitle={"startAgreement"} rowId={row.getValue("id")}>
          <LocalizeText message={"actions.startAgreement"} />
        </AgreementButton>
      </DataTableRowActions>
    ),
  },
];

const FormattedPickupTime = ({ row }: { row: Row<Booking> }) => {
  const locale = useLocale();
  const formatTime = formattedPickupTime(
    row.getValue<Booking["pickupDateTime"]>("pickupDateTime") ?? 0,
    "UPCOMING",
    locale === "ar" ? arSA : enUS
  );
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
      <span className={getColor(formatTime.colorClass)}>{formatTime.displayText}</span>
    </div>
  );
};

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: "en" | "ar") => React.ReactNode;
}) => {
  const t = useTranslations("bookings.columns");
  const locale = useLocale() as "en" | "ar";
  return <>{children(t, locale)}</>;
};
