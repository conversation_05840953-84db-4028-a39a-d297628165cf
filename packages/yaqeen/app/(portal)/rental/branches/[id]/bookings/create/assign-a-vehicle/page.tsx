import { Suspense } from "react";
import { api } from "@/api";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { WALK_IN_NAV_ITEMS } from "../constants";
import PricingBreakdown from "../_components/pricing-breakdown";
import AvailableVehicles from "./_components/available-vehicles";
import { type Vehicle } from "../../[bookingId]/assign-a-vehicle/_components/vehicle-card";
import { getPricingServerInfo, transformSingleVehicle } from "../utils";
import SidesheetWrapper from "../../[bookingId]/_components/sidesheet-wrapper";

interface VehicleModelData {
  plateNo: string;
  plateNoAr?: string;
  modelId?: number;
  name: { en: string; ar: string };
  make?: { id: number; name: { en: string; ar: string } };
  primaryImageUrl?: string;
  odometerReading?: number;
  fuelLevel?: number;
  color?: string;
  modelYear?: number;
  lastInspected?: number;
  specifications?: {
    series?: string;
    sunRoof?: boolean;
    towHook?: boolean;
    version?: string;
    absBrake?: boolean;
    cdPlayer?: boolean;
    fuelType?: string;
    alloyWheel?: boolean;
    engineSize?: string;
    automaticAC?: boolean;
    centralLock?: boolean;
    cruiseControl?: boolean;
    powerSteering?: boolean;
    splitBackSeat?: boolean;
    fuelTankVolume?: string;
    seatingCapacity?: string;
    electronicMirror?: boolean;
    electronicAntenna?: boolean;
    heightAdjustmentSeat?: boolean;
    centralLockWithRemote?: boolean;
    modelVersion?: string;
  };
}

interface VehicleGroupData {
  vehicleModels: VehicleModelData[];
  groupResponse?: {
    id: number;
    code: string;
    displayName?: string;
    description?: { en?: string; ar?: string };
  };
  tariffRates?: {
    dailyPrice: number;
    weeklyPrice: number;
    monthlyPrice: number;
  };
  dailyPrice: number;
}

const transformToSuggestedVehicle = (data: VehicleGroupData[]): Vehicle[] => {
  return data.flatMap((item) =>
    item.vehicleModels.map(
      (vehicle): Vehicle => ({
        plateNo: vehicle.plateNo,
        plateNoAr: vehicle.plateNoAr || "",
        lastInspected: vehicle.lastInspected,
        model: vehicle.modelId
          ? {
              id: vehicle.modelId,
              name: { en: vehicle.name.en || "", ar: vehicle.name.ar || "" },
              make: vehicle.make
                ? {
                    id: vehicle.make.id,
                    name: { en: vehicle.make.name.en || "", ar: vehicle.make.name.ar || "" },
                  }
                : undefined,
              modelVersion: vehicle.specifications?.modelVersion || "2024", // Default to 2024 if not provided
              version: vehicle.specifications?.version || "",
              modelSeries: vehicle.specifications?.series || "1",
              enabled: true, // Assuming all models are enabled
              primaryImageUrl: vehicle.primaryImageUrl,
              groupResponse: item.groupResponse,
              specifications: {
                series: vehicle.specifications?.series || "",
                sunRoof: vehicle.specifications?.sunRoof || false,
                towHook: vehicle.specifications?.towHook || false,
                version: vehicle.specifications?.version || "",
                absBrake: vehicle.specifications?.absBrake || false,
                cdPlayer: vehicle.specifications?.cdPlayer || false,
                fuelType: vehicle.specifications?.fuelType || "",
                alloyWheel: vehicle.specifications?.alloyWheel || false,
                engineSize: vehicle.specifications?.engineSize || "",
                automaticAC: vehicle.specifications?.automaticAC || false,
                centralLock: vehicle.specifications?.centralLock || false,
                cruiseControl: vehicle.specifications?.cruiseControl || false,
                powerSteering: vehicle.specifications?.powerSteering || false,
                splitBackSeat: vehicle.specifications?.splitBackSeat || false,
                fuelTankVolume: vehicle.specifications?.fuelTankVolume || "",
                seatingCapacity: vehicle.specifications?.seatingCapacity || "",
                electronicMirror: vehicle.specifications?.electronicMirror || false,
                electronicAntenna: vehicle.specifications?.electronicAntenna || false,
                heightAdjustmentSeat: vehicle.specifications?.heightAdjustmentSeat || false,
                centralLockWithRemote: vehicle.specifications?.centralLockWithRemote || false,
              },
            }
          : undefined, // Type assertion to avoid TS error with possible undefined model
        odometerReading: vehicle.odometerReading || 0,
        fuelLevel: vehicle.fuelLevel || 0,
        color: vehicle.color || "",
        modelYear: vehicle.modelYear || 0,
        preferenceType: "NONE",
        // offer: item?.tariffRates?.dailyPrice ?? "", // Add this to match the Vehicle interface
        offer: {
          bookingPriceDifference: item?.tariffRates?.dailyPrice ?? 0,
        },
        dailyPrice: item?.tariffRates?.dailyPrice ?? 0,
      })
    )
  );
};

export default async function VehicleSelection({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string; id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _params = await params;
  const _searchParams = await searchParams;
  const vehiclePrice = _searchParams?.vehiclePrice as string;
  const driverUId = _searchParams?.driverUid as string;

  const quoteId: string | undefined = _searchParams.quoteId as string | undefined;
  const PRICING_BREAKDOWN_KEY = Object.entries(_params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const plateNo = (_searchParams?.plateNo as string) ?? "";

  const [vehiclesResponse] = await Promise.all([
    api.booking.getBranchAvailableVehicles({
      query: {
        branchId: Number(_params.id),
      },
    }),
  ]);

  if (vehiclesResponse.status !== 200) {
    throw new Error("Failed to fetch available vehicles");
  }

  let preSelectedVehicle: Vehicle | null = null;
  if (plateNo) {
    const selectedVehicleResp =
      (await api.availability.getVehicleDetailsV3({
        query: {
          plateNo: plateNo,
          requireOpsData: true,
        },
      })) ?? null;

    if (selectedVehicleResp?.status === 200 && selectedVehicleResp.body) {
      // Transform the selected vehicle to match the Vehicle interface format

      preSelectedVehicle = transformSingleVehicle({
        ...selectedVehicleResp.body,
        tariffRates: { dailyPrice: Number(vehiclePrice) },
      });
    }
  }

  // @ts-expect-error TODO: fix this
  const availableVehicles = transformToSuggestedVehicle(vehiclesResponse.body as VehicleGroupData[]);

  // Fetch branch information
  const { branchesResponse, quoteResponse } = await getPricingServerInfo({ quoteId });

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <AvailableVehicles vehicles={availableVehicles} preSelectedVehicle={preSelectedVehicle} />
        <ActionsBar
          bookingNo="1234"
          className="w-full"
          navItemsArray={WALK_IN_NAV_ITEMS}
          successCtaDisabled={!plateNo}
        />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown
            branchesResponse={branchesResponse.body.data}
            quoteResponse={quoteResponse}
            quoteId={quoteId}
            _searchParams={_searchParams}
          >
            {driverUId && <SidesheetWrapper driverUId={driverUId ?? ""} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
