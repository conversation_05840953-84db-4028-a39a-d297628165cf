"use client";
import React, { startTransition, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { type z } from "zod";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { type AddonSchema } from "@/api/contracts/booking/schema";
import { useLocale, useTranslations } from "next-intl";

type Addons = z.infer<typeof AddonSchema>[] | [];

export default function Extras({
  _addons,
  unlimitedKm,
  preSelectedKms,
}: {
  _addons: Addons;
  unlimitedKm?: z.infer<typeof AddonSchema>;
  preSelectedKms: number;
}) {
  const t = useTranslations("extras");
  const locale = useLocale() as "en" | "ar";
  const [extras, setExtras] = useState<Addons>(_addons.filter((addon) => addon.code !== "UNLIMITED_KM"));
  const [isUnlimitedKmInAddOn, setIsUnlimitedKmInAddOn] = useState(
    !!_addons.find((addon) => addon.code === "UNLIMITED_KM" && addon.checked)
  );

  const progress = useProgressBar();
  const [addonParams, setAddonParms] = useQueryState("addOns", {
    shallow: false,
  });

  const handleCheckboxChange = (addonId: number) => {
    const updatedExtras = extras.map((addon) => {
      if (addon.id === addonId) {
        return {
          ...addon,
          checked: !addon.checked,
        };
      }
      return addon;
    });
    setExtras(updatedExtras);

    const checkedIds = updatedExtras.filter((addon) => addon.checked).map((addon) => addon.id);

    if (isUnlimitedKmInAddOn && unlimitedKm) {
      checkedIds.push(unlimitedKm.id);
    }

    progress.start();
    startTransition(() => {
      void setAddonParms(checkedIds.join(","));
      progress.done();
    });
  };

  const handleKmChange = (value: string) => {
    progress.start();
    startTransition(() => {
      if (value === "0") {
        if (addonParams && unlimitedKm) {
          const updatedParams = addonParams.split(",").filter((id) => id !== String(unlimitedKm.id));
          void setAddonParms(updatedParams.join(","));
        } else {
          const checkedIds: number[] = extras.filter((addon) => addon.checked).map((addon) => addon.id);
          if (checkedIds?.length) {
            void setAddonParms(checkedIds.join(","));
          } else void setAddonParms("");
        }
        setIsUnlimitedKmInAddOn(false);
      } else {
        if (addonParams && unlimitedKm) {
          void setAddonParms(addonParams + "," + unlimitedKm.id);
        } else {
          const checkedIds = extras.filter((addon) => addon.checked).map((addon) => addon.id);
          if (unlimitedKm?.id) checkedIds.push(unlimitedKm.id);
          void setAddonParms(checkedIds.join(","));
        }
        setIsUnlimitedKmInAddOn(true);
      }
      progress.done();
    });
  };

  return (
    <>
      <Card className="flex flex-col shadow">
        <CardHeader className="flex w-full flex-row justify-between p-4 ">
          <CardTitle className="text-lg font-bold">{t("kmsAllowance.title")}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="flex w-full p-0 max-md:flex-wrap ">
          <div className="flex w-full flex-col text-sm font-medium" dir={locale === "ar" ? "rtl" : "ltr"}>
            <RadioGroup
              defaultValue={isUnlimitedKmInAddOn ? String(unlimitedKm?.id) : "0"}
              onValueChange={(e) => handleKmChange(e)}
            >
              <div className="flex items-start gap-2 p-4 " dir={locale === "ar" ? "rtl" : "ltr"}>
                <RadioGroupItem value={String(0)} id="r1" className="mt-1 gap-x-2 text-blue-600" />
                <Label htmlFor="KM" className="flex w-full flex-row items-end justify-between">
                  <p className="text-base font-medium">{t("kmsAllowance.standardKm", { km: preSelectedKms ?? 0 })}</p>
                  <p className="text-base font-medium">{t("pricing.free")}</p>
                </Label>
              </div>
              {unlimitedKm ? (
                <>
                  <Separator />
                  <div className="flex items-start gap-2 p-4 " dir={locale === "ar" ? "rtl" : "ltr"}>
                    <RadioGroupItem value={String(unlimitedKm.id)} id="r1" className="mt-1 gap-x-2 text-blue-600" />
                    <Label htmlFor={String(unlimitedKm.id)} className="flex w-full flex-row items-end justify-between">
                      <p className="text-base font-medium">{t("kmsAllowance.unlimitedKm")}</p>
                      <p className="text-base font-medium">
                        {unlimitedKm?.price
                          ? t("pricing.addOn", {
                              currency: locale === "ar" ? "ريال" : "SAR",
                              amount: unlimitedKm?.price,
                            })
                          : t("pricing.free")}
                      </p>
                    </Label>
                  </div>
                </>
              ) : (
                <></>
              )}
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
      <Card className="flex flex-col shadow">
        <CardHeader className="flex w-full flex-row justify-between p-4 ">
          <CardTitle className="text-lg font-bold">{t("addOns.title")}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="flex w-full p-0 max-md:flex-wrap ">
          <div className="flex w-full flex-col text-sm font-medium">
            {extras?.length ? (
              extras.map((addon) => (
                <div className="flex items-start gap-2 p-4" key={addon.id} dir={locale === "ar" ? "rtl" : "ltr"}>
                  <Checkbox
                    id={String(addon.id)}
                    checked={addon.checked}
                    value={addon.id}
                    name="addon"
                    className="mt-1 data-[state=checked]:border-blue-600 data-[state=checked]:bg-blue-600"
                    onCheckedChange={() => handleCheckboxChange(addon.id)}
                  />
                  <Label htmlFor={String(addon.id)} className="flex w-full flex-row items-end justify-between">
                    <p className="text-base font-medium">{addon.name[locale]}</p>
                    <p className="text-base font-medium">
                      {addon.price
                        ? t("pricing.addOn", {
                            currency: locale === "ar" ? "ريال" : "SAR",
                            amount: Number(addon.price).toFixed(2),
                          })
                        : t("pricing.free")}
                    </p>
                  </Label>
                </div>
              ))
            ) : (
              <p className="p-6 text-center text-slate-900">{t("addOns.noExtras")}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </>
  );
}
