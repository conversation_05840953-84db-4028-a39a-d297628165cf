"use client";

import { useState, useEffect, useMemo } from "react";
import { User, Plus, X, Loader2 } from "lucide-react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type Driver } from "@/api/contracts/booking/driver-details-contract";
import { searchDrivers } from "@/lib/actions/agreement-actions";
import debounce from "lodash/debounce";
import { Card } from "@/components/ui/card";
import { useAtom } from "jotai";
import { selectedDriverAtom } from "../../atoms";
import { capitalize } from "lodash-es";
import { useTranslations } from "next-intl";
import { ID_TYPES_DROPDOWN, type ID_TYPES_DROPDOWN_TYPE } from "../../../_components/constants";

interface DriverSearchProps {
  onSelectDriver?: (driver: Driver) => void;
  onCreateNew?: () => void;
  className?: string;
  drivers: Driver[];
}

export function DriverSearch({ onSelectDriver, onCreateNew, className, drivers }: DriverSearchProps) {
  // Get translations
  const t = useTranslations("drivers");

  const [search, setSearch] = useState("");
  const [filteredDrivers, setFilteredDrivers] = useState<Driver[]>(drivers);
  const [isLoading, setIsLoading] = useState(false);
  const [, setSelectedDriver] = useAtom(selectedDriverAtom);

  // Initial load from provided drivers
  useEffect(() => {
    setFilteredDrivers(drivers);
  }, [drivers]);

  // Create a debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchString: string) => {
        if (!searchString.trim()) {
          setFilteredDrivers(drivers);
          setIsLoading(false);
          return;
        }

        try {
          const response = await searchDrivers(searchString);
          if (response.status === 200) {
            setFilteredDrivers(response.body.data);
          }
        } catch (error) {
          console.error("Error searching drivers:", error);
        } finally {
          setIsLoading(false);
        }
      }, 300),
    [drivers]
  );

  const selectDriverHandler = (driver: Driver) => {
    setSelectedDriver(driver);
    if (onSelectDriver) onSelectDriver(driver);
  };

  // Handle search input
  const handleSearch = async (value: string) => {
    setSearch(value);

    if (!value.trim()) {
      setFilteredDrivers(drivers);
      return;
    }

    setIsLoading(true);
    await debouncedSearch(value);
  };

  // Clean up the debounce on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // Clear search and reset state
  const clearSearch = () => {
    setSearch("");
    setFilteredDrivers(drivers);
  };

  // Get localized ID type label
  const getLocalizedIdType = (idType: string | undefined) => {
    if (!idType) return t("values.na");

    // Find the matching ID type in the dropdown
    const matchingType = ID_TYPES_DROPDOWN.find((type) => type.value === idType);

    if (matchingType) {
      // If there's a translation key available, use it
      if (matchingType.translationKey) {
        return t(`idTypes.${matchingType.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
          defaultValue: matchingType.label,
        });
      }
      return matchingType.label;
    }

    // Fallback to capitalized ID type if no match found
    return capitalize(idType.split("_").join(" "));
  };

  return (
    <Card className="rounded-lg border bg-white p-4 shadow-sm">
      <div className="mb-3 flex max-w-3xl items-center gap-3">
        <div className="flex h-12 w-12 items-center justify-center rounded-md bg-slate-100">
          <User className="h-6 w-6 text-slate-500" />
        </div>
        <h2 className="text-xl font-semibold">{t("labels.mainDriver")}</h2>
      </div>

      <div className={cn("relative h-[30rem] w-full rounded-lg bg-slate-100 p-4", className)}>
        <div className="mb-2">
          <h3 className="text-lg font-semibold">{t("search.idNumberTitle")}</h3>
          <p className="text-sm text-muted-foreground">{t("search.idNumberDescription")}</p>
        </div>

        <div className="relative w-full">
          <Command className="w-full rounded-lg border shadow-md">
            <div className="flex w-full items-center border-b px-3 [&>div]:w-full">
              <CommandInput
                placeholder={t("search.placeholder")}
                value={search}
                onValueChange={handleSearch}
                className="mx-2 flex h-11 w-full flex-1 rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
              {search && (
                <Button variant="ghost" size="sm" className="h-5 w-5 rounded-full p-0" onClick={clearSearch}>
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {true && (
              <div
                className="absolute left-0 right-0 top-full z-50 rounded-b-lg border border-t-0 border-gray-200 bg-white shadow-md"
                style={{ marginTop: 0, maxHeight: "calc(100vh - 300px)" }}
              >
                <CommandList className="h-full max-h-[400px] overflow-y-auto py-2">
                  <CommandGroup>
                    <CommandItem
                      onSelect={() => {
                        if (onCreateNew) onCreateNew();
                        clearSearch();
                      }}
                      className="flex cursor-pointer items-center gap-x-2 py-3 text-blue-600 hover:bg-primary/5"
                    >
                      <Plus className="mr-2 h-4 w-4 text-blue-600" />
                      <span className="text-blue-600">{t("actions.createNewDriver")}</span>
                    </CommandItem>
                  </CommandGroup>

                  {isLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                      <span className="ml-2 text-sm text-gray-500">{t("search.searching")}</span>
                    </div>
                  ) : filteredDrivers.length > 0 ? (
                    <div className="h-[270px] p-1">
                      {filteredDrivers.map((driver) => {
                        const documentOrLicenseNumber =
                          (driver.documents || []).find((doc) => doc.type === "LICENSE")?.documentNo ||
                          (driver.documents || []).find((doc) => doc.type === "ID" || doc.type === "NATIONAL_ID")
                            ?.documentNo ||
                          t("values.na");

                        // Get localized driver residence type
                        const driverResidenceType = getLocalizedIdType(driver?.idType);

                        return (
                          <div
                            key={driver.driverUId || driver.id}
                            onClick={() => {
                              if (onSelectDriver) {
                                selectDriverHandler(driver);
                              }
                              clearSearch();
                            }}
                            className="flex cursor-pointer items-center justify-between rounded-sm p-2 transition-all duration-300 hover:bg-slate-100"
                          >
                            <div className="flex w-full items-center gap-4">
                              <div className="grid w-full grid-cols-3 gap-2 text-slate-600">
                                <p className="col-1 truncate">{documentOrLicenseNumber}</p>
                                <p className="col-1 truncate capitalize">
                                  {driver?.firstName || t("values.na")} {driver?.lastName || ""}
                                </p>
                                <p className="col-1 truncate">{driverResidenceType}</p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div>
                      <CommandEmpty className="w-full py-2 text-center">{t("search.noResults")}</CommandEmpty>
                      <div className="flex justify-center p-4">
                        <Button
                          onClick={() => {
                            if (onCreateNew) onCreateNew();
                            clearSearch();
                          }}
                          variant="outline"
                          className="flex w-full items-center gap-2 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                        >
                          <Plus className="h-4 w-4" />
                          {t("actions.createNewDriver")}
                        </Button>
                      </div>
                    </div>
                  )}
                </CommandList>
              </div>
            )}
          </Command>
        </div>
      </div>
    </Card>
  );
}
