import { Skeleton } from "@/components/ui/skeleton";

export default function BookingHeaderSkeleton() {
  return (
    <div className="space-y-6">
      {/* Breadcrumb skeleton */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Skeleton className="h-4 w-12" />
        <span>/</span>
        <Skeleton className="h-4 w-24" />
        <span>/</span>
        <Skeleton className="h-4 w-32" />
      </div>

      {/* Heading skeleton */}
      <div className="space-y-4">
        <Skeleton className="h-9 w-64" />

        {/* Booking reference and tags */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-6 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
        </div>
      </div>

      {/* Navigation tabs skeleton */}
      <div className="flex border-b">
        {Array(6)
          .fill(null)
          .map((_, i) => (
            <div key={i} className="relative px-4">
              <Skeleton className="h-5 w-28" />
              {i === 0 && <div className="absolute bottom-0 left-0 right-0 h-0.5 " />}
            </div>
          ))}
      </div>
    </div>
  );
}
