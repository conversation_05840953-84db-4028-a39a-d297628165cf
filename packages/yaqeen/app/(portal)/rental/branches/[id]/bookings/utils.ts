import { type Vehicle } from "./[bookingId]/assign-a-vehicle/_components/vehicle-card";

export const buildGroupsFilters = (data: Vehicle[]): { groups: Array<{ label: string; value: string | number }> } => {
  let groupMap: Record<string, null> = {};
  return data.reduce<{ groups: Array<{ label: string; value: string | number }> }>(
    (acc, { model }) => {
      if (model?.groupResponse && !(model.groupResponse.id in groupMap)) {
        acc.groups.push({
          value: model.groupResponse.id,
          label: `${model.groupResponse.code} - ${model.groupResponse.description?.en ?? ""}`,
        });
        groupMap = { ...groupMap, [model.groupResponse.id]: null };
      }
      return acc;
    },
    { groups: [] }
  );
};
