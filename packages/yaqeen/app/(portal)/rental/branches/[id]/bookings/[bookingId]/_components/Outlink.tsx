'use client';

import { useParams } from "next/navigation";
import OpenLink from "@/components/customized/open-link";

const OpenLinkW = ({ booking }: { booking : { id: number }}) => {
 const params = useParams();
 const { id: branchId } = params;
 return <OpenLink
 url={`/rental/branches/${branchId as string}/bookings/${booking.id}/booking-details`}
 className="flex items-center gap-2"
/>
}

export default OpenLinkW;