import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton"
import ActionsBarSkeleton from "../../_components/skeletons/actions-bar-skeleton"

export default function Loading() {
  return (
    <section className="flex flex-row gap-x-6">
      <div className="flex flex-col space-y-6 w-[768px]">
        <Card className="w-full ">
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-7 w-40" />
            </CardTitle>
          </CardHeader>
          <Separator className="my-2" />
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              {/* Pick-up Section */}
              <div className="space-y-4">
                <Skeleton className="h-6 w-20" />
                <div className="space-y-3">
                  <Skeleton className="h-10 w-full rounded-lg" />
                  <div className="grid grid-cols-2 gap-3">
                    <Skeleton className="h-10 w-full rounded-lg" />
                    <Skeleton className="h-10 w-full rounded-lg" />
                  </div>
                </div>
              </div>

              {/* Drop-off Section */}
              <div className="space-y-4">
                <Skeleton className="h-6 w-20" />
                <div className="space-y-3">
                  <Skeleton className="h-10 w-full rounded-lg" />
                  <div className="grid grid-cols-2 gap-3">
                    <Skeleton className="h-10 w-full rounded-lg" />
                    <Skeleton className="h-10 w-full rounded-lg" />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <ActionsBarSkeleton />
      </div>
      <PricingBreakdownSkeleton />
    </section>
  )
}

