import React, { Suspense } from "react";
import { api } from "@/api";
import { type SearchParams } from "nuqs/server";
import type { IBranch } from "@/api/contracts/branch-contract";
import { bookingSearchParamsCache } from "@/lib/params";
import { getCurrentTimeMinusNHours, getUpcomingTimeRange } from "@/lib/utils";
import { QuickFilters } from "../_components/QuickFilters";
import Header from "../_components/Header";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./components/columns";
import { filters } from "./components/constants";
import { searchFilters } from "../_components/constants";
import Loading from "@/app/(portal)/loading";
import { getLocale, getTranslations } from "next-intl/server";

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<Record<string, string>>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const { id } = await props.params;
  const {
    sort,
    order,
    pageSize,
    pageNumber,
    bookingNo,
    mobileNumber,
    driverName,
    source,
    pickupDateRangeStart,
    paymentStatusFilter,
  } = bookingSearchParamsCache.parse(searchParams);

  const locale = (await getLocale()) as "en" | "ar";
  const t = await getTranslations("bookings.filters");
  const bookingsT = await getTranslations("bookings");

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse.find((branch) => branch.id === Number(id));

  const STATUS = "UPCOMING";
  const suspenseKey = Object.entries(searchParams)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${Array.isArray(value) ? value.join(",") : (value ?? "")}`)
    .join("-");

  const startEpoch = getCurrentTimeMinusNHours(2);
  const { start, end } = getUpcomingTimeRange(pickupDateRangeStart);

  const [getUpcomingBookingsCount, bookings] = await Promise.all([
    api.booking.getUpcomingBookingsCount({
      query: {
        pickupBranchId: Number(id),
        "pickupDateRange.start": startEpoch,
      },
    }),
    api.booking.getUpcomingBookings({
      query: {
        pickupBranchId: Number(id),
        "pickupDateRange.start": start,
        ...(end && { "pickupDateRange.end": end }),
        order: order,
        sort: sort,
        page: pageNumber,
        size: pageSize,
        bookingNo: bookingNo,
        mobileNumber: mobileNumber,
        driverName: driverName,
        status: STATUS,
        paymentStatusFilter: paymentStatusFilter,
        source,
      },
    }),
  ]);

  if (bookings?.status !== 200) {
    throw new Error(`Error: ${bookings.status}`);
  }
  if (getUpcomingBookingsCount?.status !== 200) {
    throw new Error(`Error: ${getUpcomingBookingsCount.status}`);
  }

  const data = bookings.body.data ?? [];
  const total = bookings.body.total ?? 0;
  const bookingsCount = getUpcomingBookingsCount.body;

  const quickFilters = [
    {
      label: "Upcoming in 2 hours",
      query: `?pickupDateRangeStart=NEXT_2_HOURS`,
      count: bookingsCount.nextTwoHourCount,
      translationKey: "next2Hours",
    },
    {
      label: "Upcoming today",
      query: `?pickupDateRangeStart=TODAY`,
      count: bookingsCount.todayUpcomingCount,
      translationKey: "today",
    },
    {
      label: "Paid bookings",
      query: `?paymentStatusFilter=PAID`,
      count: bookingsCount.paidCount,
      translationKey: "paid",
    },
  ];
  return (
    <Suspense key={suspenseKey} fallback={<Loading />}>
      <Header
        branchName={branch?.name?.[locale] ?? "N/A"}
        activeTab={{
          label: "Upcoming",
          count: 0,
        }}
      />
      <QuickFilters filters={quickFilters} />
      <div className="flex flex-col px-6">
        <DataTable
          searchPlaceholder={bookingsT("searchPlaceholder")}
          columns={columns}
          filters={filters}
          searchFilters={searchFilters}
          countText={``}
          rowClickId={"id"}
          baseRedirectPath={`/rental/branches/${id}/bookings/`}
          data={{
            data: data,
            total: total,
          }}
          emptyMessage="There are no upcoming bookings."
        />
      </div>
    </Suspense>
  );
}
