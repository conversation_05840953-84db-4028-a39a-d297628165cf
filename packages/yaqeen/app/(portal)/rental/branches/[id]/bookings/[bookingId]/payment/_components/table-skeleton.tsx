import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function TableSkeleton() {
  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-8 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Table Headers */}
        <div className="grid grid-cols-[1fr,1.5fr,1fr,1fr,40px] gap-6 border-b py-3 text-sm text-muted-foreground">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-5 w-20" />
        </div>

        {/* Table Rows */}
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="grid grid-cols-[1fr,1.5fr,1fr,1fr,40px] gap-6 border-b py-4 last:border-0">
            <Skeleton className="h-6 w-20" />
            <div className="space-y-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <Skeleton className="h-6 w-28" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
