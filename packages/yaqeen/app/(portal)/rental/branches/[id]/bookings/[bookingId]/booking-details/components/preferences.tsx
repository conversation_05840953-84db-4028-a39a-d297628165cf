"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Star } from "@phosphor-icons/react/dist/ssr";
import Image from "next/image";
import { type Booking } from "@/api/contracts/booking/schema";
import { useLocale, useTranslations } from "next-intl";

export default function Preferences({ booking }: { booking: Booking }) {
  const t = useTranslations("createAgreement");
  const locale = useLocale() as "en" | "ar";
  const faceModel = booking.preferredVehicle.vehicleGroup?.faceModelResponse;

  const make = faceModel?.make?.name?.[locale];
  const modal = faceModel?.name?.[locale];
  const group = booking.preferredVehicle.vehicleGroup?.code;
  return (
    <div className="flex w-full flex-col border-l bg-slate-50">
      <div className="flex flex-col p-4">
        <p className="flex w-full flex-col justify-center">
          <span className="text-base font-bold text-slate-900">{t("bookingDetails.preferences")}</span>
          <span className="mt-1 text-sm ">{t("bookingDetails.preferencesDesc")}</span>
        </p>
        <div className="flex w-full flex-col gap-y-3 py-4 text-sm font-medium text-slate-900 file:pt-4">
          <p className="flex gap-x-1.5 ">
            <Car className="size-5" />
            {modal || make || group ? (
              <span>
                {make ?? ""} {modal ?? ""} ({t("bookingDetails.group")} {group} )
              </span>
            ) : (
              "N/A"
            )}
          </p>
          <p className="flex gap-x-1.5 ">
            <ShieldCheck className="size-5" />
            {booking?.priceDetail?.insurances
              ? booking.priceDetail.insurances.map((insurance) => (
                  <span key={insurance.id}>{insurance.name?.[locale as keyof typeof insurance.name] ?? "N/A"}</span>
                ))
              : "N/A"}
          </p>

          {/* Addons */}
          {booking.priceDetail?.addOns?.length ? (
            booking.priceDetail.addOns.map((addon) => (
              <p key={addon.id} className="flex gap-x-1.5 ">
                {addon?.imageUrl ? (
                  <Image
                    src={addon.imageUrl}
                    alt={addon.name?.[locale as keyof typeof addon.name] ?? ""}
                    width={20}
                    height={20}
                  />
                ) : (
                  <Star className="size-5" />
                )}
                <span> {addon.name?.[locale as keyof typeof addon.name] ?? "N/A"}</span>
              </p>
            ))
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
}
