import { Suspense } from "react";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { WALK_IN_NAV_ITEMS } from "../constants";
import PricingBreakdown from "../_components/pricing-breakdown";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import DetailsCard from "./_components/details-card";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { getPricingServerInfo } from "../utils";
import SidesheetWrapper from "../../[bookingId]/_components/sidesheet-wrapper";

export default async function BookingDetails({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // Get translations
  const t = await getTranslations("bookings");
  // Check if all required params for booking-details are present

  const _params = await params;
  const _searchParams = await searchParams;
  const driverUId = _searchParams?.driverUid as string;

  // Generate suspense key for pricing breakdown
  const suspenseKey = Object.entries(_params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  // Get current time rounded to the next 30-minute increment
  const currentDate = new Date();
  const now = Math.floor(currentDate.getTime() / 1000);

  // Parse existing search parameters with defaults
  const pickupBranchId = String(_params?.id);
  const pickupTimestamp = Number(_searchParams.pickupTimestamp ?? now);
  const dropOffTimestamp = _searchParams.dropOffTimestamp ? Number(_searchParams.dropOffTimestamp) : undefined;
  const dropOffBranchId = String(_searchParams.dropOffBranchId ?? _params?.id);

  console.log("Raw timestamps:", pickupTimestamp, dropOffTimestamp);

  // If essential parameters are missing, redirect with default parameters
  if (!_searchParams.pickupTimestamp || !_searchParams.pickupBranchId) {
    const defaultSearchParams = new URLSearchParams();

    // Add required parameters
    defaultSearchParams.set("pickupBranchId", pickupBranchId);
    defaultSearchParams.set("pickupTimestamp", pickupTimestamp.toString());
    defaultSearchParams.set("dropOffBranchId", dropOffBranchId);
    // Only include dropOffTimestamp if it exists in the original search params
    if (_searchParams.dropOffTimestamp) {
      defaultSearchParams.set("dropOffTimestamp", String(_searchParams.dropOffTimestamp));
    }

    // Add all other existing search parameters
    Object.entries(_searchParams).forEach(([key, value]) => {
      if (value !== undefined && !defaultSearchParams.has(key)) {
        defaultSearchParams.set(key, String(value));
      }
    });

    redirect(`?${defaultSearchParams.toString()}`);
  }

  /**
   * Check if all required parameters are available for price calculation
   */

  const quoteId: string | undefined = _searchParams.quoteId as string | undefined;

  // Generate formatted parameters for components - only include dropOffTimestamp if it exists
  const formattedParams = {
    ..._searchParams,
    pickupBranchId: pickupBranchId,
    pickupTimestamp: pickupTimestamp.toString(),
    dropOffBranchId: dropOffBranchId,
    ...(dropOffTimestamp ? { dropOffTimestamp: dropOffTimestamp.toString() } : {}),
  };

  // Fetch branch information
  const { branchesResponse, quoteResponse } = await getPricingServerInfo({ quoteId });

  const isEnableContinue = !pickupBranchId || !pickupTimestamp || !dropOffTimestamp || !dropOffBranchId;

  return (
    <section className="mb-10 grid grid-cols-12 gap-x-10">
      {/* {quoteId && offerId && <Redirect newQuoteId={quoteId} offerId={offerId} />} */}

      <div className="col-span-8 flex flex-col space-y-6">
        <div className="flex flex-col gap-x-3 rounded-md">
          <Card className="flex flex-col shadow">
            <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
              <CardTitle className="text-lg font-bold">{t("details.bookingDetails")}</CardTitle>
            </CardHeader>
            <Separator />
            <DetailsCard branches={branchesResponse.body.data} searchParams={formattedParams} />
          </Card>
        </div>
        <ActionsBar
          bookingNo="1234"
          className="w-full"
          navItemsArray={WALK_IN_NAV_ITEMS}
          successCtaDisabled={isEnableContinue}
        />
      </div>

      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown
            _searchParams={formattedParams}
            branchesResponse={branchesResponse.body.data}
            quoteResponse={quoteResponse}
            quoteId={quoteId ? String(quoteId) : undefined}
          >
            {driverUId && <SidesheetWrapper driverUId={driverUId ?? ""} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
