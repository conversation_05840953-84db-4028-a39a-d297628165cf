"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowSquareOut } from "@phosphor-icons/react";
import { InfoIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface VehiclesEmptyStateProps {
  hasRecommendedVehicle?: boolean;
  isEmpty?: boolean;
  customerPreference?: string;
  groupCode?: string;
  isOnlyDowngrade?: boolean;
}

export function VehiclesEmptyState({
  customerPreference,
  groupCode,
  hasRecommendedVehicle,
  isEmpty,
  isOnlyDowngrade,
}: VehiclesEmptyStateProps) {
  const t = useTranslations("vehicles");
  let emptyState = null;

  if (isEmpty) {
    emptyState = (
      <div className="flex w-full flex-col items-center space-y-4 rounded-lg bg-slate-50 p-6">
        <p className="font-normal text-slate-500">{t("emptyState.noReadyVehicles")}</p>

        <Button variant="outline" className=" items-center gap-2" disabled>
          {t("emptyState.viewAll")}
          <ArrowSquareOut className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (isOnlyDowngrade) {
    emptyState = (
      <div className="flex w-full flex-col items-center space-y-4 rounded-lg bg-slate-50 p-6">
        <p className="font-normal text-slate-500">{t("emptyState.onlyDowngrade")} </p>
      </div>
    );
  }

  if (!hasRecommendedVehicle) {
    emptyState = (
      <div className="flex w-full flex-col items-center space-y-4 rounded-lg bg-slate-50 p-6">
        <p className="font-normal text-slate-500">{t("emptyState.noExactmatch")}</p>
      </div>
    );
  }

  return (
    <Card className="flex flex-col items-center space-y-6 rounded-lg border border-slate-200 bg-white p-6 shadow-sm">
      {customerPreference && (
        <div className="flex w-full items-center gap-2 self-start rounded-lg border p-4 text-sm font-normal text-slate-900">
          <InfoIcon className="h-5 w-5" />
          <span>
            {t("emptyState.customerPreference")}: {customerPreference} (Group {groupCode})
          </span>
        </div>
      )}

      {emptyState}
    </Card>
  );
}
