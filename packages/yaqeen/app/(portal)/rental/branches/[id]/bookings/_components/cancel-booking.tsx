"use client";

import { <PERSON><PERSON>, type ButtonProps } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { XCircle, Prohibit, Info } from "@phosphor-icons/react/dist/ssr";
import { Loader2 } from "lucide-react";
import { startTransition, useState } from "react";
import { useActionState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useProgressBar } from "@/components/progress-bar";
import { cancelBooking } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useLocale, useTranslations } from "next-intl";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

const reasons = [
  { id: 1, i18nKey: "reason_1", value: "No available vehicles" },
  { id: 2, i18nKey: "reason_2", value: "Suspicious customer" },
  { id: 3, i18nKey: "reason_3", value: "Rejected from Tajeer/Tamm" },
  { id: 4, i18nKey: "reason_4", value: "Didn’t show a credit card" },
  { id: 5, i18nKey: "reason_5", value: "Customer was not able to pay" },
  { id: 6, i18nKey: "reason_6", value: "Other" },
];

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

const initialState: State = {
  message: null,
  errors: {},
  success: false,
};

interface FormState {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
}

interface FormDataObj {
  reason: string;
  other_reason?: string;
}

export interface CancelBookingProps extends ButtonProps {
  bookingRef: string;
  isOnlinePayment?: boolean;
}

export const CancelBooking = ({ bookingRef, isOnlinePayment, ...buttonProps }: CancelBookingProps) => {
  const progress = useProgressBar();
  const router = useRouter();
  const params = useParams();
  const branchId = Number(params.id);
  const bookingId = Number(params.bookingId);
  const t = useTranslations("cancelBooking");
  const locale = useLocale();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [selectedReason, setSelectedReason] = useState<string>("");
  const [otherReason, setOtherReason] = useState("");

  const i18nKeyMap = {
    reason_1: t("reasons.reason_1"),
    reason_2: t("reasons.reason_2"),
    reason_3: t("reasons.reason_3"),
    reason_4: t("reasons.reason_4"),
    reason_5: t("reasons.reason_5"),
    reason_6: t("reasons.reason_6"),
  };

  const [, formAction, isSubmitting] = useActionState<FormState, FormData>(async (_state, formData) => {
    progress.start();

    const formDataObj: FormDataObj = Object.fromEntries(formData.entries()) as unknown as FormDataObj;
    const reason = formDataObj.reason;
    const otherReason = formDataObj.other_reason;
    const message = otherReason ? otherReason : reason;

    try {
      const response = await cancelBooking(bookingRef, message);
      if (response.status === 200) {
        startTransition(() => {
          toast({
            title: t("toast.success.title"),
            description: t("toast.success.description"),
            variant: "success",
            duration: 3000,
          });
          router.push(`/rental/branches/${branchId}/bookings/${bookingId}`);
          setOpen(false);
          setSelectedReason(""); // Reset form
          setOtherReason("");
        });

        return {
          message: t("toast.success.title"),
          errors: {},
          success: true,
        };
      } else {
        console.error("Error cancelling booking:", response);
        toast({
          title: t("toast.failed.title"),
          description: t("toast.failed.description"),
          variant: "destructive",
          duration: 3000,
        });

        return {
          message: t("toast.failed.description"),
          errors: { englishName: "Error occurred" },
          success: false,
        };
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast({
        title: t("toast.error.title"),
        description: t("toast.error.description"),
        variant: "destructive",
        duration: 3000,
      });

      return {
        message: t("toast.error.description"),
        errors: { englishName: "Unexpected error" },
        success: false,
      };
    } finally {
      progress.done();
    }
  }, initialState);

  const isOtherSelected = selectedReason === "Other";
  const isSubmitDisabled = selectedReason === "" || (isOtherSelected && otherReason.trim() === "");

  return (
    <>
      <Button
        variant="outline"
        className="flex cursor-pointer items-center gap-2"
        onClick={() => setOpen(true)}
        {...buttonProps}
      >
        <Prohibit className="h-4 w-4" />
        {t("title")}
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="p-0 sm:max-w-md">
          <DialogHeader className="p-4">
            <DialogTitle>{t("title")}</DialogTitle>
            <DialogDescription>{t("description")}</DialogDescription>
          </DialogHeader>
          <Separator />
          <section className="flex items-center gap-2 p-4">
            <div className="flex w-full items-start gap-2 rounded border border-slate-200 p-4">
              <Info className="h-[40px] w-[40px] text-slate-600" />
              <div className="flex flex-col">
                <p className="text-sm font-semibold text-slate-900">{t("refund.title")}</p>
                <p className="text-xs text-slate-600">{t("refund.description")}</p>
              </div>
            </div>
          </section>
          <form action={formAction} dir={locale === "ar" ? "rtl" : "ltr"}>
            <fieldset className="mb-2 flex flex-col items-start space-x-2 p-4">
              <Label className="text-medium mb-6 font-medium">{t("reason_field")}</Label>
              <RadioGroup
                name="reason"
                required
                value={selectedReason}
                onValueChange={(value) => setSelectedReason(value)}
              >
                {reasons.map((reason) => (
                  <fieldset
                    key={reason.id}
                    className="mb-2 flex items-start gap-2"
                    dir={locale === "ar" ? "rtl" : "ltr"}
                  >
                    <RadioGroupItem value={reason.value} id={reason.id.toString()} />
                    <section className="flex w-full flex-col justify-center">
                      <Label className="text-medium font-medium" htmlFor={reason.id.toString()}>
                        {i18nKeyMap[reason.i18nKey as keyof typeof i18nKeyMap]}
                      </Label>
                    </section>
                  </fieldset>
                ))}
              </RadioGroup>

              {isOtherSelected && (
                <Input
                  type="text"
                  name="other_reason"
                  placeholder={t("reason_field_placeholder")}
                  className="mx-0 w-full"
                  value={otherReason}
                  onChange={(e) => setOtherReason(e.target.value)}
                  required
                />
              )}
            </fieldset>

            <Separator />
            <DialogFooter className="gap-2 p-4 sm:justify-end">
              <Button type="button" onClick={() => setOpen(false)} variant="outline" disabled={isSubmitting}>
                {t("cta.cancel")}
              </Button>
              <RedirectModal>
                <Button type="submit" disabled={isSubmitDisabled || isSubmitting}>
                  {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : t("cta.submit")}
                </Button>
              </RedirectModal>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

const RedirectModal = ({ children }: { children: React.ReactNode }) => {
  const t = useTranslations("cancelBooking");

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="p-0 sm:w-[500px] [&>button]:hidden">
        <DialogTitle className="sr-only">{t("dialog.title")}</DialogTitle>
        <div className="flex flex-col items-center justify-center gap-y-4 px-6 py-12 text-center">
          <LoadingSpinner className="h-12 w-12 text-lumi-700" />
          <p className="text-xl font-bold text-slate-900">{t("dialog.description")}</p>
          <p className="text-md text-slate-600">{t("dialog.waitingMessage")}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
