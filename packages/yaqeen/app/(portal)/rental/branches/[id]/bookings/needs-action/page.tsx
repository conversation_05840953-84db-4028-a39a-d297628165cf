import React, { Suspense } from "react";
import { api } from "@/api";
import { type SearchParams } from "nuqs/server";
import { bookingSearchParamsCache } from "@/lib/params";
import { getAllBookingTimeRange } from "@/lib/utils";
import { DataTable } from "@/components/ui/data-table/data-table";
import Header from "../_components/Header";
import { columns } from "./components/columns";
import { CHECKIN_DATE, filters, searchFilters } from "../_components/constants";
import Loading from "@/app/(portal)/loading";
import { getLocale, getTranslations } from "next-intl/server";
import type { IBranch } from "@/api/contracts/branch-contract";
type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<Record<string, string>>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const _searchParams = await searchParams;
  const { id } = await params;
  const { pageSize, order, pageNumber, bookingNo, mobileNumber, driverName, dropOffDateRangeStart } =
    bookingSearchParamsCache.parse(_searchParams);

  const locale = (await getLocale()) as "en" | "ar";
  const bookingsT = await getTranslations("bookings");

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse.find((branch) => branch.id === Number(id));

  const STATUS = "NEEDS_ACTION";

  const suspenseKey = Object.entries(_searchParams)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value as string}`)
    .join("-");

  const { start: dropOffStart, end: dropOffEnd } = getAllBookingTimeRange(dropOffDateRangeStart);

  const [bookings] = await Promise.all([
    api.booking.getOngoingBookings({
      query: {
        pickupBranchId: Number(id),
        ...(dropOffDateRangeStart && { "dropOffDateRange.start": dropOffStart }),
        ...(dropOffEnd && { "dropOffDateRange.end": dropOffEnd }),
        order: order,
        sort: CHECKIN_DATE,
        page: pageNumber,
        size: pageSize,
        bookingNo: bookingNo,
        mobileNumber: mobileNumber,
        driverName: driverName,
        status: STATUS,
      },
    }),
  ]);

  if (bookings?.status !== 200) {
    throw new Error(`Error: ${bookings.status}`);
  }

  const data = bookings.body.data ?? [];
  const total = bookings.body.total ?? 0;

  return (
    <Suspense key={suspenseKey} fallback={<Loading />}>
      <Header
        branchName={branch?.name?.[locale] ?? "N/A"}
        activeTab={{
          label: "Needs action",
          count: total > 100 ? 99 : total,
        }}
      />
      <div className="flex flex-col px-6">
        <DataTable
          searchPlaceholder={bookingsT("searchPlaceholder")}
          columns={columns}
          filters={filters}
          searchFilters={searchFilters}
          countText={``}
          rowClickId={"bookingId"}
          extraParams={["agreementNo"]}
          baseRedirectPath={`/rental/branches/${id}/bookings/`}
          data={{
            data: data,
            total: total,
          }}
          emptyMessage="There are no need action bookings."
        />
      </div>
    </Suspense>
  );
}
