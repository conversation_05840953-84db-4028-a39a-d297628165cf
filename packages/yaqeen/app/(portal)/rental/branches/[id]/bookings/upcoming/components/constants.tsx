import { timeFilterOptions } from "../../_components/constants";

export const filters = [
  {
    filterKey: "pickupDateRangeStart",
    filterName: "Pickup time",
    translationKey: "pickupTime",
    columnKey: "pickupDateTime",
    isMultiSelect: false,
    options: timeFilterOptions,
  },
  {
    filterKey: "paymentStatusFilter",
    filterName: "Payment",
    translationKey: "payment",
    columnKey: "paymentStatus",
    isMultiSelect: false,
    options: [
      { label: "Paid", value: "PAID", translationKey: "paid" },
      { label: "Pending", value: "PENDING", translationKey: "pending" },
    ],
  },
  {
    filterKey: "source",
    filterName: "Source",
    translationKey: "source",
    columnKey: "source",
    isMultiSelect: true,
    options: [
      { label: "Walk in", value: "WALK_IN", translationKey: "walkIn" },
      { label: "Lumi", value: "LUMI", translationKey: "lumi" },
      { label: "Corporate", value: "CORPORATE", translationKey: "corporate" },
      { label: "Online", value: "ONLINE", translationKey: "online" },
      { label: "Carla", value: "CARLA", translationKey: "carla" },
      { label: "Yolcu", value: "YOLCU", translationKey: "yolcu" },
    ],
  },
];
