import type { VehicleDetail } from "@/api/contracts/booking/schema";
import { type VehicleDetail as VehicleDetailV2 } from "@/api/contracts/rental/availability-contract";
import { VehiclePlate } from "@/app/(portal)/rental/_components/vehicle-plate";
import { useLocale, useTranslations } from "next-intl";

export function VehicleDetails({ vehicle }: { vehicle: VehicleDetail | VehicleDetailV2 }) {
  const t = useTranslations("bookings.details");
  const locale = useLocale() as "en" | "ar";
  const [plateNo = "", plateLetters = ""] = vehicle ? String(vehicle?.plateNo)?.split(" ") : ["", ""];

  return (
    <section className="flex min-h-20 items-center justify-center p-4">
      <div className="flex h-full w-1/2 flex-col">
        <h3 className="text-gray-500">{t("vehicleDetails")}</h3>
        <h5 className="font-bold text-slate-900">
          {vehicle.model?.make?.name?.[locale]} {vehicle?.model?.name?.[locale]}
        </h5>
      </div>
      <div className="relative w-1/2">
        <VehiclePlate
          className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 bg-white"
          plateNumber={String(plateNo)}
          plateNoAr={vehicle.plateNoAr ?? ""}
          plateLetters={plateLetters}
        />
      </div>
    </section>
  );
}
