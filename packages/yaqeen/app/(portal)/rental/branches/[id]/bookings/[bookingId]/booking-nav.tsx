"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { CheckCircle } from "@phosphor-icons/react";
import { useAtom } from "jotai";
import { redirect, useParams, usePathname, useSearchParams } from "next/navigation";
import { useMemo } from "react";
import { atomWithBookingNav } from "./atoms";
import { type Route } from "next";
import { type NavItem } from "../_components/constants";
import { useTranslations } from "next-intl";
import { type LocalizableNavItem } from "../create/constants";

interface BookingNavProps {
  bookingId: string | undefined;
  navItemsArray: NavItem[] | LocalizableNavItem[];
  isAuthorizedForTajeer?: boolean;
}

export type BookingStep =
  | "bookingdetails"
  | "driverdetails"
  | "assignavehicle"
  | "insuranceandextras"
  | "payment"
  | "authorization";

export function BookingNav({ isAuthorizedForTajeer = false, bookingId, navItemsArray }: BookingNavProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = useParams<{ id: string }>();
  const t = useTranslations("navigation");

  const bookingNavAtom = useMemo(
    () => atomWithBookingNav(Number(bookingId), navItemsArray as NavItem[]),
    [bookingId, navItemsArray]
  );

  const [navItems] = useAtom(bookingNavAtom);
  const currentStepIndex = navItems.findIndex((item) => pathname.includes(item.href));
  const assignVehicleStep = navItems[2]!;

  if (currentStepIndex > 2) {
    if (!assignVehicleStep.completed) {
      const redirectUrl = `/rental/branches/${params.id}/bookings/${bookingId ? bookingId : "create"}${assignVehicleStep.href}${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      redirect(redirectUrl);
    }
  }

  return (
    <div className="flex gap-x-6">
      {navItems.map((item, index) => {
        const href = `/rental/branches/${params.id}/bookings/${bookingId ? bookingId : "create"}${item.href}${
          searchParams.toString() ? `?${searchParams.toString()}` : ""
        }`;
        const isAccessible = index <= 2 || (index > 2 && assignVehicleStep.completed);

        // Check if the item has a translationKey (is a LocalizableNavItem)
        const hasTranslationKey = "translationKey" in item && item.translationKey;

        // Get the translated label if available, otherwise use the default label
        const translatedLabel =
          hasTranslationKey && typeof item.translationKey === "string"
            ? t(`bookingSteps.${item.translationKey as BookingStep}`, { fallback: item.label })
            : t(`bookingSteps.${item.label.toLowerCase().replace(/\s+/g, "") as BookingStep}`, {
                fallback: item.label,
              });

        return (
          <ProgressBarLink
            key={index}
            href={isAuthorizedForTajeer ? "#" : (href as Route)}
            className={`flex items-center gap-2 py-3 md:text-xs xl:text-sm ${
              pathname.includes(item.href)
                ? "border-b-2 border-slate-900 font-semibold text-slate-900"
                : "text-slate-700"
            } ${!isAccessible ? "pointer-events-none opacity-50" : ""}`}
            aria-disabled={!isAccessible}
          >
            {item.completed ? (
              <CheckCircle
                weight="fill"
                className={`h-4 w-4 ${pathname.includes(item.href) ? "fill-slate-900" : "fill-slate-500"}`}
              />
            ) : null}
            {translatedLabel}
          </ProgressBarLink>
        );
      })}
    </div>
  );
}
