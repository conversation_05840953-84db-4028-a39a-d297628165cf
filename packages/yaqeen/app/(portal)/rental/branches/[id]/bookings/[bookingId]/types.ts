export interface IDriverDetails {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  countryCode: number;
  email: string;
  dob: string;
  hijrahDob: string;
  title: string;
  address: {
    street: string;
  };
  id: number;
  customer: {
    id: number;
  };
  driverCode: string;
  createdOn: number;
  updatedOn: number;
  documents: IDocument[];
  nationality: Nationality;
  driverUId: string;
  idType: string;
  metadata?: {
    borderNumber: string;
  };
}

export type ID_TYPE = "SAUDI_NATIONAL" | "RESIDENT" | "GCC" | "VISITOR";

/** Nuts and bolts */

export interface Nationality {
  code: number;
  position: number;
  name: Name;
  id: number;
}
export interface IDocument {
  id: number;
  type: string;
  documentNo: string;
  expiry: string;
  hijrahExpiry: string;
  url: string;
  issuedPlace?: {
    code: number;
    name: Name;
  };
}

interface Name {
  en: string;
  ar: string;
}
