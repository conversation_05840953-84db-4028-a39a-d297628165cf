import { type NavItem } from "../_components/constants";

// Extended NavItem interface to include translation key
export interface LocalizableNavItem extends NavItem {
  translationKey: string; // The key to use for localization
  isEnabled: boolean; // Indicates if the item is enabled
}

// Define nav items with translation keys matching the ones in the language files
export const WALK_IN_NAV_ITEMS: Array<LocalizableNavItem> = [
  {
    label: "Assign a vehicle",
    href: "/assign-a-vehicle",
    completed: false,
    isEnabled: true,
    translationKey: "assignavehicle",
  },
  {
    label: "Booking details",
    href: "/booking-details",
    completed: false,
    isEnabled: false,
    translationKey: "bookingdetails",
  },
  {
    label: "Driver details",
    href: "/driver-details",
    completed: false,
    isEnabled: false,
    translationKey: "driverdetails",
  },
  {
    label: "Insurance & extras",
    href: "/insurance-and-extras",
    completed: false,
    isEnabled: false,
    translationKey: "insuranceandextras",
  },
] as const;
