"use client";
import React, { startTransition, useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { type z } from "zod";
import { type QuotePrice, type SingleInsuranceSchema } from "@/api/contracts/booking/schema";
import { getQuoteWithAddon } from "@/lib/actions";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

export interface InsuranceProps {
  insurances: z.infer<typeof SingleInsuranceSchema>[];
}

export default function Insurance({ insurances }: InsuranceProps) {
  const t = useTranslations("insurance");
  const locale = useLocale();

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const offerId = searchParams.get("offerId");
  const vehicleGroupId = searchParams.get("vehicleGroupId");

  // Get the current insuranceIds param or default to undefined
  const currentInsuranceIds = searchParams.get("insuranceIds");

  // Get addOns from URL params
  const addOnsParam = searchParams.get("addOns");

  const [_insurances, setInsurances] = useState(insurances);
  const [, setInsuranceIds] = useQueryState("insuranceIds", {
    shallow: false,
  });

  const progress = useProgressBar();

  // On initial render, if no insurance is selected yet, auto-select insurance ID 2
  useEffect(() => {
    // Only apply default selection if no insurance is currently selected
    if (!currentInsuranceIds && offerId && vehicleGroupId) {
      // Auto-select comprehensive insurance (ID=2)
      const defaultInsuranceId = 2;
      void handleInsuranceChange(defaultInsuranceId);
    }
  }, []);

  useEffect(() => {
    // set this to default so when user redirects to 'create booking flow's Insurance and extra's page,
    // so that page can detect you are coming from create booking flow'
    localStorage.setItem("isCreateBookingRedirect", "false");
  }, []);

  const handleInsuranceChange = async (id: number) => {
    // Get addOns IDs from URL params if available
    const addOnsIds = addOnsParam ? addOnsParam.split(",").map(Number) : undefined;

    // Call getQuoteWithAddon with both insurance ID and any existing addon IDs
    const resp = await getQuoteWithAddon(vehicleGroupId ?? "1", offerId ?? "", String(id), addOnsIds);

    if (resp.status === 200) {
      const body = resp.body as unknown as QuotePrice;
      const updatedInsurances = _insurances.map((insurance) => {
        return {
          ...insurance,
          isEnabled: insurance.id === id,
        };
      });
      setInsurances(updatedInsurances);

      const current = new URLSearchParams(Array.from(searchParams.entries()));
      current.set("quoteId", body.quoteId);
      current.set("offerId", body.offerId);
      const search = current.toString();
      const query = search ? `?${search}` : "";

      progress.start();
      startTransition(() => {
        void setInsuranceIds(String(id));
        window.history.replaceState({}, "", pathname + query);
        progress.done();
      });
    }
  };

  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="flex w-full p-0 max-md:flex-wrap">
        <div className="flex w-full flex-col text-sm font-medium">
          <RadioGroup name="insurance" dir={locale === "ar" ? "rtl" : "ltr"}>
            {_insurances.map((insurance) => (
              <div className="flex items-start gap-x-2 p-4" key={insurance.id}>
                <RadioGroupItem
                  checked={insurance.isEnabled}
                  onClick={() => handleInsuranceChange(insurance.id)}
                  value={String(insurance.id) ?? ""}
                  id={insurance.name[locale as keyof typeof insurance.name]}
                  className="mt-1 gap-x-2 text-blue-600"
                />
                <Label
                  htmlFor={insurance.name[locale as keyof typeof insurance.name]}
                  className="flex w-full flex-row items-end justify-between"
                >
                  <div>
                    <p className="text-base font-medium">{insurance.name[locale as keyof typeof insurance.name]}</p>
                    <p className="text-sm font-normal text-slate-600">
                      {insurance.description[locale as keyof typeof insurance.description]}
                    </p>
                  </div>
                  <div className="flex flex-col items-end">
                    <p className="text-base font-medium">
                      {insurance.deductible
                        ? t("pricing.withDeductible", { amount: insurance.deductible?.toFixed(2) })
                        : t("pricing.free")}
                    </p>
                    {insurance.perday ? (
                      <p className="text-xs text-slate-500">
                        {t("pricing.perDay", { amount: insurance.perday?.toFixed(2) })}
                      </p>
                    ) : (
                      <></>
                    )}
                  </div>
                </Label>
              </div>
            ))}
            <Separator />
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );
}
