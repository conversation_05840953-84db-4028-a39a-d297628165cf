import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Printer, Envelope, CurrencyCircleDollar, Globe, Calendar } from "@phosphor-icons/react/dist/ssr";
import { Separator } from "@/components/ui/separator";
import CopyLink from "@/components/customized/copy-link";
import { getBadgeColor } from "../../_components/payments-columns";
import { capitalize } from "lodash-es";
import { DownloadPaymentReciept, PAYMENT_TYPE } from "./print-payment";
import { useLocale, useTranslations } from "next-intl";
import { format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";

interface PaymentDetailsProps {
  id: number;
  amount: string;
  paymentId: string;
  createdTimestamp: number;
  paymentType: string;
  status: string;
  type: string;
}

export default function PaymentDetailsSheet({
  amount,
  paymentId,
  createdTimestamp,
  paymentType,
  status,
  id,
  type,
}: PaymentDetailsProps) {
  const t = useTranslations("payment");
  const locale = useLocale();
  const createdTime = `${format(createdTimestamp * 1000, "dd/MM/yyyy")} - ${format(
    createdTimestamp * 1000,
    "hh:mm:ss a",
    {
      locale: locale === "ar" ? arSA : enUS,
    }
  )}`;
  return (
    <div className="w-full max-w-md">
      <div className="space-y-6 p-6 pt-2">
        <div className="flex gap-2" dir="ltr">
          {type.toLocaleLowerCase() === PAYMENT_TYPE.PAYMENT.toString() && (
            <DownloadPaymentReciept
              paymentId={id?.toString()}
              type={type}
              variant="outline"
              size="icon"
              className="flex items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted"
            >
              <Printer className="h-5 w-5" />
            </DownloadPaymentReciept>
          )}
          <Button
            variant="outline"
            size="icon"
            className="flex items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted"
          >
            <Envelope className="h-5 w-5" />
          </Button>
          <CopyLink />
        </div>
        <h1 className="text-2xl font-semibold">{t("sidesheet.payment_title")}</h1>
      </div>
      <div className="space-y-6 ">
        <div className="flex items-center gap-2 px-6">
          <CurrencyCircleDollar className="h-6 w-6" />
          <span className="text-xl font-semibold">
            {parseFloat(amount).toLocaleString(locale === "ar" ? "ar-AE" : "en-US", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
          <Badge variant="secondary" className={`rounded-full px-3 font-normal ${getBadgeColor(status ?? "Pending")}`}>
            {t(
              // @ts-expect-error TODO: Fix this
              `columns.${status.toLocaleLowerCase()}`
            )}
          </Badge>
        </div>
        <div className="flex items-center gap-2 px-6">
          <Globe className="h-6 w-6" />
          <span className="">
            {t(
              // @ts-expect-error TODO: Fix this
              `columns.${paymentType.toLocaleLowerCase()}`
            )}
          </span>
        </div>
        <div className="flex items-center gap-2 px-6">
          <Calendar className="h-6 w-6" />
          <span>{createdTime}</span>
        </div>
        <Separator />
        <div className="space-y-2 px-6 pt-4">
          <p className="text-sm text-muted-foreground">{t("sidesheet.payment_id")}</p>
          <p className="font-mono text-sm">{paymentId}</p>
        </div>
      </div>
    </div>
  );
}
