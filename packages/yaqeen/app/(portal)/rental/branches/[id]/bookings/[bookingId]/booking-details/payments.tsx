import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table/data-table";
import { api } from "@/api";
import { paymentColumns } from "../../_components/payments-columns";
import { getTranslations } from "next-intl/server";

export default async function Payments({ bookingId }: { bookingId: string }) {
  const t = await getTranslations("createAgreement");

  const transactionsResponse = await api.payment.getBookingTransactions({
    params: {
      bookingId,
    },
    query: {
      notInitiatedFor: "SECURITY_DEPOSIT_AUTHORIZATION",
    },
  });

  if (transactionsResponse?.status !== 200) {
    throw new Error(`Error: ${transactionsResponse.status}`);
  }

  const { data, total } = transactionsResponse.body;

  return (
    <Card className="flex flex-col">
      <CardHeader className="px-4">
        <CardTitle className="text-lg font-bold">{t("bookingDetails.paymentsRefunds")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="w-full p-0">
        <div className="h-fit w-full">
          {data.length > 0 ? (
            <DataTable
              columns={paymentColumns}
              data={{
                data,
                total,
              }}
              emptyMessage="There are no transactions."
            />
          ) : (
            <p className="p-6 text-center">{t("bookingDetails.noPayments")}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
