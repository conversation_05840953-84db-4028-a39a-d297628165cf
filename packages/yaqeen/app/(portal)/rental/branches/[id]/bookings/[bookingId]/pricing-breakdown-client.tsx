"use client";
import type { CalculatePrice, Booking } from "@/api/contracts/booking/schema";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { differenceInDays, differenceInHours } from "date-fns";
import { format } from "date-fns-tz";
import Redirect from "../_components/Redirect";
import { SaveRemainingAmount } from "../_components/SaveRemainingAmount";

import { VehicleDetails } from "@/app/(portal)/rental/branches/[id]/bookings/_components/vehicle-details";
import { useSearchParams } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";

const PricingBreakDownClient = ({
  booking,
  priceCalculatorData,
  children,
}: {
  children: React.ReactNode;
  booking: Booking;
  priceCalculatorData: CalculatePrice;
}) => {
  const _searchParams = useSearchParams();
  const searchParams = Object.fromEntries(_searchParams.entries());
  const t = useTranslations();
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;

  const { driver } = booking;
  const dropOffTimeStamp = Number(searchParams.dropOffTimestamp ?? booking.dropOffDateTime);
  const insuranceIds = [];
  const isInsuranceExist = typeof searchParams.insuranceIds === "string";
  if (isInsuranceExist && searchParams?.insuranceIds?.length) {
    insuranceIds.push(Number(searchParams.insuranceIds));
  }

  const quoteId = priceCalculatorData.quoteId ?? searchParams.quoteId;
  const { includedComprehensiveInsurance, discountDetail, priceDetail } = priceCalculatorData;
  const _priceCalculatorData = {
    ...priceDetail,
    ...discountDetail?.discountedPriceDetail,
    rentalAmount: priceDetail.rentalAmount,
  };

  const { rentalAmount, insuranceAmount, dropOffAmount, vatPercentage, vatAmount, totalSum } =
    _priceCalculatorData ?? {};
  const { remainingAmount, driverPaidAmount } = priceCalculatorData ?? {};
  const addOnsBreakdown = priceCalculatorData.addOns;

  const { pickupBranch, dropOffBranch } = priceCalculatorData?.request;

  if (!pickupBranch || !dropOffBranch) {
    throw new Error(t("common.errors.unexpectedError"));
  }

  if (!dropOffTimeStamp) {
    throw new Error(t("common.errors.unexpectedError"));
  }

  const pickUpDateTime = Math.floor(new Date().getTime() / 1000);
  const pickupDate = format(new Date(), "EEEE, dd/MM/yyyy, HH:mm", {
    locale: nLocale,
  });
  const dropOffDate = format(new Date(dropOffTimeStamp * 1000), "EEEE, dd/MM/yyyy, HH:mm", {
    locale: nLocale,
  });

  const days = differenceInDays(new Date(dropOffTimeStamp * 1000), new Date(pickUpDateTime * 1000));
  const remainingHours = differenceInHours(new Date(dropOffTimeStamp * 1000), new Date(pickUpDateTime * 1000)) % 24;

  // Format duration using translation
  const bookingSummary = t("pricing.duration", { days, hours: remainingHours });

  // Use localized branch and city names
  const pickupDetails = {
    date: pickupDate,
    location: pickupBranch.name[locale] || pickupBranch.name.en,
    city: pickupBranch.city.name[locale] || pickupBranch.city.name.en,
  };

  const dropOffDetails = {
    date: dropOffDate,
    location: dropOffBranch.name[locale] || dropOffBranch.name.en,
    city: dropOffBranch.city.name[locale] || dropOffBranch.city.name.en,
  };

  const { assignedVehicle } = booking;
  const vehicle = assignedVehicle?.vehiclePlateInfo;

  return (
    <Card className="w-full overflow-hidden text-sm text-slate-600">
      {typeof quoteId === "string" && <Redirect newQuoteId={quoteId} />}
      <div className="flex items-center justify-between">
        <CardHeader className="px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg capitalize text-slate-900">{`${driver?.title}. ${driver?.name}`}</CardTitle>
            </div>
          </div>
        </CardHeader>
        {children}
      </div>

      <Separator />

      <div className="flex items-center justify-between p-4">
        <h4 className="text-base font-bold text-slate-900">{t("pricing.Booking summary")}</h4>
        <span>{bookingSummary}</span>
      </div>

      <Separator />

      <CardContent className="p-0">
        <section className="space-y-4 p-4">
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">{t("pricing.Pickup")}</h5>
            <p className="font-medium text-slate-900">{pickupDetails.date}</p>
            <p>
              {pickupDetails.location}, {pickupDetails.city}
            </p>
          </div>
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">{t("pricing.Drop-off")}</h5>
            <p className="font-medium text-slate-900">{dropOffDetails.date}</p>
            <p>
              {dropOffDetails.location}, {dropOffDetails.city}
            </p>
          </div>
        </section>

        <Separator />

        {vehicle ? <VehicleDetails vehicle={vehicle} /> : null}

        <Separator />

        <section>
          <div className="flex items-center justify-between p-4 text-slate-900">
            <h5 className="text-base font-bold">{t("pricing.Price breakdown")}</h5>
            <span>{t("pricing.SAR")}</span>
          </div>

          <Separator />

          <div className="space-y-2 p-4">
            <div className="flex items-center justify-between">
              <span className="overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-relaxed tracking-normal">
                {t("pricing.Rental")}{" "}
                {days > 0 && t("pricing.rentalPeriod", { days, hours: remainingHours > 0 ? remainingHours : 0 })}
              </span>
              <span>{Number(rentalAmount).toFixed(2)}</span>
            </div>

            {discountDetail?.discountPercentage && (
              <div className="flex items-center justify-between text-green-600">
                <span>
                  {t("pricing.Discount")} {Number(discountDetail.discountPercentage)}% ({discountDetail?.promoCode})
                </span>
                <span>-{discountDetail.totalDiscount}</span>
              </div>
            )}

            {insuranceAmount && (
              <div className="flex items-center justify-between">
                <span>
                  {includedComprehensiveInsurance ? t("pricing.comprehensiveInsurance") : t("pricing.basicInsurance")}
                </span>
                <span>{Number(insuranceAmount).toFixed(2)}</span>
              </div>
            )}
            {addOnsBreakdown?.map((addon) => (
              <div key={addon.id} className="flex items-center justify-between">
                <span>
                  {addon?.name?.[locale] ? addon?.name?.[locale] : t("pricing.addonFallback", { number: addon?.id })}
                </span>
                <span>{Number(addon.totalCost).toFixed(2)}</span>
              </div>
            ))}
            {dropOffAmount && (
              <div className="flex items-center justify-between">
                <span>{t("pricing.dropOffFee")}</span>
                <span>{Number(dropOffAmount).toFixed(2)}</span>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span>
                {t("pricing.vat")} {vatPercentage ? `${parseInt(vatPercentage)}%` : ""}
              </span>
              <span>{Number(vatAmount).toFixed(2)}</span>
            </div>
          </div>
        </section>
      </CardContent>
      <CardFooter className="flex flex-col border-t p-0">
        <div className="w-full space-y-3 p-4">
          <div className="flex items-center justify-between text-base font-medium text-slate-900">
            <span>{t("pricing.total")}</span>
            <span>{Number(totalSum).toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>{t("pricing.paidAmount")}</span>
            <span>{Number(driverPaidAmount).toFixed(2)}</span>
          </div>
        </div>
        <Separator />
        <div
          className={`flex w-full justify-between p-4 text-base font-medium 
          ${Number(remainingAmount) > 0 ? "bg-red-50 text-red-700" : "bg-lumi-50"}
            `}
        >
          <span className="text-slate-900">{t("pricing.remaining")}</span>
          <SaveRemainingAmount amount={Number(remainingAmount).toFixed(2)} />
          <span>{Number(remainingAmount).toFixed(2)}</span>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PricingBreakDownClient;
