import { Suspense } from "react";
import { api } from "@/api";
import { notFound } from "next/navigation";
import Extras from "./_components/extras";
import Insurance from "./_components/insurance";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { WALK_IN_NAV_ITEMS } from "../constants";
import PricingBreakdown from "../_components/pricing-breakdown";
import { getTranslations } from "next-intl/server";
import { getPricingServerInfo } from "../utils";
import SidesheetWrapper from "../../[bookingId]/_components/sidesheet-wrapper";

export default async function InsuranceAndExtras({
  params,
  searchParams,
}: {
  params: Promise<Record<string, string | number>>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  // Get translations
  const t = await getTranslations("insurance");

  const searchParamsData = await searchParams;
  const vehicleGroupId = Number(searchParamsData.vehicleGroupId);
  const quoteId = Array.isArray(searchParamsData.quoteId) ? searchParamsData.quoteId[0] : searchParamsData.quoteId;
  const driverUId = searchParamsData?.driverUid as string;

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  const [insuranceResponse, addonResponse] = await Promise.all([
    api.bookingDetails.getInsuranceByGroupId({
      query: {
        groupId: isNaN(vehicleGroupId) ? 0 : vehicleGroupId,
      },
    }),
    api.bookingDetails.getAddOnsByQuoteId({
      params: {
        quoteId: quoteId ?? "",
      },
    }),
  ]);

  if (insuranceResponse.status !== 200) {
    throw new Error("Failed to fetch insurance details");
  }

  if (addonResponse.status !== 200) {
    throw new Error("Failed to fetch add-ons details");
  }

  const { branchesResponse, quoteResponse } = await getPricingServerInfo({ quoteId });

  if (!quoteResponse) {
    return notFound();
  }

  const insurances = insuranceResponse.body.data;
  const addons = addonResponse.body;
  const unlimitedKm = addons.find((addon) => addon.code === "UNLIMITED_KM");
  const thirdPartyInsurance = insurances.find((insurance) => insurance.id === 1);
  const comprehensiveInsurance = insurances.find((insurance) => insurance.id === 2);

  let _insurances = [];

  if (thirdPartyInsurance) {
    _insurances.push({
      ...thirdPartyInsurance,
      description: {
        en: quoteResponse.cdwDeductible
          ? t("thirdParty.description", { deductible: quoteResponse.cdwDeductible, locale: "en" })
          : t("thirdParty.description_default"),
        ar: quoteResponse.cdwDeductible
          ? t("thirdParty.description", { deductible: quoteResponse.cdwDeductible, locale: "ar" })
          : t("thirdParty.description_default"),
      },
      deductible: 0,
      perday: 0,
    });
  }

  if (comprehensiveInsurance) {
    _insurances.push({
      ...comprehensiveInsurance,
      deductible: quoteResponse.priceDetails.cdw,
      perday: parseInt(String(quoteResponse.cdwPerDay)) ?? 0,
    });
  }

  _insurances = [
    ..._insurances,
    ...insurances.filter((insurance) => ![thirdPartyInsurance?.id, comprehensiveInsurance?.id].includes(insurance.id)),
  ];

  _insurances.forEach((insurance) => {
    insurance.isEnabled = insurance.recommended;
  });

  const totalAllowedKms = quoteResponse.dailyKmsAllowance * quoteResponse.soldDays;

  return (
    <div>
      <section className="grid grid-flow-col gap-x-10">
        <div className="flex w-[768px] flex-col gap-y-6">
          <Insurance insurances={_insurances} />
          <Extras _addons={addons} preSelectedKms={totalAllowedKms} unlimitedKm={unlimitedKm} />
          <ActionsBar bookingNo="1234" className="w-full" navItemsArray={WALK_IN_NAV_ITEMS} />
        </div>
        <div className="w-full">
          <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
            <PricingBreakdown
              _searchParams={searchParamsData}
              branchesResponse={branchesResponse.body.data}
              quoteResponse={quoteResponse}
              quoteId={quoteId ? String(quoteId) : undefined}
            >
              {driverUId && <SidesheetWrapper driverUId={driverUId ?? ""} />}
            </PricingBreakdown>
          </Suspense>
        </div>
      </section>
    </div>
  );
}
