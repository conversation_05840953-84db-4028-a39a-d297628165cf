"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { downloadAgreement } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { Download } from "@phosphor-icons/react/dist/ssr";

export const DownloadAgreement = ({
  variant,
  agreementNo,
  className,
}: {
  variant?: string;
  agreementNo: string;
  className?: string;
}) => {
  const t = useTranslations("authorization");
  const { toast } = useToast();
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownloadAgreement = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (!agreementNo) {
      toast({
        title: t("download.downloadFailed"),
        description: t("download.agreementNotFound"),
        variant: "destructive",
      });
      return;
    }

    setIsDownloading(true);
    try {
      const response = await downloadAgreement(agreementNo);
      if (response.status !== 200) {
        toast({
          title: t("download.downloadFailed"),
          description: t("download.failedToDownloadAgreement"),
          variant: "destructive",
        });
        return;
      }
      const data = response.body;

      const printWindow = window.open("", "_blank", "width=" + window.screen.width + ",height=" + window.screen.height);
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.title = `Rental Agreement ${agreementNo ? "- " + agreementNo : "Agreement"}`;
        printWindow.document.write(data);
        printWindow.document.close();

        // Wait for the content to load before printing
        printWindow.onload = () => {
          printWindow.focus();
          printWindow.print();
        };
      } else {
        toast({
          title: t("download.printFailed"),
          description: t("download.failedToOpenPrintWindow"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error downloading agreement:", error);
      toast({
        title: t("download.downloadFailed"),
        description: error instanceof Error ? error.message : t("download.unexpectedError"),
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      variant={(variant as "default" | "link" | "destructive" | "outline" | "secondary" | "ghost") ?? "outline"}
      className={cn("w-full", className)}
      type="button"
      onClick={handleDownloadAgreement}
      disabled={isDownloading}
    >
      {isDownloading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
      {t("download.downloadAgreement")}
    </Button>
  );
};
