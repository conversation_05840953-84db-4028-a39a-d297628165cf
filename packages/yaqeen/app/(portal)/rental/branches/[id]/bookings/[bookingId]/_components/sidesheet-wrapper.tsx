import { Sheet, She<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { CaretRight } from "@phosphor-icons/react/dist/ssr";
import { DriverProfileSheet } from "./driver-sidesheet";
import { Suspense } from "react";
import { DirectionalIcon } from "@/components/ui/directional-icon";

export default function SidesheetWrapper({ driverUId }: { driverUId: string }) {
  return (
    <Sheet>
      <SheetHeader className="sr-only">
        <SheetTitle>Edit profile</SheetTitle>
        <SheetDescription>Make changes to your profile here. Click save when you are done.</SheetDescription>
      </SheetHeader>
      <SheetTrigger asChild>
        <div className="flex">
          <Button className="mx-4 h-7 w-7" variant="outline" size="icon">
            <DirectionalIcon>
              <CaretRight />
            </DirectionalIcon>
          </Button>
        </div>
      </SheetTrigger>
      <SheetContent side="right" className="w-full px-0 py-6  ring-0 sm:w-[400px] sm:max-w-full">
        <Suspense fallback={<div>Loading...</div>}>
          <DriverProfileSheet isOpen={Boolean(false)} driverUid={driverUId} />
        </Suspense>
      </SheetContent>
    </Sheet>
  );
}
