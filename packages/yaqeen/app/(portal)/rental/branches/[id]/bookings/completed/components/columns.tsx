"use client";

import { type Row, type ColumnDef } from "@tanstack/react-table";
import { type CompletedBooking } from "../../_components/types";
import { DataTableRowActions } from "@/components/ui/data-table/data-table-row-actions";
import { CaretUpDown, CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";

import { amountFormatter, formattedPickupTime } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import TooltipComponent from "@/components/tooltip-component";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";

const LocalizeText = ({ message }: { message: string }) => {
  const t = useTranslations("bookings.columns");
  // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'message' implicitly has an 'any' type.
  return <div>{t(message)}</div>;
};

export const columns: ColumnDef<CompletedBooking>[] = [
  {
    id: "id",
    accessorKey: "id",
    enableHiding: true,
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => <LocalizeText message="bookingNo" />,
    cell: ({ row }) => {
      return row.getValue<CompletedBooking["bookingNo"]>("bookingNo");
    },
  },
  {
    accessorKey: "driver",
    header: () => <LocalizeText message="driver" />,
    cell: ({ row }) => {
      return (
        <span className="flex items-center gap-x-1">
          <span className=" text-blue-600">
            {row.getValue<CompletedBooking["driver"]>("driver")?.firstName}{" "}
            {row.getValue<CompletedBooking["driver"]>("driver")?.lastName}
          </span>
        </span>
      );
    },
  },
  {
    accessorKey: "dropOffDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="dropOffTime" />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <FormattedDropoffTime row={row} />;
    },
  },
  {
    accessorKey: "source",
    header: () => <LocalizeText message="source" />,
    cell: ({ row }) => {
      return row.getValue<CompletedBooking["source"]>("source") ?? "N/A";
    },
  },
  {
    accessorKey: "paymentStatus",
    enableHiding: true,
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="total" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <p className=" flex w-full items-center gap-x-1">
          {amountFormatter(Number(row.getValue<CompletedBooking["totalPrice"]>("totalPrice")))}
          {row.getValue<CompletedBooking["paymentStatus"]>("paymentStatus")?.toLowerCase() === "paid" ? (
            <TooltipComponent content="Paid">
              <CheckCircle weight="fill" className=" size-4 fill-green-600" />
            </TooltipComponent>
          ) : row.getValue<CompletedBooking["paymentStatus"]>("paymentStatus")?.toLowerCase() === "pending" ? (
            <TooltipComponent content="Unpaid">
              <XCircle weight="fill" className="size-4 fill-slate-400" />
            </TooltipComponent>
          ) : (
            <></>
          )}
        </p>
      );
    },
  },
  // {
  //   accessorKey: "dueAmount",
  //   header: ({ column }) => {
  //     return (
  //       <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
  //         <LocalizeText message="dues" />
  //         <CaretUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     const amount = Number(row.getValue<CompletedBooking["dueAmount"]>("dueAmount") ?? 0);
  //     return (
  //       <TranslatedText>
  //         {(t) => <p className="flex">{amount ? amountFormatter(amount) : t("No dues")}</p>}
  //       </TranslatedText>
  //     );
  //   },
  // },
  {
    accessorKey: "notes",
    header: () => <LocalizeText message="notes" />,
    cell: ({ row }) => {
      return row.getValue<CompletedBooking["notes"]>("notes") ?? "N/A";
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return (
        <DataTableRowActions row={row} showFullMenu={false}>
          <></>
        </DataTableRowActions>
      );
    },
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: "en" | "ar") => React.ReactNode;
}) => {
  const t = useTranslations("bookings.columns");
  const locale = useLocale() as "en" | "ar";
  return <>{children(t, locale)}</>;
};

const FormattedDropoffTime = ({ row }: { row: Row<CompletedBooking> }) => {
  const locale = useLocale();
  const formatTime = formattedPickupTime(
    row.getValue<CompletedBooking["dropOffDateTime"]>("dropOffDateTime") ?? 0,
    "COMPLETED",
    locale === "ar" ? arSA : enUS
  );
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
      <span className={formatTime.colorClass}>{formatTime.displayText}</span>
    </div>
  );
};
