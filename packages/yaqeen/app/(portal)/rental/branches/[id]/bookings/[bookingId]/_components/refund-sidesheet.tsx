import CopyLink from "@/components/customized/copy-link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Bank, Calendar, CurrencyCircleDollar, Envelope, Printer } from "@phosphor-icons/react/dist/ssr";
import { capitalize, round } from "lodash-es";
import { getBadgeColor } from "../../_components/payments-columns";
import { toNormal } from "@/lib/utils";
import { CashRegister, Money } from "@phosphor-icons/react";
import { DownloadPaymentReciept, PAYMENT_TYPE } from "./print-payment";
import { useTranslations } from "next-intl";
interface PaymentDetailsProps {
  amount: string;
  approvedTime: string | null;
  paymentType: string;
  status: string;
  accountHolderName?: string;
  ibanNumber?: string;
  bankName?: string;
  ibanLetterFile?: string;
  recipientBankDetail?: {
    recipientAccountName?: string;
    recipientIBAN?: string;
    recipientBankName?: string;
    ibanLetterImageUrl?: string;
  };
  recordedBy?: string;
  id: number;
  type: string;
}

export default function RefundDetailsSheet({
  amount,
  approvedTime,
  paymentType,
  status,
  recipientBankDetail,
  recordedBy,
  id,
  type,
}: PaymentDetailsProps) {
  const t = useTranslations("payment");
  const { recipientAccountName, recipientIBAN, recipientBankName, ibanLetterImageUrl } = recipientBankDetail ?? {};

  return (
    <div className="w-full max-w-md ">
      <div className="space-y-6 p-6 pt-2">
        <div className="flex gap-2" dir="ltr">
          {type.toLocaleLowerCase() === PAYMENT_TYPE.REFUND.toLocaleLowerCase() && (
            <DownloadPaymentReciept
              paymentId={id?.toString()}
              type={type}
              variant="outline"
              size="icon"
              className="flex items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted"
            >
              <Printer className="h-5 w-5" />
            </DownloadPaymentReciept>
          )}
          <Button
            variant="outline"
            size="icon"
            className="flex items-center justify-center rounded-lg border border-slate-300 p-2 hover:bg-muted"
          >
            <Envelope className="h-5 w-5" />
          </Button>
          <CopyLink />
        </div>
        <h1 className="text-2xl font-semibold">{t("sidesheet.refund_title")}</h1>
      </div>
      <div className="space-y-6 ">
        <div className="flex items-center gap-2 px-6">
          <CurrencyCircleDollar className="h-6 w-6" />
          <span className="text-base leading-6 text-slate-900">
            SAR {round(Math.abs(Number(amount)), 2).toLocaleString()}
          </span>
          <Badge
            variant="secondary"
            className={`pointer-events-none rounded-full px-3 font-normal capitalize ${getBadgeColor(status ?? "Pending")}`}
          >
            {capitalize(status) ?? "Pending"}
          </Badge>
        </div>
        <div className="flex items-center gap-2 px-6">
          {paymentType?.toLowerCase() !== "cash" && <Bank className="h-6 w-6" />}
          {paymentType?.toLowerCase() === "cash" && <Money className="h-6 w-6" />}
          <span className="capitalize">{toNormal(paymentType?.replace(/[^a-zA-Z]/g, " "))}</span>
        </div>

        {approvedTime && (
          <div className="flex items-center gap-2 px-6">
            <Calendar className="h-6 w-6" />
            <span>{approvedTime}</span>
          </div>
        )}
        {recordedBy && (
          <div className="flex items-center gap-2 px-6">
            <CashRegister className="h-6 w-6" />
            <span>
              {t("sidesheet.record_by")}: {recordedBy}
            </span>
          </div>
        )}

        {status?.toLowerCase() === "success" && paymentType?.toLowerCase() !== "cash" && (
          <div className="space-y-4">
            <Separator />
            <div className="space-y-2 px-6">
              <p className="text-sm text-muted-foreground">{t("sidesheet.account_holder_name")}</p>
              <p className="text-sm font-medium">{recipientAccountName}</p>
            </div>
            <Separator />
            <div className="space-y-2 px-6">
              <p className="text-sm text-muted-foreground">{t("sidesheet.iban_number")}</p>
              <p className="font-mono text-sm font-medium">{recipientIBAN}</p>
            </div>
            <Separator />
            <div className="space-y-2 px-6">
              <p className="text-sm text-muted-foreground">{t("sidesheet.bank_name")}</p>
              <p className="text-sm font-medium">{recipientBankName}</p>
            </div>
            <Separator />
            <div className="space-y-2 px-6">
              <p className="text-sm text-muted-foreground">{t("sidesheet.iban_letter")}</p>
              <a href={ibanLetterImageUrl} className="text-sm text-blue-600 hover:underline">
                {t("sidesheet.preview")}
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
