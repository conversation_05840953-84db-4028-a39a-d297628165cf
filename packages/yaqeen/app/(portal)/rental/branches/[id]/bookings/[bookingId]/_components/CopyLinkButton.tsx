"use client";

import { toast } from "@/lib/hooks/use-toast";
import { Copy } from "@phosphor-icons/react/dist/ssr";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useEffect, useState, type ReactNode } from "react";

interface CopyLinkButtonProps {
  variant?: string;
  icon?: ReactNode;
  label?: string;
  className?: string;
  link?: string;
}

export const CopyLinkButton = ({
  variant = "outline",
  icon = <Copy className="h-4 w-4" />,
  label = "Copy Link",
  className = "",
  link = "",
}: CopyLinkButtonProps) => {
  const [cLink, setCLink] = useState(link);
  useEffect(() => {
    if (link === "") {
      setCLink(window.location.href);
    }
  }, [link]);
  const handleCopy = async () => {
    await navigator.clipboard.writeText(cLink);
    toast({
      title: "Link copied",
      description: "The link has been copied to your clipboard.",
      duration: 2000,
    });
  };

  const sharedProps = {
    onClick: handleCopy,
    className: `flex items-center gap-2 ${className}`,
  };

  if (variant === "dropdown") {
    return (
      <DropdownMenuItem {...sharedProps}>
        {icon}
        {label}
      </DropdownMenuItem>
    );
  }

  return (
    <Button variant={variant as "outline"} {...sharedProps}>
      {icon}
      {label}
    </Button>
  );
};
