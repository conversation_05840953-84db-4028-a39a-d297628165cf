import { atom, type WritableAtom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { type NavItem } from "../_components/constants";

const atomInstances = new Map<number, WritableAtom<NavItem[], [string], void>>();

export function atomWithBookingNav(bookingId: number, initialNavItems: NavItem[]) {
  if (atomInstances.has(bookingId)) {
    return atomInstances.get(bookingId)!;
  }

  const baseAtom = atomWithStorage<NavItem[]>(`booking-${bookingId}-nav-items`, initialNavItems);

  const derivedAtom = atom(
    (get) => get(baseAtom),
    (get, set, updates: string) => {
      const currentNavItems = get(baseAtom);
      const updatedNavItems = currentNavItems.map((item) => {
        if (updates.includes(item.href)) {
          return { ...item, completed: true };
        }
        return item;
      });
      set(baseAtom, updatedNavItems);
    },
  );

  atomInstances.set(bookingId, derivedAtom);
  return derivedAtom;
}

export const remainingPriceAtom = atom<string | null>(null);
