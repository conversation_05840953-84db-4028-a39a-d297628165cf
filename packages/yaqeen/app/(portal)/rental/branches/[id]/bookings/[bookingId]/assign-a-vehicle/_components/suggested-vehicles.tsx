"use client";

import { startTransition, useState } from "react";
import { useAtom } from "jotai";
import { groupBy, sortBy, difference } from "lodash-es";
import { ChevronRight, ExternalLink } from "lucide-react";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";

import { useProgressBar } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  selectedVehicleAtom,
  showSuggestedVehiclesAtom,
  type UpgradignVehicle<PERSON>tom,
  upgradingVehicleAtom,
} from "../atoms";
import { type PreferenceType, type Vehicle, VehicleCard } from "./vehicle-card";
import ReadyVehicles from "./ready-vehicles";
import { type Route } from "next";
import { VehicleUpgrade } from "./vehicle-upgrade";
import { useLocale, useTranslations } from "next-intl";
import { Label } from "@/components/ui/label";
import { parseAsString, useQueryStates } from "nuqs";

interface SuggestedVehiclesProps {
  vehicles: Vehicle[];
  assignedVehicle: Vehicle | null;
  show?: boolean;
}

export default function SuggestedVehicles({ vehicles, assignedVehicle, show }: SuggestedVehiclesProps) {
  const t = useTranslations("createAgreement");
  const locale = useLocale();
  const params = useParams();
  const bookingId: string = (params.bookingId ?? "") as string;

  const [{ addOns }, setQueryParams] = useQueryStates({
    addOns: parseAsString,
    discountCode: parseAsString,
  });

  const [readyVehicles, setReadyVehicles] = useState<Vehicle[]>([]);
  const [readyVehicleFilters, setReadyVehicleFilters] = useState<{ groups: string[] }>({ groups: [] });
  const [showReadyVehicleSheet, setShowReadyVehicleSheet] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const [showDowngradeConfirmDialog, setShowDowngradeConfirmDialog] = useState(false);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showUnavailableAddOnsDialog, setShowUnavailableAddOnsDialog] = useState(false);

  const [upgradingVehicle, setUpgradingVehicle] = useAtom<UpgradignVehicleAtom>(upgradingVehicleAtom);
  const [showSuggestedVehicles, setShowSuggestedVehicles] = useAtom(showSuggestedVehiclesAtom);

  const [selectedVehicleState, setSelectedVehicle] = useAtom(selectedVehicleAtom);
  const selectedVehicle = selectedVehicleState ? selectedVehicleState[bookingId] : null;

  const filteredVehicles = vehicles.filter((v) => v.plateNo !== selectedVehicle?.plateNo);

  // Add the assigned vehicle to the start of filtered vehicles list if:
  // 1. There is an assigned vehicle (comes from booking details API, not included in suggested vehicles API) AND
  // 2. The assigned vehicle is different from the currently selected vehicle
  if (assignedVehicle && assignedVehicle.plateNo !== selectedVehicle?.plateNo) {
    filteredVehicles.unshift(assignedVehicle);
  }

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const progress = useProgressBar();
  const hasExactMatch = vehicles.some((v) => v.preferenceType === "EXACT_MATCH");

  let groupedVehicles: Record<string, Vehicle[]> = {};

  if (hasExactMatch) {
    groupedVehicles = groupBy(filteredVehicles, "preferenceType") as Record<PreferenceType, Vehicle[]>;
  }

  if (!hasExactMatch) {
    groupedVehicles = groupBy(filteredVehicles, (v) => v.model!.id);
  }

  const representativeVehicles = Object.values(groupedVehicles).map((vehicles) => {
    const sortedVehiclesByPrice = sortBy(vehicles, (v) =>
      parseFloat((v.offer?.bookingPriceDifference ?? 0).toString())
    );

    return {
      vehicle: sortedVehiclesByPrice[0]!,
      vehicles: sortedVehiclesByPrice.slice(1),
      totalCount: sortedVehiclesByPrice.length - 1,
    };
  });

  const PREFERENCE_TYPE_ORDER: PreferenceType[] = ["EXACT_MATCH", "SIMILAR", "UPGRADE", "DOWNGRADE"];
  const sortedVehicles = sortBy(representativeVehicles, (entity) =>
    PREFERENCE_TYPE_ORDER.indexOf(entity.vehicle.preferenceType)
  );

  if (!showSuggestedVehicles && !show) return null;

  const handleSelectVehicle = (vehicleDetails: Vehicle) => {
    setShowSuggestedVehicles(false);
    setShowReadyVehicleSheet(false);
    setShowUpgradeDialog(false);
    setShowUnavailableAddOnsDialog(false);
    setShowConfirmDialog(false);
    setShowDowngradeConfirmDialog(false);
    setUpgradingVehicle(null);

    progress.start();

    const current = new URLSearchParams(Array.from(searchParams.entries()));
    current.set("plateNo", vehicleDetails.plateNo);
    current.set("vehicleGroupId", vehicleDetails.model?.groupResponse?.id?.toString() ?? "");

    const search = current.toString();
    const query = search ? `?${search}` : "";

    startTransition(() => {
      router.replace((pathname + query) as Route);
      progress.done();
    });

    setSelectedVehicle?.({ ...selectedVehicleState, [bookingId]: vehicleDetails });
  };

  const onVehicleSelect = (type = "", vehicle: Vehicle) => {
    if (assignedVehicle && assignedVehicle?.plateNo !== vehicle.plateNo) {
      setUpgradingVehicle({ vehicle, type });
      if (vehicle.preferenceType === "DOWNGRADE") {
        setShowDowngradeConfirmDialog(true);
      } else setShowConfirmDialog(true);
    } else {
      handleSelectVehicle(vehicle);
      setUpgradingVehicle(null);
    }
    setShowUpgradeDialog(false);
    setShowUnavailableAddOnsDialog(false);
  };

  const onReadyVehicleSheetOpen = (open: boolean) => {
    setShowReadyVehicleSheet(open);
    setReadyVehicles(sortedVehicles.map((v) => v.vehicles).flat());
    if (!open) setReadyVehicleFilters({ ...readyVehicleFilters, groups: [] });
  };

  const handleViewMore = (vehicle: Vehicle, vehicles: Vehicle[]) => {
    const uniqueGroupIds = Array.from(
      new Set([vehicle, ...vehicles].map((v) => v.model?.groupResponse?.id?.toString() ?? ""))
    );
    setReadyVehicleFilters({ ...readyVehicleFilters, groups: uniqueGroupIds });
    setReadyVehicles(vehicles);
    setShowReadyVehicleSheet(true);
  };

  const closeConfirmationDialog = () => {
    setShowConfirmDialog(false);
    setShowDowngradeConfirmDialog(false);
    setUpgradingVehicle(null);
    setShowUnavailableAddOnsDialog(false);
  };

  const upgradeVehicleSelect = (vehicle: Vehicle) => {
    setUpgradingVehicle({ vehicle });
    if (vehicle?.unAvailableAddons?.length || vehicle?.isPromoApplied) {
      setShowUnavailableAddOnsDialog(true);
    } else {
      setShowUpgradeDialog(true);
    }
  };

  return (
    <>
      <Card className="max-w-3xl overflow-hidden !p-0">
        <CardHeader className="flex flex-row items-center justify-between border-b p-4">
          <h2 className="text-lg font-semibold">{t("assignVehicle.suggestedVehicles")}</h2>

          <Sheet open={showReadyVehicleSheet} onOpenChange={onReadyVehicleSheetOpen}>
            <SheetTrigger asChild>
              <Button variant="outline">
                {t("assignVehicle.viewallReadyVehicles")}
                <ChevronRight className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full p-0 pt-6 ring-0 sm:w-[786px] sm:max-w-full">
              <SheetHeader className="px-4">
                <SheetDescription>
                  <Button variant="outline" size="sm" disabled>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    {t("assignVehicle.allBranchesVehicles")}
                  </Button>
                </SheetDescription>
                <SheetTitle className="py-4">
                  {t("assignVehicle.allReadyVehicles")} ({readyVehicles.length})
                </SheetTitle>
              </SheetHeader>
              <ReadyVehicles
                data={readyVehicles}
                filters={readyVehicleFilters}
                onVehicleSelect={(type, vehicle) => {
                  if (type === "upgrade") {
                    upgradeVehicleSelect(vehicle);
                  } else onVehicleSelect(type, vehicle);
                }}
              />
            </SheetContent>
          </Sheet>
        </CardHeader>
        {sortedVehicles.map(({ vehicles, vehicle, totalCount }, index) => (
          <div key={vehicle.plateNo}>
            <VehicleCard
              vehicleDetails={vehicle}
              groupCount={totalCount}
              renderActionButton={(vehicleDetails) =>
                vehicleDetails.preferenceType === "UPGRADE" ? (
                  <Button
                    variant="outline"
                    onClick={() => {
                      upgradeVehicleSelect(vehicle);
                    }}
                    className="flex h-auto items-center gap-2 rounded-md py-0"
                  >
                    {t("assignVehicle.cta.upgrade")}
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-lg"
                    onClick={() => onVehicleSelect("select", vehicleDetails)}
                  >
                    {t("assignVehicle.cta.select")}
                  </Button>
                )
              }
              onViewMore={() => handleViewMore(vehicle, vehicles)}
            />
            {index < sortedVehicles.length - 1 && <Separator />}
          </div>
        ))}
      </Card>

      <Dialog open={showConfirmDialog}>
        <DialogContent className="p-0 sm:max-w-md">
          <DialogHeader className="p-4">
            <DialogTitle>{t("assignVehicle.replaceModal.title")}</DialogTitle>
            <DialogDescription>{t("assignVehicle.replaceModal.description")}</DialogDescription>
          </DialogHeader>
          <Separator className="" />
          <DialogFooter className="p-4 sm:justify-end">
            <Button onClick={closeConfirmationDialog} variant="outline">
              {t("assignVehicle.cta.cancel")}
            </Button>

            <Button
              onClick={() => {
                if (upgradingVehicle?.vehicle) {
                  const entity: Vehicle = upgradingVehicle?.vehicle;
                  if (entity.unAvailableAddons?.length || entity?.isPromoApplied) {
                    progress.start();
                    startTransition(() => {
                      const unavailableAddOnIDs: number[] = entity?.unAvailableAddons?.map((addOn) => addOn.id) || [];
                      const parsedAddonParams = typeof addOns === "string" ? addOns.split(",").map(Number) : addOns;
                      const filteredBookingAddOnIDs = difference<number>(parsedAddonParams ?? [], unavailableAddOnIDs);

                      if (entity?.discountDetail && unavailableAddOnIDs?.length) {
                        void setQueryParams({
                          addOns: filteredBookingAddOnIDs.join(","),
                          discountCode: "",
                        });
                      } else if (unavailableAddOnIDs?.length) {
                        void setQueryParams({
                          addOns: filteredBookingAddOnIDs.join(","),
                        });
                      } else if (entity?.discountDetail) {
                        void setQueryParams({
                          discountCode: "",
                        });
                      }
                      handleSelectVehicle(entity);
                      progress.done();
                    });
                  } else {
                    handleSelectVehicle(entity);
                  }
                }
              }}
              type="button"
            >
              {t("assignVehicle.cta.replace")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showDowngradeConfirmDialog}>
        <DialogContent className="p-0 sm:max-w-md">
          <DialogHeader className="p-4">
            <DialogTitle>{t("assignVehicle.downgradeModal.title")}</DialogTitle>
            <DialogDescription>{t("assignVehicle.downgradeModal.description")}</DialogDescription>
          </DialogHeader>
          <Separator className="" />
          <DialogFooter className="p-4 sm:justify-end">
            <Button onClick={closeConfirmationDialog} variant="outline">
              {t("assignVehicle.cta.cancel")}
            </Button>

            <Button
              onClick={() => {
                if (upgradingVehicle?.vehicle) {
                  handleSelectVehicle(upgradingVehicle.vehicle);
                }
              }}
              type="button"
            >
              {t("assignVehicle.cta.confirmDowngrade")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showUnavailableAddOnsDialog}>
        <DialogContent className="p-0 sm:max-w-md">
          <DialogHeader className="p-4">
            <DialogTitle>{t("assignVehicle.vehicleChange.title")}</DialogTitle>
            <DialogDescription>
              <Label>{t("assignVehicle.vehicleChange.description")}</Label>
            </DialogDescription>
          </DialogHeader>
          <Separator />

          <ul className="ml-4 list-disc p-4 text-muted">
            {upgradingVehicle?.vehicle?.unAvailableAddons?.map((addOn) => (
              <li className="text-sm text-slate-600" key={`unavailableAddon-${addOn.id}`}>
                {addOn.name[locale as keyof typeof addOn.name]}
              </li>
            ))}
            {upgradingVehicle?.vehicle?.isPromoApplied && (
              <li className="text-sm text-slate-600">{upgradingVehicle?.vehicle?.discountDetail?.promoCode}</li>
            )}
          </ul>
          <Separator />
          <DialogFooter className="p-4 sm:justify-end">
            <Button onClick={closeConfirmationDialog} variant="outline">
              {t("assignVehicle.cta.cancel")}
            </Button>

            <Button
              onClick={() => {
                if (upgradingVehicle?.vehicle) {
                  setShowUpgradeDialog(true);
                  setShowUnavailableAddOnsDialog(false);
                  // handleSelectVehicle(upgradingVehicle.vehicle);
                }
              }}
              type="button"
            >
              {t("assignVehicle.vehicleChange.cta.submit")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <VehicleUpgrade
        open={showUpgradeDialog}
        onSelect={onVehicleSelect}
        onClose={() => {
          setShowUpgradeDialog(false);
          setUpgradingVehicle(null);
          setShowConfirmDialog(false);
          setShowUnavailableAddOnsDialog(false);
        }}
      />
    </>
  );
}
