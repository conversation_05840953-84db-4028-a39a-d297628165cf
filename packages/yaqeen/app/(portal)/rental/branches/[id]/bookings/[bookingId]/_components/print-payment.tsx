"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { downloadRefundInvoice, downloadPaymentInvoice } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";

export enum PAYMENT_TYPE {
  PAYMENT = "payment",
  REFUND = "refund",
}

export const DownloadPaymentReciept = ({
  variant,
  size,
  paymentId,
  type,
  className,
  children,
}: {
  variant?: string;
  size?: string;
  paymentId: string;
  type: string;
  className?: string;
  children: React.ReactNode;
}) => {
  const t = useTranslations("payment");
  className = className ?? "";
  variant = variant ?? "outline";
  size = size ?? "icon";
  const { toast } = useToast();
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownloadReciept = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (!paymentId) {
      toast({
        title: t("download.downloadFailed"),
        description: t("download.receiptNotFound"),
        variant: "destructive",
      });
      return;
    }

    setIsDownloading(true);
    try {
      const response =
        type.toLocaleLowerCase() === PAYMENT_TYPE.REFUND.toLocaleLowerCase()
          ? await downloadRefundInvoice(paymentId)
          : await downloadPaymentInvoice(paymentId);
      if (response.status !== 200) {
        toast({
          title: t("download.downloadFailed"),
          description: t("download.failedToDownloadReceipt"),
          variant: "destructive",
        });
        return;
      }
      const data = response.body;

      const printWindow = window.open("", "_blank", "width=" + window.screen.width + ",height=" + window.screen.height);
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.title = `${type} Reciept`;
        printWindow.document.write(data);
        printWindow.document.close();

        // Wait for the content to load before printing
        printWindow.onload = () => {
          printWindow.focus();
          printWindow.print();
        };
      } else {
        toast({
          title: t("download.printFailed"),
          description: t("download.failedToOpenPrintWindow"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error downloading reciept:", error);
      toast({
        title: t("download.downloadFailed"),
        description: error instanceof Error ? error.message : t("download.unexpectedError"),
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      variant={(variant as "default" | "link" | "destructive" | "outline" | "secondary" | "ghost") ?? "outline"}
      className={cn("", className)}
      type="button"
      size="icon"
      onClick={handleDownloadReciept}
      disabled={isDownloading}
    >
      {isDownloading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : children}
    </Button>
  );
};
