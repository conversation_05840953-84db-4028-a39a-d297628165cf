"use client";

import { useState } from "react";
import RefundModal from "../payment/_components/refund-modal";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Money } from "@phosphor-icons/react/dist/ssr";

interface PaymentRefundButtonProps {
  bookingId: string | number;
  amount: number;
  hasRefunds: boolean;
  show: boolean;
}

export const PaymentRefundButton = ({ show, bookingId, amount, hasRefunds }: PaymentRefundButtonProps) => {
  const t = useTranslations("createAgreement");
  const [refundModalOpen, setRefundModalOpen] = useState(show ? true : false);
  return (
    <>
      <Button variant="outline" disabled={hasRefunds || amount <= 0} onClick={() => setRefundModalOpen(true)}>
        <Money className="mr-2 h-4 w-4" /> {t("payment.discount.cta.issueRefund")}
      </Button>
      <RefundModal
        bookingId={String(bookingId)}
        isOpen={refundModalOpen}
        onClose={(cancel) => cancel && setRefundModalOpen(false)}
        amount={Number(Math.abs(Number(amount) || 0))}
        sourceRoute={`bookings/${bookingId}`}
      />
    </>
  );
};
