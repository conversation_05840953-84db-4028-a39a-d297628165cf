"use client";

import CopyLink from "@/components/customized/copy-link";
import OpenLink from "@/components/customized/open-link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { SheetClose } from "@/components/ui/sheet";
import type { TranslationValues } from "next-intl";
import { convertPlateToArabic, formatCleaningTime, formatInspectionTime, shimmer, toBase64 } from "@/lib/utils";
import { GasPump, Gauge, MagnifyingGlass, Sparkle } from "@phosphor-icons/react/dist/ssr";
import { useAtom } from "jotai";
import Image from "next/image";
import type { PreferenceType, Vehicle } from "../assign-a-vehicle/_components/vehicle-card";
import {
  selectedVehicleAtom,
  showSuggestedVehiclesAtom,
  type UpgradignVehicle<PERSON>tom,
  upgradingVeh<PERSON><PERSON><PERSON>,
} from "../assign-a-vehicle/atoms";
import { useParams } from "next/navigation";
import { VehiclePlate } from "@rental-components/vehicle-plate";
import { useLocale, useTranslations } from "next-intl";
import VehicleSpecs from "./vehicle-specs";

export function VehicleDetailsSideSheet({ vehicleDetails }: { vehicleDetails: Vehicle }) {
  const params = useParams();
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("vehicles");

  const PREFERENCE_TYPE_LABELS: Record<PreferenceType, string> = {
    EXACT_MATCH: t("preferenceTypes.exactMatch"),
    SIMILAR: t("preferenceTypes.similar"),
    UPGRADE: t("preferenceTypes.upgrade"),
    DOWNGRADE: t("preferenceTypes.downgrade"),
    NONE: t("preferenceTypes.none"),
  } as const;

  const bookingId: string = (params.bookingId ?? "") as string;
  const modelYear = vehicleDetails.modelYear ?? "";
  const model = vehicleDetails.model;
  const fuelLevel = vehicleDetails.fuelLevel ?? 0;
  const modelName = model?.name[locale] || "";
  const imageUrl = model?.primaryImageUrl ?? null;
  const carGroup = model?.groupResponse?.code ?? "N/A";
  const [plateNumber, plateLetters] = vehicleDetails.plateNo.split(" ");
  const plateNoAr = convertPlateToArabic(vehicleDetails.plateNo);

  const [selectedVehicleState, setSelectedVehicle] = useAtom(selectedVehicleAtom);
  const selectedVehicle = selectedVehicleState ? selectedVehicleState[bookingId] : null;

  const [, setShowSuggestedVehicles] = useAtom(showSuggestedVehiclesAtom);
  const [, setUpgradingVehicle] = useAtom<UpgradignVehicleAtom>(upgradingVehicleAtom);

  return (
    <div className="flex h-full flex-col" dir={locale === "ar" ? "rtl" : "ltr"}>
      {/* Header Section */}
      <div className="flex items-center justify-between px-6 pb-4">
        <div className="space-y-1">
          <div className="flex gap-2">
            <CopyLink />
            <OpenLink url={`#`} className="flex items-center gap-2" />
          </div>
        </div>
      </div>

      <ScrollArea className="hide-scrollbar flex-col">
        <div className="relative mb-6 overflow-hidden rounded-lg px-6 text-center">
          <Image
            src={imageUrl ?? "/static/<EMAIL>"}
            alt={modelName}
            className="inline object-contain"
            placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(200, 96))}`}
            width={200}
            height={96}
            priority
            onError={(e) => {
              e.currentTarget.src = "/static/<EMAIL>";
            }}
          />
        </div>
        {/* Vehicle Info */}
        <div className="mb-6 px-6 text-sm">
          <div className="mb-2 flex items-center gap-2">
            <Badge className="pointer-events-none bg-white font-medium" variant="outline">
              {t("group")}: {carGroup}
            </Badge>
            <Badge className="pointer-events-none bg-white font-medium" variant="outline">
              {PREFERENCE_TYPE_LABELS[vehicleDetails.preferenceType] ?? t("na")}
            </Badge>
          </div>
          <h2 className="mb-4 text-2xl font-semibold">
            {modelName} {modelYear}
          </h2>
          <VehiclePlate
            plateNumber={plateNumber}
            plateNoAr={plateNoAr}
            plateLetters={plateLetters}
            className="w-full"
          />
        </div>
        <Separator className="h-[4px] bg-slate-200" />
        {/* Vehicle Status */}
        <div className="mb-6 px-6 py-4">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold">{t("vehicleStatus")}</h3>
            <Badge className="pointer-events-none bg-lumi-200 font-medium text-slate-900">{t("readyToRent")}</Badge>
          </div>
          <div className="space-y-4 text-sm">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Gauge className="h-5 w-5" />
                {vehicleDetails.odometerReading?.toLocaleString() ?? "0"} {t("km")}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <GasPump className="h-5 w-5" />
                {fuelLevel?.toLocaleString() ?? "0"}/4
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <MagnifyingGlass className="h-5 w-5" />
                {
                  formatInspectionTime(
                    vehicleDetails.lastInspected ?? null,
                    t as (key: string, values?: TranslationValues) => string
                  ).displayText
                }
              </span>
              <button className="text-blue-600">{t("viewReport")}</button>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Sparkle className="h-5 w-5" />
                {
                  formatCleaningTime(
                    vehicleDetails.lastCleaned ?? null,
                    t as (key: string, values?: TranslationValues) => string
                  ).displayText
                }
              </span>
            </div>
          </div>
        </div>
        <Separator className="h-[4px] bg-slate-200" />
        {/* Features (Specs) */}
        {/* TODO: Hide until desing and product align on teh features to show
         <div className="p-6 text-sm">
          <h3 className="mb-4 text-lg font-semibold">{t("features")}</h3>
          <VehicleSpecs plateNo={vehicleDetails.plateNo} />
        </div> */}
      </ScrollArea>
      <div className="mt-auto bg-white px-6 py-4 shadow-[0px_-4px_8px_-2px_rgba(16,24,40,0.10),0px_-2px_4px_-2px_rgba(16,24,40,0.06)]">
        <SheetClose asChild>
          <section className="flex gap-4">
            <Button
              className="w-full font-medium text-slate-900"
              onClick={() => {
                if (vehicleDetails.preferenceType === "UPGRADE") {
                  setUpgradingVehicle({ vehicle: vehicleDetails });
                } else {
                  if (vehicleDetails.plateNo !== selectedVehicle?.plateNo) {
                    setUpgradingVehicle({ vehicle: vehicleDetails, type: "replace" });
                  } else {
                    setSelectedVehicle({ ...selectedVehicleState, [bookingId]: vehicleDetails });
                    setShowSuggestedVehicles(false);
                  }
                }
              }}
            >
              {vehicleDetails.preferenceType === "UPGRADE" ? t("upgradeVehicle") : t("selectVehicle")}
            </Button>
          </section>
        </SheetClose>
      </div>
    </div>
  );
}
