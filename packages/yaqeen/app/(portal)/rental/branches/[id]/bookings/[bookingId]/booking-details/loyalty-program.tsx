import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { api } from "@/api";
import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";

const LoyaltyCard = ({ children }: { children: React.ReactNode }) => {
  const t = useTranslations("createAgreement");

  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="px-4">
        <CardTitle className="text-lg font-bold">{t("bookingDetails.loayltyProgramTitle")}</CardTitle>
      </CardHeader>
      <Separator />
      {children}
    </Card>
  );
};

export default async function LoyaltyProgram({ bookingId }: { bookingId: string }) {
  const t = await getTranslations("createAgreement");

  const [booking] = await Promise.all([
    api.bookingDetails.getBookingById({
      params: {
        id: Number(bookingId),
      },
    }),
    api.branch.getBranchList(),
  ]);

  if (booking?.status !== 200) {
    throw new Error(`Error: ${booking.status}`);
  }

  const [loyalty] = await Promise.all([
    api.bookingDetails.getLoyaltyByReference({
      params: {
        referenceNo: String(booking.body.referenceNo),
      },
    }),
  ]);

  if (loyalty?.status === 404) {
    return <></>;
  }

  if (loyalty?.status !== 200) {
    throw new Error(`Error: ${loyalty.status}`);
  }

  const { provider, customerIdentifier, value, valueType } = loyalty.body;

  return (
    <LoyaltyCard>
      <CardContent className="flex w-full flex-col items-center p-0">
        <div className="flex w-full p-4 text-sm">
          <div className="flex w-full flex-col gap-y-2">
            <span className=" text-slate-500">{t("bookingDetails.loayltyProgram")}</span>
            <span className=" text-slate-900 ">{provider}</span>
          </div>
          <div className="flex w-full flex-col gap-y-2">
            <span className=" text-slate-500">{t("bookingDetails.accountNumber")}</span>
            <span className=" text-slate-900 ">{customerIdentifier}</span>
          </div>
          <div className="flex w-full flex-col gap-y-2">
            <span className=" text-slate-500">{t("bookingDetails.points")}</span>
            <span className=" text-slate-900 ">{value ?? valueType}</span>
          </div>
        </div>
      </CardContent>
    </LoyaltyCard>
  );
}
