import { ProgressBarLink } from "@/components/progress-bar";
import { type Route } from "next";
import * as React from "react";
import { useTranslations } from "next-intl";

interface QuickFiltersProps {
  filters: {
    label: string;
    query: string;
    count: number;
    translationKey?: string;
  }[];
}

export const QuickFilters = ({ filters }: QuickFiltersProps) => {
  const t = useTranslations("bookings.filters");

  return (
    <nav className="flex items-center gap-4 gap-x-4 px-6 pt-4 text-sm font-normal text-blue-600 ">
      {filters.map((filter) => {
        // @ts-expect-error TODO useTranslations is not typed correctly
        const translatedLabel = filter.translationKey ? t(filter.translationKey) : filter.label;
        return (
          <ProgressBarLink key={filter.query} href={`${filter.query}` as Route} className="cursor-pointer">
            {translatedLabel} ({filter.count})
          </ProgressBarLink>
        );
      })}
    </nav>
  );
};
