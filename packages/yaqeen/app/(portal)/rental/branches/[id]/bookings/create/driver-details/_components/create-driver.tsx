"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { type Country, CountryDropdown } from "@/components/ui/country-dropdown";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/lib/hooks/use-toast";
import { driverAttachOrCreate } from "@/lib/actions";
import { GREGORIAN_MONTHS, HIJRI_MONTHS, HIJ<PERSON>_MONTHS_ARABIC } from "@/lib/constants";
import { getFullDate, isValidDate, makeDateValid } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { capitalize } from "lodash-es";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { startTransition, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { type z } from "zod";
import { useLocale, useTranslations } from "next-intl";

import { IFRAME_URL } from "../../../[bookingId]/constants";
import CustomDatePicker from "../../../[bookingId]/driver-details/_components/date-picker";
import { driverFormSchema } from "../../../[bookingId]/driver-details/schema";
import { type IDriverFormValues } from "../../../[bookingId]/driver-details/types";
import { useProgressBar } from "@/components/progress-bar";
import { ID_TYPES_DROPDOWN, type ID_TYPES_DROPDOWN_TYPE } from "../../../_components/constants";
import { useQueryState } from "nuqs";
import clsx from "clsx";

type DriverFormValues = z.infer<typeof driverFormSchema>;
// create a type which dont have driverUid, from IDriverFormValues
type CreateDriverFormValues = Omit<IDriverFormValues, "driverUid">;

interface CreateDriverProps {
  countries: Country[];
}

export default function CreateDriver({ countries: _countries }: CreateDriverProps) {
  const countries = _countries;
  const router = useRouter();
  const { toast } = useToast();
  const progress = useProgressBar();
  const searchParams = useSearchParams();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [, setIdTypeState] = useState("SAUDI_NATIONAL");
  const [filteredIdTypes, setFilteredIdTypes] = useState<typeof ID_TYPES_DROPDOWN>(ID_TYPES_DROPDOWN);
  const [_idtype, setIdType] = useState("SAUDI_NATIONAL");
  const locale = useLocale();
  const t = useTranslations("drivers");
  const [, setDriverName] = useQueryState("driverName", {
    shallow: false,
  });
  const [, setDriverMode] = useQueryState("driverMode", {
    shallow: false,
  });
  const [, setDriverUid] = useQueryState("driverUid", {
    shallow: false,
  });

  const quoteId = searchParams.get("quoteId");
  const defaultNationality = "Saudi Arabia";

  const findCountryId = (countryName: string) => {
    return countries.find((country) => country.name === countryName)?.id ?? 2;
  };

  const findCountry = (countryName: string) => {
    return countries.find((country) => country.name === countryName) ?? countries[0]!;
  };

  useEffect(() => {
    if (countries.length > 0) {
      setFilteredIdTypes(updateIdType(findCountry(defaultNationality)));
    }
  }, [countries]);

  const defaultValues: Partial<DriverFormValues> = {
    title: "Mr",
    firstName: "",
    lastName: "",
    email: "",
    nationality: countries.length > 0 ? "Saudi Arabia" : "",
    countryCode: "",
    mobileNumber: "",
    idType: "SAUDI_NATIONAL",
    dob: "",
    hijrahDob: "",
    address: "",
    documentNo: "",
    idIssuedCountry: countries.length > 0 ? "Saudi Arabia" : "",
    documentExpiry: "",
    documentHijrahExpiry: "",
    licenseNo: "",
    licenseCountry: countries.length > 0 ? "Saudi Arabia" : "",
    licenseExpiry: "",
    licenseHijrahExpiry: "",
    borderNumber: "",
  };

  const form = useForm<DriverFormValues>({
    resolver: zodResolver(driverFormSchema),
    defaultValues,
    mode: "onChange",
  });

  // Update form values when countries are loaded
  useEffect(() => {
    if (countries.length > 0) {
      form.setValue("nationality", "Saudi Arabia");
      form.setValue("idIssuedCountry", "Saudi Arabia");
      form.setValue("licenseCountry", "Saudi Arabia");
    }
  }, [countries, form]);

  const getIdStatus = (countryCode: number) => {
    // 966, then idstaus isSaudi: true
    // 965, 968, 971, 973, 974, then idstatus isGCC: true

    const gccCountries = [965, 968, 971, 973, 974];

    const idStatus = {
      isSaudi: false,
      isGCC: false,
      isTourist: false,
    };

    if (countryCode === 966) {
      idStatus.isSaudi = true;
    } else if (gccCountries.includes(countryCode)) {
      idStatus.isGCC = true;
    } else {
      idStatus.isTourist = true;
    }
    return idStatus;
  };

  async function onSubmit(data: DriverFormValues) {
    data.mobileNumber = data.mobileNumber.replace(`${data.countryCode}`, "");
    const countryCode = data.countryCode.toString().replace("+", "");
    const nationalityCountryId = findCountryId(data.nationality);
    const idIssuedCountryId = findCountryId(data.idIssuedCountry);
    const licenseCountryId = findCountryId(data.licenseCountry);
    const isVisitor = data.idType === "VISITOR";

    const _data: CreateDriverFormValues = {
      title: data.title,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber ?? "",
      countryCode: Number(countryCode),
      email: data.email,
      dob: makeDateValid(data.dob),
      hijrahDob: makeDateValid(data.hijrahDob),
      documents: [],
      nationality: {
        id: nationalityCountryId,
      },
      address: {
        street: data.address,
      },
      idType: data.idType,
      metadata: {
        borderNumber: data.borderNumber ?? "",
      },
    };

    const _idType =
      data.idType === "SAUDI_NATIONAL" || data.idType === "GCC"
        ? "ID"
        : data.idType === "RESIDENT"
          ? "IQAMA"
          : data.idType === "VISITOR"
            ? "PASSPORT"
            : "PASSPORT";

    // if idtype is other than visitor, then dont add border number
    if (!isVisitor) {
      delete _data.metadata;
    }

    const document = {
      documentNo: data.documentNo,
      expiry: makeDateValid(data.documentExpiry),
      hijrahExpiry: makeDateValid(data.documentHijrahExpiry),
      issuedPlace: {
        id: idIssuedCountryId,
      },
      type: _idType,
      version: 1,
    };

    const license = {
      documentNo: data.licenseNo,
      expiry: makeDateValid(data.licenseExpiry),
      hijrahExpiry: makeDateValid(data.licenseHijrahExpiry),
      issuedPlace: {
        id: licenseCountryId,
      },
      type: "LICENSE",
    };

    _data.documents = [document, license];

    try {
      const response = await driverAttachOrCreate(_data, quoteId!);

      if (response.status === 500) {
        toast({
          variant: "destructive",
          title: t("errors.serverFailure"),
          duration: 3000,
        });
        return;
      }

      if (response.status !== 200) {
        toast({
          variant: "destructive",
          title: t("errors.failed"),
          description: capitalize(response.body.desc ?? t("errors.failedToCreateDriver")),
          duration: 3000,
        });
        return;
      }

      if (response.status === 200) {
        // update driverName and drverUid in the params
        progress.start();
        // const params = new URLSearchParams(searchParams);

        void setDriverUid(response.body.driverUId);
        void setDriverName(`${data.firstName} ${data.lastName}`);
        void setDriverMode("viewDetail");
        startTransition(() => {
          setTimeout(() => {
            window.location.reload();
          }, 500);
        });
        progress.done();

        toast({
          variant: "success",
          title: t("success.driverCreated"),
          description: "",
          duration: 3000,
        });
      }
      console.log("Driver created successfully", response);
    } catch (error) {
      console.error("Error creating driver:", JSON.stringify(error));
    }
  }

  const updateIdType = (country: Country) => {
    const code = country.countryCallingCodes[0]?.replace("+", "") ?? "966";
    const idStatus = getIdStatus(Number(code));
    const filteredIdTypes = idStatus.isGCC
      ? ID_TYPES_DROPDOWN.filter((type) => type.value !== "SAUDI_NATIONAL")
      : idStatus.isTourist
        ? ID_TYPES_DROPDOWN.filter((type) => type.value !== "SAUDI_NATIONAL" && type.value !== "GCC")
        : ID_TYPES_DROPDOWN;
    return filteredIdTypes;
  };

  // Get localized ID type label
  const getLocalizedIdType = (idType: string) => {
    const matchingType = ID_TYPES_DROPDOWN.find((type) => type.value === idType);

    if (matchingType) {
      if (matchingType.translationKey) {
        return t(`idTypes.${matchingType.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
          defaultValue: matchingType.label,
        });
      }
      return matchingType.label;
    }

    return capitalize(idType.split("_").join(" "));
  };

  return (
    <div className="m-4 flex flex-col gap-x-3 rounded-md bg-slate-100">
      <p className="p-4 text-base font-bold text-slate-900">{t("actions.createNewDriver")}</p>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("fields.firstName")}</FormLabel>
                    <div className="flex items-center overflow-hidden rounded-md border">
                      <Select onValueChange={(value) => form.setValue("title", capitalize(value))} defaultValue="Mr">
                        <SelectTrigger className="w-16 rounded-r-none border-none focus:outline-none">
                          <SelectValue placeholder="Mr" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Mr">Mr</SelectItem>
                          <SelectItem value="Mrs">Mrs</SelectItem>
                        </SelectContent>
                      </Select>
                      <Input
                        {...field}
                        placeholder={t("fields.firstName")}
                        className="flex-1 rounded-l-none border-none !pl-0 focus:outline-none"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="">{t("fields.lastName")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.lastName")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel className="">{t("fields.mobile")}</FormLabel>
                      <FormControl>
                        <PhoneInput
                          value={field.value}
                          onCountryChange={(value) => {
                            form.setValue("mobileNumber", value ?? "");
                            form.setValue("countryCode", value ?? "");
                          }}
                          onChange={(value: { countryCode: string; phoneNumber: string }) => {
                            form.setValue("mobileNumber", `${value?.countryCode}${value?.phoneNumber}`);
                            form.setValue("countryCode", value?.countryCode);
                          }}
                          placeholder="+966 50 123 4567"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="">{t("fields.email")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.email")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="nationality"
                render={({ field }) => (
                  <FormItem className="w-full ">
                    <FormLabel>{t("fields.nationality")}</FormLabel>
                    <CountryDropdown
                      placeholder={t("fields.nationality")}
                      options={countries}
                      defaultValue={field.value}
                      onChange={(country) => {
                        if (!country) return;
                        // if country is saudi arabia, then set id type to saudi_national
                        if (country.name === "Saudi Arabia") {
                          form.setValue("idType", "SAUDI_NATIONAL");
                          form.setValue("idIssuedCountry", country.name);
                        }

                        const filteredIdTypes = updateIdType(country);

                        field.onChange(country.name);

                        form.setValue("idType", filteredIdTypes[0]?.value ?? "SAUDI_NATIONAL");

                        setFilteredIdTypes(filteredIdTypes);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="idType"
                render={() => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel>{t("fields.idType")}</FormLabel>
                      <div className="flex items-center overflow-hidden rounded-md border">
                        <Select
                          onValueChange={(value) => {
                            if (form.getValues("nationality") === "Saudi Arabia") {
                              form.setValue("idType", "SAUDI_NATIONAL");
                              setIdType(value);
                              return;
                            }
                            form.setValue("idType", value);
                            setIdType(value);
                            setIdTypeState(value);
                          }}
                          defaultValue="SAUDI_NATIONAL"
                          value={form.getValues("idType")}
                          // disable it if idtype is saudi_national
                          disabled={form.getValues("nationality") === "Saudi Arabia"}
                        >
                          <SelectContent>
                            {filteredIdTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.translationKey
                                  ? t(`idTypes.${type.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
                                      defaultValue: type.label,
                                    })
                                  : type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                          <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                            <SelectValue placeholder={t("idTypes.saudiNational2")} />
                          </SelectTrigger>
                        </Select>
                      </div>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="idIssuedCountry"
                render={({ field }) => (
                  <FormItem className="w-full ">
                    <FormLabel>{t("fields.idIssuedCountry")}</FormLabel>
                    <CountryDropdown
                      disabled={form.getValues("idType") === "SAUDI_NATIONAL"}
                      options={countries}
                      placeholder={t("fields.idIssuedCountry")}
                      defaultValue={field.value}
                      onChange={(country) => {
                        field.onChange(country.name);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="documentNo"
                render={({ field }) => {
                  // if id type is saudi_national, then udpate the license number same as of document number and vice versa
                  const idtype = form.getValues("idType");
                  const idTypeLabel = getLocalizedIdType(idtype);
                  return (
                    <FormItem className="w-full">
                      <FormLabel>
                        {idTypeLabel ? (
                          <div
                            className={clsx(
                              "flex gap-x-1 pb-2",
                              locale === "ar" ? "flex-row-reverse justify-end" : "flex-row"
                            )}
                          >
                            <div>{idTypeLabel}</div>
                            <div>{t("fields.number")}</div>
                          </div>
                        ) : (
                          t("fields.idNumber")
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("fields.idNumber")}
                          {...field}
                          onChange={(e) => {
                            // Only update the documentNo field directly without side effects
                            field.onChange(e.target.value);
                            // If this is a Saudi national ID, also update license number
                            if (form.getValues("idType") === "SAUDI_NATIONAL") {
                              form.setValue("licenseNo", e.target.value);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="hijrahDob"
                render={({ field }) => (
                  <FormItem>
                    <CustomDatePicker
                      label={t("fields.dateOfBirthHijri")}
                      date={field.value}
                      onChange={(date) => {
                        form.setValue("hijrahDob", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate("", date);
                          form.setValue("dob", dates.gregorean);
                        }
                      }}
                      months={locale === "ar" ? HIJRI_MONTHS_ARABIC : HIJRI_MONTHS}
                      yearPlaceholder="1395"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <CustomDatePicker
                      label={t("fields.dateOfBirthGregorian")}
                      date={field.value}
                      onChange={(date) => {
                        form.setValue("dob", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate(date);
                          form.setValue("hijrahDob", dates.hijri);
                        }
                      }}
                      months={GREGORIAN_MONTHS}
                      yearPlaceholder="1975"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator />
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="documentHijrahExpiry"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <CustomDatePicker
                      label={t("fields.idExpiryDateHijri")}
                      date={field.value}
                      onChange={(date) => {
                        form.setValue("documentHijrahExpiry", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate("", date);
                          form.setValue("documentExpiry", dates.gregorean);
                        }
                      }}
                      months={locale === "ar" ? HIJRI_MONTHS_ARABIC : HIJRI_MONTHS}
                      yearPlaceholder="1450"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="documentExpiry"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <CustomDatePicker
                      label={t("fields.idExpiryDateGregorian")}
                      date={field.value}
                      onChange={(date) => {
                        form.setValue("documentExpiry", date);
                        if (isValidDate(date)) {
                          const dates = getFullDate(date);
                          form.setValue("documentHijrahExpiry", dates.hijri);
                        }
                      }}
                      months={GREGORIAN_MONTHS}
                      yearPlaceholder="2028"
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="flex w-full flex-row gap-x-6 p-4">
            <div className="flex w-1/2 ">
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="">{t("fields.address")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.address")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* only show for the visitors otherwise hidden */}
            {_idtype === "VISITOR" ? (
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="borderNumber"
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <div className="flex flex-row items-center justify-between">
                          <FormLabel>{t("fields.borderNumber")}</FormLabel>
                          <Link href={IFRAME_URL} target="_blank" className="h-auto p-0 font-normal text-blue-600">
                            {t("actions.getBorderNumber")}
                          </Link>
                        </div>
                        <FormControl>
                          <Input placeholder={t("fields.borderNumber")} {...field} value={field.value} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
            ) : (
              <></>
            )}
          </div>
          <Separator />

          {/* License details  */}

          <div className="flex w-full flex-col">
            <span className="p-4 text-base font-bold text-slate-900 ">{t("sections.drivingLicense")}</span>
            <div className="flex w-full flex-row gap-x-6 p-4">
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseCountry"
                  render={({ field }) => (
                    <FormItem className="w-full ">
                      <FormLabel>{t("fields.country")}</FormLabel>
                      <CountryDropdown
                        placeholder={t("fields.country")}
                        options={countries}
                        defaultValue={field.value}
                        onChange={(country) => {
                          field.onChange(country.name);
                        }}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseNo"
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <FormLabel className="">{t("fields.licenseNumber")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("fields.licenseNumber")}
                            {...field}
                            onChange={(e) => {
                              // Only update the licenseNo field directly without side effects
                              field.onChange(e.target.value);
                              // If this is a Saudi national ID, also update document number
                              if (form.getValues("idType") === "SAUDI_NATIONAL") {
                                form.setValue("documentNo", e.target.value);
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
            </div>
            <Separator />
            <div className="flex w-full flex-row gap-x-6 p-4">
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseHijrahExpiry"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <CustomDatePicker
                        label={t("fields.licenseExpiryDateHijri")}
                        date={field.value}
                        onChange={(date) => {
                          form.setValue("licenseHijrahExpiry", date);
                          if (isValidDate(date)) {
                            const dates = getFullDate("", date);
                            form.setValue("licenseExpiry", dates.gregorean);
                          }
                        }}
                        months={locale === "ar" ? HIJRI_MONTHS_ARABIC : HIJRI_MONTHS}
                        yearPlaceholder="1450"
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-1/2 ">
                <FormField
                  control={form.control}
                  name="licenseExpiry"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <CustomDatePicker
                        label={t("fields.licenseExpiryDateGregorian")}
                        date={field.value}
                        onChange={(date) => {
                          form.setValue("licenseExpiry", date);
                          if (isValidDate(date)) {
                            const dates = getFullDate(date);
                            form.setValue("licenseHijrahExpiry", dates.hijri);
                          }
                        }}
                        months={GREGORIAN_MONTHS}
                        yearPlaceholder="2028"
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="flex w-full justify-end gap-x-2 p-4">
            <Dialog open={isDialogOpen}>
              <DialogTrigger asChild onClick={() => setIsDialogOpen(true)}>
                <Button variant="outline">{t("actions.cancel")}</Button>
              </DialogTrigger>

              <DialogContent className="p-0 sm:max-w-md">
                <DialogHeader className="p-4">
                  <DialogTitle>{t("dialogs.cancelCreation.title")}</DialogTitle>
                  <DialogDescription>{t("dialogs.cancelCreation.description")}</DialogDescription>
                </DialogHeader>
                <Separator className="" />
                <DialogFooter className="p-4 sm:justify-end">
                  <Button
                    onClick={() => {
                      setIsDialogOpen(false);
                      router.back();
                    }}
                    variant="outline"
                  >
                    {t("dialogs.cancelCreation.confirm")}
                  </Button>

                  <Button
                    onClick={() => {
                      setIsDialogOpen(false);
                    }}
                    type="button"
                  >
                    {t("dialogs.cancelCreation.continue")}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <LoadingSpinner className="ml-1 text-slate-800" />
              ) : (
                t("actions.createDriver")
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
