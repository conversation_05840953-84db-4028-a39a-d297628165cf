"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { XCircle, Loader2 } from "lucide-react";
import { type InvoiceSearchResponse, type InvoiceStatusResponse } from "@/api/contracts/booking/invoice-contract";
import { useToast } from "@/lib/hooks/use-toast";
import { fetchInvoiceStatus, generateInvoice } from "@/lib/actions";
import { useState, useEffect } from "react";
import { format } from "date-fns"; // Optional: Use date-fns for formatting
import { InvoiceCategory, invoiceCategoryLabels } from "../../../../types";
import { useTranslations } from "next-intl";
import { downloadInvoiceHtml } from "../../../financials/traffic-fines/actions";

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  return format(date, "dd/MM/yyyy - HH:mm:ss"); // Format using date-fns
};

export const InvoiceCardSkeleton = () => {
  return (
    <div className="mb-2 rounded-md p-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-1/3" />
        <Skeleton className="h-8 w-24" />
      </div>
      <div className="mt-4 text-sm text-gray-600">
        <section className="grid grid-cols-2 gap-x-8 gap-y-4">
          <div className="flex flex-col">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="mt-2 h-4 w-3/4" />
          </div>
          <div className="flex flex-col">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="mt-2 h-4 w-3/4" />
          </div>
        </section>
        <Separator className="my-4" />
        <section className="grid grid-cols-2 gap-x-8 gap-y-4">
          <div className="flex flex-col">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="mt-2 h-4 w-3/4" />
          </div>
          <div className="flex flex-col">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="mt-2 h-4 w-3/4" />
          </div>
        </section>
      </div>
    </div>
  );
};

export default function InvoiceCard({
  invoice,
  onUpdateInvoice,
}: {
  invoice: InvoiceSearchResponse;
  onUpdateInvoice: (updatedInvoice: InvoiceSearchResponse) => void; // Callback to update the invoice
}) {
  const t = useTranslations("invoice");
  const tTrafficFine = useTranslations("TrafficFines");
  const { toast } = useToast();
  const [loadingInvoice, setLoadingInvoice] = useState<boolean>(false); // State to track loading for Resend Invoice
  const [loadingViewInvoice, setLoadingViewInvoice] = useState<boolean>(false); // State to track loading for View Invoice
  const [isPolling, setIsPolling] = useState<boolean>(false); // State to track polling

  const handleResendInvoice = async (invoice: InvoiceSearchResponse) => {
    try {
      setLoadingInvoice(true); // Set loading state to true
      const response = await generateInvoice(invoice.bookingNumber, invoice.invoiceConfigType);
      const invoiceResp = response.body as InvoiceSearchResponse;

      if (response.status === 200 || response.status === 201) {
        // Update respective invoice with invoiceStatus: PENDING
        onUpdateInvoice({
          ...invoice,
          externalId: invoiceResp.externalId ?? undefined,
          invoiceStatus: "PENDING", // Update the status to PENDING
        });
        setIsPolling(true); // Start polling
        toast({
          title: t("results.toast.status.success.title"),
          description: t("results.toast.status.success.description"),
          variant: "success",
        });
      } else {
        toast({
          variant: "destructive",
          title: t("results.toast.status.failed.title"),
          description: t("results.toast.status.failed.description"),
          duration: 3000,
        });
      }
    } catch (error) {
      const errorMessage = (error as { desc: string }).desc || "An error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoadingInvoice(false); // Reset loading state
    }
  };

  const viewInvoiceStatus = async (invoice: InvoiceSearchResponse) => {
    const { externalId, agreementNumber, invoiceConfigType, invoiceNumber } = invoice;
    setLoadingViewInvoice(true); // Set loading state to true
    try {
      if (invoiceConfigType === "TRAFFIC_FINE") {
        const res = await downloadInvoiceHtml(agreementNumber.toString(), invoiceNumber);

        if (res.status !== 200) {
          toast({
            title: tTrafficFine("invoice.downloadFailed"),
            description: tTrafficFine("invoice.failedToDownloadInvoice"),
            variant: "destructive",
          });
          return;
        }

        const data = res.body.htmlContent;

        const printWindow = window.open(
          "",
          "_blank",
          "width=" + window.screen.availWidth + ",height=" + window.screen.availHeight
        );
        if (printWindow) {
          printWindow.document.open();
          printWindow.document.title = `Traffic Invoice - ${invoiceNumber}`;
          printWindow.document.write(data.html);
          printWindow.document.close();

          printWindow.onload = () => {
            printWindow.focus();
            printWindow.print();
          };
        } else {
          toast({
            title: tTrafficFine("invoice.printFailed"),
            description: tTrafficFine("invoice.failedToOpenPrintWindow"),
            variant: "destructive",
          });
        }
      } else {
        const response = await fetchInvoiceStatus(agreementNumber.toString(), externalId ?? invoice.invoiceNumber);
        const invoiceStatus: InvoiceStatusResponse = response.body as InvoiceStatusResponse;

        if (response.status === 200) {
          if (invoiceStatus.downloadUrl) {
            const queryParams = new URLSearchParams({
              cdnUrl: invoiceStatus.downloadUrl,
            });
            const response = await fetch(`/next-api/download/invoice-pdf?${queryParams}`);
            if (!response.ok) {
              toast({
                variant: "destructive",
                title: t("results.toast.view.failed.title"),
                description: t("results.toast.view.failed.description"),
                duration: 3000,
              });
            }

            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);

            const printWindow = window.open(blobUrl, "_blank");

            if (printWindow) {
              printWindow.onload = () => {
                printWindow.focus();
                printWindow.print();

                // Optionally revoke the URL after a delay
                setTimeout(() => {
                  URL.revokeObjectURL(blobUrl);
                }, 0);
              };
            } else {
              console.error("Popup blocked. Please allow popups for this site.");
            }
          }

          if (invoiceStatus.invoiceStatus === "PENDING") {
            // Update respective invoice with invoiceStatus: PENDING
            onUpdateInvoice({
              ...invoice,
              invoiceStatus: "PENDING", // Update the status to PENDING
            });
            setIsPolling(true); // Start polling
          }
        } else {
          setIsPolling(false); // Start polling
          onUpdateInvoice({
            ...invoice,
            invoiceStatus: "ERROR", // Update the status to PENDING
          });
          toast({
            variant: "destructive",
            title: t("results.toast.view.failed.title"),
            description: t("results.toast.view.failed.description"),
            duration: 3000,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching invoice status:", error);
      const errorMessage = (error as { desc: string }).desc || "An error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoadingViewInvoice(false); // Reset loading state
    }
  };

  useEffect(() => {
    let pollingInterval: ReturnType<typeof setInterval> | null = null;

    const pollInvoiceStatus = async () => {
      try {
        setLoadingViewInvoice(true);
        const response = await fetchInvoiceStatus(
          invoice.agreementNumber.toString(),
          invoice.externalId ?? invoice.invoiceNumber
        );
        const invoiceStatus: InvoiceStatusResponse = response.body as InvoiceStatusResponse;

        if (response.status === 200) {
          if (invoiceStatus.invoiceStatus === "SUCCESS" || invoiceStatus.invoiceStatus === "ERROR") {
            setLoadingViewInvoice(false);
            // Stop polling when the status is SUCCESS or ERROR
            if (pollingInterval) {
              clearInterval(pollingInterval);
            }
            pollingInterval = null;
            setIsPolling(false); // Stop polling
            onUpdateInvoice({
              ...invoice,
              invoiceStatus: invoiceStatus.invoiceStatus, // Update the status
            });

            if (invoiceStatus.invoiceStatus === "ERROR") {
              toast({
                title: t("results.toast.status.failed.title"),
                description: t("results.toast.status.failed.description"),
                variant: "destructive",
              });
            }
          }
        }
      } catch (error) {
        console.error("Error polling invoice status:", error);
        if (pollingInterval) {
          clearInterval(pollingInterval);
        }
        pollingInterval = null;
        setIsPolling(false); // Stop polling on error
        toast({
          title: t("results.toast.status.exception.title"),
          description: t("results.toast.status.exception.description"),
          variant: "destructive",
        });
      }
    };

    if (isPolling) {
      // Start polling every 10 seconds
      void pollInvoiceStatus(); // Initial call
      pollingInterval = setInterval(() => {
        void pollInvoiceStatus();
      }, 5000);
    }

    return () => {
      // Cleanup polling on component unmount
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
      pollingInterval = null;
    };
  }, [isPolling, invoice]);

  if (loadingInvoice) {
    return <InvoiceCardSkeleton />;
  }

  return (
    <>
      <div className="mb-2 rounded-md bg-slate-100 p-4">
        <div className="flex items-center justify-between">
          <div className="justify-content-center flex flex-col gap-1">
            <span className="text-sm font-bold">{invoice.externalId}</span>
            {invoice.invoiceStatus === "ERROR" && (
              <span className="inline-flex w-fit items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                <XCircle className="mr-1 h-4 w-4" /> Invoice failed
              </span>
            )}
          </div>
          {invoice.invoiceStatus === "ERROR" ? (
            <Button variant="outline" size="sm" onClick={() => handleResendInvoice(invoice)}>
              Re-generate Invoice
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => viewInvoiceStatus(invoice)}
              disabled={loadingViewInvoice} // Disable the button while loading
            >
              {loadingViewInvoice ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> // Show spinner while loading
              ) : (
                "View Invoice"
              )}
            </Button>
          )}
        </div>
        <div className="mt-4 text-sm text-gray-600">
          <section className="grid grid-cols-2 gap-x-8 gap-y-4">
            {/* Invoice Format */}
            {/* TODO: comment out this for bike rental release <div className="flex flex-col">
            <label className="font-medium text-slate-500">Invoice Format</label>
            <p className="text-slate-900">{invoice.invoiceCategory}</p>
          </div> */}

            {/* Invoice Type */}
            <div className="flex flex-col">
              <label className="font-medium text-slate-500">Invoice Type</label>
              <p className="text-slate-900">
                {invoiceCategoryLabels[InvoiceCategory[invoice.invoiceConfigType as keyof typeof InvoiceCategory]]}
              </p>
            </div>
          </section>
          <Separator className="my-4" />
          <section className="grid grid-cols-2 gap-x-8 gap-y-4">
            {/* Total Invoice after VAT */}
            <div className="flex flex-col">
              <label className="font-medium text-slate-500">Total Invoice after VAT</label>
              <p className="text-slate-900">
                {invoice?.totalInvoiceAfterVat?.toLocaleString("en-US", { minimumFractionDigits: 2 })} SAR
              </p>
            </div>

            {/* Invoice Creation Date */}
            <div className="flex flex-col">
              <label className="font-medium text-slate-500">Invoice Creation Date</label>
              <p className="text-slate-900">{formatDate(invoice.issueDate)}</p>
            </div>
          </section>
        </div>
      </div>
    </>
  );
}
