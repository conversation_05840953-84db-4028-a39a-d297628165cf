"use client";
import React from "react";
import { CardContent } from "@/components/ui/card";
import BranchTimeSelector from "../../../_components/branch-time-selector";
import { Separator } from "@/components/ui/separator";
import { type branchListResponseSchema } from "@/api/contracts/branch-contract";
import { type z } from "zod";
import { useTranslations } from "next-intl";

interface IDetailsCardProps {
  branches: z.infer<typeof branchListResponseSchema>["data"][number][];
  searchParams: Record<string, string | string[] | undefined>;
}

export default function DetailsCard({ branches, searchParams }: IDetailsCardProps) {
  // Get translations
  const t = useTranslations("bookings");

  // Parameters come from the parent component now
  const pickupBranchId = searchParams.pickupBranchId as string;
  const pickupTimestamp = Number(searchParams.pickupTimestamp);
  const dropOffBranchId = searchParams.dropOffBranchId as string;
  const dropOffTimestamp = Number(searchParams.dropOffTimestamp);

  return (
    <CardContent className="flex w-full p-0 max-md:flex-wrap">
      <div className="flex-1 flex-col gap-4 text-sm font-medium">
        <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">{t("details.pickupBranch")}</p>
        <div className="p-4">
          <BranchTimeSelector
            isWalkin={true}
            branchId={pickupBranchId}
            isBranchDisabled={true}
            branchOptions={branches}
            branchDateTime={pickupTimestamp}
            preferCurrentTime={true}
            branchType="pickup"
            isSubmitButton={false}
            disablePastDates={true}
            disableDates={true}
            disableTime={true}
          />
        </div>
      </div>
      <Separator orientation="vertical" className="h-full w-[1px]" />
      <div className="flex-1 flex-col gap-4 text-sm font-medium">
        <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">{t("details.dropOffBranch")}</p>
        <div className="p-4">
          <BranchTimeSelector
            isWalkin={true}
            branchId={dropOffBranchId}
            branchOptions={branches}
            branchDateTime={dropOffTimestamp}
            branchType="dropOff"
            disableNextYearDates={true}
            isSubmitButton={false}
            disablePastDates={true}
          />
        </div>
      </div>
    </CardContent>
  );
}
