"use client";
import React, { startTransition, useState, useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { type z } from "zod";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { type QuotePrice, type AddonSchema } from "@/api/contracts/booking/schema";
import { getQuoteWithAddon } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

type Addons = z.infer<typeof AddonSchema>[] | [];

export default function Extras({
  _addons,
  unlimitedKm,
  preSelectedKms,
}: {
  _addons: Addons;
  unlimitedKm?: z.infer<typeof AddonSchema>;
  preSelectedKms: number;
}) {
  const t = useTranslations("extras");
  const commonT = useTranslations("common");
  const locale = useLocale() as "en" | "ar";
  const { toast } = useToast();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const offerId = searchParams.get("offerId");
  const vehicleGroupId = searchParams.get("vehicleGroupId");
  const insuranceIds = searchParams.get("insuranceIds");

  const [addonParams, setAddonParms] = useQueryState("addOns", {
    shallow: false,
  });

  const existingAddonIds = addonParams ? addonParams.split(",").map(Number) : [];

  const [extras, setExtras] = useState<Addons>(
    _addons
      .filter((addon) => addon.code !== "UNLIMITED_KM")
      .map((addon) => ({
        ...addon,
        checked: existingAddonIds.includes(addon.id),
      }))
  );

  const [isUnlimitedKmInAddOn, setIsUnlimitedKmInAddOn] = useState(
    unlimitedKm ? existingAddonIds.includes(unlimitedKm.id) : false
  );

  const progress = useProgressBar();

  useEffect(() => {
    if (existingAddonIds.length > 0) {
      setExtras((prev) =>
        prev.map((addon) => ({
          ...addon,
          checked: existingAddonIds.includes(addon.id),
        }))
      );

      if (unlimitedKm && existingAddonIds.includes(unlimitedKm.id)) {
        setIsUnlimitedKmInAddOn(true);
      }
    }
  }, []);

  const handleCheckboxChange = async (addonId: number) => {
    const updatedExtras = extras.map((addon) => {
      if (addon.id === addonId) {
        return {
          ...addon,
          checked: !addon.checked,
        };
      }
      return addon;
    });

    const checkedIds = updatedExtras.filter((addon) => addon.checked).map((addon) => addon.id);
    if (isUnlimitedKmInAddOn && unlimitedKm) {
      checkedIds.push(unlimitedKm.id);
    }

    try {
      const resp = await getQuoteWithAddon(vehicleGroupId ?? "1", offerId ?? "", insuranceIds ?? undefined, checkedIds);

      if (resp.status === 200) {
        const body = resp.body as unknown as QuotePrice;
        setExtras(updatedExtras);

        const finalCheckedIds = [...checkedIds];

        progress.start();
        const current = new URLSearchParams(Array.from(searchParams.entries()));
        current.set("quoteId", body.quoteId);
        current.set("offerId", body.offerId);
        const search = current.toString();
        const query = search ? `?${search}` : "";

        startTransition(() => {
          void setAddonParms(finalCheckedIds.join(","));
          history.replaceState({}, "", pathname + query);
          progress.done();
        });
      }
    } catch (error) {
      console.error("Error updating addons:", error);
      toast({
        title: commonT("errors.somethingWentWrong"),
        description: t("errors.fetchQuoteDetails"),
        variant: "destructive",
      });
    }
  };

  const handleKmChange = async (value: string) => {
    let updatedParams = "";

    try {
      if (value === "0") {
        if (addonParams && unlimitedKm) {
          updatedParams = addonParams
            .split(",")
            .filter((id) => id !== String(unlimitedKm.id))
            .join(",");
        } else {
          updatedParams = "";
        }

        const resp = await getQuoteWithAddon(
          vehicleGroupId ?? "1",
          offerId ?? "",
          insuranceIds ?? undefined,
          updatedParams ? updatedParams.split(",").map(Number) : undefined
        );

        if (resp.status === 200) {
          const body = resp.body as unknown as QuotePrice;
          progress.start();
          const current = new URLSearchParams(Array.from(searchParams.entries()));
          current.set("quoteId", body.quoteId);
          current.set("offerId", body.offerId);
          const search = current.toString();
          const query = search ? `?${search}` : "";

          startTransition(() => {
            void setAddonParms(updatedParams);
            setIsUnlimitedKmInAddOn(false);
            history.replaceState({}, "", pathname + query);
            progress.done();
          });
        } else {
          toast({
            title: commonT("errors.somethingWentWrong"),
            description: t("errors.fetchQuoteDetails"),
            variant: "destructive",
          });
        }
      } else {
        if (addonParams && unlimitedKm) {
          const existingAddons = addonParams.split(",");
          if (!existingAddons.includes(String(unlimitedKm.id))) {
            updatedParams = addonParams + "," + unlimitedKm.id;
          } else {
            updatedParams = addonParams;
          }
        } else {
          const checkedIds = extras.filter((addon) => addon.checked).map((addon) => addon.id);
          if (unlimitedKm?.id) checkedIds.push(unlimitedKm.id);
          updatedParams = checkedIds.join(",");
        }

        const resp = await getQuoteWithAddon(
          vehicleGroupId ?? "1",
          offerId ?? "",
          insuranceIds ?? undefined,
          updatedParams.split(",").map(Number)
        );

        if (resp.status === 200) {
          const body = resp.body as unknown as QuotePrice;
          progress.start();
          const current = new URLSearchParams(Array.from(searchParams.entries()));
          current.set("quoteId", body.quoteId);
          current.set("offerId", body.offerId);
          const search = current.toString();
          const query = search ? `?${search}` : "";

          startTransition(() => {
            void setAddonParms(updatedParams);
            setIsUnlimitedKmInAddOn(true);
            history.replaceState({}, "", pathname + query);
            progress.done();
          });
        } else {
          toast({
            title: commonT("errors.somethingWentWrong"),
            description: t("errors.fetchQuoteDetails"),
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error updating km allowance:", error);
      toast({
        title: commonT("errors.somethingWentWrong"),
        description: t("errors.fetchQuoteDetails"),
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Card className="flex flex-col shadow">
        <CardHeader className="flex w-full flex-row justify-between p-4 ">
          <CardTitle className="text-lg font-bold">{t("kmsAllowance.title")}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="flex w-full p-0 max-md:flex-wrap ">
          <div className="flex w-full flex-col text-sm font-medium">
            <RadioGroup
              defaultValue={isUnlimitedKmInAddOn ? String(unlimitedKm?.id) : "0"}
              onValueChange={(e) => handleKmChange(e)}
            >
              <div className="flex items-start gap-x-2 p-4 ">
                <RadioGroupItem value={String(0)} id="r1" className="mt-1 gap-x-2 text-blue-600" />
                <Label htmlFor="KM" className="flex w-full flex-row items-end justify-between">
                  <p className="text-base font-medium">{t("kmsAllowance.standardKm", { km: preSelectedKms ?? 0 })}</p>
                  <p className="text-base font-medium">{t("pricing.free")}</p>
                </Label>
              </div>
              {unlimitedKm ? (
                <>
                  <Separator />
                  <div className="flex items-start gap-x-2 p-4 ">
                    <RadioGroupItem value={String(unlimitedKm.id)} id="r1" className="mt-1 gap-x-2 text-blue-600" />
                    <Label htmlFor={String(unlimitedKm.id)} className="flex w-full flex-row items-end justify-between">
                      <p className="text-base font-medium">{t("kmsAllowance.unlimitedKm")}</p>
                      <p className="text-base font-medium">
                        {unlimitedKm?.price
                          ? t("pricing.addOn", {
                              currency: locale === "ar" ? "ريال" : "SAR",
                              amount: unlimitedKm?.price,
                            })
                          : t("pricing.free")}
                      </p>
                    </Label>
                  </div>
                </>
              ) : (
                <></>
              )}
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
      <Card className="flex flex-col shadow">
        <CardHeader className="flex w-full flex-row justify-between p-4 ">
          <CardTitle className="text-lg font-bold">{t("addOns.title")}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="flex w-full p-0 max-md:flex-wrap ">
          <div className="flex w-full flex-col text-sm font-medium">
            {extras?.length ? (
              extras.map((addon) => (
                <div key={addon.id} className="flex items-start gap-x-2 p-4">
                  <Checkbox
                    id={String(addon.id)}
                    checked={addon.checked}
                    value={addon.id}
                    className="mt-1 data-[state=checked]:border-blue-600 data-[state=checked]:bg-blue-600"
                    onCheckedChange={() => handleCheckboxChange(addon.id)}
                  />
                  <Label htmlFor={String(addon.id)} className="flex w-full flex-row items-end justify-between">
                    <p className="text-base font-medium">{addon.name[locale]}</p>
                    <p className="text-base font-medium">
                      {addon.price
                        ? t("pricing.addOn", {
                            currency: locale === "ar" ? "ريال" : "SAR",
                            amount: Number(addon.price).toFixed(2),
                          })
                        : t("pricing.free")}
                    </p>
                  </Label>
                </div>
              ))
            ) : (
              <p className="p-6 text-center text-slate-900">{t("addOns.noExtras")}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </>
  );
}
