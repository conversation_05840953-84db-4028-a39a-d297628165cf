"use client";

import { Calendar, Gear, CurrencyCircleDollar } from "@phosphor-icons/react";
import type { Route } from "next";
import { useParams } from "next/navigation";
import { ProgressBarLink } from "@/components/progress-bar";
import type { Session } from "next-auth";
import type { IBranch } from "@/api/contracts/branch-contract";
import { useLocale, useTranslations } from "next-intl";

const BranchDashboard = ({ branch, session }: { branch?: IBranch; session: Session | null }) => {
  const params = useParams();
  const t = useTranslations("common");
  const locale = useLocale() as "en" | "ar";
  const branchId = Number(params.id);

  const mockUser = {
    name: session?.user?.name ?? session?.user?.email ?? "",
    branchName: branch?.name?.[locale] ?? "N/A",
  };

  return (
    <div className="w-full p-8">
      {/* Welcome Section */}
      <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <h1 className="text-2xl font-bold text-gray-800">
          {t("welcome")}, {mockUser.name}
        </h1>
        <p className="text-gray-600">{mockUser.branchName}</p>
      </div>

      {/* Dashboard Options */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <DashboardCard
          icon={<Calendar size={24} weight="bold" />}
          title={t("View Bookings")}
          description={t("Manage your rental bookings")}
          href={`/rental/branches/${branchId}/bookings` as Route}
        />
        <DashboardCard
          icon={<Gear size={24} weight="bold" />}
          title={t("Branch Settings")}
          description={t("Configure branch settings")}
          href={`/rental/branches/${branchId}/settings` as Route}
        />
        <DashboardCard
          icon={<CurrencyCircleDollar size={24} weight="bold" />}
          title={t("Cash Register")}
          description={t("View cash register details")}
          href={`/rental/branches/${branchId}/cash-register` as Route}
        />
      </div>
    </div>
  );
};

interface DashboardCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: Route;
}

const DashboardCard = ({ icon, title, description, href }: DashboardCardProps) => {
  return (
    <ProgressBarLink
      href={href}
      className="rounded-lg bg-white p-6 text-left shadow-sm transition-shadow duration-200 hover:shadow-md"
    >
      <div className="mb-4 text-blue-600">{icon}</div>
      <h3 className="mb-2 text-lg font-semibold text-gray-800">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </ProgressBarLink>
  );
};

export default BranchDashboard;
