"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { type Branch } from "./types";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

type ColumnMessageKey = "name" | "code" | "type" | "city" | "region" | "longitude" | "latitude" | "location";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("branches");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

export const columns: ColumnDef<Branch>[] = [
  {
    accessorKey: "id",
    enableHiding: true,
  },
  {
    accessorKey: "name",
    header: () => <Message messageKey="name" />,
    cell: ({ row }) => {
      return <LocalizedObject localizedObject={row.getValue<Branch["name"]>("name")} />;
    },
  },
  {
    accessorKey: "code",
    header: () => <Message messageKey="code" />,
    cell: ({ row }) => {
      return row.getValue<Branch["code"]>("code");
    },
  },
  {
    accessorKey: "type",
    header: () => <Message messageKey="type" />,
    cell: ({ row }) => {
      return <span>{row.getValue<Branch["type"]>("type") || "N/A"}</span>;
    },
  },
  {
    accessorKey: "city",
    header: () => <Message messageKey="city" />,
    cell: ({ row }) => {
      return (
        <LocalizedObject
          localizedObject={{
            en: row.getValue<Branch["city"]>("city")?.name.en ?? "",
            ar: row.getValue<Branch["city"]>("city")?.name.ar ?? "",
          }}
        />
      );
    },
  },
  {
    accessorKey: "region",
    header: () => <Message messageKey="region" />,
    cell: ({ row }) => {
      return (
        <LocalizedObject
          localizedObject={{
            en: row.getValue<Branch["city"]>("city")?.region?.name.en ?? "",
            ar: row.getValue<Branch["city"]>("city")?.region?.name.ar ?? "",
          }}
        />
      );
    },
  },
  {
    accessorKey: "longitude",
    enableHiding: true,
  },
  {
    accessorKey: "latitude",
    enableHiding: true,
  },
  {
    id: "location",
    header: () => <Message messageKey="location" />,
    cell: ({ row }) => {
      const latLon = `${row.getValue<Branch["longitude"]>("longitude")},${row.getValue<Branch["latitude"]>("latitude")}`;
      const location = (
        <a
          onClick={(e) => e.stopPropagation()}
          href={`https://www.google.com/maps/place/${latLon}`}
          className="text-xs text-blue-500 hover:underline"
          target="_blank"
          rel="noreferrer"
        >
          {latLon}
        </a>
      );
      return location;
    },
  },
];
