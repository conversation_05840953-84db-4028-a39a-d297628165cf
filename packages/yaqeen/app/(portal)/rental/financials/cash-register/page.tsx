import { api } from "@/api";
import PageTitle from "../_components/page-title";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import { FileX2 } from "lucide-react";
import { format } from "date-fns";
import { orderBy } from "lodash-es";
import CashRegisterTable from "../_components/cash-register-table";
import { auth } from "@/auth";

function EmptyState() {
  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center rounded-lg border border-dashed bg-slate-50">
      <FileX2 className="h-12 w-12 text-slate-400" />
      <h3 className="mt-4 text-lg font-medium text-slate-900">No Registers Found</h3>
      <p className="mt-2 text-slate-500">There are no cash registers available for the selected branch and date.</p>
    </div>
  );
}

export default async function CashRegisterPage({
  searchParams,
}: {
  searchParams: Promise<{ branchId?: string; updateDate: string }>;
}) {
  const session = await auth();
  const roles = session?.roles.includes("rental:finance:admin") ? "finance" : "cse";
  const userRole = roles; // could be "cse" also;
  const _searchParams = await searchParams;
  const [branches, bankDetails] = await Promise.all([
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.cashRegister.getBankDetails(),
  ]);

  if (branches.status !== 200) {
    return <div>Error fetching branches</div>;
  }

  if (bankDetails.status !== 200) {
    return <div>Error fetching bank details</div>;
  }

  const branchId = _searchParams.branchId;
  const updateDate = _searchParams.updateDate ?? format(new Date(), "dd-MM-yyyy");

  const cashRegisters = await api.cashRegister.getFinanceRegister({
    query: {
      branchIds: branchId ?? branches.body.data[0]!.id.toString(),
      updateDate,
    },
  });

  if (cashRegisters.status !== 200) {
    return <div>Error fetching cash register data</div>;
  }

  // Sort the registers by status in the desired order using lodash orderBy
  const statusOrder = { OPEN: 0, PRE_CLOSED: 1, CLOSED: 2 };
  const sortedRegisters = orderBy(cashRegisters.body, [(register) => statusOrder[register.status]], ["asc"]);

  return (
    <div>
      <PageTitle userRole={userRole} branches={branches.body.data} />
      <div className="flex flex-col gap-8">
        <div className="container px-24 py-6">
          <Suspense fallback={<TableSkeleton showPagination={false} />}>
            {sortedRegisters.length > 0 ? (
              sortedRegisters.map((register) => (
                <CashRegisterTable
                  key={register.registerNo}
                  bankDetails={bankDetails.body}
                  data={{
                    ...register,
                  }}
                />
              ))
            ) : (
              <EmptyState />
            )}
          </Suspense>
        </div>
      </div>
    </div>
  );
}
