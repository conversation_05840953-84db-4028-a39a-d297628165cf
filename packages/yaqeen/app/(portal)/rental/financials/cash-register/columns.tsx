"use client";

import { type ColumnDef } from "@tanstack/react-table";

type CashRegisterRow = {
  item: string;
  cash: string;
  pos: string;
  bankTransfer: string;
  total: string;
};

export const columns: ColumnDef<CashRegisterRow>[] = [
  {
    accessorKey: "item",
    header: "Item",
  },
  {
    accessorKey: "cash",
    header: "Cash",
    cell: ({ row }) => {
      const amount = row.getValue<string>("cash");
      return amount === "N/A" ? amount : amount;
    },
  },
  {
    accessorKey: "pos",
    header: "POS",
    cell: ({ row }) => {
      const amount = row.getValue<string>("pos");
      return amount === "N/A" ? amount : amount;
    },
  },
  {
    accessorKey: "bankTransfer",
    header: "Bank Transfer",
    cell: ({ row }) => {
      const amount = row.getValue<string>("bankTransfer");
      return amount === "N/A" ? amount : amount;
    },
  },
  {
    accessorKey: "total",
    header: "Total",
    cell: ({ row }) => row.getValue<string>("total"),
  },
];

type Balance = {
  cashAmount: string;
  posAmount: string;
  bankTransferAmount: string;
};

export const transformBalanceToRow = (item: string, balance: Balance | undefined, total?: string): CashRegisterRow => {
  if (!balance) {
    return {
      item,
      cash: "0",
      pos: "0",
      bankTransfer: "0",
      total: total || "0",
    };
  }

  return {
    item,
    cash: balance.cashAmount,
    pos: balance.posAmount,
    bankTransfer: balance.bankTransferAmount,
    total:
      total ||
      ((sum) => (sum === 0 ? "0" : sum.toFixed(2)))(
        Number(balance.cashAmount) + Number(balance.posAmount) + Number(balance.bankTransferAmount)
      ),
  };
};
