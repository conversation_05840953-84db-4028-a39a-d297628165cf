"use client";

import { Calendar, Gear, CurrencyCircleDollar } from "@phosphor-icons/react";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import type { Route } from "next";
import type { Session } from "next-auth";
import type { IBranch } from "@/api/contracts/branch-contract";

const FinanceDashboard = ({ branch, session }: { branch?: IBranch; session: Session | null }) => {
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("common");
  const mockUser = {
    name: session?.user?.name ?? session?.user?.email ?? "",
    branchName: branch?.name?.[locale] ?? "N/A",
  };

  return (
    <div className="w-full p-8">
      {/* Welcome Section */}
      <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <h1 className="text-2xl font-bold text-gray-800">
          {t("welcome")}, {mockUser.name}
        </h1>
      </div>

      {/* Dashboard Options */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <DashboardCard
          icon={<Calendar size={24} weight="bold" />}
          title="All Payments"
          description="See all payments by all customers"
          href={`/rental/financials/payments`}
        />
        <DashboardCard
          icon={<Gear size={24} weight="bold" />}
          title="All Refunds"
          description="View and manage all refunds"
          href={`/rental/financials/payments/all-refunds`}
        />
        <DashboardCard
          icon={<CurrencyCircleDollar size={24} weight="bold" />}
          title="Cash Register"
          description="View cash register details"
          href={`/rental/financials/cash-register?branchId=${branch?.id}`}
        />
      </div>
    </div>
  );
};

interface DashboardCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: Route;
}

const DashboardCard = ({ icon, title, description, href }: DashboardCardProps) => {
  return (
    <Link
      href={href}
      className="rounded-lg bg-white p-6 text-left shadow-sm transition-shadow duration-200 hover:shadow-md"
    >
      <div className="mb-4 text-blue-600">{icon}</div>
      <h3 className="mb-2 text-lg font-semibold text-gray-800">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </Link>
  );
};

export default FinanceDashboard;
