import { api } from "@/api";
import { auth } from "@/auth";
import type { IBranch } from "@/api/contracts/branch-contract";
import FinanceDashboard from "./FinanceDashboard";

const Page = async () => {
  const session = await auth();
  console.log(session?.roles);
  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse?.[0];

  return <FinanceDashboard branch={branch} session={session} />;
};

export default Page;
