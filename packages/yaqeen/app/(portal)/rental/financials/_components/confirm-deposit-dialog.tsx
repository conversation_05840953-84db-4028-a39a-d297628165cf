import { ContinueButton } from "@/components/ContinueButton";
import { <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { approveCashRegister } from "@/lib/actions/cash-register-actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useActionState } from "react";

interface ConfirmDepositDialogProps {
  onConfirm: () => void;
  children: React.ReactNode;
  registerId: number;
  bankId: number | null;
  displayRegisterNo: number;
}

export function ConfirmDepositDialog({
  onConfirm,
  children,
  registerId,
  bankId,
  displayRegisterNo,
}: ConfirmDepositDialogProps) {
  const { toast } = useToast();

  const [, approveAction, isPending] = useActionState(async () => {
    if (!bankId) return;

    try {
      const response = await approveCashRegister(registerId, bankId);
      if (response.status === 200) {
        toast({
          title: "Register closed successfully",
          description: `The deposit has been confirmed and register #${displayRegisterNo} is now closed`,
          variant: "success",
        });
        onConfirm();
      } else {
        toast({
          title: "Failed to close register",
          description: `Failed to close register #${displayRegisterNo}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to approve cash register:", error);
    }
  }, null);

  return (
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Confirm your deposit</DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        {children}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-full">
                <form action={approveAction}>
                  <ContinueButton className="w-full" disabled={bankId === null || isPending}>
                    Confirm deposit & Close register
                  </ContinueButton>
                </form>
              </div>
            </TooltipTrigger>
            {!bankId && (
              <TooltipContent>
                <p>Please select a bank first</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>
    </DialogContent>
  );
}
