"use client";

import { type Branch } from "@/api/contracts/branch-contract";
import { ProgressBarLink, useProgressBar } from "@/components/progress-bar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { format, parse } from "date-fns";
import { useLocale } from "next-intl";
import { useQueryState } from "nuqs";
import { startTransition, useState } from "react";

interface PageTitleProps {
  branches: Branch[];
  userRole: string;
}

export default function PageTitle({ branches }: PageTitleProps) {
  const locale = useLocale();
  const progress = useProgressBar();
  const [branchId, setBranchId] = useQueryState("branchId", {
    shallow: false,
    defaultValue: branches[0]?.id.toString() ?? "",
  });

  const [updateDate, setUpdateDate] = useQueryState("updateDate", {
    shallow: false,
    defaultValue: format(new Date(), "dd-MM-yyyy"),
  });

  const [selectedDate, setSelectedDate] = useState<Date>(() => {
    if (updateDate) {
      return parse(updateDate, "dd-MM-yyyy", new Date());
    }
    return new Date();
  });

  const handleDateChange = (date: Date) => {
    progress.start();
    startTransition(() => {
      setSelectedDate(date);
      void setUpdateDate(format(date, "dd-MM-yyyy"));
      progress.done();
    });
  };

  const handleBranchChange = (value: string) => {
    progress.start();
    startTransition(() => {
      void setBranchId(value);
      progress.done();
    });
  };

  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/financials`}>Home</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">Cash Register</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-center justify-between py-6">
          <div className="space-y-4">
            <h1 className="text-3xl font-medium tracking-tight">Cash Register</h1>
            <div className="w-[340px] space-y-2">
              <Select defaultValue={branchId} onValueChange={handleBranchChange}>
                <SelectTrigger
                  className={cn(
                    "flex items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0"
                  )}
                  aria-label="Select branch"
                >
                  <SelectValue placeholder="Select a branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                        {locale === "en" ? branch.name.en : branch.name.ar}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <DatePicker
                date={selectedDate}
                setDate={handleDateChange}
                name="cash-register-date"
                disablePastDates={false}
                disableFutureDates={true}
              />
            </div>
          </div>
          <Button variant="outline">Export</Button>
        </div>
      </div>
    </section>
  );
}
