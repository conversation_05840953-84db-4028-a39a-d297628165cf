"use client";

import type { BankDetailsResponse, CashRegisterResponse } from "@/api/contracts/rental/cash-register-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useState } from "react";
import { CloseRegisterDialog } from "./close-register-dialog";
import { ConfirmDepositDialog } from "./confirm-deposit-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { columns, transformBalanceToRow } from "../cash-register/columns";

interface CashRegisterTableProps {
  data: CashRegisterResponse[number] & {
    approvedBy: string | null;
  };
  bankDetails: BankDetailsResponse;
}

export default function CashRegisterTable({ data, bankDetails }: CashRegisterTableProps) {
  const [isCloseDialogOpen, setIsCloseDialogOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [selectedBankId, setSelectedBankId] = useState<number | null>(null);

  const tableData = {
    total: 5,
    data: [
      transformBalanceToRow("Opening Balance", data.openingBalance),
      transformBalanceToRow("Received", data.received),
      transformBalanceToRow("Withdraw", data.withdraw),
      transformBalanceToRow("Bank Deposit", data.bankDeposit),
      transformBalanceToRow("Closing Balance", data.closingBalance),
    ],
  };

  const totalBalance =
    Number(data.closingBalance.cashAmount) +
    Number(data.closingBalance.posAmount) +
    Number(data.closingBalance.bankTransferAmount);

  return (
    <>
      <Card className="mb-8 shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-2xl font-medium tracking-tight">Cash Register {data.displayRegisterNo}</CardTitle>
          <Dialog open={isCloseDialogOpen} onOpenChange={setIsCloseDialogOpen}>
            {data.status === "OPEN" && (
              <DialogTrigger asChild>
                <Button variant="outline">Close Register</Button>
              </DialogTrigger>
            )}
            <CloseRegisterDialog
              onOpenChange={setIsCloseDialogOpen}
              totalBalance={totalBalance}
              registerId={data.registerNo.toString()}
              displayRegisterNo={data.displayRegisterNo}
            />
          </Dialog>
          {data.status === "PRE_CLOSED" && (
            <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">Confirm deposit and Close Register</Button>
              </DialogTrigger>
              <ConfirmDepositDialog
                onConfirm={() => {
                  setIsConfirmDialogOpen(false);
                }}
                registerId={data.registerNo}
                bankId={selectedBankId}
                displayRegisterNo={data.displayRegisterNo}
              >
                <div className="space-y-2">
                  <label className="text-sm font-medium">Name of bank</label>
                  <Select
                    onValueChange={(value) => setSelectedBankId(Number(value))}
                    value={selectedBankId?.toString()}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a bank" />
                    </SelectTrigger>
                    <SelectContent>
                      {bankDetails.map((bank) => (
                        <SelectItem key={bank.id} value={bank.id.toString()}>
                          {bank.bankName} - {bank.accountNo}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </ConfirmDepositDialog>
            </Dialog>
          )}
        </CardHeader>
        <CardContent>
          <div className="mb-6 mt-2 flex flex-wrap items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className={cn("capitalize", {
                  "border-lumi-500 bg-lumi-50 text-lumi-700": data.status === "OPEN",
                  "border-yellow-500 bg-yellow-50 text-yellow-700": data.status === "PRE_CLOSED",
                  "border-blue-500 bg-blue-50 text-blue-700": data.status === "CLOSED",
                })}
              >
                {data.status.toLowerCase().replace("_", " ")}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Open time:</span>
              <span>{format(data.openingTime * 1000, "dd/MM/yyyy, h:mma")}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Close time:</span>
              <span>{data.closingTime ? format(data.closingTime * 1000, "dd/MM/yyyy, h:mma") : "-"}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Closed by:</span>
              <span>{data.closedBy || "-"}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Approved by:</span>
              <span>{data.approvedBy || "-"}</span>
            </div>
          </div>
          <Separator className="mb-6" />
          <DataTable
            columns={columns}
            data={tableData}
            paginationEnabled={false}
            emptyMessage="No cash register data available"
          />
        </CardContent>
      </Card>
    </>
  );
}
