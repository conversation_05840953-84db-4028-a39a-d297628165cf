import { But<PERSON> } from "@/components/ui/button";
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useState } from "react";
import { closeCashRegister, depositCashRegister } from "@/lib/actions/cash-register-actions";
import { ArrowLeft } from "@phosphor-icons/react";
import { useToast } from "@/lib/hooks/use-toast";
import { ContinueButton } from "@/components/ContinueButton";
interface CloseRegisterDialogProps {
  onOpenChange: (open: boolean) => void;
  totalBalance: number;
  registerId: string;
  displayRegisterNo: number;
}

export function CloseRegisterDialog({
  onOpenChange,
  totalBalance,
  registerId,
  displayRegisterNo,
}: CloseRegisterDialogProps) {
  const [showDepositView, setShowDepositView] = useState(false);
  const { toast } = useToast();
  const [, depositAction] = useActionState(async () => {
    const response = await depositCashRegister(Number(registerId));
    if (response.status === 200) {
      toast({
        title: "Success",
        description: `Cash deposit recorded successfully for register #${displayRegisterNo}`,
        variant: "success",
      });
    } else {
      toast({
        title: "Failed to close register",
        description: `Failed to close register #${displayRegisterNo}`,
        variant: "destructive",
      });
    }

    onOpenChange(false);
  }, null);
  const [, closeAction] = useActionState(async () => {
    const response = await closeCashRegister(Number(registerId));
    if (response.status === 200) {
      toast({
        title: "Success",
        description: `Cash register #${displayRegisterNo} closed successfully`,
        variant: "success",
      });
    } else {
      toast({
        title: "Failed to close register",
        description: `Failed to close register #${displayRegisterNo}`,
        variant: "destructive",
      });
    }

    onOpenChange(false);
  }, null);

  // Round down to nearest whole number for deposit amount
  const amountToDeposit = Math.floor(totalBalance);
  const remainingBalance = totalBalance - amountToDeposit;

  if (showDepositView) {
    return (
      <DialogContent>
        <DialogHeader className="space-y-6">
          <DialogTitle>Create Deposit Record</DialogTitle>
          <DialogDescription>You have a cash balance of SAR {totalBalance.toFixed(2)}</DialogDescription>
        </DialogHeader>
        <div className="mt-4 space-y-2 text-muted-foreground">
          <div>
            <div className="text-base">Amount to deposit</div>
            <div className="text-xl">SAR {amountToDeposit.toFixed(2)}</div>
          </div>
          <div>
            <div className="text-base">Remaining (Moved to next day balance)</div>
            <div className="text-xl">SAR {remainingBalance.toFixed(2)}</div>
          </div>
        </div>
        <form action={depositAction} className="space-y-2">
          <ContinueButton className="mt-6 w-full">Confirm deposit & Close register</ContinueButton>
          <Button
            variant="outline"
            className="mt-6 flex w-full items-center gap-2"
            onClick={() => setShowDepositView(false)}
          >
            <ArrowLeft size={18} />
            Go Back
          </Button>
        </form>
      </DialogContent>
    );
  }

  if (totalBalance >= 0 && totalBalance < 1) {
    return (
      <DialogContent>
        <DialogHeader className="space-y-4">
          <DialogTitle>Close Cash Register</DialogTitle>
          <DialogDescription>Are you sure you want to close the cash register?</DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-4">
          <form action={closeAction} className="space-x-2 space-y-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <ContinueButton>Confirm</ContinueButton>
          </form>
        </DialogFooter>
      </DialogContent>
    );
  }

  return (
    <DialogContent>
      <DialogHeader className="space-y-4">
        <DialogTitle>Cash Balance Present</DialogTitle>
        <DialogDescription>
          <span className="text-base">You have a cash balance of {totalBalance.toFixed(2)} SAR</span>
        </DialogDescription>
      </DialogHeader>
      <DialogFooter className="mt-4 flex !flex-col gap-2">
        <form action={closeAction} className="space-y-2">
          <ContinueButton className="w-full">Move full balance to next day & close register</ContinueButton>
        </form>
        {totalBalance > 0 && (
          <Button variant="outline" className="w-full" onClick={() => setShowDepositView(true)}>
            Create a deposit record
          </Button>
        )}
      </DialogFooter>
    </DialogContent>
  );
}
