"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { CheckCircle2, XCircle } from "lucide-react";
import type { Route } from "next";

type Payment = {
  id: number;
  paymentId: string;
  referenceId: string;
  trackId: string;
  customerName: string;
  customerEmail: string;
  amount: number;
  createdOn: number;
  paymentStatus: string;
  paymentDetail: {
    last4Digit: string;
    pyScheme: string;
  };
  approved: boolean;
};

export const columns: ColumnDef<Payment>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "paymentId",
    header: "Payment ID",
    cell: ({ row }) => {
      const paymentId = row.getValue<string>("paymentId");
      return (
        <ProgressBarLink
          href={`/rental/financials/payments/${paymentId}` as Route}
          className="text-blue-600 hover:text-blue-800 hover:underline"
        >
          {paymentId}
        </ProgressBarLink>
      );
    },
  },
  {
    accessorKey: "referenceId",
    header: "Reference ID",
  },
  {
    accessorKey: "trackId",
    header: "Track ID",
  },
  {
    accessorKey: "customerName",
    header: "Customer Name",
  },
  {
    accessorKey: "customerEmail",
    header: "Customer Email",
  },
  {
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => (
      <div>{row.getValue<number>("amount").toLocaleString("en-US", { minimumFractionDigits: 2 })} SAR</div>
    ),
  },
  {
    accessorKey: "createdOn",
    header: "Created On",
    cell: ({ row }) => <div>{format(new Date(row.getValue<number>("createdOn") * 1000), "dd MMM yyyy HH:mm")}</div>,
  },
  {
    accessorKey: "paymentStatus",
    header: "Status",
  },
  {
    accessorKey: "paymentDetail",
    header: "Card",
    cell: ({ row }) => {
      const payment = row.original.paymentDetail;
      return (
        <div>
          {payment.pyScheme} **** {payment.last4Digit}
        </div>
      );
    },
  },
  {
    accessorKey: "approved",
    header: "Approved",
    cell: ({ row }) => {
      const approved = row.getValue<boolean>("approved");
      return (
        <div className="flex items-center">
          {approved ? (
            <CheckCircle2 className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-gray-300" />
          )}
        </div>
      );
    },
  },
];
