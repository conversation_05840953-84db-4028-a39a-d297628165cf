import { api } from "@/api";
import { format } from "date-fns";
import { Suspense } from "react";
import { CheckCircle2, XCircle } from "lucide-react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

type PageProps = {
  params: Promise<{
    paymentId: string;
  }>;
};

const DetailsRow = ({ label, value }: { label: string; value: React.ReactNode }) => (
  <div className="grid grid-cols-3 py-3 border-b border-gray-100">
    <div className="text-gray-600">{label}</div>
    <div className="col-span-2 font-medium">{value}</div>
  </div>
);

export default async function Page({ params }: PageProps) {
  const sParams = await params;
  const [payment, refunds] = await Promise.all([
    api.payment.getPaymentDetail({
      params: {
        paymentId: sParams.paymentId,
      },
    }),
    api.payment.getPaymentRefunds({
      params: {
        paymentId: sParams.paymentId,
      },
    }),
  ]);

  if (payment.status !== 200) {
    throw new Error("Failed to fetch payment details");
  }

  const data = payment.body;

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <div className="flex flex-col px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-semibold">Payment Details</h1>
          <div className="flex items-center gap-2">
            {data.approved ? (
              <CheckCircle2 className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-gray-300" />
            )}
            <span className={data.approved ? "text-green-600" : "text-gray-500"}>
              {data.approved ? "Approved" : "Not Approved"}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Payment Information</h2>
            <DetailsRow label="Payment ID" value={data.paymentId} />
            <DetailsRow label="Reference ID" value={data.referenceId} />
            <DetailsRow label="Track ID" value={data.trackId} />
            <DetailsRow 
              label="Amount" 
              value={`${data.amount.toLocaleString("en-US", { minimumFractionDigits: 2 })} ${data.currency}`} 
            />
            <DetailsRow 
              label="Created On" 
              value={format(new Date(data.createdOn * 1000), "dd MMM yyyy HH:mm")} 
            />
            <DetailsRow label="Status" value={data.paymentStatus} />
            <DetailsRow label="Initiated For" value={data.initiatedFor} />
            <DetailsRow label="Payment Method" value={data.paidThrough} />
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Customer & Card Details</h2>
            <DetailsRow label="Customer Name" value={data.customerName} />
            <DetailsRow label="Customer Email" value={data.customerEmail} />
            <DetailsRow label="Card Scheme" value={data.paymentDetail.pyScheme} />
            <DetailsRow label="Card Number" value={`**** **** **** ${data.paymentDetail.last4Digit}`} />
            <DetailsRow 
              label="Card Expiry" 
              value={`${data.paymentDetail.expiryMonth}/${data.paymentDetail.expiryYear}`} 
            />
            <DetailsRow label="Name on Card" value={data.paymentDetail.nameOnCard} />
            <DetailsRow label="3DS Secure" value={data.is3ds ? "Yes" : "No"} />
            <DetailsRow label="Response" value={data.responseSummary} />
          </div>

          {refunds.status === 200 && refunds.body.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
              <h2 className="text-lg font-semibold mb-4">Refund History</h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="py-2 px-4 text-left">Date</th>
                      <th className="py-2 px-4 text-left">Reference ID</th>
                      <th className="py-2 px-4 text-left">Amount</th>
                      <th className="py-2 px-4 text-left">Status</th>
                      <th className="py-2 px-4 text-left">Action ID</th>
                    </tr>
                  </thead>
                  <tbody>
                    {refunds.body.map((refund) => (
                      <tr key={refund.id} className="border-b">
                        <td className="py-2 px-4">
                          {format(new Date(refund.createdOn * 1000), "dd MMM yyyy HH:mm")}
                        </td>
                        <td className="py-2 px-4">{refund.referenceId}</td>
                        <td className="py-2 px-4">{refund.amount} SAR</td>
                        <td className="py-2 px-4">
                          <span 
                            className={`inline-block px-2 py-1 rounded text-sm ${
                              refund.status === "SUCCESS" 
                                ? "bg-green-100 text-green-800" 
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {refund.status}
                          </span>
                        </td>
                        <td className="py-2 px-4">{refund.actionId}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </Suspense>
  );
}
