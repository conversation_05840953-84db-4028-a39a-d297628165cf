import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    email?: string;
    reference?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.email ?? sParams.reference ?? '';

  const payments = await api.payment.getPayments({
    query: {
      page: pageNumber,
      size: pageSize,
      query: searchQuery,
    },
  });

  if (payments.status !== 200) {
    throw new Error("Failed to fetch payments");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <div className="flex flex-col px-6 py-6">
        <h1 className="mb-6 text-2xl font-semibold">Payments</h1>
        <DataTable
          legacyPage
          searchFilters={[
            {
              label: "Email",
              value: "email",
            },
          
            {
              label: "Reference ID",
              value: "reference",
            }
          ]}
          searchPlaceholder="Search by reference ID or email"
          columns={columns}
          data={{
            data: payments.body.data,
            total: payments.body.total,
          }}
          emptyMessage="There are no payments."
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
