import { api } from "@/api";
import PageTitle from "./_components/page-title";
import { TABS } from "./navigation-items";
interface LayoutProps {
  children: React.ReactNode;
}

export default async function Layout({ children }: LayoutProps) {
  const refunds = await api.refund.searchRefunds({
    query: { page: 1, size: 1, refundRequestStatus: "REQUESTED" },
  });

  if (refunds.status !== 200) {
    throw new Error("Failed to fetch refund requests");
  }

  const tabs = TABS.map((tab) => {
    if (tab.href === "/rental/all-bookings/refund-requests") {
      return { ...tab, count: refunds.body.total };
    }
    return tab;
  });

  return (
    <>
      <PageTitle tabs={tabs} />
      {children}
    </>
  );
}
