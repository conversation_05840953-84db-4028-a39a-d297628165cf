import { type Branch } from "@/api/contracts/branch-contract";

export const getFilters = (
  branches: Branch[],
  locale: "en" | "ar",
  translations: {
    pickupBranch: string;
    dropOffBranch: string;
    dropOffTime: string;
    pickupTime: string;
    status: string;
  }
) => {
  const branchOptions = branches.map((branch) => ({
    label: branch.name[locale],
    value: String(branch.id),
  }));

  return [
    {
      filterKey: "pickUpBranchIds",
      filterName: translations.pickupBranch,
      columnKey: "pickupBranch",
      options: branchOptions,
      isMultiSelect: true,
    },
    {
      filterKey: "dropOffBranchIds",
      filterName: translations.dropOffBranch,
      columnKey: "dropOffBranch",
      options: branchOptions,
      isMultiSelect: true,
    },
    {
      filterKey: "dropOffDateRangeStart",
      filterName: translations.dropOffTime,
      columnKey: "dropOffDateTime",
      isMultiSelect: false,
      options: [
        { label: "Next 2 hours", value: "NEXT_2_HOURS" },
        { label: "Next 6 hours", value: "NEXT_6_HOURS" },
        { label: "Today", value: "TODAY" },
        { label: "Next 48 hours", value: "NEXT_48_HOURS" },
        { label: "This week", value: "THIS_WEEK" },
        { label: "This month", value: "THIS_MONTH" },
      ],
    },
    {
      filterKey: "pickupDateRangeStart",
      filterName: translations.pickupTime,
      columnKey: "pickupDateTime",
      isMultiSelect: false,
      options: [
        { label: "Next 2 hours", value: "NEXT_2_HOURS" },
        { label: "Next 6 hours", value: "NEXT_6_HOURS" },
        { label: "Today", value: "TODAY" },
        { label: "Next 48 hours", value: "NEXT_48_HOURS" },
        { label: "This week", value: "THIS_WEEK" },
        { label: "This month", value: "THIS_MONTH" },
      ],
    },
    {
      filterKey: "status",
      filterName: translations.status,
      columnKey: "status",
      isMultiSelect: true,
      options: [
        { label: "Upcoming", value: "UPCOMING" },
        { label: "No show", value: "NO_SHOW" },
        { label: "Ongoing", value: "ONGOING" },
        { label: "Late return", value: "LATE_RETURN" },
        { label: "Suspended", value: "SUSPENDED" },
        { label: "Cancelled", value: "CANCELLED" },
        { label: "Completed", value: "COMPLETED" },
      ],
    },
    // {
    //   filterKey: "paymentStatus",
    //   filterName: "Payment Status",
    //   columnKey: "paymentStatus",
    //   isMultiSelect: false,
    //   options: [
    //     { label: "Paid", value: "paid" },
    //     { label: "Unpaid", value: "unpaid" },
    //   ],
    // },
  ];
};

export const searchFilters = [
  {
    label: "Agreement No",
    value: "agreementNo",
    translationKey: "agreementNo",
  },
  {
    label: "Booking No",
    value: "bookingNo",
    translationKey: "bookingNo",
  },
  {
    label: "Driver Name",
    value: "driverName",
    translationKey: "driverName",
  },
];

export const BOOKING_DATE_TIME = "bookingDateTime";
