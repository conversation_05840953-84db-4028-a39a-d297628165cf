import { api } from "@/api";
import { type Branch } from "@/api/contracts/branch-contract";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getAllBookingTimeRange } from "@/lib/utils";
import { getLocale, getTranslations } from "next-intl/server";
import { Suspense } from "react";
import { columns } from "./columns";
import { getFilters, searchFilters } from "./filters";

type PageProps = {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
};

export default async function AllBookings({ searchParams }: PageProps) {
  const {
    pageSize,
    pageNumber,
    bookingNo,
    driverName,
    status,
    dropOffDateRangeStart,
    pickupDateRangeStart,
    agreementNo,
    pickUpBranchIds,
    dropOffBranchIds,
  } = await searchParams;
  const ORDER = "desc";
  const { start, end } = getAllBookingTimeRange(pickupDateRangeStart as string);
  const { start: dropOffStart, end: dropOffEnd } = getAllBookingTimeRange(dropOffDateRangeStart as string);

  const t = await getTranslations("AllBookings");
  const tCommon = await getTranslations("common");
  const tFilters = await getTranslations("Filters");
  const locale = await getLocale();

  const [bookings, branches] = await Promise.all([
    api.booking.getUpcomingBookings({
      query: {
        ...(start && { "pickupDateRange.start": start }),
        ...(end && { "pickupDateRange.end": end }),
        ...(dropOffStart && { "dropOffDateRange.start": dropOffStart }),
        ...(dropOffEnd && { "dropOffDateRange.end": dropOffEnd }),
        pickUpBranchIds: pickUpBranchIds as string,
        dropOffBranchIds: dropOffBranchIds as string,
        order: ORDER,
        page: Number(pageNumber ?? 0),
        size: Number(pageSize ?? 10),
        ...(bookingNo && { bookingNo: String(bookingNo) ?? "" }),
        ...(driverName && { driverName: String(driverName) ?? "" }),
        ...(status && { status: String(status) ?? "" }),
        ...(agreementNo && { agreementNo: String(agreementNo) ?? "" }),
      },
    }),
    api.branch.getDetailedBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (bookings.status !== 200) {
    throw new Error(tCommon("errors.failedToFetchBookings"));
  }

  if (branches.status !== 200) {
    throw new Error(tCommon("errors.failedToFetchBranches"));
  }

  const filters = getFilters(branches.body.data, locale as "en" | "ar", {
    pickupBranch: tFilters("pickupBranch"),
    dropOffBranch: tFilters("dropOffBranch"),
    dropOffTime: tFilters("dropOffTime"),
    pickupTime: tFilters("pickupTime"),
    status: tFilters("status"),
  });

  return (
    <div className="px-6 py-4">
      <Suspense fallback={<TableSkeleton />}>
        <DataTable
          searchPlaceholder={t("search")}
          columns={columns}
          filters={filters}
          searchFilters={searchFilters}
          data={{
            data: (bookings.body.data ?? []).map((booking) => {
              const pickupBranch = branches.body.data.find(
                (branchData: Branch) => branchData.id === booking.pickupBranchId
              );

              const dropOffBranch = branches.body.data.find((branchData: Branch) => {
                return branchData.id === booking.dropOffBranchId;
              });
              return {
                ...booking,
                pickupBranch,
                dropOffBranch,
              };
            }),
            total: bookings.body.total ?? 0,
          }}
          emptyMessage={t("emptyMessage")}
        />
      </Suspense>
    </div>
  );
}
