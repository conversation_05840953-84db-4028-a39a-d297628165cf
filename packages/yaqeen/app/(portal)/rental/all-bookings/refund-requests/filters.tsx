import { type Branch } from "@/api/contracts/branch-contract";

export const getFilters = (
  branches: Branch[],
  locale: "en" | "ar",
  transations: {
    pickupBranch: string;
    dropOffBranch: string;
    ibanStatus: string;
    values: {
      filled: string;
      notFilled: string;
    };
  }
) => {
  const branchOptions = branches.map((branch) => ({
    label: branch.name[locale],
    value: String(branch.id),
  }));

  return [
    {
      filterKey: "pickUpBranchIds",
      filterName: transations.pickupBranch,
      columnKey: "pickUpBranchIds",
      isMultiSelect: true,
      options: branchOptions,
    },
    {
      filterKey: "dropOffBranchIds",
      filterName: transations.dropOffBranch,
      columnKey: "dropOffBranchIds",
      isMultiSelect: true,
      options: branchOptions,
    },
    {
      filterKey: "customerDetailAvailable",
      filterName: transations.ibanStatus,
      columnKey: "recipientIBAN",
      isMultiSelect: false,
      options: [
        { label: transations.values.filled, value: "TRUE" },
        { label: transations.values.notFilled, value: "FALSE" },
      ],
    },
  ];
};

export const searchFilters = [
  {
    label: "Booking No",
    value: "bookingNo",
  },
  {
    label: "Agreement No",
    value: "agreementNo",
  },
];
