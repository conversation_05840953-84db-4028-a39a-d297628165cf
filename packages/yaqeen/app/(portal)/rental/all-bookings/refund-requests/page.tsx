import { api } from "@/api";
import Loading from "@/app/(portal)/loading";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type SearchParams } from "nuqs/server";
import { Suspense } from "react";
import { columns } from "./columns";
import { getFilters, searchFilters } from "./filters";
import { getTranslations, getLocale } from "next-intl/server";
type PageProps = {
  searchParams: Promise<SearchParams>;
};

export default async function RefundRequestsPage(props: PageProps) {
  const t = await getTranslations("Refund");
  const tFilters = await getTranslations("Filters");
  const tCommon = await getTranslations("common");
  const locale = await getLocale();

  const searchParams = await props.searchParams;
  const pageNumber = searchParams.pageNumber || "0";
  const pageSize = searchParams.pageSize || "10";
  const bookingNo = searchParams.bookingNo || "";
  const customerDetailAvailable = searchParams.customerDetailAvailable || "";
  const pickUpBranchIds = searchParams.pickUpBranchIds || "";
  const dropOffBranchIds = searchParams.dropOffBranchIds || "";
  const agreementNo = searchParams.agreementNo || "";
  const [branches, refunds] = await Promise.all([
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.refund.searchRefunds({
      query: {
        page: Number(pageNumber) + 1,
        size: Number(pageSize),
        refundRequestStatus: "REQUESTED",
        bookingNo: bookingNo as string,
        agreementNo: agreementNo as string,
        customerDetailAvailable: customerDetailAvailable as string,
        pickUpBranchIds: pickUpBranchIds as string,
        dropOffBranchIds: dropOffBranchIds as string,
      },
    }),
  ]);

  if (branches.status !== 200) {
    throw new Error(tCommon("errors.failedToFetchBranches"));
  }

  if (refunds.status !== 200) {
    throw new Error(t("errors.failedToFetchRefundRequests"));
  }

  const filters = getFilters(branches.body.data, locale as "en" | "ar", {
    pickupBranch: tFilters("pickupBranch"),
    dropOffBranch: tFilters("dropOffBranch"),
    ibanStatus: tFilters("ibanStatus"),
    values: {
      filled: tFilters("values.filled"),
      notFilled: tFilters("values.notFilled"),
    },
  });

  const mappedData = refunds.body?.data?.map((item) => {
    return {
      ...item,
      recipientIBAN: item.recipientDetails?.recipientIBAN || "",
    };
  });

  console.log("mappedData", mappedData);

  return (
    <Suspense fallback={<Loading />}>
      <div className="flex flex-col px-6 py-4">
        <DataTable
          searchPlaceholder={t("search")}
          columns={columns}
          filters={filters}
          data={{
            data: mappedData,
            total: refunds.body.total,
          }}
          emptyMessage={t("emptyMessage")}
          searchFilters={searchFilters}
        />
      </div>
    </Suspense>
  );
}
