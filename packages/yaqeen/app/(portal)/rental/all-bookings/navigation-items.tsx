export interface TabItem {
  labelKey: "all-bookings" | "refund-requests" | "all-agreements";
  isActive?: boolean;
  href: string;
  count?: number;
}

export const TABS: TabItem[] = [
  {
    labelKey: "all-agreements",
    isActive: false,
    href: "/rental/all-bookings/agreements",
  },
  {
    labelKey: "all-bookings",
    isActive: true,
    href: "/rental/all-bookings",
  },
  {
    labelKey: "refund-requests",
    isActive: false,
    href: "/rental/all-bookings/refund-requests",
  },
];
