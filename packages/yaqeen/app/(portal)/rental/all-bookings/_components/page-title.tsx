"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { usePathname } from "next/navigation";
import { type Route } from "next";
import { useTranslations } from "next-intl";

interface PageTitleProps {
  tabs: {
    labelKey: "all-bookings" | "refund-requests" | "all-agreements";
    href: string;
    count?: number;
  }[];
}

export default function PageTitle({ tabs }: PageTitleProps) {
  const pathname = usePathname();
  const t = useTranslations("AllBookings");

  return (
    <section className="border-b bg-slate-50">
      <div className="flex w-full flex-col px-6">
        <div className="flex w-full items-center justify-between py-6">
          <div>
            <h1 className="text-3xl font-medium tracking-tight">{t("title")}</h1>
          </div>
        </div>
        <div className="flex">
          {tabs.map((tab) => (
            <ProgressBarLink
              key={tab.labelKey}
              href={tab.href as Route}
              className={`mx-2 flex items-center py-2 text-sm font-medium ${
                pathname === tab.href ? "border-b-2 border-primary text-primary" : "text-slate-500 hover:text-slate-700"
              }`}
            >
              {t(tab.labelKey)}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="mx-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-xs font-semibold text-white">
                  {tab.count}
                </span>
              )}
            </ProgressBarLink>
          ))}
        </div>
      </div>
    </section>
  );
}
