"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/lib/hooks/use-toast";
import { <PERSON>, Copy, DotsThree } from "@phosphor-icons/react";
import { useMutation } from "@tanstack/react-query";
import clsx from "clsx";
import { useState } from "react";
import { useTranslations } from "next-intl";

export default function IbanDropdown({
  bookingNo,
  ibanLink,
  className,
}: {
  bookingNo: string;
  ibanLink: string;
  className?: string;
}) {
  const [isCopied, setIsCopied] = useState(false);
  const { toast } = useToast();
  const t = useTranslations("Refund");

  const { mutate: sendPaymentLink, isPending } = useMutation({
    mutationFn: async (bookingNo: string) => {
      const response = await fetch(`/next-api/refund?bookingNo=${bookingNo}`);
      if (!response.ok) throw new Error("Failed to send payment link");
      return response.json();
    },
    onError: (error) => {
      console.error(error);
      toast({
        title: "Error",
        description: "Failed to send payment link",
        variant: "destructive",
      });
    },
  });

  const handleCopyLink = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (ibanLink) {
      try {
        await navigator.clipboard.writeText(ibanLink);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (error) {
        console.error("Failed to copy link:", error);
      }
    }
  };

  const handleResendLink = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    sendPaymentLink(bookingNo);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className={clsx("h-8 w-8", className)} asChild>
        <Button variant="ghost" className="border border-slate-200 bg-white p-0">
          <span className="sr-only">Open menu</span>
          <DotsThree className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuItem onClick={handleResendLink}>
          <Bank className="mr-2 h-4 w-4" />
          {isPending ? t("resending") : t("resendIBANLink")}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleCopyLink}>
          <Copy className="mr-2 h-4 w-4" />
          {isCopied ? t("copied") : t("copyLink")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
