"use client";

import { type Booking } from "@/api/contracts/booking/booking-contract";
import { type Branch } from "@/app/(portal)/rental/branches/types";
import { ProgressBarLink } from "@/components/progress-bar";
import TooltipComponent from "@/components/tooltip-component";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTableRowActions } from "@/components/ui/data-table/data-table-row-actions";
import { amountFormatter, getBadgeColor, toNormal } from "@/lib/utils";
import { CaretUpDown, CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";
import { type ColumnDef } from "@tanstack/react-table";
import type { Route } from "next";
import { useTranslations } from "next-intl";
import { format } from "date-fns";

type ColumnMessageKey =
  | "bookingNo"
  | "agreementNo"
  | "pickupDateTime"
  | "dropOffDateTime"
  | "pickupBranch"
  | "dropOffBranch"
  | "status"
  | "totalPrice";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("AllBookings");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

interface BookingSchemaWithBranch extends Booking {
  pickupBranch?: Branch;
  dropOffBranch?: Branch;
}

export const columns: ColumnDef<BookingSchemaWithBranch>[] = [
  {
    id: "id",
    accessorKey: "id",
    enableHiding: true,
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => {
      return <Message messageKey="bookingNo" />;
    },
    cell: ({ row }) => {
      const bookingNo = row.getValue<string>("bookingNo");
      const bookingId = row.original.id;
      const branchId = row.original.pickupBranch?.id;

      return (
        <ProgressBarLink href={`/rental/branches/${branchId}/bookings/${bookingId}` as Route}>
          <span className="text-blue-600">{bookingNo}</span>
        </ProgressBarLink>
      );
    },
  },
  {
    id: "agreementNo",
    accessorKey: "agreementNo",
    header: () => <Message messageKey="agreementNo" />,
    cell: ({ row }) => {
      return row.original.agreementNo || "-";
    },
  },
  {
    accessorKey: "pickupDateTime",
    header: () => <Message messageKey="pickupDateTime" />,
    cell: ({ row }) => {
      if (!row.original.pickupDateTime) {
        return <div>N/A</div>;
      }
      const date = new Date(Number(row.original.pickupDateTime) * 1000);
      return (
        <div className="flex flex-col items-start">
          <span>{format(date, "dd/MM/yyyy")}</span>
          <span className="text-slate-500">{format(date, "HH:mm")}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "pickupBranch",
    header: () => <Message messageKey="pickupBranch" />,
    cell: ({ row }) => {
      return row.original?.pickupBranch?.name?.en;
    },
  },
  {
    accessorKey: "dropOffDateTime",
    header: () => <Message messageKey="dropOffDateTime" />,
    cell: ({ row }) => {
      if (!row.original.dropOffDateTime) {
        return <div>N/A</div>;
      }
      const date = new Date(Number(row.original.dropOffDateTime) * 1000);
      return (
        <div className="flex flex-col items-start">
          <span>{format(date, "dd/MM/yyyy")}</span>
          <span className="text-slate-500">{format(date, "HH:mm")}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "dropOffBranch",
    header: () => <Message messageKey="dropOffBranch" />,
    cell: ({ row }) => {
      return row.original?.dropOffBranch?.name?.en;
    },
  },
  {
    accessorKey: "source",
    header: () => <Message messageKey="status" />,
    cell: ({ row }) => {
      return row.original.source || "N/A";
    },
  },
  {
    accessorKey: "status",
    header: () => <Message messageKey="status" />,
    cell: ({ row }) => {
      const status = row.original.status ?? "";
      return (
        <Badge
          variant="secondary"
          className={`rounded-full px-3 font-normal capitalize ${getBadgeColor(status.toUpperCase() ?? "UPCOMING")}`}
        >
          {status ? toNormal(status) : "Upcoming"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="totalPrice" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const price = row.original.totalPrice;
      const isPaid = row.original.paymentStatus?.toLowerCase() === "paid";
      return (
        <p className="flex w-full items-center gap-x-1">
          {amountFormatter(Number(price))}
          {row.original.status === "UPCOMING" &&
            (isPaid ? (
              <TooltipComponent content="Paid">
                <CheckCircle weight="fill" className="size-4 fill-green-600" />
              </TooltipComponent>
            ) : (
              <TooltipComponent content="Unpaid">
                <XCircle weight="fill" className="size-4 fill-slate-400" />
              </TooltipComponent>
            ))}
        </p>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return (
        <DataTableRowActions<BookingSchemaWithBranch>
          row={row}
          showFullMenu
          pickupBranchId={row.original.pickupBranch?.id?.toString()}
          rowId={row.original.id.toString()}
        />
      );
    },
  },
];
