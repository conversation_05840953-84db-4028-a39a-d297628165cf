"use client";

import { CaretUpDown } from "@phosphor-icons/react/dist/ssr";
import { type ColumnDef } from "@tanstack/react-table";

import { amountFormatter } from "@/lib/utils";

import type { Agreement } from "@/api/contracts/booking/schema";
import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getBadgeColor } from "@/lib/utils";
import { format } from "date-fns";
import { type Route } from "next";
import { useLocale, useTranslations } from "next-intl";

type ColumnMessageKey =
  | "bookingNo"
  | "agreementNo"
  | "pickupDateTime"
  | "dropOffDateTime"
  | "pickupBranch"
  | "dropOffBranch"
  | "status"
  | "total"
  | "pickupTime"
  | "driver"
  | "vehicle";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("AllBookings");
  return (
    <div className="text-start">
      {t(
        // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'messageKey' implicitly has an 'any' typ...
        `columns.${messageKey}`
      )}
    </div>
  );
};

export const columns: ColumnDef<Agreement>[] = [
  {
    id: "agreementNo",
    accessorKey: "agreementNo",
    header: () => <Message messageKey="agreementNo" />,
    cell: ({ row }) => {
      return row.getValue("agreementNo");
    },
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => {
      return <Message messageKey="bookingNo" />;
    },
    cell: ({ row }) => {
      const bookingNo = row.getValue<string>("bookingNo");
      const bookingId = row.original.bookingId;
      const branchId = row.original.pickupBranch?.id;

      return (
        <ProgressBarLink href={`/rental/branches/${branchId}/bookings/${bookingId}` as Route}>
          <span className="text-blue-600">{bookingNo}</span>
        </ProgressBarLink>
      );
    },
  },
  {
    accessorKey: "pickupDate",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="pickupDateTime" />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = new Date(Number(row.original.pickupDateTime) * 1000);
      const formatTime = format(date, "dd/MM/yyyy");

      return formatTime ? (
        <div className="flex flex-col items-start">
          <span>{format(date, "dd/MM/yyyy")}</span>
          <span className="text-slate-500">{format(date, "HH:mm")}</span>
        </div>
      ) : (
        "N/A"
      );
    },
  },
  {
    accessorKey: "dropoffDate",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="dropOffDateTime" />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = new Date(Number(row.original.dropOffDateTime) * 1000);
      const formatTime = format(date, "dd/MM/yyyy");

      return formatTime ? (
        <div className="flex flex-col items-start">
          <span>{format(date, "dd/MM/yyyy")}</span>
          <span className="text-slate-500">{format(date, "HH:mm")}</span>
        </div>
      ) : (
        "N/A"
      );
    },
  },
  {
    accessorKey: "pickupBranch",
    header: () => <Message messageKey="pickupBranch" />,
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t, locale) => <span className="text-blue-600">{row.original?.pickupBranch?.name[locale] || "N/A"}</span>}
        </TranslatedText>
      );
    },
  },
  {
    accessorKey: "dropOffBranch",
    header: () => <Message messageKey="dropOffBranch" />,
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t, locale) => <span className="text-blue-600">{row.original?.dropOffBranch?.name[locale] || "N/A"}</span>}
        </TranslatedText>
      );
    },
  },
  {
    accessorKey: "driver",
    header: () => <Message messageKey="driver" />,
    cell: ({ row }) => {
      const driver = row.getValue<Agreement["driver"]>("driver");
      return (
        <span className="flex items-center gap-x-1">
          <span className="text-blue-600">
            {driver?.firstName} {driver?.lastName}
          </span>
        </span>
      );
    },
  },
  {
    accessorKey: "assignedVehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => {
      const vehicle = row.getValue<Agreement["assignedVehicle"]>("assignedVehicle");
      return (
        <div className="flex flex-col">
          <span className="font-medium text-slate-900">{vehicle?.plateNo}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: () => <Message messageKey="status" />,
    cell: ({ row }) => {
      const status = row.original.status ?? "";
      return (
        <TranslatedText tKey="bookings.columns">
          {(t, _locale) => (
            <Badge
              variant="secondary"
              className={`rounded-full px-3 font-normal capitalize ${getBadgeColor(status.toUpperCase() ?? "UPCOMING")}`}
            >
              {t(
                // @ts-expect-error any
                status ? status : "Upcoming"
              )}
            </Badge>
          )}
        </TranslatedText>
      );
    },
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="total" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <p className="flex w-full items-center gap-x-1">{amountFormatter(Number(row.getValue("totalPrice")))}</p>;
    },
  },
];

const TranslatedText = ({
  children,
  tKey,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: "en" | "ar") => React.ReactNode;
  tKey?: string;
}) => {
  // @ts-expect-error TODO: Fix this type
  const t = useTranslations(tKey);
  const locale = useLocale() as "en" | "ar";
  return <>{children(t, locale)}</>;
};
