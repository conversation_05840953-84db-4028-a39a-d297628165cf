import { type Branch } from "@/api/contracts/branch-contract";

export const getFilters = (
  branches: Branch[],
  locale: "en" | "ar",
  transations: {
    pickupBranch: string;
    dropOffBranch: string;
    dropOffTime: string;
    pickupTime: string;
    status: string;
  }
) => {
  const branchOptions = branches.map((branch) => ({
    label: branch.name[locale],
    value: String(branch.id),
  }));

  return [
    {
      filterKey: "pickUpBranchIds",
      filterName: transations.pickupBranch,
      columnKey: "pickupBranch",
      options: branchOptions,
      isMultiSelect: true,
    },
    {
      filterKey: "dropOffBranchIds",
      filterName: transations.dropOffBranch,
      columnKey: "dropOffBranch",
      options: branchOptions,
      isMultiSelect: true,
    },
    {
      filterKey: "status",
      filterName: transations.status,
      columnKey: "status",
      isMultiSelect: true,
      options: [
        { label: "Ongoing", value: "ONGOING" },
        { label: "Late return", value: "LATE_RETURN" },
        { label: "Suspended", value: "SUSPENDED" },
        { label: "Cancelled", value: "CANCELLED" },
        { label: "Completed", value: "COMPLETED" },
      ],
    },
    {
      filterType: "daterange",
      filterKey: "pickupDate",
      filterName: "Pickup date",
      columnKey: "pickupDate",
      isMultiSelect: false,
      dateRangeTitle: transations.pickupTime,
    },
    {
      filterType: "daterange",
      filterKey: "dropoffDate",
      filterName: "Dropoff date",
      columnKey: "dropoffDate",
      isMultiSelect: false,
      dateRangeTitle: transations.dropOffTime,
    },
  ];
};

export const searchFilters = [
  {
    label: "Booking No",
    value: "bookingNo",
  },
  {
    label: "Agreement No",
    value: "agreementNo",
  },
  {
    label: "Driver Name",
    value: "driverName",
  },
];

export const BOOKING_DATE_TIME = "bookingDateTime";
