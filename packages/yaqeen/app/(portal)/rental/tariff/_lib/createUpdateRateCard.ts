"use server";

import { revalidatePath } from "@/lib/nextjs";
import { api } from "@/api";
import { type TariffCardDetailsReq, type TariffCardDetails, type ErrorResponse } from "@/api/contracts/tariff-contract";

export async function createUpdateRateCard(
  formData: FormData
): Promise<{ error?: ErrorResponse; data?: TariffCardDetailsReq }> {
  try {
    const isDeleteMode = formData.get("isDeleteMode") === "true";
    const isEditMode = formData.get("isEditMode") === "true";

    if (isDeleteMode) {
      const id = formData.get("id") as string;
      const response = await api.tariff.deleteTariffRate({
        params: {
          id,
        },
      });

      if (response.status !== 200) {
        return { error: response.body };
      }

      revalidatePath(`/tariff/${response.body.id}`);
      return { data: response.body };
    }

    const validFrom = new Date(formData.get("validFrom") as string)?.toISOString();
    const validTill = new Date(formData.get("validTill") as string)?.toISOString();
    const type = formData.get("type") as TariffCardDetails["type"];
    const baseTariffRateCardId = formData.get("baseTariffRateCardId") as string;

    const body = {
      tariffIdentifierKey: formData.get("tariffIdentifierKey") as TariffCardDetails["tariffIdentifierKey"],
      tariffIdentifierValue: formData.get("tariffIdentifierValue") as TariffCardDetails["tariffIdentifierValue"],
      tariffRateName: formData.get("tariffRateName") as string,
      currency: formData.get("currency") as string,
      validFrom,
      validTill,
      type,
      tariffRates: JSON.parse(formData.get("tariffRates") as string) as TariffCardDetails["tariffRates"],
      ...(baseTariffRateCardId && { baseTariffRateCardId: Number(baseTariffRateCardId) }),
    } satisfies Omit<TariffCardDetailsReq, "id">;

    if (isEditMode) {
      const id = formData.get("id") as string;
      const response = await api.tariff.updateTariffRate({
        params: {
          id,
        },
        body,
      });

      if (response.status !== 200) {
        return { error: response.body };
      }

      return { data: response.body };
    }

    const response = await api.tariff.createTariffRate({
      body,
    });

    if (response.status !== 201) {
      return { error: response.body };
    }

    return { data: response.body };
  } catch (error) {
    console.error(error);
    let message = "Unknown Error";
    if (error instanceof Error) message = error.message;
    return { error: { desc: message, code: "FE error" } };
  }
}
