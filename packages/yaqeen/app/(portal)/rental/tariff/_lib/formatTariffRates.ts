import { type TariffCardDetails } from "@/api/contracts/tariff-contract";

export const formatTariffData = (data: TariffCardDetails["tariffRates"]) => {
  // remove keys for creation
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return data.map(({ id, createdOn, updatedOn, createdBy, updatedBy, tariffRateCardId, ...keepAttrs }) => {
    Object.entries(keepAttrs).forEach(([key, value]) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      keepAttrs[key] = typeof value === "string" && value === "" ? undefined : value;
    });
    return keepAttrs;
  });
};
