"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function ActionsBar({ disabled }: { disabled?: boolean }) {
  return (
    <footer
      className={cn(
        "flex flex-wrap items-center justify-between rounded-lg border  border-solid border-slate-200 bg-white p-4 text-sm font-medium leading-none text-slate-900 shadow",
      )}
    >
      <Button type="submit" name="draftBtn" value="true" variant="outline" disabled={disabled}>
        Save as draft
      </Button>
      <Button type="submit" name="continueBtn" value="true" disabled={disabled}>
        Continue
      </Button>
    </footer>
  );
}
