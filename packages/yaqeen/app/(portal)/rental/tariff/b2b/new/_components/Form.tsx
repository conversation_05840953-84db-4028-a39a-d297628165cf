"use client";
import { useRouter } from "next/navigation";

import { toast } from "@/lib/hooks/use-toast";
import { createUpdateRateCard } from "../../../_lib/createUpdateRateCard";

export default function RatesForm({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  const onSubmit = async (form: FormData) => {
    const isDraftSave = form.get("draftBtn");

    form.append("currency", "SAR");

    const response = await createUpdateRateCard(form);
    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }
    const isEditMode = form.get("isEditMode") === "true";
    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    if (isDraftSave) {
      router.replace(`/rental/tariff/b2b`);
      return;
    }

    const id = response.data?.id;
    router.replace(`/rental/tariff/b2b/${id}`);
  };
  return <form action={onSubmit}>{children}</form>;
}
