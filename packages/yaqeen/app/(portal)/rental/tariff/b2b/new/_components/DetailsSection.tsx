"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

import InputField from "@/components/atoms/InputField";
import CardWrapper from "@/components/atoms/CardWrapper";

import Header from "@/app/(portal)/rental/tariff/_components/Header";
import { ActionsBar } from "./ActionsBar";
import RatesForm from "./Form";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import SelectBaseCard from "../../[id]/_components/SelectBaseCard";

type CustomerData = {
  data: Array<{
    accountType: number;
    carProId: string;
    id: number;
    isActive: boolean;
    name: { en: string };
    sapId: string;
    email?: string;
    phone?: string;
  }>;
  total: number;
};

const DetailsSection = ({ activeBaseCards }: { activeBaseCards: TariffCardDetails[] }) => {
  const t = useTranslations("tariff");
  const [customerData, setCustomerData] = useState<CustomerData["data"][number] | null>(null);
  const [debtorCode, setDebtorCode] = useState("");
  const [rateName, setRateName] = useState("");
  const [validFrom, setValidFrom] = useState("");
  const [validTill, setValidTill] = useState("");
  const [type, setType] = useState<"fixed" | "dynamic">("fixed");
  const [isDebtorDetailsLoading, setIsDebtorDetailsLoading] = useState(false);

  const formattedCards = activeBaseCards
    ?.filter((card) => !card.baseTariffRateCardId)
    ?.map((card) => ({
      label: card.tariffRateName,
      value: card.id,
    }));

  const isFieldsEmpty =
    !rateName ||
    !validFrom ||
    !validTill ||
    !debtorCode ||
    !customerData?.id ||
    debtorCode !== String(customerData?.id);

  const handleFetchData = async () => {
    if (debtorCode) {
      try {
        setIsDebtorDetailsLoading(true);
        const response = await fetch(`/next-api/customerAccounts?id=${debtorCode}`);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = (await response.json()) as CustomerData;
        setCustomerData(data.data?.[0] ?? null);
      } catch (error) {
        console.error("Error fetching customer data:", error);
      }
      setIsDebtorDetailsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50/30">
      <Header
        smallPage
        homeLink="/rental/tariff/b2b"
        pageName={t("debtor.newDebtorCard")}
        subTitle={t("debtor.newDebtorCardSubTitle")}
      />
      <RatesForm>
        <div className="mx-auto max-w-3xl space-y-6 p-6">
          <input hidden name="tariffIdentifierKey" defaultValue="B2B" />

          <div className="space-y-4">
            <div className="space-y-2">
              <InputField
                required
                name="tariffRateName"
                label="Rate name"
                id="rateName"
                className="w-full"
                placeholder="Enter the name of debtor card"
                value={rateName}
                onChange={(e) => setRateName(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">{t("debtor.startDate")}</Label>
                <Input
                  name="validFrom"
                  id="startDate"
                  type="date"
                  required
                  value={validFrom}
                  onChange={(e) => setValidFrom(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">{t("debtor.endDate")}</Label>
                <Input
                  name="validTill"
                  id="endDate"
                  type="date"
                  required
                  value={validTill}
                  onChange={(e) => setValidTill(e.target.value)}
                />
              </div>
            </div>
          </div>

          <CardWrapper title={t("debtor.debtorCard")}>
            <RadioGroup
              required
              name="type"
              value={type}
              onValueChange={(value: "fixed" | "dynamic") => setType(value)}
            >
              <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                <RadioGroupItem value="fixed" id="fixed" className="mt-1" />
                <Label htmlFor="fixed" className="flex cursor-pointer flex-col gap-2 font-medium">
                  <span>{t("debtor.fixedRate")}</span>
                  <p className="text-sm  text-gray-500">{t("debtor.fixedRateDescription")}</p>
                </Label>
              </div>

              <Separator />

              <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                <RadioGroupItem value="dynamic" id="dynamic" className="mt-1" />
                <Label htmlFor="dynamic" className="flex cursor-pointer flex-col gap-2 font-medium">
                  <span>{t("debtor.dynamicRate")}</span>
                  <p className="text-sm text-gray-500">{t("debtor.dynamicRateDescription")}</p>
                </Label>
              </div>
            </RadioGroup>
          </CardWrapper>

          {type === "dynamic" && (
            <CardWrapper title={t("debtor.baseCardDetails")}>
              <SelectBaseCard required options={formattedCards} />
            </CardWrapper>
          )}

          <CardWrapper title={t("debtor.debtorDetails")}>
            <div className="flex items-end gap-4">
              <div className="flex-1 space-y-2">
                <Label htmlFor="debtorCode">Debtor Id</Label>
                <Input
                  required
                  name="tariffIdentifierValue"
                  id="debtorCode"
                  placeholder="Enter code"
                  type="number"
                  value={debtorCode}
                  onChange={(e) => setDebtorCode(e.target.value)}
                />
              </div>
              <Button
                type="button"
                variant="secondary"
                disabled={!debtorCode || isDebtorDetailsLoading}
                className="mb-0.5"
                onClick={handleFetchData}
              >
                Fetch data {isDebtorDetailsLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : <></>}
              </Button>
            </div>
            {customerData && (
              <div className="mt-4 flex flex-wrap justify-between gap-4 rounded-lg bg-slate-50 p-4">
                <InputField
                  className="w-72"
                  label="Company Name"
                  name="debtorName"
                  value={customerData?.name?.en ?? ""}
                  onChange={(e) => setCustomerData({ ...customerData, name: { en: e.target.value } })}
                  readOnly
                />
                <InputField
                  className="w-72"
                  label="Debtor Id"
                  name="debtorId"
                  value={customerData?.id ?? ""}
                  readOnly
                />
                <InputField
                  className="w-72"
                  label="Email Address"
                  name="debtorEmail"
                  value={customerData?.email ?? ""}
                  onChange={(e) => setCustomerData({ ...customerData, email: e.target.value })}
                  readOnly
                />
                <InputField
                  className="w-72"
                  label="Phone Number"
                  name="debtorPhone"
                  value={customerData?.phone ?? ""}
                  onChange={(e) => setCustomerData({ ...customerData, phone: e.target.value })}
                  readOnly
                />
              </div>
            )}
          </CardWrapper>

          <ActionsBar disabled={isFieldsEmpty} />
        </div>
      </RatesForm>
    </div>
  );
};

export default DetailsSection;
