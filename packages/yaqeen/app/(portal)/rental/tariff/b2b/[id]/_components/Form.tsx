"use client";

import { useRouter } from "next/navigation";
import { toast } from "@/lib/hooks/use-toast";

import { useRatesContext } from "./Rates.context";
import { createUpdateRateCard } from "../../../_lib/createUpdateRateCard";

export default function RatesForm({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  const { rates } = useRatesContext();

  const onSubmit = async (formData: FormData) => {
    const type = formData.get("type");

    if (!rates?.length) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: "Rates should not be empty",
      });
      return;
    }

    let ratesWithType = [];
    if (type === "dynamic") {
      ratesWithType =
        rates?.map((el) => ({
          ...(el.type === "dynamic" && { id: el.id }),
          type,
          carGroupId: el.carGroupId,
          carGroupCode: el.carGroupCode,
          rentalDiscountPercentage: el.rentalDiscountPercentage,
          cdwDiscountPercentage: el.rentalDiscountPercentage,
          dailyKmDiscount: el.rentalDiscountPercentage,
          extraKmDiscount: el.rentalDiscountPercentage,
        })) ?? [];
    } else {
      ratesWithType = rates?.map((el) => ({ ...el, type })) ?? [];
    }

    formData.append("tariffRates", JSON.stringify(ratesWithType));
    formData.append("currency", "SAR");

    const response = await createUpdateRateCard(formData);
    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }

    const isEditMode = formData.get("isEditMode") === "true";
    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    if (isEditMode) {
      router.replace(`/rental/tariff/b2b`);
      return;
    }

    const id = response.data?.id;
    router.replace(`/rental/tariff/b2b/${id}`);
  };
  return <form action={onSubmit}>{children}</form>;
}
