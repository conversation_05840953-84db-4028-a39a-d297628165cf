"use client";

import { useQueryState } from "nuqs";

import Select, { type SelectProps } from "@/components/atoms/Select";

type Option = { value: string; label: string };

const SelectBaseCard = ({
  options,
  defaultValue,
  customClasses,
  required,
}: {
  options: Option[];
  defaultValue?: string;
  customClasses?: SelectProps<Option>["customClasses"];
  required?: boolean;
}) => {
  const [baseTariffRateCardId, setBaseTariffRateCardId] = useQueryState("baseTariffRateCardId");

  return (
    <div>
      <Select
        required={required}
        name="baseTariffRateCardId"
        defaultValue={baseTariffRateCardId ?? defaultValue ?? ""}
        placeholder="Use available rate card"
        options={options}
        onSelect={(value) => {
          void setBaseTariffRateCardId(`${value}`);
        }}
        customClasses={customClasses}
      />
    </div>
  );
};

export default SelectBaseCard;
