"use client";

import { DataTable } from "@/components/ui/data-table/data-table";
import { Plus } from "@phosphor-icons/react/dist/ssr";

import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";

import { Button } from "@/components/ui/button";

import { generateColumns } from "./columns";
import { generateColumns as generateDynamicColumns } from "./dynamicRatesColumns";
import { useRatesContext } from "./Rates.context";

export default function RatesTable({
  isEditEnabled,
  vehicleGroups,
  type,
}: {
  isEditEnabled: boolean;
  vehicleGroups: VehicleGroups;
  type: TariffCardDetails["type"];
}) {
  const { rates, setRates } = useRatesContext();
  const formattedVehicleGroups = vehicleGroups.content.map((el) => ({ label: el.code, value: el.code }));

  const fixedColumns = generateColumns(formattedVehicleGroups);
  const dynamicColumns = generateDynamicColumns();

  const newColumns = isEditEnabled ? fixedColumns : fixedColumns.filter((col) => col.id !== "edit");
  const newDynamicColumns = isEditEnabled ? dynamicColumns : dynamicColumns.filter((col) => col.id !== "edit");

  const columns = type === "dynamic" ? newDynamicColumns : newColumns;

  return (
    <div>
      <DataTable
        columns={columns}
        paginationEnabled={false}
        data={{
          data: rates ?? [],
          total: rates?.length ?? 0,
        }}
        onDataChange={(newRates) => {
          const newRates2: TariffCardDetails["tariffRates"] = JSON.parse(JSON.stringify(newRates));
          const updatedRates = newRates2?.map((el) => {
            const carGroupId = vehicleGroups.content.find((vehicle) => el.carGroupCode === vehicle.code)?.id;
            return { ...el, carGroupId: carGroupId ? carGroupId : "" };
          });

          setRates(updatedRates);
        }}
        footerRow={
          !isEditEnabled || type === "dynamic"
            ? null
            : (table) => {
                const meta = table.options.meta;

                return (
                  <>
                    <span
                      className="flex w-fit cursor-pointer items-center gap-2 rounded-md border border-slate-200 p-2 pe-6"
                      onClick={() => meta?.addRow()}
                    >
                      <Plus />
                      <span>Add another car group price</span>
                    </span>
                  </>
                );
              }
        }
        emptyMessage={(table) => (
          <div className="flex flex-col gap-2">
            There are no Details
            {type === "fixed" && (
              <Button type="button" variant="outline" onClick={() => table.options.meta?.addRow()}>
                Add new
              </Button>
            )}
          </div>
        )}
      />
    </div>
  );
}
