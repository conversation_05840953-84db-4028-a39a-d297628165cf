import { api } from "@/api";

import RateDetailsSection from "./_components/RatesDetailsSection";

const getData = async (id: string) => {
  const [cardDetails, vehicleGroups] = await Promise.allSettled([
    api.tariff.getTariffCardById({
      params: {
        id: Number(id),
      },
    }),
    api.fleet.vehicleGroupsContract.getCarGroups({
      query: {
        pageSize: 50,
      },
    }),
  ]);

  if (cardDetails?.status === "rejected") {
    throw new Error(`Error: ${cardDetails.reason}`);
  }

  if (cardDetails?.value.status !== 200) {
    throw new Error(`Error: ${cardDetails.value.body.code}::${cardDetails.value.body.desc}`);
  }

  if (vehicleGroups?.status === "rejected") {
    throw new Error(`Error: ${vehicleGroups.reason}`);
  }

  if (vehicleGroups?.value.status !== 200) {
    throw new Error(`Error: ${vehicleGroups.value.body.code}::${vehicleGroups.value.body.desc}`);
  }

  const debtorDetails = await api.customer.getCustomerAccountsById({
    query: {
      id: cardDetails?.value.body.tariffIdentifierValue,
    },
  });

  if (debtorDetails?.status !== 200) {
    throw new Error(`Error: ${debtorDetails.body.code}::${debtorDetails.body.desc}`);
  }

  const isDynamic = cardDetails?.value.body.type === "dynamic";

  let ratesCards;
  if (isDynamic) {
    const activeBaseCards = await api.tariff.getActiveTariffRateCards({
      query: {
        size: 100,
      },
    });

    if (activeBaseCards?.status !== 200) {
      throw new Error(`Error: ${activeBaseCards.body.code}::${activeBaseCards.body.desc}`);
    }

    ratesCards = activeBaseCards?.body;
  } else {
    const [b2bCards, b2cCards] = await Promise.allSettled([
      api.tariff.getTariffRateCards({
        query: {
          type: "B2B",
          size: 100,
        },
      }),
      api.tariff.getTariffRateCards({
        query: {
          type: "B2C",
          size: 100,
        },
      }),
    ]);

    if (b2bCards?.status === "rejected") {
      throw new Error(`Error: ${b2bCards.reason}`);
    }

    if (b2bCards?.value.status !== 200) {
      throw new Error(`Error: ${b2bCards.value.body.code}::${b2bCards.value.body.desc}`);
    }

    if (b2cCards?.status === "rejected") {
      throw new Error(`Error: ${b2cCards.reason}`);
    }

    if (b2cCards?.value.status !== 200) {
      throw new Error(`Error: ${b2cCards.value.body.code}::${b2cCards.value.body.desc}`);
    }

    ratesCards = [...(b2bCards?.value?.body?.content ?? []), ...(b2cCards?.value?.body?.content ?? [])];
  }

  return {
    data: cardDetails.value.body,
    vehicleGroups: vehicleGroups.value.body,
    activeBaseCards: ratesCards,
    debtorDetails: debtorDetails.body,
  };
};

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const paramsA = await params;
  const { id } = paramsA;

  const { data, vehicleGroups, activeBaseCards, debtorDetails } = await getData(id);

  return (
    <RateDetailsSection
      vehicleGroups={vehicleGroups}
      activeBaseCards={activeBaseCards}
      isEditMode
      data={data}
      debtorDetails={debtorDetails}
    />
  );
}
