"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import TableCell from "@/components/atoms/Table/TableCell";
import ActionsCell from "@/components/atoms/Table/ActionsCell";

export const generateColumns = (): ColumnDef<TariffCardDetails["tariffRates"][number]>[] => {
  return [
    {
      id: "general",
      columns: [
        {
          header: "Car group code",
          accessorKey: "carGroupCode",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "Discount %",
          accessorKey: "rentalDiscountPercentage",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
          meta: {
            type: "number",
            max: 100,
          },
        },
        {
          header: "Daily Allowance (KM)",
          accessorKey: "dailyKmAllowance",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "Extra KM(SAR)",
          accessorKey: "extraKmCharges",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
      ],
    },
    {
      header: "Rental Price",
      columns: [
        {
          header: "daily",
          accessorKey: "dailyPrice",
          cell: ({ row, getValue }) => {
            const val = Number(getValue() ?? 0);
            return <div>{Number((val - (val * (row.original?.rentalDiscountPercentage ?? 0)) / 100).toFixed(2))}</div>;
          },
        },
        {
          header: "weekly",
          accessorKey: "weeklyPrice",
          cell: ({ row, getValue }) => {
            const val = Number(getValue() ?? 0);
            return <div>{Number((val - (val * (row.original?.rentalDiscountPercentage ?? 0)) / 100).toFixed(2))}</div>;
          },
        },
        {
          header: "monthly",
          accessorKey: "monthlyPrice",
          cell: ({ row, getValue }) => {
            const val = Number(getValue() ?? 0);
            return <div>{Number((val - (val * (row.original?.rentalDiscountPercentage ?? 0)) / 100).toFixed(2))}</div>;
          },
        },
        {
          header: "yearly",
          accessorKey: "annualPrice",
          cell: ({ row, getValue }) => {
            const val = Number(getValue() ?? 0);
            return <div>{Number((val - (val * (row.original?.rentalDiscountPercentage ?? 0)) / 100).toFixed(2))}</div>;
          },
        },
      ],
    },
    {
      header: "Insurance",
      columns: [
        {
          header: "Deductibles",
          accessorKey: "cdwDeductibles",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "daily",
          accessorKey: "dailyCdw",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "weekly",
          accessorKey: "weeklyCdw",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "monthly",
          accessorKey: "monthlyCdw",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "yearly",
          accessorKey: "annualCdw",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
      ],
    },
    {
      header: "Credit Card authorization",
      columns: [
        {
          header: "daily",
          accessorKey: "dailyAuthAmount",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "weekly",
          accessorKey: "weeklyAuthAmount",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "monthly",
          accessorKey: "monthlyAuthAmount",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
        {
          header: "yearly",
          accessorKey: "annualAuthAmount",
          cell: TableCell<TariffCardDetails["tariffRates"][number]>,
        },
      ],
    },
    {
      id: "edit",
      cell: ActionsCell,
    },
  ];
};
