"use client";

import { type JSX, useEffect, useState } from "react";
import { type ColumnDef, type CellContext } from "@tanstack/react-table";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { Select, SelectContent, SelectGroup, SelectItem, SelectValue } from "@/components/ui/select";
import { SelectTrigger } from "@radix-ui/react-select";
// import { Trash } from "@phosphor-icons/react/dist/ssr";

type RowType = TariffCardDetails["tariffRates"][number];

const TableCell = ({ getValue, row, column, table }: CellContext<RowType, string | number>): JSX.Element => {
  const columnMeta = column.columnDef.meta;
  const tableMeta = table.options.meta;
  const col = column.id as keyof RowType;
  const rowIdx = row.id as unknown as number;
  const initialValue = (tableMeta?.localData?.[rowIdx] as RowType)?.[col] ?? getValue();
  const [value, setValue] = useState<string | number>(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue, tableMeta?.editedRows?.[row.id]]);

  const onBlur = () => {
    let formattedValue = value;
    if (columnMeta?.type === "number") {
      formattedValue = +value;
    }

    table.options.meta?.updateTempData?.(row.index, column.id, formattedValue);
  };

  if (tableMeta?.editedRows[row.id]) {
    if (columnMeta?.type === "select") {
      const options = columnMeta?.selectOptions;

      return (
        <div className="max-w-18 border p-1" onBlur={onBlur}>
          <Select
            defaultValue={`${value}`}
            onValueChange={(value) => {
              setValue(value);
            }}
          >
            <SelectTrigger className="min-h-6 w-full min-w-6 text-start focus:outline-none">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {options?.map((option) => {
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  );
                })}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      );
    }

    return (
      <input
        className="max-w-14 border p-1"
        value={value || ""}
        onChange={(e) => setValue(e.target.value)}
        onBlur={onBlur}
        type={columnMeta?.type ?? "text"}
      />
    );
  }
  return <span>{getValue() || "N/A"}</span>;
};

const ActionsCell = ({ row, table }: CellContext<RowType, unknown>) => {
  const meta = table.options.meta;

  const setEditedRows = () => {
    meta?.toggleEdit(row.index);
  };
  const revert = () => {
    meta?.revertData?.(row.index);
  };
  const doneEdit = () => {
    meta?.updateData?.(row.index);
  };
  // const deleteRow = () => {
  //   meta?.removeRow?.(row.index);
  // };

  return meta?.editedRows[row.id] ? (
    <div className="flex gap-2">
      <button type="button" onClick={revert} className="me-2" name="cancel">
        X
      </button>
      <button type="button" onClick={doneEdit} name="done">
        ✔
      </button>
    </div>
  ) : (
    <div className="flex gap-2">
      <button type="button" onClick={setEditedRows} name="edit">
        ✐
      </button>
      {/* <button type="button" onClick={deleteRow} name="delete">
        <Trash />
      </button> */}
    </div>
  );
};

export const generateColumns = (
  vehicleGroups: { label: string; value: string }[]
): ColumnDef<TariffCardDetails["tariffRates"][number]>[] => {
  return [
    {
      id: "general",
      columns: [
        {
          header: "Car group code",
          accessorKey: "carGroupCode",
          cell: TableCell,
          meta: {
            type: "select",
            selectOptions: vehicleGroups,
          },
        },
        {
          header: "Daily Allowance (KM)",
          accessorKey: "dailyKmAllowance",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "Extra KM(SAR)",
          accessorKey: "extraKmCharges",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
      ],
    },
    {
      header: "Rental Price",
      columns: [
        {
          header: "daily",
          accessorKey: "dailyPrice",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "weekly",
          accessorKey: "weeklyPrice",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "monthly",
          accessorKey: "monthlyPrice",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "yearly",
          accessorKey: "annualPrice",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
      ],
    },
    {
      header: "Insurance",
      columns: [
        {
          header: "Deductibles",
          accessorKey: "cdwDeductibles",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "daily",
          accessorKey: "dailyCdw",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "weekly",
          accessorKey: "weeklyCdw",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "monthly",
          accessorKey: "monthlyCdw",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "yearly",
          accessorKey: "annualCdw",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
      ],
    },
    {
      header: "Credit Card authorization",
      columns: [
        {
          header: "daily",
          accessorKey: "dailyAuthAmount",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "weekly",
          accessorKey: "weeklyAuthAmount",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "monthly",
          accessorKey: "monthlyAuthAmount",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
        {
          header: "yearly",
          accessorKey: "annualAuthAmount",
          cell: TableCell,
          meta: {
            type: "number",
          },
        },
      ],
    },
    {
      id: "edit",
      cell: ActionsCell,
    },
  ];
};
