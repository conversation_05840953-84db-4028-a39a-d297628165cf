"use client";

import { useCallback, useEffect, startTransition } from "react";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { FilePlus } from "@phosphor-icons/react/dist/ssr";
import { useTranslations } from "next-intl";

import { ProgressBarLink, useProgressBar } from "@/components/progress-bar";

import { Button } from "@/components/ui/button";

import RateCard, { type RateCardProps } from "./RateCard";
import { type Route } from "next";

type RateCardsSectionProps = {
  cards: Array<RateCardProps>;
  total: number;
};

export default function RateCardsSection({ cards, total }: RateCardsSectionProps) {
  const t = useTranslations("tariff");
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const progress = useProgressBar();

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);

      return params.toString();
    },
    [searchParams],
  );

  useEffect(() => {
    router.push((pathname + "?" + createQueryString("size", `10`)) as Route, { scroll: false });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <div className="flex flex-col bg-white px-6">
        <section className="p-2">
          <div className="my-2 flex justify-between">
            <h2 className="text-2xl font-medium leading-8">{t("rateCard")}</h2>
            <div className="flex gap-2">
              <ProgressBarLink className="w-full" href={`/rental/tariff/b2b/new`}>
                <Button className="flex gap-2">{t("debtor.addDebtor")}</Button>
              </ProgressBarLink>
            </div>
          </div>
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-wrap gap-5">
              {cards.length < 1 && (
                <div className="flex min-h-60 w-full flex-col items-center  justify-center gap-3 rounded-lg bg-slate-100 p-3">
                  <h3>{t("noCardsAvailable")}</h3>
                  <div>
                    <ProgressBarLink className="w-full" href={`/rental/tariff/b2b/new`}>
                      <div className="flex w-full items-center gap-2 rounded-md border border-slate-300 bg-white p-3 hover:bg-slate-100">
                        <FilePlus className="h-4 w-4" weight="bold" />
                        {t("debtor.addDebtor")}
                      </div>
                    </ProgressBarLink>
                  </div>
                </div>
              )}
              {cards?.map((card) => {
                return <RateCard key={card.id} {...card} />;
              })}
            </div>
            {total > cards.length && (
              <div className="flex items-center justify-center">
                <Button
                  className="w-56"
                  onClick={() => {
                    const size = searchParams.get("size") ?? 10;
                    progress.start();
                    startTransition(() => {
                      router.push((pathname + "?" + createQueryString("size", `${Number(size) + 10}`)) as Route, {
                        scroll: false,
                      });
                      progress.done();
                    });
                  }}
                >
                  {t("loadMore")}
                </Button>
              </div>
            )}
          </div>
        </section>
      </div>
    </>
  );
}
