import { format } from "date-fns";
import { useTranslations } from "next-intl";

import { Card, CardContent } from "@/components/ui/card";

import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import TariffCardStatusBadge, { type TariffCardStatusBadgeProps } from "../../../_components/RateCards/StatusBadge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { PencilSimple } from "@phosphor-icons/react/dist/ssr/PencilSimple";
import { Prohibit } from "@phosphor-icons/react/dist/ssr/Prohibit";
import { DotsThree } from "@phosphor-icons/react/dist/ssr/DotsThree";
import { ProgressBarLink } from "@/components/progress-bar";
import { createUpdateRateCard } from "../../../_lib/createUpdateRateCard";
import { toast } from "@/lib/hooks/use-toast";
import { type Route } from "next";

export type RateCardProps = {
  id: string;
  tariffRateName: string;
  baseTariffRateCardId?: number;
  tariffIdentifierKey: TariffCardDetails["tariffIdentifierKey"];
  status: TariffCardStatusBadgeProps["status"];
  type: TariffCardDetails["type"];
  updatedBy: string;
  createdBy: string;
  /** Date */
  updatedOn: string;
  /** Date */
  createdOn: string;
  validFrom: Date;
  validTill: Date;
};

export default function RateCard({
  id,
  status,
  tariffRateName,
  createdBy,
  updatedOn,
  validFrom,
  validTill,
  type,
}: RateCardProps) {
  const t = useTranslations("tariff");
  const formattedUpdatedOn = format(updatedOn, "dd-MM-yyyy");
  const validPeriod = `${format(validFrom, "dd-MM-yyyy")} till ${format(validTill, "dd-MM-yyyy")}`;

  const isEditAllowed = status !== "ACTIVE" && status !== "APPROVED";

  const onDelete = async () => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("isDeleteMode", "true");
    try {
      const response = await createUpdateRateCard(formData);

      if (response.error) {
        toast({
          variant: "destructive",
          title: "Failed",
          description: response.error?.desc,
        });
        return;
      }

      toast({
        title: "Success",
        description: "Rate card deleted successfully",
      });
    } catch (error) {
      console.error(error);
      let message = "Unknown Error";
      if (error instanceof Error) message = error.message;

      toast({
        variant: "destructive",
        title: "Failed",
        description: message,
      });
    }
  };

  return (
    <Card className="w-[32%] min-w-96 pt-5">
      <CardContent className="flex w-full flex-col gap-5">
        <div className="relative flex justify-between">
          <div>
            <span className="my-auto flex-auto text-sm font-normal leading-4 text-slate-500">
              {t("lastUpdated")} {formattedUpdatedOn}
            </span>
            <p className="flex items-center gap-1 text-md font-bold tracking-tight text-slate-900">
              {tariffRateName}
              <span className="text-xs font-light tracking-tight text-slate-600">({type} rate)</span>
            </p>
            <p className="text-xs tracking-tight text-slate-600">
              {t("validPeriod")}: {validPeriod}
            </p>
          </div>
          <TariffCardStatusBadge status={status} className=" absolute end-1  top-2" />
          <div className="absolute end-1 top-12 flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="flex h-8 w-8 items-center rounded-md p-1 outline-none focus:outline-none focus-visible:ring-0"
                >
                  <DotsThree className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[150px]">
                <ProgressBarLink href={`/rental/tariff/b2b/${id}` as Route}>
                  <DropdownMenuItem className="flex cursor-pointer items-center gap-2">
                    <PencilSimple className="h-4 w-4" />
                    {t("modify")}
                  </DropdownMenuItem>
                </ProgressBarLink>

                {isEditAllowed && (
                  <DropdownMenuItem className="flex cursor-pointer items-center gap-2" onClick={onDelete}>
                    <Prohibit className="h-4 w-4" />
                    {t("delete")}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="flex justify-between">
          <div>
            <p className="text-xs font-bold tracking-tight text-slate-600">{t("createdBy")}</p>
            <p className="text-sm  tracking-tight text-slate-600">{createdBy}</p>
          </div>
          <div>
            <p className="text-xs font-bold tracking-tight text-slate-600">{t("approvalDate")}</p>
            <p className="text-sm  tracking-tight text-slate-600">-</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
