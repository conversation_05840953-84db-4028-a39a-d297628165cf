"use client";

import React from "react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  Drawer<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

import AddOnsRatesForm from "./Form";
import { useAddOnsRatesContext } from "./AddOnsRates.context";
import { Textarea } from "@/components/ui/textarea";

const addOnTypes = [
  { value: "FIXED", label: "One time" },
  {
    value: "DURATION",
    label: "Duration",
  },
];

export type AddOnRatesDrawerProps = {
  formattedAddons: { value: string; label: string }[];
  formattedCarGroups: { value: string; label: string }[];
};

export function AddOnRatesDrawer({ formattedAddons, formattedCarGroups }: AddOnRatesDrawerProps) {
  const t = useTranslations("tariff");
  const { openDrawer, setOpenDrawer, defaultValues, setDefaultValues } = useAddOnsRatesContext();

  const defaultCarGroup = defaultValues?.carGroupCode ? `${defaultValues?.carGroupCode}` : `all`;

  return (
    <Drawer
      direction="right"
      open={openDrawer}
      onClose={() => {
        setDefaultValues(undefined);
        setOpenDrawer(undefined);
      }}
    >
      <DrawerTrigger asChild>
        <Button variant="secondary">{t("addons.addAddOnsRatesBtn")}</Button>
      </DrawerTrigger>
      <DrawerContent className="w-[26rem]">
        <AddOnsRatesForm className="grid h-screen grid-rows-[auto_1fr_auto]">
          <DrawerHeader className="p p-6">
            <DrawerTitle className="text-2xl font-bold">{t("addons.addAddOnsRatesTitle")}</DrawerTitle>
          </DrawerHeader>
          <DrawerDescription asChild className="overflow-auto">
            <div className="flex flex-col gap-3 p-4">
              <input hidden name="isEditMode" defaultValue={`${!!defaultValues?.id}`} />
              <input hidden name="id" defaultValue={defaultValues?.id ? `${defaultValues?.id}` : undefined} />
              <div>
                <Label htmlFor="name" className="w-full text-sm font-medium text-slate-900">
                  {t("name")}
                </Label>
                <Input
                  required
                  id="name"
                  name="name"
                  placeholder="Enter addon name"
                  defaultValue={defaultValues?.name}
                />
              </div>

              <div>
                <Label htmlFor="addons" className="w-full text-sm font-medium text-slate-900">
                  {t("addons.addonsIds")}
                </Label>
                <Select
                  name="addOnId"
                  required
                  defaultValue={defaultValues?.addOnId ? `${defaultValues?.addOnId}` : undefined}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    {formattedAddons.map((addon) => {
                      return (
                        <SelectItem key={addon.value} value={addon.value}>
                          {addon.label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="carGroupCode" className="w-full text-sm font-medium text-slate-900">
                  {t("addons.applicableGroup")}
                </Label>
                <Select name="carGroupCode" required defaultValue={defaultCarGroup}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={`all`}>All</SelectItem>
                    {formattedCarGroups.map((group) => {
                      return (
                        <SelectItem key={group.value} value={group.value}>
                          {group.label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              <Separator className="-mx-4 my-4 w-auto" />
              <div className="flex flex-col gap-4">
                <div>
                  <Label htmlFor="pricingStrategy" className="w-full text-sm font-medium text-slate-900">
                    {t("addons.type")}
                  </Label>
                  <Select name="pricingStrategy" required defaultValue={defaultValues?.pricingStrategy}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      {addOnTypes.map((type) => {
                        return (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
                <div className="mt-4 flex flex-col gap-2">
                  {/* <Label className="w-full text-sm font-medium text-slate-900">{t("Prices(SAR)")}</Label> */}
                  <div className="flex flex-wrap gap-3">
                    <div className="flex gap-2">
                      <div>
                        <Label htmlFor="dailyPrice" className="w-full text-sm font-medium text-slate-900">
                          {t("addons.price")}
                        </Label>
                        <Input
                          required
                          id="dailyPrice"
                          type="number"
                          name="dailyPrice"
                          placeholder="Price of add on"
                          defaultValue={defaultValues?.dailyPrice}
                        />
                      </div>
                      <div>
                        <Label htmlFor="currency" className="w-full text-sm font-medium text-slate-900">
                          {t("addons.priceUnit")}
                        </Label>
                        <Input
                          required
                          readOnly
                          id="currency"
                          name="currency"
                          defaultValue="SAR"
                          placeholder="Enter price unit"
                        />
                      </div>
                    </div>
                    {/* <div className="flex gap-2">
                      <div>
                        <Label htmlFor="weeklyPrice" className="w-full text-sm font-medium text-slate-900">
                          {t("Weekly Price")}
                        </Label>
                        <Input
                          // required
                          id="weeklyPrice"
                          type="number"
                          name="weeklyPrice"
                          placeholder="weeklyPrice of add on"
                          defaultValue={defaultValues?.weeklyPrice}
                        />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div>
                        <Label htmlFor="monthlyPrice" className="w-full text-sm font-medium text-slate-900">
                          {t("Monthly Price")}
                        </Label>
                        <Input
                          // required
                          id="monthlyPrice"
                          type="number"
                          name="monthlyPrice"
                          placeholder="monthlyPrice of add on"
                          defaultValue={defaultValues?.monthlyPrice}
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <div>
                        <Label htmlFor="quarterlyPrice" className="w-full text-sm font-medium text-slate-900">
                          {t("Quarterly Price")}
                        </Label>
                        <Input
                          // required
                          id="quarterlyPrice"
                          type="number"
                          name="quarterlyPrice"
                          placeholder="quarterlyPrice of add on"
                          defaultValue={defaultValues?.quarterlyPrice}
                        />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div>
                        <Label htmlFor="halfYearlyPrice" className="w-full text-sm font-medium text-slate-900">
                          {t("HalfYearly Price")}
                        </Label>
                        <Input
                          // required
                          id="halfYearlyPrice"
                          type="number"
                          name="halfYearlyPrice"
                          placeholder="halfYearlyPrice of add on"
                          defaultValue={defaultValues?.halfYearlyPrice}
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <div>
                        <Label htmlFor="annualPrice" className="w-full text-sm font-medium text-slate-900">
                          {t("Annual Price")}
                        </Label>
                        <Input
                          // required
                          id="annualPrice"
                          type="number"
                          name="annualPrice"
                          placeholder="annualPrice of add on"
                          defaultValue={defaultValues?.annualPrice}
                        />
                      </div>
                    </div> */}
                  </div>
                </div>

                <Separator className="-mx-4 my-4 w-auto" />
                <div>
                  <Label htmlFor="description" className="w-full text-sm font-medium text-slate-900">
                    {t("addons.description")}
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Enter description of add on"
                    defaultValue={defaultValues?.description}
                  />
                </div>
                {/* <DateSelection validFrom={defaultValues?.validFrom} validTill={defaultValues?.validTill} /> */}
              </div>
            </div>
          </DrawerDescription>
          <DrawerFooter className="flex flex-row">
            <DrawerClose asChild>
              <Button data-testid="drawer-close-button" variant="outline" className="w-20">
                {t("cancel")}
              </Button>
            </DrawerClose>
            <Button type="submit" className="w-20">
              {t("save")}
            </Button>
          </DrawerFooter>
        </AddOnsRatesForm>
      </DrawerContent>
    </Drawer>
  );
}
