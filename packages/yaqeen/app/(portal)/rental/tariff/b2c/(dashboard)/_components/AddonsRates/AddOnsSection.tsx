import { getTranslations } from "next-intl/server";

import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import AddOnsRatesProvider from "./AddOnsRates.context";
import { AddOnRatesDrawerWrapper } from "./AddOnRatesDrawerWrapper";
import { type AddOnsRatesCards } from "@/api/contracts/tariff-contract";

type AddOnsRatesSectionProps = {
  addOnsRates: AddOnsRatesCards["addOnResponses"];
};

export default async function AddOnsRatesSection({ addOnsRates }: AddOnsRatesSectionProps) {
  const t = await getTranslations("tariff");

  return (
    <div className="flex flex-col bg-white px-6">
      <section className="p-2">
        <AddOnsRatesProvider>
          <div className="my-2 flex justify-between">
            <h2 className="text-2xl font-medium leading-8">{t("addons.addOnsRates")}</h2>
            <div className="flex gap-2">
              <AddOnRatesDrawerWrapper />
            </div>
          </div>
          <div>
            <div>
              <DataTable
                styleClasses={{
                  wrapper: "table-scroll",
                }}
                columns={columns}
                paginationEnabled={false}
                data={{
                  data: addOnsRates,
                  total: addOnsRates.length,
                }}
                emptyMessage={t("addons.noAddOns")}
              />
            </div>
          </div>
        </AddOnsRatesProvider>
      </section>
    </div>
  );
}
