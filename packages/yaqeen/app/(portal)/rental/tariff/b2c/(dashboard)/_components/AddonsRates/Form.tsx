"use client";

import { toast } from "@/lib/hooks/use-toast";

import { createAddOnRates } from "../../_lib/addOnsRatesAction";
import { type DefaultAddOns, useAddOnsRatesContext } from "./AddOnsRates.context";

export default function AddOnsRatesForm({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const { setDefaultValues } = useAddOnsRatesContext();
  const onSubmit = async (formData: FormData) => {
    const body = {} as DefaultAddOns;
    for (const p of formData) {
      const name = p[0];
      const value = p[1];
      // @ts-expect-error: key type
      body[name] = value;
    }
    setDefaultValues(body);
    const response = await createAddOnRates(formData);
    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }

    const isEditMode = formData.get("isEditMode") === "true";
    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    document?.querySelector<HTMLButtonElement>('[data-testid="drawer-close-button"]')?.click();
  };

  return (
    <form className={className} action={onSubmit}>
      {children}
    </form>
  );
}
