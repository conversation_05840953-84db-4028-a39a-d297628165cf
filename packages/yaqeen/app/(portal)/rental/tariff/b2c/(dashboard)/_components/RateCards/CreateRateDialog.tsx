"use client";

import { useState, startTransition, useCallback, useEffect } from "react";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import { useProgressBar } from "@/components/progress-bar";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import InfiniteScroll from "@/components/ui/InfiniteScroll";
import { type Route } from "next";

type CreateRateDialogProps = {
  trigger: React.ReactNode;
  options: { id: string; tariffRateName: string; description?: string }[];
  hasMoreOptions: boolean;
};

export function CreateRateDialog({ trigger, options = [], hasMoreOptions }: CreateRateDialogProps) {
  const t = useTranslations("tariff");
  const router = useRouter();
  const progress = useProgressBar();
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const [hasMore, setHasMore] = useState(hasMoreOptions);
  const [loading, setLoading] = useState(false);
  const [radioOptions, setRadioOptions] = useState(options);

  useEffect(() => {
    if (options.length > radioOptions.length) {
      setRadioOptions(options);
      setLoading(false);
      setHasMore(hasMoreOptions);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);

  const [selectedId, setSelectedId] = useState(radioOptions?.[0]?.id);

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);

      return params.toString();
    },
    [searchParams],
  );

  const next = async () => {
    setLoading(true);
    const size = searchParams.get("size") ?? 10;
    router.push((pathname + "?" + createQueryString("size", `${Number(size) + 10}`)) as Route, { scroll: false });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="m-0 w-full">
          {trigger}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("rates.existingRateCard")}</DialogTitle>
          <DialogDescription>{t("rates.existingRateCardDescription")}</DialogDescription>
        </DialogHeader>
        <div className="mb-4 grid max-h-64 gap-4 overflow-auto py-4">
          <RadioGroup onValueChange={(id) => setSelectedId(id)} defaultValue={radioOptions?.[0]?.id}>
            {radioOptions.map((option) => {
              return (
                <div key={option.id} className="flex items-center border border-slate-200 ps-4">
                  <RadioGroupItem value={option.id} id={option.id} />
                  <Label htmlFor={option.id} className="w-full cursor-pointer p-4">
                    <div>
                      <span className="font-medium">{option.tariffRateName}</span>
                      <span className="font-medium">{option.description}</span>
                    </div>
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
          <InfiniteScroll isLoading={loading} hasMore={hasMore} next={next}>
            <div className="flex w-full justify-center">
              {hasMore && <LoadingSpinner className="my-4 h-8 w-8 animate-spin" />}
            </div>
          </InfiniteScroll>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="secondary">{t("cancel")}</Button>
          </DialogClose>
          <Button
            disabled={isLoading}
            onClick={() => {
              setIsLoading(true);
              progress.start();
              startTransition(() => {
                router.push(`/rental/tariff/b2c/new/${selectedId}`);
                progress.done();
              });
            }}
          >
            {t("continue")}
            {isLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : <></>}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
