import React from "react";

import { api } from "@/api";
import { AddOnRatesDrawer } from "./AddOnRatesDrawer";

const getData = async () => {
  const [addOns, carGroups] = await Promise.allSettled([
    api.fleet.addonContract.getAddons({
      query: {
        pageSize: 20,
      },
    }),
    api.fleet.vehicleGroupsContract.getCarGroups({
      query: {
        pageSize: 50,
      },
    }),
  ]);

  if (addOns?.status === "rejected") {
    throw new Error(`Error: ${addOns.reason}`);
  }

  if (addOns?.value.status !== 200) {
    throw new Error(`Error: ${addOns.value.body.code}::${addOns.value.body.desc}`);
  }

  if (carGroups?.status === "rejected") {
    throw new Error(`Error: ${carGroups.reason}`);
  }

  if (carGroups?.value.status !== 200) {
    throw new Error(`Error: ${carGroups.value.body.code}::${carGroups.value.body.desc}`);
  }
  return {
    addOns: addOns?.value?.body,
    carGroups: carGroups.value.body,
  };
};

export async function AddOnRatesDrawerWrapper() {
  const { addOns, carGroups } = await getData();

  const formattedAddons = addOns.content.map((addon) => {
    return { value: `${addon.id}`, label: `${addon.id} (${addon.name.en})` };
  });

  const formattedCarGroups = carGroups.content.map((group) => {
    return { value: `${group.code}`, label: group.code };
  });
  return <AddOnRatesDrawer formattedAddons={formattedAddons} formattedCarGroups={formattedCarGroups} />;
}
