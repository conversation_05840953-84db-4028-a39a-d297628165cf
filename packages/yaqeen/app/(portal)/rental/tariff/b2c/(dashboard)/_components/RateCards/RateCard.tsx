import { format } from "date-fns";
import { useTranslations } from "next-intl";

import { Card, CardContent } from "@/components/ui/card";
import ModifyButton from "./ModifyButton";
import StopSalesButton from "../../../../_components/RateCards/StopSalesButton";
import TariffCardStatusBadge, { type TariffCardStatusBadgeProps } from "../../../../_components/RateCards/StatusBadge";

export type RateCardProps = {
  id: string;
  tariffRateName: string;
  status: TariffCardStatusBadgeProps["status"];
  updatedBy: string;
  createdBy: string;
  /** Date */
  updatedOn: string;
  /** Date */
  createdOn: string;
  validFrom: Date;
  validTill: Date;
};

export default function RateCard({
  id,
  status,
  tariffRateName,
  createdBy,
  updatedOn,
  validFrom,
  validTill,
}: RateCardProps) {
  const t = useTranslations("tariff");
  const formattedUpdatedOn = format(updatedOn, "dd-MM-yyyy");
  const validPeriod = `${format(validFrom, "dd-MM-yyyy")} till ${format(validTill, "dd-MM-yyyy")}`;

  return (
    <Card className="w-[32%] min-w-96 pt-5">
      <CardContent className="flex w-full flex-col gap-5">
        <div className="relative flex justify-between">
          <div>
            <span className="my-auto flex-auto text-sm font-normal leading-4 text-slate-500">
              {t("lastUpdated")} {formattedUpdatedOn}
            </span>
            <p className="text-md font-bold tracking-tight text-slate-900">{tariffRateName}</p>
            <p className="text-xs tracking-tight text-slate-600">
              {t("validPeriod")}: {validPeriod}
            </p>
          </div>
          <TariffCardStatusBadge status={status} className="absolute end-1 top-2" />
        </div>

        <div className="flex justify-between">
          <div>
            <p className="text-xs font-bold tracking-tight text-slate-600">{t("createdBy")}</p>
            <p className="text-sm  tracking-tight text-slate-600">{createdBy}</p>
          </div>
          <div>
            <p className="text-xs font-bold tracking-tight text-slate-600">{t("approvalDate")}</p>
            <p className="text-sm  tracking-tight text-slate-600">-</p>
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <ModifyButton id={id} />
          <StopSalesButton status={status} />
        </div>
      </CardContent>
    </Card>
  );
}
