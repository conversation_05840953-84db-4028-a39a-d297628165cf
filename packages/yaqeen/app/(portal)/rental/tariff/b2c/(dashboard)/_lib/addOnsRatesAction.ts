"use server";

import { api } from "@/api";
import { type AddOnsRatesCardsReq, type AddOnsRatesCards } from "@/api/contracts/tariff-contract";
import { revalidatePath } from "@/lib/nextjs";

export async function createAddOnRates(
  formData: FormData
): Promise<{ error?: { desc: string }; data?: AddOnsRatesCards }> {
  try {
    const isEditMode = formData.get("isEditMode") === "true";
    const rawCarGroupCode = formData.get("carGroupCode") as string;
    const carGroupCode = rawCarGroupCode === "all" ? undefined : rawCarGroupCode;

    const body = {
      name: formData.get("name") as string,
      addOnId: formData.get("addOnId") as string,
      ...(carGroupCode && { carGroupCode }),
      currency: formData.get("currency") as string,
      dailyPrice: Number(formData.get("dailyPrice")),
      weeklyPrice: Number(formData.get("weeklyPrice")),
      monthlyPrice: Number(formData.get("monthlyPrice")),
      quarterlyPrice: Number(formData.get("quarterlyPrice")),
      halfYearlyPrice: Number(formData.get("halfYearlyPrice")),
      annualPrice: Number(formData.get("annualPrice")),
      pricingStrategy: formData.get("pricingStrategy") as AddOnsRatesCardsReq["pricingStrategy"],
      validFrom: new Date(formData.get("validFrom") as string)?.toISOString(),
      validTill: new Date(formData.get("validTill") as string)?.toISOString(),
      description: formData.get("description") as string,
    } satisfies AddOnsRatesCardsReq;

    if (isEditMode) {
      const id = formData.get("id") as string;
      const response = await api.tariff.updateAddOnsRates({
        body: { addOnRequests: [{ id, ...body }] },
      });

      if (response.status !== 200) {
        return { error: response.body };
      }

      revalidatePath(`/tariff/b2c`);
      return { data: response.body };
    }

    const response = await api.tariff.createAddOnsRates({
      body: { addOnRequests: [body] },
    });

    if (response.status !== 201) {
      return { error: response.body };
    }

    revalidatePath(`/tariff/b2c`);
    return { data: response.body };
  } catch (error) {
    console.error(error);
    let message = "Unknown Error";
    if (error instanceof Error) message = error.message;
    return { error: { desc: message } };
  }
}
