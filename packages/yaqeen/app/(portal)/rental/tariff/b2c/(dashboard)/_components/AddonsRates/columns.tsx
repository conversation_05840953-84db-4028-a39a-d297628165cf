"use client";

import { type ColumnDef, type CellContext } from "@tanstack/react-table";
import { useAddOnsRatesContext } from "./AddOnsRates.context";
import { type AddOnsRatesCards } from "@/api/contracts/tariff-contract";

type AddOnRates = AddOnsRatesCards["addOnResponses"][number];

export function ActionsCell(info: CellContext<AddOnRates, unknown>) {
  const { setOpenDrawer, setDefaultValues } = useAddOnsRatesContext();
  const setEditedRows = () => {
    setDefaultValues(info.row.original);
    setOpenDrawer(true);
  };

  return (
    <button onClick={setEditedRows} name="edit">
      ✐
    </button>
  );
}

export const columns: ColumnDef<AddOnRates>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "carGroupCode",
    header: "Car Group Code",
    cell: (info) => (info.getValue() || "All") as string,
  },
  {
    accessorKey: "pricingStrategy",
    header: "Type",
  },
  {
    accessorKey: "dailyPrice",
    header: "Price SAR",
  },
  {
    accessorKey: "description",
    header: "description",
    cell: (info) => (info.getValue() || "N/A") as string,
  },
  {
    id: "actions",
    cell: ActionsCell,
  },
];
