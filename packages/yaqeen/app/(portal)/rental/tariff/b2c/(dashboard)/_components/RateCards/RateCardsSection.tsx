"use client";

import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState, startTransition } from "react";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { CaretDown, FilePlus, CaretUp, Plus } from "@phosphor-icons/react/dist/ssr";
import { UploadCloud } from "lucide-react";

import { ProgressBarLink, useProgressBar } from "@/components/progress-bar";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

import RateCard, { type RateCardProps } from "./RateCard";
import { CreateRateDialog } from "./CreateRateDialog";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { type Route } from "next";

type RateCardsSectionProps = {
  cards: Array<RateCardProps>;
  total: number;
  tariffIdentifierKey: TariffCardDetails["tariffIdentifierKey"];
};

const CARDS_LIMIT = 3;

export default function RateCardsSection({ tariffIdentifierKey, cards, total }: RateCardsSectionProps) {
  const t = useTranslations("tariff");
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const progress = useProgressBar();

  const [isExpanded, setIsExpanded] = useState(false);

  const slicedCards = cards.slice(0, CARDS_LIMIT);

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);

      return params.toString();
    },
    [searchParams]
  );

  useEffect(() => {
    router.push((pathname + "?" + createQueryString("size", `10`)) as Route, { scroll: false });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const showExtraCardsText = `Show ${isExpanded ? "less" : "more"} tariff cards`;

  return (
    <>
      <div className="flex flex-col bg-white px-6">
        <section className="p-2">
          <div className="my-2 flex justify-between">
            <h2 className="text-2xl font-medium leading-8">{t("rateCard")}</h2>
            <div className="flex gap-2">
              {cards.length > CARDS_LIMIT ? (
                <Button variant="ghost" className="gap-2 text-blue-600" onClick={() => setIsExpanded((prev) => !prev)}>
                  {showExtraCardsText}
                  {isExpanded ? (
                    <CaretUp className="h-4 w-4" weight="bold" />
                  ) : (
                    <CaretDown className="h-4 w-4" weight="bold" />
                  )}
                </Button>
              ) : null}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="flex gap-2">
                    <Plus className="h-4 w-4" weight="bold" />
                    {t("addNewRate")}
                    <CaretDown className="h-4 w-4" weight="bold" />
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuLabel className="flex cursor-pointer items-center gap-2 text-sm font-normal">
                    <ProgressBarLink
                      href={`/rental/tariff/b2c/new?tariffIdentifierKey=${tariffIdentifierKey}&upload=1`}
                    >
                      <div className="flex w-full items-center gap-2 rounded-md p-3 hover:bg-slate-100">
                        <UploadCloud className="h-4 w-4" />
                        {t("uploadSheetTemplate")}
                      </div>
                    </ProgressBarLink>
                  </DropdownMenuLabel>
                  <DropdownMenuLabel className="flex cursor-pointer items-center gap-2 text-sm font-normal">
                    {cards.length > 0 ? (
                      <CreateRateDialog
                        hasMoreOptions={total > cards.length}
                        options={cards}
                        trigger={
                          <div className="flex w-full items-center gap-2">
                            <FilePlus className="h-4 w-4" weight="bold" />
                            {t("useExistingRateCard")}
                          </div>
                        }
                      />
                    ) : (
                      <ProgressBarLink
                        className="w-full"
                        href={`/rental/tariff/b2c/new?tariffIdentifierKey=${tariffIdentifierKey}`}
                      >
                        <div className="flex w-full items-center gap-2 rounded-md p-3 hover:bg-slate-100">
                          <FilePlus className="h-4 w-4" weight="bold" />
                          {t("createNew")}
                        </div>
                      </ProgressBarLink>
                    )}
                  </DropdownMenuLabel>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-wrap gap-5">
              {cards.length < 1 && (
                <div className="flex min-h-60 w-full flex-col items-center  justify-center gap-3 rounded-lg bg-slate-100 p-3">
                  <h3>{t("noCardsAvailable")}</h3>
                  <div>
                    <ProgressBarLink
                      className="w-full"
                      href={`/rental/tariff/b2c/new?tariffIdentifierKey=${tariffIdentifierKey}`}
                    >
                      <div className="flex w-full items-center gap-2 rounded-md border border-slate-300 bg-white p-3 hover:bg-slate-100">
                        <FilePlus className="h-4 w-4" weight="bold" />
                        {t("createNew")}
                      </div>
                    </ProgressBarLink>
                  </div>
                </div>
              )}
              {(!isExpanded ? slicedCards : cards)?.map((card) => {
                return <RateCard key={card.id} {...card} />;
              })}
            </div>
            {isExpanded && total > cards.length && (
              <div className="flex items-center justify-center">
                <Button
                  className="w-56"
                  onClick={() => {
                    const size = searchParams.get("size") ?? 10;
                    progress.start();
                    startTransition(() => {
                      router.push((pathname + "?" + createQueryString("size", `${Number(size) + 10}`)) as Route, {
                        scroll: false,
                      });
                      progress.done();
                    });
                  }}
                >
                  {t("loadMore")}
                </Button>
              </div>
            )}
          </div>
        </section>
      </div>
    </>
  );
}
