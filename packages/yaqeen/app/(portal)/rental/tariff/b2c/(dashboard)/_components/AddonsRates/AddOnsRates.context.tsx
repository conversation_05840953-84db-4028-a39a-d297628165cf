"use client";

import { type AddOnsRatesCards } from "@/api/contracts/tariff-contract";
import React, { createContext, type Dispatch, type SetStateAction, useContext, useMemo, useState } from "react";

type IContextProps = {
  children: React.ReactNode;
};

export type DefaultAddOns = AddOnsRatesCards["addOnResponses"][number];

type ContextProviderType = Omit<IContextProps, "children"> & {
  openDrawer: boolean | undefined;
  setOpenDrawer: Dispatch<SetStateAction<boolean | undefined>>;
  defaultValues: DefaultAddOns | undefined;
  setDefaultValues: Dispatch<SetStateAction<DefaultAddOns | undefined>>;
};

const Context = createContext<ContextProviderType | null>(null);

const AddOnsRatesProvider = ({ children }: IContextProps): React.ReactNode => {
  const [openDrawer, setOpenDrawer] = useState<boolean>();
  const [defaultValues, setDefaultValues] = useState<DefaultAddOns>();

  const value = useMemo(
    () =>
      ({
        openDrawer,
        setOpenDrawer,
        defaultValues,
        setDefaultValues,
      }) satisfies ContextProviderType,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [openDrawer, defaultValues]
  );

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export default AddOnsRatesProvider;

export const useAddOnsRatesContext = (): ContextProviderType => {
  const context = useContext(Context);
  if (!context) {
    throw new Error("AddOnsRates context must be used within a AddOnsRates Provider");
  }
  return context;
};
