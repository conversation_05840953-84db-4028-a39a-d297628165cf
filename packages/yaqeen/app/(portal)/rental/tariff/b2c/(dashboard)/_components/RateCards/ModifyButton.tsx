"use client";

import { startTransition } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import { useProgressBar } from "@/components/progress-bar";

import { Button } from "@/components/ui/button";

export type ModifyButtonProps = {
  id: string;
};

export default function ModifyButton({ id }: ModifyButtonProps) {
  const t = useTranslations("tariff");
  const router = useRouter();
  const progress = useProgressBar();

  return (
    <Button
      variant="secondary"
      onClick={() => {
        progress.start();
        startTransition(() => {
          router.push(`/rental/tariff/b2c/${id}`);
          progress.done();
        });
      }}
    >
      {t("modify")}
    </Button>
  );
}
