"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";

import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";

export function DateSelection({ validFrom, validTill }: { validFrom?: Date; validTill?: Date }) {
  const t = useTranslations("tariff");
  const [localStartDate, setLocalStartDate] = useState(validFrom ?? new Date());
  const [localEndDate, setLocalEndDate] = useState(validTill ?? new Date());

  return (
    <div className="flex gap-4">
      <div>
        <Label htmlFor="validFrom" className="w-full text-sm font-medium text-slate-900">
          {t("startDate")}
        </Label>
        <DatePicker
          name="validFrom"
          date={localStartDate}
          setDate={function (date: Date): void {
            setLocalStartDate(date);
          }}
        />
      </div>
      <div>
        <Label htmlFor="validTill" className="w-full text-sm font-medium text-slate-900">
          {t("endDate")}
        </Label>
        <DatePicker
          name="validTill"
          date={localEndDate}
          setDate={function (date: Date): void {
            setLocalEndDate(date);
          }}
        />
      </div>
    </div>
  );
}
