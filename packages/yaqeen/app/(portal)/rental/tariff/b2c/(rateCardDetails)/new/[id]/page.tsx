import { api } from "@/api";
import RateDetailsSection from "../../_components/RatesDetailsSection";
import { formatTariffData } from "../../../../_lib/formatTariffRates";

const getData = async (id: string) => {
  const [cardDetails, vehicleGroups] = await Promise.allSettled([
    api.tariff.getTariffCardById({
      params: {
        id: Number(id),
      },
    }),
    api.fleet.vehicleGroupsContract.getCarGroups({
      query: {
        pageSize: 50,
      },
    }),
  ]);

  if (cardDetails?.status === "rejected") {
    throw new Error(`Error: ${cardDetails.reason}`);
  }

  if (cardDetails?.value.status !== 200) {
    throw new Error(`Error: ${cardDetails.value.body.code}::${cardDetails.value.body.desc}`);
  }

  if (vehicleGroups?.status === "rejected") {
    throw new Error(`Error: ${vehicleGroups.reason}`);
  }

  if (vehicleGroups?.value.status !== 200) {
    throw new Error(`Error: ${vehicleGroups.value.body.code}::${vehicleGroups.value.body.desc}`);
  }

  return { data: cardDetails.value.body, vehicleGroups: vehicleGroups.value.body };
};

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const paramsA = await params;
  const { id } = paramsA;

  const { data, vehicleGroups } = await getData(id);

  const formattedTariffData = formatTariffData(data.tariffRates);
  const formattedData = {
    ...data,
    tariffRates: formattedTariffData,
  };

  return <RateDetailsSection vehicleGroups={vehicleGroups} isEditMode={false} data={formattedData} />;
}
