"use client";

import React, { createContext, type Dispatch, type SetStateAction, useContext, useMemo, useState } from "react";

import { type TariffCardDetails } from "@/api/contracts/tariff-contract";

type IContextProps = {
  children: React.ReactNode;
  defaultRates?: TariffCardDetails["tariffRates"];
};

type ContextProviderType = Omit<IContextProps, "children" | "defaultRates"> & {
  rates?: TariffCardDetails["tariffRates"];
  setRates: Dispatch<SetStateAction<TariffCardDetails["tariffRates"] | undefined>>;
};

const Context = createContext<ContextProviderType | null>(null);

const RatesProvider = ({ children, defaultRates }: IContextProps): React.ReactNode => {
  const [rates, setRates] = useState(defaultRates);

  const value = useMemo(
    () =>
      ({
        rates,
        setRates,
      }) satisfies ContextProviderType,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rates]
  );

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export default RatesProvider;

export const useRatesContext = (): ContextProviderType => {
  const context = useContext(Context);
  if (!context) {
    throw new Error("Rates context must be used within a Rates Provider");
  }
  return context;
};
