"use client";

import { DataTable } from "@/components/ui/data-table/data-table";
import { generateColumns } from "./columns";

import { useRatesContext } from "./Rates.context";
import { Plus } from "@phosphor-icons/react/dist/ssr";
import { Button } from "@/components/ui/button";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

export default function RatesTable({
  isEditEnabled,
  vehicleGroups,
}: {
  isEditEnabled: boolean;
  vehicleGroups: VehicleGroups;
}) {
  const { rates, setRates } = useRatesContext();
  const formattedVehicleGroups = vehicleGroups.content.map((el) => ({ label: el.code, value: el.code }));

  const columns = generateColumns(formattedVehicleGroups);
  const newColumns = isEditEnabled ? columns : columns.filter((col) => col.id !== "edit");

  return (
    <div>
      <DataTable
        columns={newColumns}
        paginationEnabled={false}
        data={{
          data: rates ?? [],
          total: rates?.length ?? 0,
        }}
        onDataChange={(newRates) => {
          const ratesWithId = newRates?.map((el) => {
            const carGroupId = vehicleGroups.content.find((vehicle) => el.carGroupCode === vehicle.code)?.id;

            return { ...el, carGroupId: carGroupId ? carGroupId : "" };
          });

          setRates(ratesWithId);
        }}
        footerRow={
          !isEditEnabled
            ? null
            : (table) => {
                const meta = table.options.meta;

                return (
                  <>
                    <span
                      className="flex w-fit cursor-pointer items-center gap-2 rounded-md border border-slate-200 p-2 pe-6"
                      onClick={() => meta?.addRow()}
                    >
                      <Plus />
                      <span>Add another car group price</span>
                    </span>
                  </>
                );
              }
        }
        emptyMessage={(table) => (
          <div className="flex flex-col gap-2">
            There are no Details
            <Button type="button" variant="outline" onClick={() => table.options.meta?.addRow()}>
              Add new
            </Button>
          </div>
        )}
      />
    </div>
  );
}
