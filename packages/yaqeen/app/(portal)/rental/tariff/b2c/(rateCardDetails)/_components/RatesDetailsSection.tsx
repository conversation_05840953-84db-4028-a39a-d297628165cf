import { format } from "date-fns";
import { getTranslations } from "next-intl/server";

import { But<PERSON> } from "@/components/ui/button";

import { type TariffCardDetails } from "@/api/contracts/tariff-contract";

import Header from "../../../_components/Header";
import StopSalesButton from "../../../_components/RateCards/StopSalesButton";
import DownloadCSVButton from "../../../_components/RateCards/DownloadCSVButton";
import TariffCardStatusBadge from "../../../_components/RateCards/StatusBadge";
import CancelButton from "../../../_components/RateCards/CancelButton";

import RatesProvider from "./Rates.context";
import RateDetails from "../../../_components/RateCards/RateDetails";
import RatesTable from "./RatesTable";
import RatesForm from "./Form";
import UploadCSVWrapper from "./UploadCSVWrapper";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

export default async function RateDetailsSection({
  isEditMode,
  tariffIdentifierKey,
  data,
  openUpload = false,
  vehicleGroups,
}: {
  isEditMode: boolean;
  tariffIdentifierKey?: TariffCardDetails["tariffIdentifierKey"];
  data?: TariffCardDetails;
  openUpload?: boolean;
  vehicleGroups: VehicleGroups;
}) {
  const t = await getTranslations("tariff");
  const validPeriod = data?.validFrom
    ? `${format(data?.validFrom, "dd-MM-yyyy")} till ${format(data?.validTill, "dd-MM-yyyy")}`
    : null;
  const formattedUpdatedOn = data?.updatedOn ? format(data?.updatedOn, "dd-MM-yyyy") : null;

  const isEditRatesAllowed = !isEditMode || (data?.status !== "ACTIVE" && data?.status !== "APPROVED");

  return (
    <RatesProvider defaultRates={data?.tariffRates}>
      <Header
        pageName={isEditMode ? data?.tariffRateName : "New Tariff Card"}
        pageTitle={
          isEditMode ? (
            <div className="flex items-center justify-start gap-2">
              <span>{data?.tariffRateName}</span>
              {data?.status && <TariffCardStatusBadge status={data?.status} />}
            </div>
          ) : (
            "New Tariff Card"
          )
        }
        homeLink="/rental/tariff/b2c"
        subTitle={
          isEditMode ? `Valid period: ${validPeriod}` : "You can create the name , set the range & set the hourly rate"
        }
        actions={
          <div className="flex items-center gap-2">
            {data?.id && <DownloadCSVButton isEditRatesAllowed={isEditRatesAllowed} id={data?.id} />}
            {isEditRatesAllowed && <UploadCSVWrapper open={openUpload} />}
            {isEditRatesAllowed && data?.status && <StopSalesButton status={data?.status} />}
          </div>
        }
      />
      <RatesForm>
        <input hidden name="isEditMode" defaultValue={`${isEditMode}`} />
        <input hidden name="id" defaultValue={data?.id} />
        <input hidden name="tariffIdentifierKey" defaultValue={data?.tariffIdentifierKey ?? tariffIdentifierKey} />
        <section className="p-8">
          <div className="my-2 flex justify-between">
            <h2 className="text-2xl font-medium leading-8">{isEditMode ? "Detail breakdown" : "New Rate Card"}</h2>
            <span className="text-sm font-normal leading-4 text-slate-500">
              {isEditMode ? `Last Update: ${formattedUpdatedOn}` : null}
            </span>
          </div>
          <div>
            <input hidden name="type" defaultValue="fixed" />
            <input hidden name="tariffIdentifierValue" defaultValue="DEFAULT" />
            <RateDetails
              isNameEnabled={isEditRatesAllowed}
              tariffRateName={isEditMode ? data?.tariffRateName : undefined}
              startDate={isEditMode ? data?.validFrom : undefined}
              endDate={isEditMode ? data?.validTill : undefined}
            />

            <RatesTable isEditEnabled={isEditRatesAllowed} vehicleGroups={vehicleGroups} />
            <div className="my-2 flex gap-3">
              <CancelButton type={data?.tariffIdentifierKey ?? tariffIdentifierKey ?? "B2C"} />
              <Button type="submit">{t("save")}</Button>
            </div>
          </div>
        </section>
      </RatesForm>
    </RatesProvider>
  );
}
