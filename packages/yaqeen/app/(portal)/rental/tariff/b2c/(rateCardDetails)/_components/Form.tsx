"use client";
import { useRouter } from "next/navigation";

import { toast } from "@/lib/hooks/use-toast";

import { useRatesContext } from "./Rates.context";
import { createUpdateRateCard } from "../../../_lib/createUpdateRateCard";

export default function RatesForm({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { rates } = useRatesContext();

  const onSubmit = async (form: FormData) => {
    const type = form.get("type");
    const ratesWithType = rates?.map((el) => ({ ...el, type }));

    if (!ratesWithType?.length) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: "Rates should not be empty",
      });
      return;
    }

    form.append("tariffRates", JSON.stringify(ratesWithType));
    form.append("currency", "SAR");

    const response = await createUpdateRateCard(form);
    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }
    const isEditMode = form.get("isEditMode") === "true";
    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    const id = response.data?.id;
    router.replace(`/rental/tariff/b2c/${id}`);
  };
  return <form action={onSubmit}>{children}</form>;
}
