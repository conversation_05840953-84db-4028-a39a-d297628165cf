import { api } from "@/api";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import RateDetailsSection from "../_components/RatesDetailsSection";

const getData = async () => {
  const [vehicleGroups] = await Promise.allSettled([
    api.fleet.vehicleGroupsContract.getCarGroups({
      query: {
        pageSize: 50,
      },
    }),
  ]);

  if (vehicleGroups?.status === "rejected") {
    throw new Error(`Error: ${vehicleGroups.reason}`);
  }

  if (vehicleGroups?.value.status !== 200) {
    throw new Error(`Error: ${vehicleGroups.value.body.code}::${vehicleGroups.value.body.desc}`);
  }

  return { vehicleGroups: vehicleGroups.value.body };
};

export default async function Page({
  searchParams,
}: {
  searchParams?: Promise<Record<string, string | string[] | undefined>>;
}) {
  const tariffIdentifierKey = (await searchParams)!.tariffIdentifierKey as TariffCardDetails["tariffIdentifierKey"];
  const openUpload = Boolean((await searchParams)!.upload as string);

  const { vehicleGroups } = await getData();

  return (
    <RateDetailsSection
      openUpload={openUpload}
      vehicleGroups={vehicleGroups}
      tariffIdentifierKey={tariffIdentifierKey}
      isEditMode={false}
    />
  );
}
