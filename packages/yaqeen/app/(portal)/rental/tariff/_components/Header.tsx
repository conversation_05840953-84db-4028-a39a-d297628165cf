import React from "react";
import { CaretRight } from "@phosphor-icons/react/dist/ssr";
import { ProgressBarLink } from "@/components/progress-bar";
import { type Route } from "next";
import { useTranslations } from "next-intl";

interface HeaderProps {
  pageName?: string;
  pageTitle?: string | React.ReactNode;
  subTitle?: string;
  homeLink?: Route<string>;
  actions?: React.ReactNode;
  smallPage?: boolean;
  extraDetails?: React.ReactNode;
}

export default function Header({
  pageName = "Rental Tariff",
  pageTitle,
  subTitle,
  actions,
  homeLink = "/",
  smallPage = false,
  extraDetails,
}: HeaderProps) {
  const commonT = useTranslations("common");

  return (
    <div className="box-border border-b bg-slate-50  ">
      <section className={`mx-auto flex w-full flex-col self-stretch px-6 ${smallPage ? "max-w-3xl" : ""}`}>
        <div className="flex w-full items-start gap-1 pt-4 text-xs leading-relaxed">
          <div className="flex items-center gap-2 text-slate-700">
            <div className="my-auto self-stretch">
              <ProgressBarLink href={homeLink}>{commonT("home")}</ProgressBarLink>
            </div>
            <CaretRight className="h-4 w-4" />
          </div>
          <div className=" self-stretch text-slate-500">{pageName}</div>
        </div>
        <div className="flex justify-between">
          <div className="flex w-full items-start gap-4 py-6 font-medium text-slate-900 ">
            <div className="flex w-full flex-col justify-center ">
              <div className="flex w-full items-center justify-between  gap-x-2">
                <h2 className="text-3xl tracking-tight ">{pageTitle ?? pageName}</h2>
              </div>
              <p className="mt-2 size-5 w-full font-normal text-slate-700">{subTitle}</p>
              {extraDetails}
            </div>
          </div>
          {actions}
        </div>
      </section>
    </div>
  );
}
