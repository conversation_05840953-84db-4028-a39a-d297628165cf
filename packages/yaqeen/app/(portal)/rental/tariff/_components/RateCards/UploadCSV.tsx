"use client";

import { type Dispatch, type SetStateAction, useEffect, useRef, useState } from "react";
import Papa from "papaparse";

import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { Button } from "@/components/ui/button";
import { formatTariffData } from "../../_lib/formatTariffRates";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { UploadSimple } from "@phosphor-icons/react/dist/ssr";

const acceptableCSVFileTypes = ".csv";
// const acceptableCSVFileTypes =
// "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel";

export default function UploadCSV({
  open,
  setRates,
}: {
  open?: boolean;
  setRates: Dispatch<SetStateAction<TariffCardDetails["tariffRates"] | undefined>>;
}) {
  const [error, setError] = useState<boolean | string>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const openedRef = useRef<boolean>(false);

  useEffect(() => {
    inputRef.current?.addEventListener("cancel", () => {
      setIsLoading(false);
    });

    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      inputRef?.current?.removeEventListener("cancel", () => {
        setIsLoading(false);
      });
    };
  }, []);

  useEffect(() => {
    if (open && !openedRef?.current) {
      inputRef.current?.click();
      openedRef.current = true;
    }
  }, [inputRef, openedRef, open]);

  const onFileChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event?.target?.files?.length) {
      return;
    }

    const csvFile = event.target.files[0];
    if (csvFile) {
      Papa.parse<TariffCardDetails["tariffRates"][number]>(csvFile, {
        skipEmptyLines: true,
        header: true,
        complete: function (results: { errors: { message: string }[]; data: TariffCardDetails["tariffRates"] }) {
          if (results?.errors.length) {
            setError(results?.errors?.[0]?.message ?? "");
            setIsLoading(false);
            return;
          }

          const formattedRated = formatTariffData(results.data);

          setRates(formattedRated);
          setError(false);
          setIsLoading(false);
        },
      });
    }
  };

  return (
    <>
      <Button
        variant="secondary"
        className="flex items-center gap-4"
        disabled={isLoading}
        onClick={() => {
          setIsLoading(true);
          inputRef.current?.click();
        }}
      >
        <UploadSimple size={16} />
        <div className="flex items-center gap-1">
          Upload
          <span className=" items-baseline text-xs text-slate-400">(csv)</span>
        </div>
        {isLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : <></>}
      </Button>
      <input
        ref={inputRef}
        hidden
        type="file"
        id="csvFileSelector"
        accept={acceptableCSVFileTypes}
        onChange={onFileChangeHandler}
      />
      {error && <div className="text-red-500">{error} || something went wrong</div>}
    </>
  );
}
