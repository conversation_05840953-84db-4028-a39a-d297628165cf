import { cn } from "@/lib/utils";

export type TariffCardStatusBadgeProps = {
  status: "ACTIVE" | "DRAFT" | "APPROVED" | "IN_APPROVAL";
  className?: string;
};

const statusColorMapper: Record<TariffCardStatusBadgeProps["status"], `bg-${string}`> = {
  DRAFT: "bg-slate-300",
  ACTIVE: "bg-lumi-200",
  APPROVED: "bg-lumi-500",
  IN_APPROVAL: "bg-red-100",
};

const statusNameMapper: Record<TariffCardStatusBadgeProps["status"], string> = {
  DRAFT: "Draft",
  ACTIVE: "Active",
  APPROVED: "Approved",
  IN_APPROVAL: "Pending",
};

export default function TariffCardStatusBadge({ status, className }: TariffCardStatusBadgeProps) {
  return (
    <span
      className={cn(`rounded-full p-1 px-3 text-xs capitalize text-slate-900 ${statusColorMapper[status]}`, className)}
    >
      {statusNameMapper[status]}
    </span>
  );
}
