"use client";

import { startTransition } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import { useProgressBar } from "@/components/progress-bar";

import { Button } from "@/components/ui/button";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { type Route } from "next";

export type CancelButtonProps = {
  type: TariffCardDetails["tariffIdentifierKey"];
};

export default function CancelButton({ type }: CancelButtonProps) {
  const t = useTranslations("tariff");
  const router = useRouter();
  const progress = useProgressBar();

  const formattedType = type?.toLowerCase();

  return (
    <Button
      variant="secondary"
      type="button"
      onClick={() => {
        progress.start();
        startTransition(() => {
          router.push(`/rental/tariff/${formattedType}` as Route<string>);
          progress.done();
        });
      }}
    >
      {t("cancel")}
    </Button>
  );
}
