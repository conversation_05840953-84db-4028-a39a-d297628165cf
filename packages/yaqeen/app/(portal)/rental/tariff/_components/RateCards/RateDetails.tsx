"use client";

import { useState } from "react";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import InputField from "@/components/atoms/InputField";

type RateDetailsProps = {
  isNameEnabled: boolean;
  isDateEnabled?: boolean;
  tariffRateName?: string;
  startDate?: Date;
  endDate?: Date;
};

export default function RateDetails({
  isNameEnabled,
  isDateEnabled = true,
  tariffRateName,
  startDate = new Date(),
  endDate = new Date(),
}: RateDetailsProps) {
  const t = useTranslations("tariff");
  const [localName, setLocalName] = useState(tariffRateName);
  const [localStartDate, setLocalStartDate] = useState(new Date(startDate));
  const [localEndDate, setLocalEndDate] = useState(new Date(endDate));

  return (
    <div className="mb-8 flex flex-col gap-4">
      <div>
        <InputField
          required
          label={t("name")}
          id="tariffRateName"
          name="tariffRateName"
          placeholder="Enter name"
          value={localName}
          onChange={(e) => {
            setLocalName(e.target.value);
          }}
          readOnly={!isNameEnabled}
        />
      </div>

      <div className="flex gap-4">
        <div>
          <Label htmlFor="validFrom" className="w-full text-sm font-medium text-slate-900">
            {t("startDate")}
          </Label>
          <Input
            required
            readOnly={!isDateEnabled}
            type="date"
            id="validFrom"
            name="validFrom"
            // max={format(localEndDate, "yyyy-MM-dd")}
            defaultValue={format(localStartDate, "yyyy-MM-dd")}
            onChange={(e) => {
              setLocalStartDate(new Date(e.target.value));
            }}
          />
        </div>
        <div>
          <Label htmlFor="validTill" className="w-full text-sm font-medium text-slate-900">
            {t("endDate")}
          </Label>
          <Input
            required
            readOnly={!isDateEnabled}
            type="date"
            id="validTill"
            name="validTill"
            // min={format(localStartDate, "yyyy-MM-dd")}
            defaultValue={format(localEndDate, "yyyy-MM-dd")}
            onChange={(e) => {
              setLocalEndDate(new Date(e.target.value));
            }}
          />
        </div>
      </div>
    </div>
  );
}
