"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

export default function DownloadCSVButton({ id, isEditRatesAllowed }: { id: string; isEditRatesAllowed: boolean }) {
  const t = useTranslations("tariff");
  const text = isEditRatesAllowed ? t("downloadAndModify") : t("download");

  return (
    <Button variant="outline" asChild>
      <Link className="hover:underline" href={`/next-api/csv/tariffRates/${id}`}>
        {text}
      </Link>
    </Button>
  );
}
