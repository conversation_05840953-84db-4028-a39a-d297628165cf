"use client";

import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import { type RateCardProps } from "../../b2c/(dashboard)/_components/RateCards/RateCard";

type StopSalesButtonProps = {
  status: RateCardProps["status"];
};

export default function StopSalesButton({ status }: StopSalesButtonProps) {
  const t = useTranslations("tariff");
  // TODO: remove after sales API integration
  return null;
  return status !== "DRAFT" ? <Button variant="outline">{t("stopSales")}</Button> : null;
}
