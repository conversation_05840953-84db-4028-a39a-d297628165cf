import { getTranslations } from "next-intl/server";

import { ProgressBarLink } from "@/components/progress-bar";
import Header from "./_components/Header";

export const metadata = {
  title: "Tariff",
};

export default async function Page() {
  const t = await getTranslations("tariff");

  return (
    <div className="grid grid-rows-[auto_1fr]">
      <Header pageName={t("rentalTariff")} subTitle={t("rentalTariffSubTitle")} />
      <div className="flex h-96 items-center justify-center gap-2">
        <div className="cursor-pointer border-2 p-4 hover:bg-slate-100">
          <ProgressBarLink href={"/rental/tariff/b2c"}>B2C</ProgressBarLink>
        </div>
        <div className="cursor-pointer border-2 p-4 hover:bg-slate-100">
          <ProgressBarLink href={"/rental/tariff/b2b"}>B2B</ProgressBarLink>
        </div>
      </div>
    </div>
  );
}
