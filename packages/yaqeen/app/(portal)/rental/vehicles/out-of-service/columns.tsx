"use client";

import { type OOSVehicle } from "@/api/contracts/rental/availability-contract";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { isPast, formatDistanceToNowStrict } from "date-fns";
import { useState } from "react";
import { OpenNRMDialog } from "../_components/open-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { MoveToAvailableDialog } from "../_components/move-to-available-dialog";
import { MoreHorizontal } from "lucide-react";
import { CheckCircle, NotePencil } from "@phosphor-icons/react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTranslations } from "next-intl";

type ColumnMessageKey =
  | "plateNo"
  | "group"
  | "location"
  | "vehicle"
  | "status"
  | "fuel"
  | "waitingPeriod"
  | "actions"
  | "reason";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

function ActionsCell({ row }: { row: Row<OOSVehicle> }) {
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const [isMoveToAvailableDialogOpen, setIsMoveToAvailableDialogOpen] = useState(false);
  const plateNo = row.original.plateNo;
  const t = useTranslations("NRM");

  const handleOpenNRM = () => {
    setIsNRMDialogOpen(true);
  };

  const handleMoveToAvailable = () => {
    setIsMoveToAvailableDialogOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 border border-gray-200 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleOpenNRM}>
            <NotePencil className="mr-2 h-4 w-4" />
            {t("actions.openNRM")}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleMoveToAvailable}>
            <CheckCircle className="mr-2 h-4 w-4" />
            {t("actions.moveToAvailable")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {isNRMDialogOpen && (
        <OpenNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={plateNo}
          checkoutBranchId={row.original.location?.lumiBranchId}
        />
      )}

      {isMoveToAvailableDialogOpen && (
        <MoveToAvailableDialog
          open={isMoveToAvailableDialogOpen}
          onOpenChange={setIsMoveToAvailableDialogOpen}
          plateNo={plateNo}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<OOSVehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicleGroup",
    header: () => <Message messageKey="group" />,
    cell: ({ row }) => {
      const group = row.original.model.vehicleGroup;
      return <div>{group}</div>;
    },
  },
  {
    accessorKey: "location",
    header: () => <Message messageKey="location" />,
    cell: ({ row }) => {
      const location = row.original.location?.name?.en ?? "N/A";
      return <div>{location}</div>;
    },
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model.make.name}
        model={row.original.model.name}
        version={row.original.model.version}
      />
    ),
  },
  {
    accessorKey: "vehicleStatus.statusReason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => {
      const reason = row.original.vehicleStatus.statusReason;
      return (
        <div className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-slate-800">
          {reason}
        </div>
      );
    },
  },
  {
    accessorKey: "fuelLevelInfo",
    header: () => <Message messageKey="fuel" />,
    cell: ({ row }) => {
      const fuelLevel = row.original.fuelLevelInfo.fuelLevel;
      const fuelDescription = row.original.fuelLevelInfo.displayName;

      return (
        <div>
          {fuelLevel}/4 ({fuelDescription})
        </div>
      );
    },
  },
  {
    accessorKey: "vehicleStatus.waitingTime",
    header: () => <Message messageKey="waitingPeriod" />,
    cell: ({ row }) => {
      const waitingTime = row.original.vehicleStatus.waitingTime;
      const date = new Date(waitingTime);
      const isPastDate = isPast(date);

      const timePeriod = formatDistanceToNowStrict(date, { addSuffix: false });
      const displayText = isPastDate ? `${timePeriod} passed` : timePeriod;

      return <div className={isPastDate ? "text-red-600" : ""}>{displayText}</div>;
    },
  },
  {
    id: "actions",
    cell: ActionsCell,
  },
];
