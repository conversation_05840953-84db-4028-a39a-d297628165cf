import { type Branch } from "@/api/contracts/branch-contract";
import { type VehicleGroup, type VehicleModel } from "@/api/contracts/rental/availability-contract";
import { type getTranslations } from "next-intl/server";

export const getFilters = (
  vehicleGroups: VehicleGroup[],
  models: VehicleModel[],
  branches: Branch[],
  locale: "en" | "ar",
  t: Awaited<ReturnType<typeof getTranslations>>
) => [
  {
    filterKey: "currentLocationIds",
    filterName: t("filters.location"),
    columnKey: "location",
    options: [
      ...branches.map((branch) => ({
        label: locale === "ar" ? branch.name.ar : branch.name.en,
        value: String(branch.id),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "modelIds",
    filterName: t("filters.model"),
    columnKey: "vehicle",
    options: [
      ...models.map((model) => ({
        label: `${model.make.name.en} ${model.name.en} (${model.vehicleGroup})`,
        value: String(model.id),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "groupCodes",
    filterName: t("filters.group"),
    columnKey: "vehicleGroup",
    options: [
      ...vehicleGroups.map((group) => ({ label: group.code ?? group.description.en, value: String(group.code) })),
    ],
    isMultiSelect: true,
  },
];
