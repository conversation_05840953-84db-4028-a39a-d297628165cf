"use client";

import { type NrmAvailabilityItem } from "@/api/contracts/rental/nrm-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { useState } from "react";
import { CloseNRMDialog } from "../_components/close-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { TimestampCell } from "../_components/table-cells/timestamp-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

type ColumnMessageKey = "nrmNo" | "plateNo" | "vehicle" | "from" | "to" | "reason" | "driver" | "nrmStartTime";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

function ActionsCell({ row }: { row: Row<NrmAvailabilityItem> }) {
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const plateNo = row.original.plateNo;
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";

  const handleCloseNRM = () => {
    setIsNRMDialogOpen(true);
  };

  return (
    <>
      <div>
        <Button variant="outline" onClick={handleCloseNRM}>
          {t("actions.closeNRM")}
        </Button>
      </div>

      {isNRMDialogOpen && (
        <CloseNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={plateNo}
          moveTo={row.original.checkInLocation.name?.[locale]}
          reason={row.original.nrmReason}
          nrmId={row.original.nrmId}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<NrmAvailabilityItem>[] = [
  {
    accessorKey: "nrmId",
    header: () => <Message messageKey="nrmNo" />,
    cell: ({ row }) => {
      return <div>{row.original.nrmId}</div>;
    },
  },
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={{ en: row.original.make, ar: row.original.make }}
        model={{ en: row.original.model, ar: row.original.model }}
        version={row.original.modelVersion}
      />
    ),
  },
  {
    accessorKey: "from",
    header: () => <Message messageKey="from" />,
    cell: ({ row }) => {
      const locationName = row.original.checkoutLocation.name;
      return <LocalizedObject localizedObject={locationName} />;
    },
  },
  {
    accessorKey: "to",
    header: () => <Message messageKey="to" />,
    cell: ({ row }) => {
      const locationName = row.original.checkInLocation.name;
      return <LocalizedObject localizedObject={locationName} />;
    },
  },
  {
    accessorKey: "nrmReason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => {
      const reason = row.original.nrmReason;
      let badgeClass = "bg-red-100";

      if (reason === "Fueling/Cleaning") {
        badgeClass = "bg-blue-100";
      }

      return (
        <Badge variant="outline" className={`${badgeClass} border-0 font-medium`}>
          {reason}
        </Badge>
      );
    },
  },
  {
    accessorKey: "driverName",
    header: () => <Message messageKey="driver" />,
    cell: ({ row }) => {
      return <div>{row.original.driverName || "-"}</div>;
    },
  },
  {
    accessorKey: "nrmStartDate",
    header: () => <Message messageKey="nrmStartTime" />,
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t, locale) => <TimestampCell timestamp={row.original.nrmStartDate} locale={locale} />}
        </TranslatedText>
      );
    },
  },
  {
    id: "actions",
    header: "",
    cell: ActionsCell,
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: string) => React.ReactNode;
}) => {
  const t = useTranslations("NRM.columns");
  const locale = useLocale();
  return <>{children(t, locale)}</>;
};
