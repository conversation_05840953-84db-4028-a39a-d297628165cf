"use client";

import { type NeedsPreparationVehicle } from "@/api/contracts/rental/availability-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { type Row, type ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { useState } from "react";
import { MoveToAvailableDialog } from "../_components/move-to-available-dialog";
import { MoveToServiceDialog } from "../_components/move-to-service-dialog";
import { CheckCircle, NotePencil, Wrench } from "@phosphor-icons/react";
import { OpenNRMDialog } from "../_components/open-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { useLocale, useTranslations } from "next-intl";

type ColumnMessageKey = "plateNo" | "group" | "location" | "vehicle" | "reason" | "fuel" | "waitingPeriod" | "actions";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

function ActionsCell({ row }: { row: Row<NeedsPreparationVehicle> }) {
  const t = useTranslations("NRM");
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const [isMoveToServiceDialogOpen, setIsMoveToServiceDialogOpen] = useState(false);
  const [isMoveToAvailableDialogOpen, setIsMoveToAvailableDialogOpen] = useState(false);

  const plateNo = row.original.plateNo;

  const handleOpenNRM = () => {
    setIsNRMDialogOpen(true);
  };

  const handleMoveToService = () => {
    setIsMoveToServiceDialogOpen(true);
  };

  const handleMoveToAvailable = () => {
    setIsMoveToAvailableDialogOpen(true);
  };

  return (
    <>
      <div className="text-right">
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 border border-gray-200 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleOpenNRM}>
                <NotePencil className="mr-2 h-4 w-4" />
                <div dir="rtl">{t("actions.openNRM")}</div>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleMoveToAvailable}>
                <CheckCircle className="mr-2 h-4 w-4" />
                {t("actions.moveToAvailable")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleMoveToService}>
                <Wrench className="mr-2 h-4 w-4" />
                {t("actions.moveToService")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {isNRMDialogOpen && (
        <OpenNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={plateNo}
          checkoutBranchId={row.original.location?.lumiBranchId}
          defaultReason={row.original.vehicleStatus.statusReason}
        />
      )}
      {isMoveToServiceDialogOpen && (
        <MoveToServiceDialog
          open={isMoveToServiceDialogOpen}
          onOpenChange={setIsMoveToServiceDialogOpen}
          plateNo={plateNo}
        />
      )}

      {isMoveToAvailableDialogOpen && (
        <MoveToAvailableDialog
          open={isMoveToAvailableDialogOpen}
          onOpenChange={setIsMoveToAvailableDialogOpen}
          plateNo={plateNo}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<NeedsPreparationVehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicleGroup",
    header: () => <Message messageKey="group" />,
    cell: ({ row }) => {
      return (
        <LocalizedObject
          localizedObject={{ en: row.original.model.vehicleGroup, ar: row.original.model.vehicleGroup }}
        />
      );
    },
  },
  {
    accessorKey: "locationName",
    header: () => <Message messageKey="location" />,
    cell: ({ row }) => {
      const location = row.original.location;
      return <LocalizedObject localizedObject={location?.name} />;
    },
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model.make.name}
        model={row.original.model.name}
        version={row.original.model.version}
      />
    ),
  },
  {
    accessorKey: "statusReason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => {
      const reason = row.original.vehicleStatus.statusReason;
      let badgeClass = "bg-red-100";

      if (reason === "Fueling/Cleaning") {
        badgeClass = "bg-blue-100";
      }

      return (
        <Badge variant="outline" className={`${badgeClass} border-0 font-medium`}>
          {reason}
        </Badge>
      );
    },
  },
  {
    accessorKey: "fuelLevelInfo",
    header: () => <Message messageKey="fuel" />,
    cell: ({ row }) => {
      const fuelInfo = row.original.fuelLevelInfo;
      if (!fuelInfo) return <div>-</div>;

      return (
        <div>
          {fuelInfo.fuelLevel}/4 ({fuelInfo.displayName})
        </div>
      );
    },
  },
  {
    accessorKey: "waitingPeriod",
    header: () => <Message messageKey="waitingPeriod" />,
    cell: ({ row }) => {
      const waitingTime = row.original.vehicleStatus.waitingTime;
      const now = Date.now();
      const diff = Math.abs(now - waitingTime);
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const weeks = Math.floor(days / 7);

      let waitingText = "";
      let isOverdue = false;

      if (weeks >= 1) {
        waitingText = `${weeks} ${weeks === 1 ? "week" : "weeks"}`;
        isOverdue = true;
      } else if (days >= 1) {
        waitingText = `${days} ${days === 1 ? "day" : "days"}`;
        isOverdue = days > 1;
      } else if (hours >= 1) {
        waitingText = `${hours} ${hours === 1 ? "hour" : "hours"}`;
        isOverdue = false;
      } else {
        waitingText = `${minutes} ${minutes === 1 ? "minute" : "minutes"}`;
        isOverdue = false;
      }

      return <div className={isOverdue ? "font-medium text-red-700" : ""}>{waitingText}</div>;
    },
  },
  {
    id: "actions",
    cell: ActionsCell,
  },
];
