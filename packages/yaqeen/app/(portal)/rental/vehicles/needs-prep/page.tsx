import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import { columns } from "./columns";
import { getFilters } from "./filters";
import { getTranslations, getLocale } from "next-intl/server";
import { convertPlateToEnglish } from "@/lib/utils";

type SearchParams = {
  plateNo: string;
  groupCodes: string;
  pageNumber: string;
  pageSize: string;
  modelIds: string;
  currentLocationIds: string;
  statusReason: string;
  branchId: string;
  statusReasonIds: string;
};

async function NeedsPrepVehiclesContent({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const _searchParams = await searchParams;
  const plateNo = _searchParams.plateNo || "";
  const groupCodes = _searchParams.groupCodes || "";
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const modelIds = _searchParams.modelIds || "";
  const currentLocationIds = _searchParams.currentLocationIds || "";
  const statusReason = _searchParams.statusReason || "";
  const branchId = _searchParams.branchId || "";
  const statusReasonIds = _searchParams.statusReasonIds || "";
  const t = await getTranslations("NRM");
  const locale = await getLocale();

  const [vehicleGroups, models, needsPrepVehicles, preparationReasons, branches] = await Promise.all([
    api.availability.getVehicleGroupList({
      query: { pageNumber: "0", pageSize: "150" },
    }),
    api.availability.getModelList({
      query: { pageNumber: "0", pageSize: "10000" },
    }),
    api.availability.getNeedsPreparationVehicles({
      query: {
        pageNumber,
        pageSize,
        ...(groupCodes && { groupCodes }),
        ...(modelIds && { modelIds }),
        ...(plateNo && { plateNo: locale === "ar" ? convertPlateToEnglish(plateNo) : plateNo }),
        ...(currentLocationIds && { currentLocationIds }),
        ...(statusReason && { statusReason }),
        ...(branchId && { branchId }),
        ...(statusReasonIds && { statusReasonIds }),
      },
    }),
    api.availability.getPreparationReasons({}),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (vehicleGroups.status !== 200) {
    throw new Error("Error fetching vehicle groups");
  }

  if (models.status !== 200) {
    throw new Error("Error fetching models");
  }

  if (needsPrepVehicles.status !== 200) {
    throw new Error("Error fetching vehicles that need preparation");
  }

  if (branches.status !== 200) {
    throw new Error("Error fetching branch list");
  }

  if (preparationReasons.status !== 200) {
    throw new Error("Error fetching preparation reasons");
  }

  const filters = getFilters(
    vehicleGroups.body.content,
    models.body.content,
    preparationReasons.body,
    branches.body.data,
    locale as "en" | "ar",
    {
      location: t("filters.location"),
      model: t("filters.model"),
      group: t("filters.group"),
      reason: t("filters.reason"),
    }
  );

  return (
    <>
      <div className="space-y-4">
        <DataTable
          columns={columns}
          data={{
            total: needsPrepVehicles.body.totalElements,
            data: needsPrepVehicles.body.content,
          }}
          searchPlaceholder={t("searchPlaceholder")}
          paginationEnabled={true}
          filters={filters}
          emptyMessage={t("emptyMessage")}
        />
      </div>
    </>
  );
}

export default async function NeedsPrepVehiclesPage({ searchParams }: { searchParams: Promise<SearchParams> }) {
  return (
    <Suspense fallback={<TableSkeleton filterCount={4} showPagination={true} />}>
      <NeedsPrepVehiclesContent searchParams={searchParams} />
    </Suspense>
  );
}
