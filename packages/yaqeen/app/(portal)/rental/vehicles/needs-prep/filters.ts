import {
  type PreparationReason,
  type VehicleGroup,
  type VehicleModel,
} from "@/api/contracts/rental/availability-contract";
import { type FilterOption } from "@/components/ui/data-table/toolbar";
import { type Branch } from "@/api/contracts/branch-contract";

export const getFilters = (
  vehicleGroups: VehicleGroup[],
  models: VehicleModel[],
  preparationReasons: PreparationReason[],
  branches: Branch[] = [],
  locale: "en" | "ar" = "en",
  filterNames: Record<string, string> = {}
): FilterOption[] => [
  {
    filterKey: "currentLocationIds",
    filterName: filterNames.location || "Location",
    columnKey: "locationName",
    options: branches.map((branch) => ({
      label: locale === "en" ? branch.name.en : branch.name.ar,
      value: String(branch.id),
    })),
    isMultiSelect: true,
  },
  {
    filterKey: "modelIds",
    filterName: filterNames.model || "Model",
    columnKey: "vehicle",
    options: [
      ...models.map((model) => ({
        label: `${model.make.name[locale]} ${model.name[locale]} (${model.vehicleGroup})`,
        value: String(model.id),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "groupCodes",
    filterName: filterNames.group || "Group",
    columnKey: "vehicleGroup",
    options: [
      ...vehicleGroups.map((group) => ({
        label: group.code ?? group.description,
        value: String(group.code),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "statusReasonIds",
    filterName: filterNames.reason || "Reason",
    columnKey: "statusReason",
    options: preparationReasons.map((reason) => ({
      label: reason.name,
      value: String(reason.id),
    })),
    isMultiSelect: true,
  },
];
