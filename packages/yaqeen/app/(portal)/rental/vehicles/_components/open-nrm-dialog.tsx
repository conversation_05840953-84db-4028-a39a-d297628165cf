"use client";

import { type Branch } from "@/api/contracts/branch-contract";
import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { type NrmReason, type UserSearchItem } from "@/api/contracts/rental/nrm-contract";
import { ContinueButton } from "@/components/ContinueButton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Autocomplete } from "@/components/ui/autocomplete";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { createNrm } from "@/lib/actions/nrm-actions";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { toast } from "@/lib/hooks/use-toast";
import { Warning } from "@phosphor-icons/react";
import { useActionState, useState } from "react";
import { VehicleDetailsCard } from "./vehicle-details-card";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

export interface OpenNRMDialogProps {
  open: boolean;
  checkoutBranchId: number | undefined;
  onOpenChange: (open: boolean) => void;
  plateNo?: string;
  defaultReason?: string;
}

export function OpenNRMDialog({ open, onOpenChange, plateNo, checkoutBranchId }: OpenNRMDialogProps) {
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";
  const [employeeId, setEmployeeId] = useState("");
  const [employeeName, setEmployeeName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [reason, setReason] = useState("");
  const [reasonId, setReasonId] = useState<number | null>(null);
  const [moveTo, setMoveTo] = useState("");
  const [checkoutDate, setCheckoutDate] = useState(new Date());
  const [remarks, setRemarks] = useState("");

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(createNrm, initialState);

  if (state.message) {
    if (state.success) {
      toast({
        title: "NRM created successfully",
        description: state.message,
        variant: "success",
      });
      onOpenChange(false);
    } else {
      toast({
        title: "Error creating NRM",
        description: state.message,
        variant: "destructive",
      });
    }
  }

  const { data: vehicleDetails, isLoading: isLoadingVehicle } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo || ""],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo || "")}&requireOpsData=true`,
    {
      enabled: open && !!plateNo,
      staleTime: 60 * 60 * 1000, // 1 hour
    }
  );

  const { data: branchesData, isLoading: isLoadingBranches } = useCustomQuery<{ data: Branch[] }>(
    ["branches"],
    "/next-api/branches",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000, // 1 day
    }
  );

  const branches = branchesData?.data || [];
  const { data: nrmReasons, isLoading: isLoadingReasons } = useCustomQuery<NrmReason[]>(
    ["nrmReasons"],
    "/next-api/nrm/reasons",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000, // 1 day
    }
  );

  const { data: usersData } = useCustomQuery<{ data: UserSearchItem[] }>(
    ["users", searchQuery],
    `/next-api/users?query=${encodeURIComponent(searchQuery)}`,
    {
      enabled: open && searchQuery.length > 0,
      staleTime: 30 * 1000, // 30 seconds
    }
  );

  const handleReasonChange = (value: string) => {
    setReason(value);
    const selectedReason = nrmReasons?.find((r) => r.name === value);
    if (selectedReason) {
      setReasonId(selectedReason.id);
    }
  };

  const filterUsers = (users: UserSearchItem[], query: string) => {
    setSearchQuery(query);
    return usersData?.data || [];
  };

  const renderUser = (user: UserSearchItem, isSelected: boolean) => (
    <div className={`p-2 ${isSelected ? "bg-muted" : ""}`}>
      <div className="font-medium">{`${user.firstName} ${user.lastName}`}</div>
      <div className="text-sm text-muted-foreground">ID: {user.id}</div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[855px]">
        <DialogHeader>
          <DialogTitle className="text-xl">{t("newNRM")}</DialogTitle>
        </DialogHeader>

        <VehicleDetailsCard vehicleDetails={vehicleDetails} isLoading={isLoadingVehicle} />

        <form action={formAction} className="mt-6">
          <input type="hidden" name="plateNo" value={vehicleDetails?.plateNo || ""} />

          <h4 className="font-medium">{t("checkoutDetails")}</h4>
          <Separator className="my-4" />

          <div className="space-y-4">
            <div>
              <label htmlFor="employeeId" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                {t("driverName")}
              </label>
              <Autocomplete<UserSearchItem>
                data={usersData?.data}
                filterFunction={filterUsers}
                renderItem={renderUser}
                getItemValue={(user) => `${user.firstName} ${user.lastName}`}
                getItemKey={(user) => user.successFactorId}
                placeholder={t("searchByDriverName")}
                onItemSelect={(user) => {
                  setEmployeeId(user.successFactorId || "");
                  setEmployeeName(`${user.firstName} ${user.lastName}`);
                }}
                debounce={300}
                limit={10}
              />
              <input type="hidden" name="driverId" value={employeeId} />
              <input type="hidden" name="driverName" value={employeeName} />
            </div>

            <div className="flex gap-4">
              <div className="flex-1">
                <label htmlFor="reason" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("reason")}*
                </label>
                <Select value={reason} onValueChange={handleReasonChange}>
                  <SelectTrigger id="reason">
                    <SelectValue placeholder={isLoadingReasons ? t("loadingReasons") : t("selectReason")} />
                  </SelectTrigger>
                  <SelectContent>
                    {nrmReasons?.map((nrmReason) => (
                      <SelectItem key={nrmReason.id} value={nrmReason.name}>
                        {nrmReason.name}
                      </SelectItem>
                    )) || []}
                  </SelectContent>
                </Select>
                <input type="hidden" name="reasonId" value={reasonId || ""} />
              </div>
              <div className="flex-1">
                <label htmlFor="moveTo" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("moveTo")}*
                </label>
                <Select value={moveTo} onValueChange={setMoveTo}>
                  <SelectTrigger id="moveTo">
                    <SelectValue placeholder={isLoadingBranches ? t("loadingBranches") : t("selectLocation")} />
                  </SelectTrigger>
                  <SelectContent>
                    {branches?.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id.toString()}>
                        {branch.name[locale]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <input type="hidden" name="checkoutBranchId" value={checkoutBranchId} />
                <input type="hidden" name="checkinBranchId" value={moveTo} />
              </div>
              <div className="flex-1">
                <label className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("checkoutTime")}
                </label>
                <DatePicker date={checkoutDate} setDate={setCheckoutDate} disabled />
              </div>
            </div>
          </div>

          <div className="mt-4">
            <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
              {t("remarks")}
            </label>
            <Textarea
              id="remarks"
              name="remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              placeholder={t("remarksPlaceholder")}
              className="min-h-[40px]"
            />
          </div>

          {reason === "Workshop Transfer" && (
            <Alert variant="warning" className="my-4">
              <Warning weight="bold" className="h-4 w-4" />
              <AlertTitle>{t("alertTitle")}</AlertTitle>
              <AlertDescription>{t("alertDescription")}</AlertDescription>
            </Alert>
          )}

          <DialogFooter className="mt-6 gap-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              {t("actions.cancel")}
            </Button>
            <ContinueButton>{t("actions.openNRM")}</ContinueButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
