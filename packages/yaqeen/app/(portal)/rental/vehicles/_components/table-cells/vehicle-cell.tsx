import { useLocale } from "next-intl";

interface VehicleInfo {
  make?: {
    en: string;
    ar?: string;
  };
  model?: {
    en: string;
    ar?: string;
  };
  version?: string;
}

export function VehicleCell({ make, model, version }: VehicleInfo) {
  const locale = useLocale();
  if (!make || !model) return null;

  return (
    <div>
      <div>{`${locale === "ar" ? make.ar : make.en} ${locale === "ar" ? model.ar : model.en}`}</div>
      {version && <div className="text-sm text-muted-foreground">{version}</div>}
    </div>
  );
}
