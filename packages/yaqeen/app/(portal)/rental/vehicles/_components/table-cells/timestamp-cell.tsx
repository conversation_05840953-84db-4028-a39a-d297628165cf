import { differenceInMinutes, formatDate, isToday, isTomorrow } from "date-fns";

interface TimestampCellProps {
  timestamp: Date | number;
  showOnlyDateTime?: boolean;
  locale?: string;
}

const enWordMap = {
  "1 day passed": "1 day passed",
  "days passed": "days passed",
  "Today - ": "Today - ",
  "Tomorrow - ": "Tomorrow - ",
  "in ": "in ",
  NOW: "NOW",
  "1 hour": "1 hour",
  hour: "hour",
  hours: "hours",
  minutes: "minutes",
};

const arWordMap = {
  "1 day passed": "يوم مضى",
  "days passed": "أيام مضت",
  "Today - ": "اليوم - ",
  "Tomorrow - ": "غدا - ",
  "in ": "في ",
  NOW: "الآن",
  "1 hour": "ساعة واحدة",
  hour: "ساعة",
  hours: "ساعات",
  minutes: "دقائق",
};

export function TimestampCell({ timestamp, locale }: TimestampCellProps) {
  const isArabic = locale === "ar";
  const wordMap = isArabic ? arWordMap : enWordMap;
  const now = new Date();
  const diffInMinutes = differenceInMinutes(timestamp, now);
  const formattedDateTime = `${formatDate(timestamp, "dd-MM-yyyy")}, ${formatDate(timestamp, "HH:mm")}`;

  const daysPassed = diffInMinutes < 0 ? Math.abs(Math.floor(diffInMinutes / (24 * 60))) : 0;
  const showDaysPassed = daysPassed > 0 && daysPassed <= 3;

  let timeDisplay;
  const isCurrentDay = isToday(timestamp);
  const isNextDay = isTomorrow(timestamp);

  if (isCurrentDay) {
    timeDisplay = `${wordMap["Today - "]} ${formatDate(timestamp, "HH:mm")}`;
  } else if (isNextDay) {
    timeDisplay = `${wordMap["Tomorrow - "]} ${formatDate(timestamp, "HH:mm")}`;
  }

  return (
    <div dir="ltr" className="text-start">
      {/* Show date if not today/tomorrow OR if past due */}
      {(!isCurrentDay && !isNextDay) || diffInMinutes < 0 ? <div>{formattedDateTime}</div> : null}

      {/* Past due - only show "days passed" for up to 3 days */}
      {diffInMinutes < 0 && showDaysPassed && (
        <div className="text-red-700">
          {daysPassed === 1 ? wordMap["1 day passed"] : `${daysPassed} ${wordMap["days passed"]}`}
        </div>
      )}

      {diffInMinutes >= 0 && (
        <>
          {timeDisplay && <div>{timeDisplay}</div>}

          {diffInMinutes <= 24 * 60 &&
            (diffInMinutes < 60 ? (
              diffInMinutes === 0 ? (
                <div className="text-lumi-700">{wordMap.NOW}</div>
              ) : (
                <div className="text-lumi-700">{`${wordMap["in "]} ${diffInMinutes} ${wordMap.minutes}`}</div>
              )
            ) : diffInMinutes === 60 ? (
              <div className="text-lumi-700">{`${wordMap["in "]} 1 ${wordMap.hour}`}</div>
            ) : (
              <div className="text-slate-500">{`${wordMap["in "]} ${Math.ceil(diffInMinutes / 60)} ${wordMap.hours}`}</div>
            ))}
        </>
      )}
    </div>
  );
}
