import { Sheet, She<PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet";
import { VehicleDetailsSideSheet } from "../vehicle-details-sidesheet";
import { useLocale } from "next-intl";
import { convertPlateToArabic } from "@/lib/utils";
interface PlateNoCellProps {
  plateNo: string | undefined;
  showDetails?: boolean;
}

export function PlateNoCell({ plateNo, showDetails = true }: PlateNoCellProps) {
  const locale = useLocale();
  if (!plateNo) return null;

  if (!showDetails) {
    return <div className="font-medium text-blue-600">{locale === "ar" ? convertPlateToArabic(plateNo) : plateNo}</div>;
  }

  return (
    <Sheet>
      <SheetHeader className="sr-only">
        <SheetTitle>Vehicle details</SheetTitle>
        <SheetDescription>View vehicle details here.</SheetDescription>
      </SheetHeader>

      <SheetTrigger asChild>
        <button className="p-0 text-blue-600 hover:text-blue-700">
          {locale === "ar" ? convertPlateToArabic(plateNo) : plateNo}
        </button>
      </SheetTrigger>
      <SheetContent side="right" className="w-full p-0 pt-6 ring-0 sm:w-[400px] sm:max-w-full">
        <VehicleDetailsSideSheet plateNo={plateNo} />
      </SheetContent>
    </Sheet>
  );
}
