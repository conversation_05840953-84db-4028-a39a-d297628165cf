"use client";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import CopyLink from "@/components/customized/copy-link";
import OpenLink from "@/components/customized/open-link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { SheetClose } from "@/components/ui/sheet";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { convertPlateToArabic, formatInspectionTime, shimmer, toBase64 } from "@/lib/utils";
import { PaintBrushBroad } from "@phosphor-icons/react";
import { GasPump, Gauge, MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import { VehiclePlate } from "@rental-components/vehicle-plate";
import capitalize from "lodash-es/capitalize";
import { useTranslations } from "next-intl";
import Image from "next/image";
import type { TranslationValues } from "next-intl";

export function VehicleDetailsSideSheetContent({ vehicleDetails }: { vehicleDetails: VehicleDetail | undefined }) {
  const t = useTranslations("vehicles");

  if (!vehicleDetails) {
    return null;
  }

  const model = vehicleDetails.model;
  const fuelLevel = vehicleDetails?.vehicleOperationDTO?.fuelLevel?.fuelLevel ?? 0;
  const fuelText = vehicleDetails?.vehicleOperationDTO?.fuelLevel?.displayName ?? "";
  const modelName = model?.name?.en ?? "";
  const makeName = model?.make?.name?.en ?? "";
  const imageUrl = model?.primaryImageUrl ?? null;
  const carGroup = model?.groupResponse?.code ?? "N/A";
  const [plateNumber, plateLetters] = vehicleDetails.plateNo.split(" ");

  return (
    <div className="flex h-full flex-col">
      {/* Header Section */}
      <div className="flex items-start justify-between px-6 pb-4">
        <div className="space-y-1">
          <div className="flex gap-2">
            <CopyLink />
            <OpenLink url={`#`} className="flex items-center gap-2" />
          </div>
        </div>
      </div>

      <ScrollArea className="hide-scrollbar flex-col">
        <div className="relative mb-6 overflow-hidden rounded-lg px-6 text-center">
          <Image
            src={imageUrl ?? "/static/<EMAIL>"}
            alt={modelName}
            className="inline object-contain"
            placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(200, 96))}`}
            width={200}
            height={96}
            priority
            onError={(e) => {
              e.currentTarget.src = "/static/<EMAIL>";
            }}
          />
        </div>
        {/* Vehicle Info */}
        <div className="mb-6 px-6 text-sm">
          <div className="mb-2 flex items-center gap-2">
            <Badge className="pointer-events-none bg-white font-medium" variant="outline">
              Group: {carGroup}
            </Badge>
          </div>
          <h2 className="mb-4 text-2xl font-semibold">
            {makeName} {modelName}
          </h2>
          <VehiclePlate
            plateNumber={plateNumber}
            plateNoAr={convertPlateToArabic(vehicleDetails.plateNo)}
            plateLetters={plateLetters}
            className="w-full"
          />
        </div>
        <Separator className="h-[4px] bg-slate-200" />
        {/* Vehicle Status */}
        <div className="mb-6 px-6 py-4">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold">Vehicle status</h3>
            <Badge className="pointer-events-none bg-lumi-200 font-medium text-slate-900">
              {capitalize(vehicleDetails?.vehicleOperationDTO?.vehicleStatus?.status)}
            </Badge>
          </div>
          <div className="space-y-4 text-sm">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Gauge className="h-5 w-5" />
                {vehicleDetails?.vehicleOperationDTO?.odometerReading?.toLocaleString() ?? "0"} KM
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <GasPump className="h-5 w-5" />
                {fuelLevel?.toLocaleString() ?? "0"}/4 ({fuelText})
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <MagnifyingGlass className="h-5 w-5" />
                {/* TODO: get the last inspected date once the API is ready */}
                {formatInspectionTime(null, t as (key: string, values?: TranslationValues) => string).displayText}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <PaintBrushBroad className="h-5 w-5" />
                {vehicleDetails.color ?? "N/A"}
              </span>
            </div>
          </div>
        </div>
      </ScrollArea>
      <div className="mt-auto bg-white px-6 py-4 shadow-[0px_-4px_8px_-2px_rgba(16,24,40,0.10),0px_-2px_4px_-2px_rgba(16,24,40,0.06)]">
        <SheetClose asChild>
          <section className="flex gap-4">
            <Button className="w-full font-medium text-slate-900">Close</Button>
          </section>
        </SheetClose>
      </div>
    </div>
  );
}

export function VehicleDetailsSideSheet({ plateNo }: { plateNo: string }) {
  const t = useTranslations();

  const {
    data: vehicleDetail,
    isLoading,
    isError,
    error,
  } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo)}&requireOpsData=true`,
    {
      enabled: !!plateNo,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-sm text-slate-500">{t("NRM.loadingVehicleDetails")}</div>
      </div>
    );
  }

  if (isError || !vehicleDetail) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-sm text-red-500">
          {error instanceof Error ? error.message : "Error loading vehicle details"}
        </div>
      </div>
    );
  }

  return <VehicleDetailsSideSheetContent vehicleDetails={vehicleDetail} />;
}
