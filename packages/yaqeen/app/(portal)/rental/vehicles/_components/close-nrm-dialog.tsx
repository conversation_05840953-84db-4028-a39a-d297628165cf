"use client";

import { type FuelLevel, FuelLevelValueEnum } from "@/api/contracts/fleet/maintenance/fuel-contract";
import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { type UserSearchItem } from "@/api/contracts/rental/nrm-contract";
import { ContinueButton } from "@/components/ContinueButton";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { closeNrm, type CloseNRMState } from "@/lib/actions/nrm-actions";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { toast } from "@/lib/hooks/use-toast";
import { useActionState, useState } from "react";
import { type z } from "zod";
import { VehicleDetailsCard } from "./vehicle-details-card";
import { Autocomplete } from "@/components/ui/autocomplete";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export interface CloseNRMDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  nrmId: number;
  plateNo?: string;
  reason?: string;
  moveTo?: string;
  driverId?: string;
  driverName?: string;
}

export function CloseNRMDialog({
  open,
  onOpenChange,
  plateNo,
  nrmId,
  reason,
  moveTo,
  driverId,
  driverName,
}: CloseNRMDialogProps) {
  const t = useTranslations("NRM.closeDialog");
  const [checkoutDate, setCheckoutDate] = useState(new Date());
  const [remarks, setRemarks] = useState("");
  const [odometerReading, setOdometerReading] = useState("");
  const [fuelLevel, setFuelLevel] = useState<z.infer<typeof FuelLevelValueEnum>>("-1");
  const [highMileageConfirmed, setHighMileageConfirmed] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [employeeId, setEmployeeId] = useState(driverId || "");
  const [employeeName, setEmployeeName] = useState(driverName || "");
  const queryClient = useQueryClient();

  const { data: vehicleDetails, isLoading: isLoadingVehicle } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo || ""],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo || "")}&requireOpsData=true`,
    {
      enabled: open && !!plateNo,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const { data: fuelLevels = [], isLoading: isLoadingFuelLevels } = useCustomQuery<FuelLevel[]>(
    ["fuelLevels"],
    "/next-api/fleet/maintenance/fuel-level",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000, // 24 hours
    }
  );

  const { data: usersData } = useCustomQuery<{ data: UserSearchItem[] }>(
    ["users", searchQuery],
    `/next-api/users?query=${encodeURIComponent(searchQuery)}`,
    {
      enabled: open && searchQuery.length > 0,
      staleTime: 30 * 1000, // 30 seconds
    }
  );

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [, formAction] = useActionState(async (state: CloseNRMState, formData: FormData) => {
    const result = await closeNrm(state, formData);

    if (result.success) {
      void queryClient.invalidateQueries({ queryKey: ["vehicleDetails", plateNo || ""] });
      toast({
        title: t("success.title"),
        description: result.message,
        variant: "success",
      });
      onOpenChange(false);
    }

    if (result.message && !result.success) {
      toast({
        title: t("error.title"),
        description: result.message,
        variant: "destructive",
      });
    }

    return result;
  }, initialState);

  const handleKmInChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOdometerReading(e.target.value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ","));
  };

  const isMileageHigherThanUsual = () => {
    if (!vehicleDetails?.vehicleOperationDTO?.odometerReading || !odometerReading) return false;

    const checkoutMileage = vehicleDetails.vehicleOperationDTO.odometerReading;
    const checkinMileage = parseInt(odometerReading.replace(/,/g, ""), 10);

    if (isNaN(checkinMileage)) return false;

    return checkinMileage - checkoutMileage > 100;
  };

  const isMileageLowerThanCurrent = () => {
    if (!vehicleDetails?.vehicleOperationDTO?.odometerReading || !odometerReading) return false;

    const checkoutMileage = vehicleDetails.vehicleOperationDTO.odometerReading;
    const checkinMileage = parseInt(odometerReading.replace(/,/g, ""), 10);

    if (isNaN(checkinMileage)) return false;

    return checkinMileage < checkoutMileage;
  };

  const filterUsers = (users: UserSearchItem[], query: string) => {
    setSearchQuery(query);
    return usersData?.data || [];
  };

  const renderUser = (user: UserSearchItem, isSelected: boolean) => (
    <div className={`p-2 ${isSelected ? "bg-muted" : ""}`}>
      <div className="font-medium">{`${user.firstName} ${user.lastName}`}</div>
      <div className="text-sm text-muted-foreground">ID: {user.id}</div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[855px]">
        <DialogHeader>
          <DialogTitle className="text-xl">{t("title", { nrmId })}</DialogTitle>
        </DialogHeader>

        <VehicleDetailsCard vehicleDetails={vehicleDetails} isLoading={isLoadingVehicle} />

        <form action={formAction} className="mt-6">
          <input type="hidden" name="nrmId" value={nrmId} />
          <h4 className="font-medium">{t("checkoutDetails")}</h4>
          <Separator className="my-4" />

          <div className="space-y-4">
            <div>
              <label htmlFor="employeeId" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                {t("driverName")}
              </label>
              <Autocomplete<UserSearchItem>
                data={usersData?.data}
                filterFunction={filterUsers}
                renderItem={renderUser}
                getItemValue={(user) => `${user.firstName} ${user.lastName}`}
                getItemKey={(user) => user.id}
                placeholder={t("searchByName")}
                onItemSelect={(user) => {
                  setEmployeeId(user.successFactorId || "");
                  setEmployeeName(`${user.firstName} ${user.lastName}`);
                }}
                debounce={300}
                limit={10}
              />
              <input type="hidden" name="driverId" value={employeeId} />
              <input type="hidden" name="driverName" value={employeeName} />
            </div>

            <div className="flex gap-4">
              <div className="flex-1">
                <label htmlFor="reason" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("reason")}
                </label>
                <Input id="reason" value={reason} disabled inputClassName="h-full w-full !bg-transparent opacity-50" />
              </div>
              <div className="flex-1">
                <label htmlFor="moveTo" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("moveTo")}
                </label>
                <Input id="moveTo" value={moveTo} disabled inputClassName="h-full w-full !bg-transparent opacity-50" />
              </div>
              <div className="flex-1">
                <label className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("checkoutTime")}
                </label>
                <DatePicker date={checkoutDate} setDate={setCheckoutDate} disabled />
              </div>
            </div>
          </div>

          <h4 className="mt-6 font-medium">{t("checkinDetails")}</h4>
          <Separator className="my-4" />
          <div className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <label
                  htmlFor="odometerReading"
                  className="mb-1 block text-sm font-medium leading-[21px] text-slate-900"
                >
                  {t("kmIn")}
                </label>
                <Input
                  id="odometerReading"
                  name="odometerReading"
                  value={odometerReading}
                  onChange={handleKmInChange}
                  placeholder={t("kmInPlaceholder")}
                />
              </div>
              <div className="flex-1">
                <label htmlFor="fuelLevel" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("fuelIn")}
                </label>
                <Select
                  value={fuelLevel}
                  onValueChange={(value) => {
                    if (FuelLevelValueEnum.safeParse(value).success) {
                      setFuelLevel(value as z.infer<typeof FuelLevelValueEnum>);
                    }
                  }}
                >
                  <SelectTrigger id="fuelLevel">
                    <SelectValue placeholder={isLoadingFuelLevels ? t("loadingFuelLevels") : t("selectFuelLevel")} />
                  </SelectTrigger>
                  <SelectContent>
                    {fuelLevels.map((level: FuelLevel) => (
                      <SelectItem key={level.id} value={level.id.toString()}>
                        {level.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <input type="hidden" name="fuelLevel" value={fuelLevel} />
              </div>
            </div>

            {isMileageHigherThanUsual() && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="highMileageConfirm"
                  checked={highMileageConfirmed}
                  onCheckedChange={(checked) => setHighMileageConfirmed(checked as boolean)}
                />
                <label
                  htmlFor="highMileageConfirm"
                  className="text-sm font-medium leading-none text-amber-600 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {t("highMileageWarning")}
                </label>
              </div>
            )}

            {isMileageLowerThanCurrent() && (
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium leading-none text-destructive">
                  {t("lowMileageError", {
                    reading: vehicleDetails?.vehicleOperationDTO?.odometerReading?.toLocaleString(),
                  })}
                </label>
              </div>
            )}
          </div>

          <div className="mt-4">
            <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
              {t("remarks")}
            </label>
            <Textarea
              id="remarks"
              name="remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              placeholder={t("remarksPlaceholder")}
              className="min-h-[40px]"
            />
          </div>

          <DialogFooter className="mt-6 gap-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              {t("cancel")}
            </Button>
            <ContinueButton
              disabled={
                !odometerReading || (isMileageHigherThanUsual() && !highMileageConfirmed) || isMileageLowerThanCurrent()
              }
            >
              {t("closeNrm")}
            </ContinueButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
