import { type Route } from "next";

type LabelKey = "readyForRental" | "rented" | "needsPrep" | "nrms" | "outOfService";

export const vehicleTabs: { labelKey: LabelKey; href: Route<string> }[] = [
  { labelKey: "readyForRental", href: "/rental/vehicles/ready" as Route<string> },
  { labelKey: "rented", href: "/rental/vehicles/rented" as Route<string> },
  { labelKey: "needsPrep", href: "/rental/vehicles/needs-prep" as Route<string> },
  { labelKey: "nrms", href: "/rental/vehicles/nrm" as Route<string> },
  { labelKey: "outOfService", href: "/rental/vehicles/out-of-service" as Route<string> },
] as const;
