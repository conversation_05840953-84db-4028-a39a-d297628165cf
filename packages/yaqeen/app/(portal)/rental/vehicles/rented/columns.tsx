"use client";

import { type RentedVehicle } from "@/api/contracts/rental/availability-contract";
import { type ColumnDef } from "@tanstack/react-table";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { TimestampCell } from "../_components/table-cells/timestamp-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

type ColumnMessageKey = "plateNo" | "group" | "vehicle" | "bookingNo" | "checkInBranch" | "checkInDate";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

export const columns: ColumnDef<RentedVehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicleGroup",
    header: () => <Message messageKey="group" />,
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={{ en: row.original.make, ar: row.original.make }}
        model={{ en: row.original.model, ar: row.original.model }}
        version={row.original.modelVersion}
      />
    ),
  },
  {
    accessorKey: "bookingId",
    header: () => <Message messageKey="bookingNo" />,
  },
  {
    accessorKey: "checkInBranch",
    header: () => <Message messageKey="checkInBranch" />,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original?.checkInBranch?.name} />,
  },
  {
    accessorKey: "checkInDate",
    header: () => <Message messageKey="checkInDate" />,
    cell: ({ row }) => (
      <TranslatedText>
        {(locale) => <TimestampCell timestamp={row.original.checkInDate} locale={locale} />}
      </TranslatedText>
    ),
  },
];

const TranslatedText = ({ children }: { children: (locale: string) => React.ReactNode }) => {
  const locale = useLocale();
  return <>{children(locale)}</>;
};
