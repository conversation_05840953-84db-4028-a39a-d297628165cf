import { cn, convertPlateToArabic } from "@/lib/utils";

interface VehiclePlateProps {
  className?: string;
  plateNumber: string | undefined;
  plateNoAr: string | undefined;
  plateLetters: string | undefined;
}

export function VehiclePlate({ className, plateNumber, plateNoAr, plateLetters }: VehiclePlateProps) {
  if (!plateNumber || !plateLetters || !plateNoAr) return null;
  const arabicLetters = convertPlateToArabic(plateLetters.split("").reverse()?.join(" "));
  return (
    <div dir="ltr" className={cn("overflow-hidden rounded-lg border border-b border-slate-800 font-medium", className)}>
      <div className="grid grid-cols-2">
        <div className="flex min-h-3 items-center justify-center border-b border-r border-slate-800 p-1">
          <span className="text-xs leading-tight">{plateNumber}</span>
        </div>
        <div className="flex min-h-3 items-center justify-center border-b border-slate-800 p-1">
          <span className="text-xs leading-tight">{plateLetters}</span>
        </div>

        <div className="flex min-h-3 items-center justify-center border-r border-slate-800 p-1" dir="rtl">
          <span className="text-sm font-bold leading-tight">
            {plateNumber.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[Number(d)] ?? d)}
          </span>
        </div>
        <div className="flex min-h-3 items-center justify-center whitespace-nowrap p-1" dir="rtl">
          <span className="text-sm font-bold leading-tight">{arabicLetters}</span>
        </div>
      </div>
    </div>
  );
}
