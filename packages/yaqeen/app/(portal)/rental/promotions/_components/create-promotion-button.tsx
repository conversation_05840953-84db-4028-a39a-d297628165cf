"use client";

import { type Route } from "next";
import { useTranslations } from "next-intl";

import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export default function CreatePromotionButton() {
  const t = useTranslations("promotions");

  return (
    <ProgressBarLink href={`/rental/promotions/new` as Route}>
      <Button variant="default" className="bg-[#9CE642] text-black hover:bg-[#8AD532]">
        <Plus className="mr-2 h-4 w-4" />
        {t("createNewPromotion")}
      </Button>
    </ProgressBarLink>
  );
}
