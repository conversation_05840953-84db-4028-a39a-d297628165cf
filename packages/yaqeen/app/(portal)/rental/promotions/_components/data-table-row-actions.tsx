"use client";

import React from "react";
import { type Route } from "next";
import { type Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
// import { CopySimple, DotsThree } from "@phosphor-icons/react/dist/ssr";

// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ProgressBarLink } from "@/components/progress-bar";
import { type RowType } from "./columns";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}

export function DataTableRowActions<TData extends RowType>({ row }: DataTableRowActionsProps<TData>) {
  const t = useTranslations("promotions");
  const promoCode = row.original.code;

  return (
    <div className=" flex items-center justify-end gap-x-2">
      <ProgressBarLink href={`/rental/promotions/${promoCode}` as Route}>
        <Button variant="outline">{t("edit")}</Button>
      </ProgressBarLink>
      {/* <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center justify-end gap-x-1">
            <Button variant="outline" className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
              <DotsThree className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[190px] ">
          <DropdownMenuItem>
            <CopySimple className="mr-2 h-4 w-4" />
            Duplicate
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu> */}
    </div>
  );
}
