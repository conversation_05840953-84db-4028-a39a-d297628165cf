"use client";

import React from "react";

import { Search } from "lucide-react";

import { type PromotionsRes } from "@/api/contracts/pricing/promotions/schema";

import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table/data-table";

import Select from "@/components/atoms/Select";

import { generateColumns } from "./columns";
import { useQueryNav } from "@/app/lib/useQueryNav";
import DebounceInput from "@/components/atoms/DebounceInput";

const TABS = ["All", "Single-use"] as const;
export type Tabs = (typeof TABS)[number];

const TYPES = [
  { label: "All", value: null },
  { label: "Corporate CDP", value: "CORPORATE" },
  { label: "Promotional Campaign", value: "PROMOTIONAL" },
];

const STATUSES = [
  { label: "All", value: null },
  { label: "Active", value: "true" },
  { label: "Inactive", value: "false" },
];

const customParser = (value: string): "All" | "Single-use" | null => {
  if (value === "All" || value === "Single-use") {
    return value;
  }
  return null;
};

const Dashboard = ({ promotions }: { promotions: PromotionsRes }) => {
  const [, setSearchPageNumber] = useQueryNav("pageNumber");
  const [searchQuery, setSearchQuery] = useQueryNav("text");
  const [activeTab, setActiveTab] = useQueryNav<Tabs>("tab", {
    parse: customParser,
    defaultValue: "All",
  });
  const [selectedType, setSelectedType] = useQueryNav("type");
  const [selectedStatus, setSelectedStatus] = useQueryNav("status");

  const columns = generateColumns();

  return (
    <div>
      <div className="mb-6 flex gap-6 border-b bg-slate-50 px-4 pt-4">
        {TABS.map((tab) => (
          <Button
            key={tab}
            variant="ghost"
            className={`rounded-none px-1 pb-2 ${
              activeTab === tab ? "border-b-2 border-black font-medium text-black" : "text-gray-500"
            }`}
            onClick={() => {
              void setActiveTab(tab);
              void setSearchQuery(null);
              void setSelectedType(null);
              void setSelectedStatus(null);
              void setSearchPageNumber(null);
            }}
          >
            {tab}
          </Button>
        ))}
      </div>
      <div className="bg-white p-4">
        <div className="mb-6 flex justify-between">
          <div className="flex gap-4">
            {activeTab !== "Single-use" && (
              <Select
                hideSearch
                defaultValue={selectedType}
                placeholder="Type"
                onSelect={(value) => {
                  void setSelectedType(value);
                  void setSearchPageNumber(null);
                }}
                options={TYPES}
              />
            )}

            <Select
              hideSearch
              defaultValue={selectedStatus}
              placeholder="Status"
              onSelect={(value) => {
                void setSelectedStatus(value);
                void setSearchPageNumber(null);
              }}
              options={STATUSES}
            />
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-2.5 text-gray-400" size={20} />
            <DebounceInput
              type="text"
              placeholder="Search by code, name"
              className="w-72 pl-10"
              defaultValue={searchQuery ?? ""}
              onChange={(value) => {
                void setSearchQuery(value);
                void setSearchPageNumber(null);
              }}
            />
          </div>
        </div>

        <DataTable columns={columns} data={{ total: promotions.totalElements, data: promotions.content }} />
      </div>
    </div>
  );
};

export default Dashboard;
