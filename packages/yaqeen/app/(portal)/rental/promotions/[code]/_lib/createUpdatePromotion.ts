"use server";

import { format } from "date-fns";

import { api } from "@/api";
import { type ErrorResponse } from "@/api/types";
import { type PromotionsReq, type PromotionsRes } from "@/api/contracts/pricing/promotions/schema";

import { formatError } from "@/app/lib/formatError";

import { promotionFormSchema, type PromotionFormValues } from "../_components/promotionDetails/schema";

export async function createUpdatePromotion(
  data: PromotionFormValues
): Promise<{ error?: ErrorResponse; data?: PromotionsRes["content"][number] }> {
  try {
    const isEditMode = !!data.createdOn;
    const result = promotionFormSchema.safeParse(data);

    if (result.error) {
      const desc = formatError(result.error);
      return { error: { code: "FE error", desc } };
    }

    const body = {
      ...data,
      validFrom: format(data.validFrom, "dd-MM-yyyy"),
      validTo: format(data.validTo, "dd-MM-yyyy"),
      carGroupCodes: data.carGroupCodes.codes,
      isEnabled: data.isEnabled,
    } satisfies PromotionsReq;

    if (isEditMode) {
      const code = data.code;
      const response = await api.pricing.promotionsContract.updatePromotion({
        params: {
          code,
        },
        body,
      });

      if (response.status !== 200) {
        return { error: response.body };
      }

      return { data: response.body };
    }

    const response = await api.pricing.promotionsContract.createPromotion({
      body,
    });

    if (response.status !== 200) {
      return { error: response.body };
    }

    return { data: response.body };
  } catch (error) {
    console.error(error);
    let message = "Unknown Error";
    if (error instanceof Error) message = error.message;
    return { error: { desc: message, code: "FE error" } };
  }
}
