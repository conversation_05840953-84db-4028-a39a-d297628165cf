"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { useFormContext, useWatch } from "react-hook-form";

import CardWrapper from "@/components/atoms/CardWrapper";

import EligibleDays from "./EligibleDays";
import RedemptionLimit from "./RedemptionLimit";

export default function AdditionalDetails() {
  const t = useTranslations("promotions");

  const { control } = useFormContext();
  const type = useWatch({ control, name: "voucherType" });

  return (
    <CardWrapper title={t("details")}>
      <div className="flex flex-col gap-4">
        {type !== "ONE_TIME" && <RedemptionLimit />}
        <EligibleDays />
      </div>
    </CardWrapper>
  );
}
