import { z } from "zod";

const promotionBaseFormSchema = z.object({
  createdOn: z.number().optional(),
  nameEn: z.string().nonempty("Promotion Name is required"),
  nameAr: z.string().nonempty("Promotion Name is required"),
  descriptionEn: z.string().nonempty("Description is required"),
  descriptionAr: z.string().nonempty("Description is required"),
  percentageDiscount: z.coerce
    .number()
    .min(0, "Discount must be non-negative")
    .max(100, "Discount must be less than or equal to 100"),
  code: z.string().nonempty("Promotion Code is required"),
  validFrom: z.coerce.date(),
  validTo: z.coerce.date(),
  showInOffers: z.boolean(),
  eligibleDays: z
    .array(z.enum(["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"]))
    .optional(),
  validCount: z.coerce.number().optional(),
  branchIds: z.array(z.number()),
  carGroupCodes: z.object({
    details: z.array(z.object({ id: z.number(), code: z.string().optional() })),
    codes: z.array(z.string()),
  }),
  paymentOption: z.enum(["PAY_ONLINE", "PAY_AT_BRANCH"]),
  termsAndConditions: z.string().optional(),
  isEnabled: z.boolean().optional().default(false),
});

const promotionalForm = z
  .object({
    voucherType: z.enum(["PROMOTIONAL"]),
  })
  .merge(promotionBaseFormSchema);

const oneTimeForm = z
  .object({
    voucherType: z.enum(["ONE_TIME"]),
    noOfVouchers: z.coerce.number(),
  })
  .merge(promotionBaseFormSchema);

const corporateForm = z
  .object({
    voucherType: z.enum(["CORPORATE"]),
    eligibleEmailDomains: z.array(z.string()),
  })
  .merge(promotionBaseFormSchema);

export const promotionFormSchema = z.discriminatedUnion("voucherType", [corporateForm, oneTimeForm, promotionalForm]);

export type PromotionFormValues = z.infer<typeof promotionFormSchema>;
