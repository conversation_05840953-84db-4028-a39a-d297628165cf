"use client";

import React from "react";
import { useTranslations } from "next-intl";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

import CardWrapper from "@/components/atoms/CardWrapper";
import { FormField, FormMessage } from "@/components/ui/form";

export default function PaymentOptions() {
  const t = useTranslations("promotions");

  return (
    <CardWrapper title={t("paymentOptions")}>
      <FormField
        name="paymentOption"
        render={({ field }) => {
          return (
            <>
              <RadioGroup defaultValue={field.value} onValueChange={field.onChange}>
                <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                  <RadioGroupItem value="PAY_ONLINE" id="PAY_ONLINE" className="mt-1" />
                  <Label htmlFor="PAY_ONLINE" className="flex cursor-pointer flex-col gap-2 font-medium ">
                    <span>{t("payOnline")}</span>
                    <p className="text-sm  text-gray-500">{t("payOnlineDescription")} </p>
                  </Label>
                </div>

                <Separator />
                <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                  <RadioGroupItem value="PAY_AT_BRANCH" id="PAY_AT_BRANCH" className="mt-1" />
                  <Label htmlFor="PAY_AT_BRANCH" className="flex cursor-pointer flex-col gap-2 font-medium ">
                    <span>{t("payAtBranch")}</span>
                    <p className="text-sm text-gray-500">{t("payAtBranchDescription")}</p>
                  </Label>
                </div>
              </RadioGroup>
              <FormMessage />
            </>
          );
        }}
      />
    </CardWrapper>
  );
}
