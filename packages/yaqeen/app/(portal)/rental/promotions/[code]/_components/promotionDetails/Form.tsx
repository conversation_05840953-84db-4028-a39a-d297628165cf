"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format, parse } from "date-fns";

import { toast } from "@/lib/hooks/use-toast";

import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";
import { type PromotionApiResSchema } from "@/api/contracts/pricing/promotions/schema";

import { Form } from "@/components/ui/form";

import { createUpdatePromotion } from "../../_lib/createUpdatePromotion";
import { promotionFormSchema, type PromotionFormValues } from "./schema";

export default function PromotionForm({
  children,
  defaultValue,
  vehicleGroups,
}: {
  children: React.ReactNode;
  defaultValue?: PromotionApiResSchema;
  vehicleGroups: VehicleGroups;
}) {
  const router = useRouter();

  const form = useForm<PromotionFormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(promotionFormSchema),
    defaultValues: {
      createdOn: defaultValue?.createdOn,
      voucherType: defaultValue?.voucherType || "PROMOTIONAL",
      nameEn: defaultValue?.name?.en || "",
      nameAr: defaultValue?.name?.ar || "",
      descriptionEn: defaultValue?.description?.en || "",
      descriptionAr: defaultValue?.description?.ar || "",
      percentageDiscount: defaultValue?.percentageDiscount || 0,
      code: defaultValue?.code || "",
      validFrom: defaultValue?.validFrom
        ? (format(new Date(parse(defaultValue?.validFrom, "dd-MM-yyyy", new Date())), "yyyy-MM-dd") as unknown as Date)
        : undefined,
      validTo: defaultValue?.validTo
        ? (format(new Date(parse(defaultValue?.validTo, "dd-MM-yyyy", new Date())), "yyyy-MM-dd") as unknown as Date)
        : new Date(),
      showInOffers: defaultValue?.showInOffers ?? false,
      branchIds: defaultValue?.branchIds || [],
      carGroupCodes: {
        details:
          vehicleGroups.content
            .filter((group) => defaultValue?.carGroupCodes?.includes(group.code))
            .map((group) => ({
              ...group,
              id: Number(group.id),
            })) || [],
        codes: defaultValue?.carGroupCodes || [],
      },
      eligibleDays: defaultValue?.eligibleDays || [],
      paymentOption: defaultValue?.paymentOption || "PAY_ONLINE",
      termsAndConditions: defaultValue?.termsAndConditions || "",
      eligibleEmailDomains: defaultValue?.eligibleEmailDomains || [],
      noOfVouchers: defaultValue?.noOfVouchers,
      isEnabled: defaultValue?.isEnabled ?? false,
    },
  });

  const onSubmit = async (data: PromotionFormValues) => {
    const isEditMode = !!data.createdOn;

    const response = await createUpdatePromotion(data);

    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }

    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    router.replace(`/rental/promotions`);
  };

  return (
    <Form {...form}>
      <form className="w-full" onSubmit={form.handleSubmit(onSubmit)}>
        {children}
      </form>
    </Form>
  );
}
