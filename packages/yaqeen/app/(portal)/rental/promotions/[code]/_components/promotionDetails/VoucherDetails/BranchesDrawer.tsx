"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  Drawer<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";

import { type BranchesListRes } from "@/api/contracts/branch-contract";

export type BranchesDrawerProps = {
  branches: BranchesListRes;
};

const FIELD_NAME = "branchIds";

export function BranchesDrawer({ branches }: BranchesDrawerProps) {
  const t = useTranslations("promotions");
  const { getValues, setValue } = useFormContext();

  const [isOpen, setIsOpen] = React.useState(false);
  const [selectedBranches, setSelectedBranches] = useState<Record<string, boolean>>({});

  // Group branches by region and then by city
  const groupedBranches = branches.reduce(
    (acc, branch) => {
      const region = branch.city.region.name.en;
      const city = branch.city.name.en;
      if (!acc[region]) {
        acc[region] = {};
      }
      if (!acc[region][city]) {
        acc[region][city] = [];
      }
      acc[region][city].push(branch);
      return acc;
    },
    {} as Record<string, Record<string, BranchesListRes>>
  );

  React.useEffect(() => {
    const defaultBranches = getValues(FIELD_NAME) || [];
    setSelectedBranches(
      defaultBranches.reduce((acc: Record<string, boolean>, id: string) => ({ ...acc, [id]: true }), {})
    );
  }, [getValues]);

  const handleSelectBranch = (isChecked: boolean | "indeterminate", branch: BranchesListRes[number]) => {
    if (isChecked) {
      setSelectedBranches((prev) => ({ ...prev, [branch.id]: true }));
    } else {
      setSelectedBranches((prev) => ({ ...prev, [branch.id]: false }));
    }
  };

  const selectAndClose = () => {
    setValue(
      FIELD_NAME,
      Object.keys(selectedBranches)
        .filter((id) => selectedBranches[id])
        .map(Number)
    );
    setIsOpen(false);
  };

  const handleSelectAll = (checked: boolean | "indeterminate", branches: BranchesListRes) => {
    setSelectedBranches(branches.map((branch) => branch.id).reduce((acc, id) => ({ ...acc, [id]: checked }), {}));
  };

  const handleRegionChange = (
    checked: boolean | "indeterminate",
    region: string,
    cities: Record<string, BranchesListRes>
  ) => {
    setSelectedBranches((prev) =>
      Object.values(cities)
        .flat()
        .reduce((acc, branch) => ({ ...acc, [branch.id]: checked }), prev)
    );
  };

  const handleCityChange = (
    checked: boolean | "indeterminate",
    region: string,
    city: string,
    branches: BranchesListRes
  ) => {
    setSelectedBranches((prev) => branches.reduce((acc, branch) => ({ ...acc, [branch.id]: checked }), prev));
  };

  const getCheckedState = (branches: BranchesListRes) => {
    const isAllChecked = branches.map((branch) => selectedBranches[branch.id]).every((isChecked) => isChecked);
    const isSomeChecked = branches.map((branch) => selectedBranches[branch.id]).some((isChecked) => isChecked);
    return isAllChecked ? true : isSomeChecked ? "indeterminate" : false;
  };

  const getCheckedRegionState = (cities: Record<string, BranchesListRes>) => {
    const isAllChecked = Object.values(cities)
      .flat()
      .every((branch) => selectedBranches[branch.id]);
    const isSomeChecked = Object.values(cities)
      .flat()
      .some((branch) => selectedBranches[branch.id]);
    return isAllChecked ? true : isSomeChecked ? "indeterminate" : false;
  };

  const getCheckedCityState = (branches: BranchesListRes) => {
    const isAllChecked = branches.every((branch) => selectedBranches[branch.id]);
    const isSomeChecked = branches.some((branch) => selectedBranches[branch.id]);
    return isAllChecked ? true : isSomeChecked ? "indeterminate" : false;
  };

  return (
    <Drawer
      direction="right"
      open={isOpen}
      onClose={() => {
        setIsOpen(false);
      }}
    >
      <DrawerTrigger asChild>
        <Button variant="outline" onClick={() => setIsOpen(true)}>
          {t("selectBranches")}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="w-[26rem]">
        <DrawerHeader className="p p-6">
          <DrawerTitle className="text-2xl font-bold">{t("selectBranches")}</DrawerTitle>
        </DrawerHeader>
        <DrawerDescription asChild className="overflow-auto">
          <div className="flex flex-col gap-3 p-4 text-slate-900">
            <div className="flex gap-2">
              <Checkbox
                role="checkbox"
                checked={getCheckedState(branches)}
                onClick={(e) => e.stopPropagation()}
                onCheckedChange={(checked) => {
                  handleSelectAll(checked, branches);
                }}
              />
              <span className="text-slate-900">{t("selectAll")}</span>
            </div>
            <div className="ps-4">
              {Object.entries(groupedBranches).map(([region, cities]) => (
                <Accordion key={region} type="multiple">
                  <AccordionItem value={region}>
                    <AccordionTrigger asChild className="flex justify-between gap-2">
                      <div className="flex gap-2">
                        <Checkbox
                          role="checkbox"
                          checked={getCheckedRegionState(cities)}
                          onClick={(e) => e.stopPropagation()}
                          onCheckedChange={(checked) => {
                            handleRegionChange(checked, region, cities);
                          }}
                        />
                        <span className="text-slate-900">{region}</span>
                        <Badge
                          variant="secondary"
                          className={`mx-2 flex gap-1 rounded-full bg-lumi-200 px-3 font-normal capitalize hover:bg-lumi-200/80`}
                        >
                          <span>{Object.values(cities).flat().length}</span>
                          {t("branchesCount", { count: Object.values(cities).flat().length })}
                        </Badge>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="ps-4">
                      {Object.entries(cities).map(([city, branches]) => (
                        <Accordion collapsible key={city} type="single">
                          <AccordionItem value={city}>
                            <AccordionTrigger asChild className="flex justify-between gap-2">
                              <div className="flex gap-2">
                                <Checkbox
                                  role="checkbox"
                                  checked={getCheckedCityState(branches)}
                                  onClick={(e) => e.stopPropagation()}
                                  onCheckedChange={(checked) => {
                                    handleCityChange(checked, region, city, branches);
                                  }}
                                />
                                <span className="font-bold">{city}</span>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="flex flex-col gap-2 ps-4">
                              {branches.map((branch) => (
                                <div key={branch.id} className="flex items-center">
                                  <Checkbox
                                    role="checkbox"
                                    checked={selectedBranches[branch.id]}
                                    id={`branch-${branch.id}`}
                                    onCheckedChange={(checked) => {
                                      handleSelectBranch(checked, branch);
                                    }}
                                  />
                                  <label htmlFor={`branch-${branch.id}`} className="ml-2">
                                    {branch.name.en}
                                  </label>
                                </div>
                              ))}
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      ))}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              ))}
            </div>
          </div>
        </DrawerDescription>
        <DrawerFooter className="flex flex-row">
          <DrawerClose asChild>
            <Button data-testid="drawer-close-button" variant="outline" className="w-20">
              {t("cancel")}
            </Button>
          </DrawerClose>
          <Button onClick={selectAndClose} className="w-20">
            {t("select")}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
