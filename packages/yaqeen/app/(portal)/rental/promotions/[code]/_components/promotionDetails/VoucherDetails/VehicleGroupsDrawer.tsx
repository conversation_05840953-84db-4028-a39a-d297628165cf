"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drawer<PERSON>rigger,
} from "@/components/ui/drawer";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";

import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

export type VehicleGroupsDrawerProps = {
  vehicleGroups: VehicleGroups;
};

const FIELD_NAME = "carGroupCodes";

export function VehicleGroupsDrawer({ vehicleGroups }: VehicleGroupsDrawerProps) {
  const t = useTranslations("promotions");
  const { setValue, getValues } = useFormContext();

  const [isOpen, setIsOpen] = React.useState(false);

  const [selectedVehicles, setSelectedVehicles] = React.useState<VehicleGroups["content"][number][]>([]);

  React.useEffect(() => {
    const initialDetails = getValues(FIELD_NAME)?.details || [];
    setSelectedVehicles(initialDetails);
  }, [getValues(FIELD_NAME)]);

  const handleSelectAll = (isChecked: boolean | "indeterminate", vehicles: VehicleGroups["content"]) => {
    if (isChecked) {
      setSelectedVehicles(vehicles);
    } else {
      setSelectedVehicles([]);
    }
  };

  const handleSelectVehicle = (isChecked: boolean | "indeterminate", vehicle: VehicleGroups["content"][number]) => {
    if (isChecked) {
      setSelectedVehicles((prev) => [...prev, vehicle]);
    } else {
      setSelectedVehicles((prev) => prev.filter((v) => v.id !== vehicle.id));
    }
  };

  const getCheckedState = (vehicles: VehicleGroups["content"]) => {
    const isAllChecked = vehicles.every((vehicle) => selectedVehicles.some((v) => v.id === vehicle.id));
    const isSomeChecked = vehicles.some((vehicle) => selectedVehicles.some((v) => v.id === vehicle.id));

    return isAllChecked ? true : isSomeChecked ? "indeterminate" : false;
  };

  const selectAndClose = () => {
    setValue(FIELD_NAME, {
      details: selectedVehicles,
      codes: selectedVehicles.map((vehicle) => vehicle.code),
    });
    setIsOpen(false);
  };

  return (
    <Drawer
      direction="right"
      open={isOpen}
      onClose={() => {
        setIsOpen(false);
      }}
    >
      <DrawerTrigger asChild>
        <Button variant="outline" onClick={() => setIsOpen(true)}>
          {t("selectCarGroups")}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="w-[26rem]">
        <DrawerHeader className="p p-6">
          <DrawerTitle className="text-2xl font-bold">{t("selectCarGroups")}</DrawerTitle>
        </DrawerHeader>
        <DrawerDescription asChild className="overflow-auto">
          <div className="flex flex-col gap-3 p-4 text-slate-900">
            <div className="flex items-center">
              <Checkbox
                role="checkbox"
                checked={getCheckedState(vehicleGroups.content)}
                onClick={(e) => e.stopPropagation()}
                onCheckedChange={(checked) => {
                  handleSelectAll(checked, vehicleGroups.content);
                }}
              />
              <label className="ml-2">{t("selectCarGroups")}</label>
              <Badge
                variant="secondary"
                className={`mx-2 rounded-full bg-lumi-200 px-3 font-normal capitalize hover:bg-lumi-200/80`}
              >
                {vehicleGroups.content.length} {t("carGroups")}
              </Badge>
            </div>
            <div className="flex flex-col gap-2 ps-4">
              {vehicleGroups.content.map((vehicle) => (
                <div key={vehicle.id} className="flex items-center">
                  <Checkbox
                    key={`${vehicle.id}-${selectedVehicles.some((v) => v.id === vehicle.id)}`}
                    role="checkbox"
                    checked={selectedVehicles.some((v) => v.id === vehicle.id)}
                    id={`vehicle-${vehicle.id}`}
                    onCheckedChange={(checked) => {
                      handleSelectVehicle(checked, vehicle);
                    }}
                  />
                  <label htmlFor={`vehicle-${vehicle.id}`} className="ml-2">
                    {vehicle.code}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </DrawerDescription>
        <DrawerFooter className="flex flex-row">
          <DrawerClose asChild>
            <Button data-testid="drawer-close-button" variant="outline" className="w-20">
              {t("cancel")}
            </Button>
          </DrawerClose>
          <Button onClick={selectAndClose} className="w-20">
            {t("select")}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
