"use client";

import React from "react";
import { useTranslations } from "next-intl";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

import CardWrapper from "@/components/atoms/CardWrapper";
import { FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";

export default function VoucherType() {
  const t = useTranslations("promotions");

  return (
    <CardWrapper title={t("voucherType")}>
      <FormField
        name="voucherType"
        render={({ field }) => {
          return (
            <FormItem>
              <FormControl>
                <RadioGroup defaultValue={field.value} onValueChange={field.onChange}>
                  <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                    <RadioGroupItem value="PROMOTIONAL" id="PROMOTIONAL" className="mt-1" />
                    <Label htmlFor="PROMOTIONAL" className="flex cursor-pointer flex-col gap-2 font-medium ">
                      <span>{t("promotionalCampaign")}</span>
                      <p className="text-sm  text-gray-500">{t("promotionalCampaignDescription")} </p>
                    </Label>
                  </div>

                  <Separator />
                  <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                    <RadioGroupItem value="CORPORATE" id="CORPORATE" className="mt-1" />
                    <Label htmlFor="CORPORATE" className="flex cursor-pointer flex-col gap-2 font-medium ">
                      <span>{t("corporateCDP")}</span>
                      <p className="text-sm text-gray-500">{t("corporateCdpDescription")}</p>
                    </Label>
                  </div>

                  <Separator />
                  <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                    <RadioGroupItem value="ONE_TIME" id="ONE_TIME" className="mt-1" />
                    <Label htmlFor="ONE_TIME" className="flex cursor-pointer flex-col gap-2 font-medium">
                      <span>{t("oneTimePromo")}</span>
                      <p className="text-sm text-gray-500">{t("oneTimePromoDescription")}</p>
                    </Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    </CardWrapper>
  );
}
