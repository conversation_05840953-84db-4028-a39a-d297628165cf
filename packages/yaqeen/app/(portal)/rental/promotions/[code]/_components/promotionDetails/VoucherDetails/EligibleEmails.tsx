import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { XIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { FormField, FormMessage } from "@/components/ui/form";

const FIELD_NAME = "eligibleEmailDomains";

const validateDomain = (domain: string): { isValid: boolean; error: string } => {
  const domainRegex = /^((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  const isValid = domainRegex.test(domain);
  return { isValid, error: "Domain not valid" };
};

const EligibleEmails: React.FC = () => {
  const t = useTranslations("promotions");
  const { getValues, setValue, setError } = useFormContext();

  const defaultValue = getValues(FIELD_NAME) || [];
  const [inputValue, setInputValue] = useState<string>("");
  const [emails, setEmails] = useState<string[]>(defaultValue);

  const validateDomainAddition = (inputValue: string) => {
    const { isValid, error } = validateDomain(inputValue) ?? {};
    if (!isValid) {
      setError(FIELD_NAME, { message: error });
      return;
    }
    setEmails((prev) => [...prev, inputValue]);
    setInputValue("");
    setValue(FIELD_NAME, [...emails, inputValue]);
    setError(FIELD_NAME, { message: "" });
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && inputValue) {
      event.preventDefault();
      event.stopPropagation();
      validateDomainAddition(inputValue);
    }
  };

  const handleBlur = () => {
    if (inputValue) {
      validateDomainAddition(inputValue);
    }
  };

  const handleRemoveEmail = (emailToRemove: string) => {
    const updatedEmails = emails.filter((email) => email !== emailToRemove);
    setEmails(updatedEmails);
    setValue(FIELD_NAME, updatedEmails);
  };

  return (
    <FormField
      name={FIELD_NAME}
      render={({ field }) => {
        return (
          <>
            <Label>{t("eligibleEmailDomains")}</Label>
            <div className="flex flex-wrap rounded border p-2">
              <div className="flex w-fit flex-wrap gap-2">
                {emails.map((email) => (
                  <Badge key={email} variant="outline">
                    {email}
                    <XIcon
                      width={18}
                      height={18}
                      className="ms-2 cursor-pointer"
                      onClick={() => handleRemoveEmail(email)}
                    />
                  </Badge>
                ))}
              </div>
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                onBlur={handleBlur}
                placeholder="Enter email"
                className="h-fit border-none p-0"
                inputClassName="p-0"
              />
              <input type="hidden" {...field} />
            </div>
            <span className="ps-1 text-xs text-gray-500">{t("eligibleEmailDomainsDescription")}</span>
            <FormMessage />
          </>
        );
      }}
    />
  );
};

export default EligibleEmails;
