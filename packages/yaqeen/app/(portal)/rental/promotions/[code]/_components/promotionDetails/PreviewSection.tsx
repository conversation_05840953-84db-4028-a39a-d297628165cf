"use client";

import { useTranslations } from "next-intl";

import { type PromotionApiResSchema } from "@/api/contracts/pricing/promotions/schema";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function PreviewSection({ batchPromos }: { batchPromos: PromotionApiResSchema["batchPromotionCodes"] }) {
  const t = useTranslations("promotions");
  return (
    <Card className="h-fit w-80">
      <CardHeader>
        <CardTitle className="text-lg font-medium">{t("preview")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between bg-gray-200 p-2">
            <span className="text-sm font-medium">{t("code")}</span>
            <span className="text-sm font-medium">{t("remainingCount")}</span>
          </div>
          <div className="flex flex-col gap-2">
            {batchPromos?.map((promo) => (
              <div key={promo.code} className=" odd:bg-white even:bg-gray-100">
                <div className="flex items-center justify-between p-2">
                  <span className="text-sm font-medium">{promo.code}</span>
                  <span className="text-sm font-medium">{promo.validCount}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
