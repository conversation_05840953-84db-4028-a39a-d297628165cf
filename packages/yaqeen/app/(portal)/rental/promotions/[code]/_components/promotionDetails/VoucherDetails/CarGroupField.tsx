"use client";

import React from "react";
import { XIcon } from "lucide-react";
import { useTranslations } from "next-intl";

import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

import { Badge } from "@/components/ui/badge";
import { FormField, FormMessage } from "@/components/ui/form";

import { VehicleGroupsDrawer } from "./VehicleGroupsDrawer";

export default function CarGroupField({ vehicleGroups }: { vehicleGroups: VehicleGroups }) {
  const t = useTranslations("promotions");
  return (
    <FormField
      name="carGroupCodes"
      render={({ field }) => {
        return (
          <div className="flex items-center gap-2">
            <div className="flex w-[90%] flex-wrap gap-2 rounded border p-2">
              {field.value?.details?.length ? (
                <>
                  {field.value?.details?.map((carGroup: VehicleGroups["content"][number]) => (
                    <Badge key={carGroup.id} variant="outline">
                      <span className="text-base">{carGroup.code}</span>
                      <XIcon
                        width={18}
                        height={18}
                        className="ms-2 cursor-pointer text-slate-900 hover:text-slate-600 "
                        onClick={() =>
                          field.onChange({
                            ...field.value,
                            details: field.value?.details?.filter(
                              (item: VehicleGroups["content"][number]) => item.code !== carGroup.code
                            ),
                            codes: field.value?.details
                              ?.filter((item: VehicleGroups["content"][number]) => item.code !== carGroup.code)
                              .map((item: VehicleGroups["content"][number]) => item.code),
                          })
                        }
                      />
                    </Badge>
                  ))}
                  <FormMessage />
                </>
              ) : (
                <span>{t("selectCarGroups")}</span>
              )}
            </div>
            <VehicleGroupsDrawer vehicleGroups={vehicleGroups} />
          </div>
        );
      }}
    />
  );
}
