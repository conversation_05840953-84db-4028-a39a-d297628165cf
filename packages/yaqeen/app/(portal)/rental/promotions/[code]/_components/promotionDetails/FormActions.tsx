"use client";
import React from "react";
import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";

import { Button } from "@/components/ui/button";
import { ProgressBarLink } from "@/components/progress-bar";

export default function FormActions() {
  const t = useTranslations("promotions");

  const {
    formState: { isSubmitting },
  } = useFormContext();

  return (
    <div className="mt-4 flex justify-end gap-4">
      <ProgressBarLink href="/rental/promotions">
        <Button type="button" variant="outline">
          {t("cancel")}
        </Button>
      </ProgressBarLink>
      <Button type="submit" disabled={isSubmitting}>
        {t("save")}
      </Button>
    </div>
  );
}
