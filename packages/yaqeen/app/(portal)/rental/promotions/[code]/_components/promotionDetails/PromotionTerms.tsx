"use client";
import { useTranslations } from "next-intl";

import { FormField, FormItem, FormMessage } from "@/components/ui/form";

import TextAreaField from "@/components/atoms/TextAreaField";
import CardWrapper from "@/components/atoms/CardWrapper";

export const PromotionTerms = () => {
  const t = useTranslations("promotions");

  return (
    <CardWrapper title={t("termsAndConditions")}>
      <div className="flex flex-col gap-4">
        <FormField
          name="termsAndConditions"
          render={({ field }) => (
            <FormItem>
              <TextAreaField placeholder={t("termsAndConditionsPlaceholder")} className="h-40 w-full" {...field} />
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </CardWrapper>
  );
};
