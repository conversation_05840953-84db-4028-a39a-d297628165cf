import React from "react";

import { type BranchesListRes } from "@/api/contracts/branch-contract";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";
import { type PromotionApiResSchema } from "@/api/contracts/pricing/promotions/schema";

import PromotionForm from "./promotionDetails/Form";
import VoucherDetails from "./promotionDetails/VoucherDetails/Voucher-Details";
import VoucherType from "./promotionDetails/Voucher-Type";
import PaymentOptions from "./promotionDetails/PaymentOptions";
// import AdditionalDetails from "./promotionDetails/AdditionalDetails/AdditionalDetails";
import { PromotionTerms } from "./promotionDetails/PromotionTerms";
import FormActions from "./promotionDetails/FormActions";
import PreviewSection from "./promotionDetails/PreviewSection";

interface PromotionDetailsSectionProps {
  branches: BranchesListRes;
  vehicleGroups: VehicleGroups;
  promotion?: PromotionApiResSchema;
}

function PromotionDetails({ branches = [], vehicleGroups }: PromotionDetailsSectionProps) {
  return (
    <div className="flex flex-col gap-8">
      {/* Sections */}
      <VoucherType />
      <VoucherDetails branches={branches} vehicleGroups={vehicleGroups} />
      {/* <AdditionalDetails /> */}
      <PaymentOptions />
      <PromotionTerms />

      {/* Actions */}
      <FormActions />
    </div>
  );
}

export default function PromotionDetailsSection(props: PromotionDetailsSectionProps) {
  return (
    <div className="flex gap-8 p-6">
      <PromotionForm defaultValue={props.promotion} vehicleGroups={props.vehicleGroups}>
        <PromotionDetails {...props} />
      </PromotionForm>
      {props?.promotion?.batchPromotionCodes?.length ? (
        <PreviewSection batchPromos={props.promotion?.batchPromotionCodes} />
      ) : null}
    </div>
  );
}
