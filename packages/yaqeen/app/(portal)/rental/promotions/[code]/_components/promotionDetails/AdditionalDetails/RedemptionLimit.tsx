"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";

import InputField from "@/components/atoms/InputField";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { FormItem, FormField, FormMessage } from "@/components/ui/form";

export default function RedemptionLimit() {
  const t = useTranslations("promotions");
  const [hasRedemptionLimit, setHasRedemptionLimit] = useState(false);

  return (
    <div>
      <div className="flex items-baseline gap-2">
        <Checkbox
          checked={hasRedemptionLimit}
          role="checkbox"
          id="redemptionLimit"
          onCheckedChange={(value: boolean) => setHasRedemptionLimit(value)}
        />
        <Label htmlFor="redemptionLimit" className="flex cursor-pointer flex-col gap-2">
          <span className="text-base text-slate-900">{t("redemptionLimit")}</span>
          <span className="text-slate-600">{t("redemptionLimitDescription")}</span>
        </Label>
      </div>
      {hasRedemptionLimit && (
        <div className="mt-8 flex flex-col justify-start gap-2 ps-5">
          <FormField
            name="validCount"
            render={({ field }) => (
              <FormItem>
                <InputField
                  label={t("numberOfRedemptions")}
                  type="number"
                  placeholder="Enter redemption limit"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )}
    </div>
  );
}
