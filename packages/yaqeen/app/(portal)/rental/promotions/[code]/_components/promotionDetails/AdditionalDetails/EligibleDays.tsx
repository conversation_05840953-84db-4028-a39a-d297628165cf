"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

const daysOfWeek = [
  { name: "Sunday", value: "SUNDAY" },
  { name: "Monday", value: "MONDAY" },
  { name: "Tuesday", value: "TUESDAY" },
  { name: "Wednesday", value: "WEDNESDAY" },
  { name: "Thursday", value: "THURSDAY" },
  { name: "Friday", value: "FRIDAY" },
  { name: "Saturday", value: "SATURDAY" },
];

export default function EligibleDays() {
  const t = useTranslations("promotions");
  const [hasEligible, setHasEligible] = useState(false);
  const [selectedDays, setSelectedDays] = useState<Set<string>>(new Set());

  const { setValue } = useFormContext();

  const toggleDay = (dayValue: string) => {
    const newSelectedDays = new Set(selectedDays);
    if (newSelectedDays.has(dayValue)) {
      newSelectedDays.delete(dayValue);
    } else {
      newSelectedDays.add(dayValue);
    }
    setSelectedDays(newSelectedDays);
    setValue("eligibleDays", Array.from(newSelectedDays)); // Register selected days in the form
  };

  return (
    <div>
      <div className="flex items-baseline gap-2">
        <div className="flex items-baseline gap-2">
          <Checkbox
            checked={hasEligible}
            role="checkbox"
            id="eligibleDays"
            onCheckedChange={(value: boolean) => {
              setHasEligible(value);
              if (!value) {
                setValue("eligibleDays", []);
              }
            }}
          />
          <Label htmlFor="eligibleDays" className="flex cursor-pointer flex-col gap-2">
            <span className="text-base text-slate-900">{t("eligibleDays")}</span>
            <span className="text-slate-600">{t("eligibleDaysDescription")}</span>
          </Label>
        </div>
      </div>
      {hasEligible && (
        <div className="mt-8 flex flex-col justify-start gap-2 ps-5">
          <span className="text-sm text-slate-900">{t("validDate")}</span>
          <div className="flex flex-wrap items-center gap-4">
            {daysOfWeek.map((day) => {
              return (
                <div
                  key={day.value}
                  className={cn(
                    "h-10 w-10 cursor-pointer rounded-full border p-2 text-center",
                    selectedDays.has(day.value) ? "bg-blue-500 text-white" : "bg-slate-50"
                  )}
                  onClick={() => toggleDay(day.value)}
                >
                  {day.name.charAt(0)}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
