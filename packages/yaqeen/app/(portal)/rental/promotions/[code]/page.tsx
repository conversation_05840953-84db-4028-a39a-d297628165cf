import { getTranslations } from "next-intl/server";

import { api } from "@/api";

import PromotionDetailsSection from "./_components/PromotionDetailsSection";
import Header from "@/components/atoms/Header";

const getData = async (code: string) => {
  const [branches, vehicleGroups, promotion] = await Promise.allSettled([
    await api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.fleet.vehicleGroupsContract.getCarGroups({
      query: {
        pageSize: 50,
      },
    }),
    code && code !== "new" ? api.pricing.promotionsContract.getPromotionByCode({ params: { code } }) : null,
  ]);

  if (branches?.status === "rejected") {
    throw new Error(`Error: ${branches.reason}`);
  }

  if (branches?.value.status !== 200) {
    throw new Error(`Error: ${branches.value.body.code}::${branches.value.body.desc}`);
  }

  if (vehicleGroups?.status === "rejected") {
    throw new Error(`Error: ${vehicleGroups.reason}`);
  }

  if (vehicleGroups?.value.status !== 200) {
    throw new Error(`Error: ${vehicleGroups.value.body.code}::${vehicleGroups.value.body.desc}`);
  }

  if (promotion?.status === "rejected") {
    throw new Error(`Error: ${promotion.reason}`);
  }

  return {
    branches: branches.value.body,
    vehicleGroups: vehicleGroups.value.body,
    promotion: promotion?.value?.status === 200 ? promotion?.value?.body : undefined,
  };
};

export const metadata = {
  title: "Promotions",
};

export default async function Page({ params }: { params: Promise<{ code: string }> }) {
  const t = await getTranslations("promotions");
  const paramsA = await params;
  const { code } = paramsA;
  const isEditMode = code !== "new";
  const { branches, vehicleGroups, promotion } = await getData(code);

  return (
    <div className="min-h-screen bg-gray-50/30">
      <Header
        homeLink="/rental/promotions"
        pageName={isEditMode ? t("editPromotion") : t("createNewPromotion")}
        subTitle={isEditMode ? t("editPromotionDescription") : t("createCustomizedCampaigns")}
      />
      <PromotionDetailsSection promotion={promotion} branches={branches?.data} vehicleGroups={vehicleGroups} />
    </div>
  );
}
