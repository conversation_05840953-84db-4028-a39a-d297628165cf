"use server";

import { api } from "@/api";
import { type PromotionApiResSchema, type PromotionsReq } from "@/api/contracts/pricing/promotions/schema";
import { type ErrorResponse } from "@/api/types";

export async function statusAction(
  code: string,
  body: PromotionsReq
): Promise<{ error?: ErrorResponse; data?: PromotionApiResSchema }> {
  try {
    const backendResponse = await api.pricing.promotionsContract.updatePromotion({
      params: {
        code: code,
      },
      body,
    });

    if (backendResponse.status !== 200) {
      return { error: backendResponse.body };
    }

    return { data: backendResponse.body };
  } catch (error) {
    console.error(error);
    let message = "Unknown Error";
    if (error instanceof Error) message = error.message;
    return { error: { desc: message, code: "FE error" } };
  }
}
