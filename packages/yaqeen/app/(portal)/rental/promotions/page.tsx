import { getTranslations } from "next-intl/server";

import { api } from "@/api";

import Header from "@/components/atoms/Header";

import Dashboard, { type Tabs } from "./_components/Dashboard";
import CreatePromotionButton from "./_components/create-promotion-button";
import { paginationQuery } from "@/app/lib/paginationQuery";
import { type PaginationQuery } from "@/api/types";

export const metadata = {
  title: "Promotions",
};

const getData = async (tab: Tabs, params: PaginationQuery & { voucherType?: string; isEnabled?: string }) => {
  const [promotions] = await Promise.allSettled([
    tab === "Single-use"
      ? api.pricing.promotionsContract.getBatchPromotions({ query: params })
      : api.pricing.promotionsContract.getNonBatchPromotions({ query: params }),
  ]);

  if (promotions?.status === "rejected") {
    throw new Error(`Error: ${promotions.reason}`);
  }

  if (promotions?.value.status !== 200) {
    throw new Error(`Error: ${promotions.value.body.code}::${promotions.value.body.desc}`);
  }

  return { promotions: promotions.value.body };
};

export default async function Page({ searchParams }: { searchParams: Promise<Record<string, string | undefined>> }) {
  const t = await getTranslations("promotions");
  const _params = await searchParams;
  const pagination = paginationQuery(_params);
  const tab = _params.tab as Tabs;
  const type = _params.type;
  const status = _params.status;

  const { promotions } = await getData(tab, { ...pagination, voucherType: type, isEnabled: status });

  return (
    <div className="grid grid-rows-[auto_1fr]">
      <Header
        pageName={t("promotions")}
        subTitle={t("createCustomizedCampaigns")}
        actions={<CreatePromotionButton />}
      />
      <Dashboard promotions={promotions} />
    </div>
  );
}
