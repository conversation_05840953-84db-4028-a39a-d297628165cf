import { YAQEEN_SERVICE } from "@/api/constant";
import { auth } from "@/auth";
import type { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const params = new URLSearchParams(req.nextUrl.search);
  const myHeaders = new Headers();
  const session = await auth();
  myHeaders.append("Authorization", `Bearer ${session?.accessToken}`);
  myHeaders.append("Content-Type", "application/pdf");
  const requestOptions: RequestInit = {
    method: "GET",
    headers: myHeaders,
    redirect: "follow",
  };
  const raw = await fetch(
    `${process.env.API_URL}${YAQEEN_SERVICE}/agreements/driver/authorization/${params.get("referenceNumber")}/print/tajeer`,
    requestOptions
  );
  return raw;
}
