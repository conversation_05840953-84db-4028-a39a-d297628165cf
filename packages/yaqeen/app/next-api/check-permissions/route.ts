import { type NextRequest, NextResponse } from "next/server";
import { checkPermission } from "@/lib/server-permission";

/**
 * API endpoint for checking permissions
 * This follows the Layer Separation Principle from Java Spring Boot Architecture
 * by keeping permission checking logic on the server while allowing client components
 * to access permission information
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { permission, permissions, requireAll = false } = body;

    // Validate request
    if (!permission && (!permissions || permissions.length === 0)) {
      return NextResponse.json({ error: "No valid permissions specified" }, { status: 400 });
    }

    // Handle single permission case
    if (permission) {
      const hasPermission = await checkPermission(permission);
      return NextResponse.json({ hasPermission });
    }

    // Handle multiple permissions case
    if (permissions && permissions.length > 0) {
      // Check each permission
      const permissionResults = await Promise.all(permissions.map((perm: string) => checkPermission(perm)));

      // If requireAll is true, all permissions must be true
      // Otherwise, at least one permission must be true
      const hasPermission = requireAll
        ? permissionResults.every((result) => result)
        : permissionResults.some((result) => result);

      return NextResponse.json({ hasPermission });
    }

    // Fallback case (should never reach here due to initial validation)
    return NextResponse.json({ hasPermission: false });
  } catch (error) {
    console.error("Error checking permissions:", error);
    return NextResponse.json({ error: "Failed to check permissions" }, { status: 500 });
  }
}
