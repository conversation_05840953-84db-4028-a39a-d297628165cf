import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(_req: NextRequest) {
  try {
    const response = await api.availability.getPreparationReasons();

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch preparation reasons" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching preparation reasons:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
