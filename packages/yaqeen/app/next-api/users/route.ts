import { api } from "@/api";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get("query") || "";

  try {
    const response = await api.nrm.searchUsers({
      query: {
        query,
      },
    });

    console.log("users response", response);

    if (!response.status) {
      throw new Error("Failed to fetch users");
    }

    return NextResponse.json(response.body);
  } catch {
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 });
  }
}
