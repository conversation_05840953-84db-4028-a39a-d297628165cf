import { type NextRequest, NextResponse } from "next/server";

/**
 * API route to handle file uploads for police reports
 * This proxies requests to avoid CORS issues
 */
export async function POST(request: NextRequest) {
  try {
    // Clone the request to avoid reading the body multiple times
    const clonedRequest = request.clone();
    
    // Extract the FormData from the request
    const formData = await clonedRequest.formData();
    const file = formData.get("file");
    
    // Validate the file
    if (!file || !(file instanceof File)) {
      return NextResponse.json(
        { error: "No file uploaded" },
        { status: 400 }
      );
    }
    
    // Create a new FormData object to send to the external API
    const apiFormData = new FormData();
    apiFormData.append("file", file);
    
    // Send the request to the external API
    const response = await fetch(
      `${process.env.API_URL}/content-service/content/agreement`,
      {
        method: "POST",
        body: apiFormData,
        // Add any required headers for authorization
        headers: {
          // Forward authorization header if needed
          ...(request.headers.get("authorization")
            ? { authorization: request.headers.get("authorization") || "" }
            : {}),
        },
      }
    );
    
    if (!response.ok) {
      // Forward the error status and message
      return NextResponse.json(
        { error: `Upload failed with status: ${response.status}` },
        { status: response.status }
      );
    }
    
    // Return the response from the external API
    const data = await response.json();
    return NextResponse.json(data);
    
  } catch (error) {
    console.error("Error in police report upload proxy:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}