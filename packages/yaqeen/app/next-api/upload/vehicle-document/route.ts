import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const plateNo = formData.get("plateNo") as string;
    const file = formData.get("file") as File;
    const path = request.nextUrl.searchParams.get("path") || "";

    if (!plateNo || !file) {
      return NextResponse.json({ error: "Missing plateNo or file", success: false }, { status: 400 });
    }

    try {
      const response = await api.fleet.vehiclesContract.uploadVehicleDocument({
        params: { plateNo },
        body: { file },
      });

      if (response.status === 200) {
        revalidatePath(path);
        return NextResponse.json({
          revalidated: true,
          url: response.body.url,
          success: true,
        });
      } else {
        return NextResponse.json(
          {
            error: "Failed to upload document",
            success: false,
          },
          { status: response.status }
        );
      }
    } catch (error) {
      console.error("API error:", error);
      return NextResponse.json(
        {
          error: "Failed to upload to API",
          success: false,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error uploading document:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        success: false,
      },
      { status: 500 }
    );
  }
}
