import { api } from "@/api";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ success: false, message: "No file provided" }, { status: 400 });
    }

    const response = await api.content.uploadInspectionImage({
      body: { file },
      requiresAuth: false,
    });

    if (response.status !== 200) {
      return NextResponse.json({ success: false, message: response.body.desc }, { status: response.status });
    }

    return NextResponse.json({ success: true, url: response.body.data });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json({ success: false, message: "Failed to upload file" }, { status: 500 });
  }
}
