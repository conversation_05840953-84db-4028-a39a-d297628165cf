import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const startDateTime = url.searchParams.get("startDateTime") || "";
    const endDateTime = url.searchParams.get("endDateTime") || "";
    const rewardProviders = url.searchParams.get("rewardProviders") || "";
    const status = url.searchParams.get("status") || "";

    const query = {
      startDateTime,
      endDateTime,
      rewardProviders,
      status,
    };

    const response = await api.payment.extractReconciliationReport({
      query,
    });

    if (response.status === 200 && response.body instanceof Blob) {
      // Fallback values in case headers are missing
      const contentType = response.headers.get("content-type") || "application/octet-stream";
      const contentDisposition = response.headers.get("content-disposition") || "attachment; filename=report.xlsx";

      return new Response(Buffer.from(await response.body.arrayBuffer()), {
        status: 200,
        headers: {
          "Content-Type": contentType,
          "Content-Disposition": contentDisposition,
        },
      });
    }

    return NextResponse.json({ error: "Failed to fetch Reconciliation Report" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching Reconciliation Report:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
