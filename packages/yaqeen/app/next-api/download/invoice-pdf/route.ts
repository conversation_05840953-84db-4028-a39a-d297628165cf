import { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const cdnUrl = url.searchParams.get("cdnUrl");
    if (!cdnUrl) {
      return new Response("cdnUrl query parameter is missing", { status: 400 });
    }
    const response = await fetch(cdnUrl);

    if (!response.ok) {
      return new Response("Failed to fetch PDF from CDN", { status: 500 });
    }

    const blob = await response.blob();
    return new Response(blob, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": "inline",
      },
    });
  } catch (error) {
    return new Response("Server error", { status: 500 });
  }
}
