import type { NextRequest } from "next/server";

const mockTajeerResponse = {
  id: 1,
  status: "SUCCESS",
  type: "TAJEER",
  metadata: {
    createStatus: "COMPLETED",
    failureReason: null
  },
  externalAuthorizationNumber: "123456789",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

const mockErrorResponse = {
  code: "ERROR",
  desc: "Something went wrong",
  reqId: "123",
  errorKey: "ERROR_KEY"
};

const shouldFail = false;

export async function GET(request: NextRequest, { params }: { params: Promise<{ rest: string[] }> }) {
  const { rest } = await params;
  const path = rest.join('/');
  const searchParams = new URLSearchParams(request.url.split('?')[1]);
  // const shouldFail = searchParams.get('shouldFail') === 'true';

  // GET /agreements/driver/authorization
  if (path === "agreements/driver/authorization") {
    const referenceNumber = searchParams.get('referenceNumber');
    
    if (!referenceNumber) {
      return Response.json(mockErrorResponse, { status: 400 });
    }

    if (shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }

    return Response.json({
      data: [mockTajeerResponse]
    }, { status: 200 });
  }

  return new Response("Not found", { status: 404 });
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ rest: string[] }> }) {
  const { rest } = await params;
  const path = rest.join('/');
  // const searchParams = new URLSearchParams(request.url.split('?')[1]);
  // const shouldFail = searchParams.get('shouldFail') === 'true';

  // POST /agreements/driver/authorization/:bookingNo/initiate/tajeer
  if (path.match(/agreements\/driver\/authorization\/.*\/initiate\/tajeer/)) {
    if (shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 200 });
  }

  // POST /agreements/driver/authorization/:bookingNo/initiate/tamm
  if (path.match(/agreements\/driver\/authorization\/.*\/initiate\/tamm/)) {
    if (shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 200 });
  }

  // POST /agreements/driver/authorization/:bookingNo/confirm/tajeer
  if (path.match(/agreements\/driver\/authorization\/.*\/confirm\/tajeer/)) {
    const body = await request.json();
    if (!body.otp || shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 200 });
  }

  // POST /agreements/driver/authorization/:bookingNo/confirm/tamm
  if (path.match(/agreements\/driver\/authorization\/.*\/confirm\/tamm/)) {
    const body = await request.json();
    if (!body.otp || shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 200 });
  }

  // POST /agreements
  if (path === "agreements") {
    const body = await request.json();
    if (!body.bookingNo || shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 201 });
  }

  // POST /agreements/driver/authorization/:bookingNo/tajeer/resend-otp
  if (path.match(/agreements\/driver\/authorization\/.*\/tajeer\/resend-otp/)) {
    if (shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 200 });
  }

  if (path.match(/agreements/)) {
    if (shouldFail) {
      return Response.json(mockErrorResponse, { status: 400 });
    }
    return Response.json(mockTajeerResponse, { status: 201 });
  }

  return new Response("Not found", { status: 404 });
}