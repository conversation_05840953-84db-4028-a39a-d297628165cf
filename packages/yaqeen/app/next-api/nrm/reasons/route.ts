import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(_req: NextRequest) {
  try {
    const response = await api.nrm.getNrmReasons();

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch NRM reasons" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching NRM reasons:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
