import { NextResponse } from "next/server";
import { api } from "@/api";

export async function GET() {
  try {
    const response = await api.availability.getOOSReasons();

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch OOS reasons" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching OOS reasons:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
