import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET() {
  try {
    const response = await api.trafficFine.getMunicipalityList();

    if (response.status !== 200) {
      return NextResponse.json({ error: "Failed to fetch municipalities" }, { status: response.status });
    }

    return NextResponse.json(response.body, { status: response.status });
  } catch (error) {
    console.error("Error in getMunicipalityList:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
