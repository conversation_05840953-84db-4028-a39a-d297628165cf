import { NextResponse } from "next/server";
import { api } from "@/api";
export async function GET() {
  try {
    const response = await api.trafficFine.getViolationCodeList();

    if (response.status !== 200) {
      return NextResponse.json({ error: "Failed to fetch violation codes" }, { status: response.status });
    }

    return NextResponse.json(response.body, { status: response.status });
  } catch (error) {
    console.error("Error in getViolationCodeList:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
