import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(request: NextRequest) {
  const trafficFineId = request.nextUrl.searchParams.get("trafficFineId");

  if (!trafficFineId) {
    return NextResponse.json({ error: "Missing trafficFineId parameter" }, { status: 400 });
  }

  try {
    const response = await api.trafficFine.checkSecurityDeposit({
      params: { trafficFineId },
    });

    return NextResponse.json(response.body, { status: response.status });
  } catch (error) {
    console.error("Error in checkSecurityDeposit:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
