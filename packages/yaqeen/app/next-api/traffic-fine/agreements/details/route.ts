import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(request: NextRequest) {
  const agreementNo = request.nextUrl.searchParams.get("agreementNo");

  if (!agreementNo) {
    return NextResponse.json({ error: "Missing agreementNo parameter" }, { status: 400 });
  }

  try {
    const response = await api.trafficFine.getAgreementDetails({
      params: { agreementNo },
    });

    return NextResponse.json(response.body, { status: response.status });
  } catch (error) {
    console.error("Error in getAgreementDetails:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
