import { NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body.plateNo || !body.url || !body.typeId || body.pageNo === undefined) {
      return NextResponse.json({ error: "Missing required fields: plateNo, url, typeId, pageNo" }, { status: 400 });
    }

    if (!body.issuingDate || !body.expiryDate) {
      return NextResponse.json({ error: "Missing required fields: issuingDate, expiryDate" }, { status: 400 });
    }

    const response = await api.fleet.vehiclesContract.createVehicleDocument({
      body: {
        plateNo: decodeURIComponent(body.plateNo),
        url: body.url,
        typeId: body.typeId,
        pageNo: body.pageNo,
        issuingDate: body.issuingDate,
        expiryDate: body.expiryDate,
        internal: body.internal,
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    } else {
      return NextResponse.json({ error: "Failed to create vehicle document" }, { status: response.status });
    }
  } catch (error) {
    console.error("Error creating vehicle document:", error);
    return NextResponse.json({ error: "An unexpected error occurred" }, { status: 500 });
  }
}
