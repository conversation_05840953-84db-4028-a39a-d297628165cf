import { api } from "@/api";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const response = await api.fuel.getFuelLevels();
    console.log("response", response);

    if (response.status !== 200) {
      return NextResponse.json({ error: "Failed to fetch fuel levels" }, { status: response.status });
    }

    return NextResponse.json(response.body);
  } catch (error) {
    console.error("Error fetching fuel levels:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
