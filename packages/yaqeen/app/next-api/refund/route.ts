import { NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const bookingNo = searchParams.get("bookingNo");

    if (!bookingNo) {
      return NextResponse.json({ error: "Booking number is required" }, { status: 400 });
    }

    const response = await api.refund.sendPaymentLink({
      query: { bookingNo },
    });

    return NextResponse.json(response.body);
  } catch (error) {
    console.error("Error sending payment link:", error);
    return NextResponse.json({ error: "Failed to send payment link" }, { status: 500 });
  }
}
