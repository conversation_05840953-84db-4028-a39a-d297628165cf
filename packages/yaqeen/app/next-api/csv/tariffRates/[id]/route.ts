import { type NextRequest } from "next/server";
import <PERSON> from "papaparse";

import { api } from "@/api";
import { formatTariffData } from "@/app/(portal)/rental/tariff/_lib/formatTariffRates";

const getData = async (id: string) => {
  const [cardDetails] = await Promise.allSettled([
    api.tariff.getTariffCardById({
      params: {
        id: Number(id),
      },
    }),
  ]);

  if (cardDetails?.status === "rejected") {
    throw new Error(`Error: ${cardDetails.reason}`);
  }

  if (cardDetails?.value.status !== 200) {
    throw new Error(`Error: ${cardDetails.value.body.code}::${cardDetails.value.body.desc}`);
  }

  return { data: cardDetails.value.body };
};

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    if (!id) throw new Error("id name required");

    const { data } = await getData(id);

    const rateName = data.tariffRateName;

    const columns = formatTariffData(data.tariffRates);

    const csv = Papa.unparse(columns);

    return new Response(csv, {
      status: 200,
      headers: {
        "Content-Disposition": `attachment; filename="${rateName}.csv"`,
        "Content-Type": "text/csv",
      },
    });
  } catch (e) {
    if (e instanceof Error) {
      console.error(e);
      return new Response(e.message, {
        status: 400,
      });
    }
  }
}
