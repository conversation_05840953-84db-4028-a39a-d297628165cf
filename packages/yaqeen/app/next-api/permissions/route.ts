import { auth } from "@/auth";
import { api } from "@/api";
import { type NextRequest, NextResponse } from "next/server";

/**
 * Proxy API endpoint for fetching user permissions
 * This follows the Java Spring Boot architecture standards with proper layer separation
 * by isolating the permission checking logic in a dedicated endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const realm = searchParams.get("realm") || "LUMI";
    const clientId = searchParams.get("clientId") || "yaqeen-auth-client";

    // Call the actual permissions API
    const permissionsResponse = await api.auth.permissions({
      query: {
        realm,
        clientId,
      },
      requiresAuth: true,
    });

    // Return the response
    return NextResponse.json(permissionsResponse.body, {
      status: permissionsResponse.status,
    });
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return NextResponse.json({ error: "Failed to fetch permissions" }, { status: 500 });
  }
}
