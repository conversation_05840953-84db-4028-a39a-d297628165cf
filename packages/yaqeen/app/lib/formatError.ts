import { type SafeParseError } from "zod";

/**
 * Format Zod SafeParseError to a string
 * @example
 * input: {
 *   a: ["should be string"],
 *   b: ["should be string"],
 *   c: ["should be string"],
 * }
 * output: "a is should be string, b is should be string, c is should be string"
 * @returns {string}
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const formatError = (error: SafeParseError<any>["error"]) => {
  return Object.entries(error.flatten().fieldErrors).reduce((acc, el) => {
    if (!el?.[1]?.length) return acc;
    if (acc) acc += ", ";

    return acc + `${el[0]} is ${el[1]?.[0]}`;
  }, "");
};
