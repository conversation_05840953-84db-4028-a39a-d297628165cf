export type PaginationQueryProps = {
  sortBy?: string;
  page?: number;
  pageNumber?: number;
  size?: number;
  pageSize?: number;
  orderBy?: "asc" | "desc";
  query?: string;
  text?: string;
};

export const paginationQuery = (searchParams: PaginationQueryProps) => {
  const sortByVal = searchParams.sortBy ?? "updatedBy";
  const orderByVal = searchParams.orderBy ?? "desc";
  const page = searchParams.page ?? searchParams.pageNumber ?? 0;
  const size = searchParams.size ?? searchParams.pageSize ?? 10;
  const query = searchParams.query ?? "";
  const text = searchParams.text ?? "";

  return {
    query,
    text,
    page,
    size,
    sort: sortByVal,
    order: orderByVal,
  };
};
