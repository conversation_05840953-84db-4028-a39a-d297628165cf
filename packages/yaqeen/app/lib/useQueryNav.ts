"use client";
import { startTransition } from "react";

import { useQueryState, parseAsString, type UseQueryStateOptions } from "nuqs";

import { useProgressBar } from "@/components/progress-bar";

export const useQueryNav = <T>(name: string, options?: UseQueryStateOptions<T> & { defaultValue?: string }) => {
  const progress = useProgressBar();

  return useQueryState(
    name,
    parseAsString.withOptions({
      startTransition: (callback) => {
        progress.start();
        startTransition(() => {
          void callback();
          progress.done();
        });
      },
      history: "replace",
      shallow: false,
      ...options,
    })
  );
};
