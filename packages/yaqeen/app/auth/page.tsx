"use client";

import { initiateAuth } from "@/lib/actions";
import { ContinueButton } from "@/components/ContinueButton";
import Logo from "@/public/static/logo.svg";
import Image from "next/image";
import { Suspense } from "react";

export default function SignIn() {
  // Create a function to handle form submission with the current URL
  const handleSubmit = async () => {
    const callbackUrl = `${window.location.origin}/auth/callback`;
    return initiateAuth(callbackUrl);
  };

  return (
    <Suspense>
      <div className="mx-auto flex h-screen max-w-sm flex-col items-center">
        <div className="my-auto flex h-1/2 flex-col justify-between space-y-4 rounded-lg border p-8 text-center shadow-md">
          <Image className="mx-auto" src={Logo as string} priority alt="Lumi Logo" width={122} height={52} />
          <h2>Welcome! Please sign in using SSO</h2>
          <form action={handleSubmit} className="flex flex-col">
            <ContinueButton className="mb-5 w-full bg-lumi-500 hover:bg-lumi-400">Sign in</ContinueButton>
          </form>
        </div>
        <p className="mb-8 mt-auto text-sm text-slate-400">Copyright © Lumi {new Date().getFullYear()}</p>
      </div>
    </Suspense>
  );
}
