import { signIn } from "@/auth";
import { api } from "@/api";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = { code: searchParams.get("code") ?? "", state: searchParams.get("state") ?? "" };
  const ssoResponse = await api.auth.sso({
    query,
    requiresAuth: false,
  });

  await signIn("credentials", ssoResponse.body);
}
