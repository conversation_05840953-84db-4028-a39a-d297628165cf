<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Simplified TAX Invoice - فاتورة ضريبية مبسطة</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        font-size: 10pt;
        line-height: 1.4;
        margin: 0;
        padding: 0;
        background-color: #f8f8f8;
        color: #333;
      }
      .invoice-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background-color: #fff;
        border: 1px solid #ddd;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 15px;
      }
      th,
      td {
        border: 1px solid #ccc;
        padding: 6px 8px;
        vertical-align: top;
        text-align: left;
        font-size: 9pt;
      }
      th {
        background-color: #f2f2f2;
        font-weight: bold;
      }
      .header-table td {
        border: none;
        vertical-align: middle;
        padding: 5px;
      }
      .header-table .company-info-left {
        text-align: left;
      }
      .header-table .company-info-right {
        text-align: right;
        direction: rtl; /* Right-to-left for Arabic */
      }
      .header-table .logo {
        text-align: center;
      }
      .header-table .qr-code {
        text-align: center;
        vertical-align: top;
      }
      .header-table img {
        max-height: 60px;
        vertical-align: middle;
      }
      .header-table .qr-code img {
        max-width: 80px;
        height: auto;
      }
      .section-title {
        background-color: #e0e0e0;
        font-weight: bold;
        padding: 8px;
        margin-top: 15px;
        margin-bottom: 0;
        border: 1px solid #ccc;
        border-bottom: none;
        font-size: 10pt;
        overflow: hidden; /* Contain floats */
      }
      .section-title-ar {
        float: right;
        direction: rtl;
      }
      /* Info tables (3 columns: Eng Label | Value | Ar Label) */
      .info-table td:first-child {
        width: 180px; /* Fixed width for English labels */
        font-weight: 500;
      }
      .info-table td:nth-child(2) {
        text-align: left; /* Keep English values left */
        direction: ltr;
        font-weight: normal;
      }
      .info-table td:last-child {
        text-align: right; /* Align Arabic labels to the right */
        direction: rtl;
        font-weight: 500;
      }
      .info-table.full-width td:first-child,
      .info-table.full-width td:last-child {
        width: 180px; /* Keep consistent width even if table is full */
      }
      .info-table.full-width td:nth-child(2) {
        width: auto; /* Allow value column to expand */
      }
      /* Special case for address row spanning columns */
      .info-table .address-row td:nth-child(2) {
        text-align: right;
        direction: rtl;
      }

      /* Line items table */
      .line-items-table thead th {
        background-color: #4caf50; /* Green background */
        color: white;
        font-weight: bold;
        text-align: center;
        font-size: 8pt;
        padding: 4px;
        vertical-align: middle;
      }
      .line-items-table tbody td,
      .line-items-table tfoot td {
        text-align: center;
        padding: 5px;
        vertical-align: middle;
      }
      .line-items-table .text-left {
        text-align: left;
      }
      .line-items-table .text-right {
        text-align: right;
      }
      .line-items-table .total-row td {
        font-weight: bold;
        border-top: 2px solid #aaa;
      }
      .line-items-table .total-row .label-cell {
        text-align: right;
        direction: rtl;
        padding-right: 10px; /* Add some padding */
      }

      /* Summary table (2 columns: Labels | Value) */
      .summary-table {
        width: 100%; /* Adjusted width */
        float: right;
        margin-top: 10px;
        clear: both; /* Ensure it's below floated elements */
      }
      .summary-table td {
        border: 1px solid #ccc;
        padding: 5px 8px;
        font-size: 9pt;
        vertical-align: middle;
      }
      .summary-table td:first-child {
        /* Label Cell */
        text-align: left; /* English label aligns left */
        font-weight: 500;
        line-height: 1.6; /* Ensure space for floated element */
      }
      .summary-table td:first-child .arabic-label {
        /* Arabic span within label cell */
        float: right;
        direction: rtl;
        font-weight: 500; /* Match English weight */
      }
      /* Clearfix for the float inside the first td */
      .summary-table td:first-child::after {
        content: "";
        clear: both;
        display: table;
      }

      .total-words {
        clear: both;
        text-align: left;
        font-weight: bold;
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px dashed #ccc;
        font-size: 9pt;
        overflow: hidden; /* Contain floats */
      }
      .total-words .arabic {
        float: right;
        direction: rtl;
      }

      /* Footer table (like info tables) */
      .footer-table {
        margin-top: 20px;
        border-top: 1px solid #ccc;
        padding-top: 10px;
      }
      .footer-table td {
        border: 1px solid #ccc;
      }
      .footer-table td:first-child {
        font-weight: 500; /* Consistent label weight */
        width: 180px;
      }
      .footer-table td:nth-child(2) {
        text-align: left;
        font-weight: normal;
      }
      .footer-table td:last-child {
        text-align: right;
        direction: rtl;
        font-weight: 500; /* Consistent label weight */
      }

      /* Helper classes */
      .bold {
        font-weight: bold;
      }
      .arabic {
        direction: rtl;
        text-align: right;
      }
      .text-right {
        text-align: right;
      }

      /* Specific styles based on image */
      .invoice-title {
        text-align: center;
        font-size: 14pt;
        font-weight: bold;
        margin-bottom: 15px;
      }
      .invoice-title span {
        margin: 0 10px;
      }
    </style>
  </head>
  <body>
    <div class="invoice-container">
      <div class="invoice-title"><span>Simplified TAX Invoice</span> - <span>فاتورة ضريبية مبسطة</span></div>

      <table class="header-table">
        <tr>
          <td class="company-info-left">
            <b>Lumi Rental Company</b><br />
            (Listed joint stock Company)<br />
            6475 - Hail St - Al Taawun Dist<br />
            Riyadh, 12476 - 3647<br />
            Phone: *********<br />
            Email: <EMAIL><br />
            Lumi CR No: 1010228226<br />
            Company VAT No: 300077385900003
          </td>
          <td class="logo">
            <!-- Replace with actual logo URL -->
            <img src="https://via.placeholder.com/150x60.png?text=Lumi+Logo" alt="Lumi Logo" />
          </td>
          <td class="qr-code">
            <!-- Replace with actual QR code URL -->
            <img src="https://via.placeholder.com/80x80.png?text=QR+Code" alt="QR Code" />
          </td>
          <td class="company-info-right arabic">
            <b>شركة لومي للتأجير</b><br />
            (شركة مساهمة مدرجة)<br />
            مبنى 6475 - شارع حائل - حي التعاون<br />
            الرياض، 12476 - 3647<br />
            رقم تليفون: *********<br />
            البريد الالكتروني: <EMAIL><br />
            رقم السجل التجاري: 1010228226<br />
            الرقم الضريبي: 300077385900003
          </td>
        </tr>
      </table>

      <div class="section-title">Customer Information <span class="section-title-ar">معلومات العميل</span></div>
      <table class="info-table">
        <tr>
          <td>Driver Name:</td>
          <td>الأحوص أحمد</td>
          <td class="arabic">اسم السائق:</td>
        </tr>
        <tr>
          <td>Driver ID:</td>
          <td>0019018463</td>
          <td class="arabic">هوية السائق:</td>
        </tr>
        <tr>
          <td>Driver Mobile Number:</td>
          <td>342386789</td>
          <td class="arabic">رقم جوال السائق:</td>
        </tr>
        <tr class="address-row">
          <td>Driver Address:</td>
          <td colspan="2">
            مبنى رقم - 3233 / شارع - شارع الاختبار / حي - منطقة الاختبار / مدينة - الرياض / الرمز البريدي - 4321 - 45632
            <span class="arabic"> : العنوان</span>
          </td>
        </tr>
      </table>

      <table class="info-table">
        <tr>
          <td>Invoice Date:</td>
          <td>2024-08-28 11:02:00</td>
          <td class="arabic">تاريخ الفاتورة:</td>
        </tr>
        <tr>
          <td>Invoice No.:</td>
          <td>0019018463</td>
          <td class="arabic">رقم الفاتورة:</td>
        </tr>
        <tr>
          <td>Invoice Type:</td>
          <td>Driver Invoice</td>
          <td class="arabic">نوع الفاتورة:</td>
        </tr>
      </table>

      <div class="section-title">Agreement Details <span class="section-title-ar">تفاصيل العقد</span></div>
      <table class="info-table full-width">
        <tr>
          <td>Agreement Number:</td>
          <td>16105656</td>
          <td class="arabic">رقم العقد:</td>
        </tr>
        <tr>
          <td>Booking Number:</td>
          <td>128378912</td>
          <td class="arabic">رقم الحجز:</td>
        </tr>
        <tr>
          <td>Branch:</td>
          <td>Jubail City</td>
          <td class="arabic">فرع:</td>
        </tr>
        <tr>
          <td>Vehicle Number:</td>
          <td>1685 NAR - ر ا ن 1685</td>
          <td class="arabic">رقم السيارة:</td>
        </tr>
        <tr>
          <td>Make & Model:</td>
          <td>TOYOTA-Yris 1.3 - تويوتا</td>
          <td class="arabic">نوع السيارة:</td>
        </tr>
        <tr>
          <td>Model Year:</td>
          <td>2023</td>
          <td class="arabic">موديل السيارة:</td>
        </tr>
        <tr>
          <td>Vehicle Group:</td>
          <td>B</td>
          <td class="arabic">فئة السيارة:</td>
        </tr>
        <tr>
          <td>From Date & Time:</td>
          <td>09/09/24 20:30</td>
          <td class="arabic">تاريخ الخروج:</td>
        </tr>
        <tr>
          <td>To Date & Time:</td>
          <td>10/09/24 20:48</td>
          <td class="arabic">تاريخ الدخول:</td>
        </tr>
        <tr>
          <td>Check Out KM:</td>
          <td>41960</td>
          <td class="arabic">كيلو متر الخروج:</td>
        </tr>
        <tr>
          <td>Check In KM:</td>
          <td>42193</td>
          <td class="arabic">كيلو متر الدخول:</td>
        </tr>
        <tr>
          <td>KM free:</td>
          <td>300</td>
          <td class="arabic">كيلو متر مجاني:</td>
        </tr>
      </table>

      <div class="section-title">Description <span class="section-title-ar">بيان الفاتورة</span></div>
      <table class="line-items-table">
        <thead>
          <tr>
            <th>إجمالي المبلغ<br />Gross Amount</th>
            <th>قيمة الضريبة<br />VAT Amount</th>
            <th>نسبة الضريبة<br />VAT Rate</th>
            <th>قيمة الإيجار<br />Net Amount</th>

            <th colspan="3" style="padding: 0">
              <table style="border: 0">
                <tr>
                  <th colspan="3">الوصف Description</th>
                </tr>
                <tr>
                  <th>السعر<br />Cost / quantity</th>
                  <th>الكمية<br />Quantity</th>
                  <th>الإيجار<br />Rental</th>
                </tr>
              </table>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="text-right">207.00</td>
            <td class="text-right">27.00</td>
            <td class="text-right">15.00</td>

            <td class="text-right">180.00</td>
            <td class="text-right">180.00</td>
            <td>1 Days 1 Hours</td>
            <td class="text-left">No. of days</td>
          </tr>
          <tr>
            <td class="text-right">-51.75</td>
            <td class="text-right">-6.75</td>
            <td class="text-right">15.00</td>
            <td class="text-right">-45.00</td>
            <td class="text-right">25%</td>
            <td>1 Days 1 Hours</td>
            <td class="text-left">(BMW 5) Discount Code BMW 5</td>
          </tr>
        </tbody>
        <tfoot>
          <tr class="total-row">
            <td class="bold text-right">155.25</td>
            <td class="bold text-right">20.25</td>
            <td></td>
            <!-- Span 2 columns -->
            <td class="bold text-right">135.00</td>
            <td></td>
            <td></td>
            <td class="label-cell">إجمالي التكلفة Total</td>
          </tr>
        </tfoot>
      </table>

      <!-- Corrected Summary Table -->
      <table class="summary-table">
        <tr>
          <td>Total Invoice Before VAT</td>
          <td class="text-right">135.00</td>
          <td class="arabic">إجمالي الفاتورة قبل الضريبة:</td>
        </tr>
        <tr>
          <td>Total VAT Amount:</td>
          <td class="text-right">20.25</td>
          <td class="arabic">إجمالي قيمة الضريبة:</td>
        </tr>
        <tr>
          <td>Total Invoice After VAT</td>
          <td class="text-right">155.25</td>
          <td class="arabic">إجمالي الفاتورة بعد قيمة الضريبة المضافة:</td>
        </tr>
        <tr>
          <td>Total Amount Paid</td>
          <td class="text-right">931.50</td>
          <td class="arabic">إجمالي المبلغ المدفوع:</td>
        </tr>
        <tr>
          <td>Total Balance Due</td>
          <td class="text-right">-776.25</td>
          <td class="arabic">إجمالي المبلغ المستحق بعد الضريبة:</td>
        </tr>
      </table>

      <div class="total-words">
        <span>ONE HUNDRED FIFTY FIVE SAUDI RIYAL AND TWENTY FIVE HALALA ONLY</span>
        <span class="arabic">مائة وخمسة و خمسون ريالاً سعودياً وخمسون وعشرون هللة لا غير</span>
      </div>

      <table class="footer-table">
        <tr>
          <td>Employee Name</td>
          <td>3986712 Ghuman Khurram</td>
          <td class="arabic">اسم الموظف</td>
        </tr>
      </table>
    </div>
  </body>
</html>
