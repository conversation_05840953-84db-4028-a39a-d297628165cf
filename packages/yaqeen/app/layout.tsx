import { ProgressBar } from "@/components/progress-bar";
import { Toaster } from "@/components/ui/toaster";
import { TanstackQueryProvider } from "@/components/providers/query-client-provider";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import { Inter } from "next/font/google";
import Script from "next/script";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { type ReactNode } from "react";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  robots: {
    index: false,
    follow: false,
  },
};

export default async function RootLayout({ children }: { children: ReactNode }) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html dir={locale === "ar" ? "rtl" : "ltr"} lang={locale}>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages} locale={locale}>
          <TanstackQueryProvider>
            <ProgressBar className="fixed top-0 z-50 h-1.5 bg-lumi-500 ">
              <NuqsAdapter>
                <main className="mb-5">{children}</main>
                <Toaster />
              </NuqsAdapter>
            </ProgressBar>
          </TanstackQueryProvider>
        </NextIntlClientProvider>
      </body>
      <Script id="ms_clarity" type="text/javascript">
        {`(function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "opns2o6fmn");`}
      </Script>
    </html>
  );
}
