import { z } from "zod";

export const branchResponseBody = z.object({
  id: z.number(),
  code: z.string(),
  name: z.object({
    en: z.string(),
    ar: z.string(),
  }),
  email: z.string(),
  phoneNumber: z.string(),
  latitude: z.number(),
  longitude: z.number(),
  city: z.object({
    id: z.number(),
    code: z.string(),
    name: z.object({
      en: z.string(),
      ar: z.string(),
    }),
    position: z.number(),
    latitude: z.number(),
    longitude: z.number(),
    region: z.object({
      id: z.number(),
      code: z.string(),
      name: z.object({
        en: z.string(),
        ar: z.string(),
      }),
    }),
  }),
  type: z.string(),
  timings: z.array(
    z.object({
      day: z.string(),
      open: z.boolean(),
      start: z.string(),
      end: z.string(),
      timeRanges: z.array(
        z.object({
          start: z.string(),
          end: z.string(),
        }),
      ),
    }),
  ),
  timingSummary: z.array(
    z.object({
      startDay: z.string(),
      endDay: z.string(),
      start: z.string(),
      end: z.string(),
    }),
  ),
  timezone: z.string(),
  directions: z.string(),
  leadTimeMinutes: z.number(),
  tags: z.array(z.string()),
  carDelivery: z.boolean(),
});
