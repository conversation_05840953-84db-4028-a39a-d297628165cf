import { z } from "zod";

export type CarType = "MODEL" | "MAKE";

export interface APIError {
  code: string;
  desc: string;
  reqId?: string;
}

export const paginationQuery = z.object({
  query: z.string().optional(),
  text: z.string().optional(),
  page: z.number().optional(),
  size: z.number().optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
});

export const errorResponse = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

export const serverFailureError = z.object({
  code: z.string(),
  desc: z.string(),
});

export type PaginationQuery = z.infer<typeof paginationQuery>;
export type ErrorResponse = z.infer<typeof errorResponse | typeof serverFailureError>;
