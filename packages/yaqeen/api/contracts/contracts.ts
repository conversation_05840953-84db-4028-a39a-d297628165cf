import { initContract } from "@ts-rest/core";
import { authContract } from "./auth-contract";
import { bookingContract } from "./booking/booking-contract";
import { bookingDetailsContract } from "./booking/booking-details-contract";
import { suggestedVehiclesContract } from "./booking/suggested-vehicles-contract";
import { branchContract } from "./branch-contract";
import { UnauthorizedError } from "./schema";
import { driverDetailsContract } from "./booking/driver-details-contract";
import { tajeerContract } from "./tajeer-contract";
import * as fleetContract from "./fleet";
import { contentContract } from "./content-contract";

import { paymentContract } from "./payment-contract";
import { tariffContract } from "./tariff-contract";
import { customerContract } from "./customer-contract";
import { cashRegisterContract } from "./rental/cash-register-contract";
import * as pricingContract from "./pricing";
import { availabilityContract } from "./rental/availability-contract";
import { nrmContract } from "./rental/nrm-contract";
import { fuelContract } from "./fleet";
import { invoiceStatusContract } from "./booking/invoice-contract";
import { refundContract } from "./rental/refund-contract";
import { trafficFineContract } from "./rental/traffic-fine-contract";
import { invoicesContract } from "./invoices-contract";
import { keycloakContract } from "./keycloak-contract";
import { permissionContract } from "./permission-contract";
import { userContract } from "./user/user-contract";

const c = initContract();

export const contracts = c.router(
  {
    auth: authContract,
    branch: branchContract,
    booking: bookingContract,
    bookingDetails: bookingDetailsContract,
    driverDetails: driverDetailsContract,
    suggestedVehicles: suggestedVehiclesContract,
    tajeer: tajeerContract,
    payment: paymentContract,
    tariff: tariffContract,
    fleet: fleetContract,
    customer: customerContract,
    pricing: pricingContract,
    cashRegister: cashRegisterContract,
    availability: availabilityContract,
    nrm: nrmContract,
    fuel: fuelContract,
    invoiceStatus: invoiceStatusContract,
    refund: refundContract,
    invoices: invoicesContract,
    content: contentContract,
    trafficFine: trafficFineContract,
    keycloak: keycloakContract,
    permissions: permissionContract,
    user: userContract,
  },
  {
    commonResponses: {
      401: UnauthorizedError,
    },
  }
);
