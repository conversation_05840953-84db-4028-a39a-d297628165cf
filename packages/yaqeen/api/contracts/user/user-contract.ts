import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema } from "../common";

const c = initContract();

// Request schema for updating user details
const UpdateUserDetailsRequestSchema = z.object({
  firstName: z.string(),
  lastName: z.string().optional(),
  email: z.string(),
  countryCode: z.string(),
  phoneNumber: z.string(),
  externalId: z.string(),
  designation: z.string().optional(),
});

// Request schema for updating user locations
const UpdateUserLocationsRequestSchema = z.object({
  locationIds: z.array(z.number()),
  isEnable: z.boolean(),
  email: z.string(),
  roles: z.array(
    z.object({
      id: z.string(),
      name: z.string()
    })
  ).optional()
});

// Response schema for user details
const UserDetailsResponseSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string().optional(),
  email: z.string(),
  countryCode: z.string().optional(),
  phoneNumber: z.string().optional(),
  externalId: z.string().optional(),
  designation: z.string().optional(),
  successFactorId: z.string().optional(),
});

// Response schema for user details with roles and locations
const UserDetailsWithRolesResponseSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string().optional(),
  email: z.string(),
  locationIds: z.array(z.number()),
  isEnable: z.boolean(),
  roles: z.array(
    z.object({
      id: z.string(),
      name: z.string()
    })
  ),
  platform: z.string().optional(),
  externalId: z.string().optional(),
  successFactorId: z.string().optional(),
  groups: z.array(z.unknown()).optional(),
});

export const userContract = c.router({
  updateUserDetails: {
    method: "PUT",
    path: "/core-user-service/v1/users/:id",
    pathParams: z.object({
      id: z.string(),
    }),
    body: UpdateUserDetailsRequestSchema,
    responses: {
      200: UserDetailsResponseSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ErrorSchema,
    },
    summary: "Update user details by ID",
  },
  getUserDetails: {
    method: "GET",
    path: "/core-user-service/v1/users/:id",
    pathParams: z.object({
      id: z.string(),
    }),
    responses: {
      200: UserDetailsResponseSchema,
      404: ErrorSchema,
      500: ErrorSchema,
    },
    summary: "Get user details by ID",
  },
  getUserById: {
    method: "GET",
    path: "/core-user-service/v2/users/:userId",
    pathParams: z.object({
      userId: z.string(),
    }),
    responses: {
      200: UserDetailsWithRolesResponseSchema,
      404: ErrorSchema,
      500: ErrorSchema,
    },
    summary: "Get user details with roles and locations by ID",
  },
  updateUserLocations: {
    method: "PUT",
    path: "/core-user-service/v2/users/:userId",
    pathParams: z.object({
      userId: z.string(),
    }),
    body: UpdateUserLocationsRequestSchema,
    responses: {
      200: z.object({
        success: z.boolean(),
        message: z.string().optional(),
      }),
      400: ErrorSchema,
      500: ErrorSchema,
    },
    summary: "Update user locations and roles",
  },
  searchUsers: {
    method: "GET",
    path: "/core-user-service/v2/users",
    query: z.object({
      email: z.string().optional(),
      successFactorId: z.string().optional(),
    }).partial(),
    responses: {
      200: z.object({
        total: z.number(),
        data: z.array(UserDetailsWithRolesResponseSchema),
      }),
      400: ErrorSchema,
      500: ErrorSchema,
    },
    summary: "Search users by email or successFactorId",
  },
});

// Export types
export type UserContract = typeof userContract;
export type UpdateUserDetailsRequest = z.infer<typeof UpdateUserDetailsRequestSchema>;
export type UpdateUserLocationsRequest = z.infer<typeof UpdateUserLocationsRequestSchema>;
export type UserDetailsResponse = z.infer<typeof UserDetailsResponseSchema>;
export type UserDetailsWithRolesResponse = z.infer<typeof UserDetailsWithRolesResponseSchema>;
