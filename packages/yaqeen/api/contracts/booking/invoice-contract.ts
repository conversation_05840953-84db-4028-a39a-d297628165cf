import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "@/api/constant";
import { ErrorSchema, ServerFailureError } from "../common";

const c = initContract();

// InvoiceStatus Schema
export const InvoiceStatusSchema = z.object({
  id: z.string(),
  name: z.string(),
  idType: z.enum(["License number", "Passport"]),
  status: z.enum(["active", "warning", "blacklisted"]),
  // Add more fields as needed based on the actual API response
  email: z.string().optional(),
  mobile: z.string().optional(),
  nationality: z.string().optional(),
});

export const InvoiceStatusResponseSchema = z.object({
  externalId: z.string(), // The external ID of the invoice
  invoiceStatus: z.enum(["SUCCESS", "ERROR", "PENDING"]), // Enum for invoice status
  errorMessage: z.string().nullable(), // Nullable error message
  downloadUrl: z.string().url(), // URL for downloading the invoice
  htmlContent: z
    .object({
      html: z.string(),
      fileName: z.string(),
    })
    .optional(),
});

// Define the InvoiceSearchResponseSchema
export const InvoiceSearchResponseSchema = z.object({
  id: z.number(),
  externalId: z.string(),
  agreementNumber: z.number(),
  bookingNumber: z.string(),
  invoiceNumber: z.string(),
  invoiceConfigType: z.string(),
  issueDate: z.number(),
  invoiceType: z.string(),
  invoiceCategory: z.string(),
  totalInvoiceBeforeVat: z.number(),
  totalVatAmount: z.number(),
  totalInvoiceAfterVat: z.number(),
  totalAmountPaid: z.number(),
  invoiceStatus: z.enum(["SUCCESS", "ERROR", "PENDING"]),
});

// Define the MetadataSchema
const MetadataSchema = z.object({
  driver_address: z.string(),
  from_date_time: z.string(),
  driver_id: z.string(),
  summary_vat_amount: z.string(),
  summary_balance: z.string(),
  summary_gross_amount: z.string(),
  vehicle_group: z.string(),
  driver_mobile: z.string(),
  discount: z.string(),
  km_free: z.string(),
  branch: z.string(),
  created_by: z.string().nullable(),
  summary_amount_paid: z.string(),
  to_date_time: z.string(),
  driver_name: z.string(),
  vehicle_make_model: z.string(),
  checkin_km: z.string(),
  agreement_number: z.string(),
  vehicle_number: z.string(),
  vehicle_model_year: z.string(),
  summary_net_amount: z.string(),
  invoice_type: z.string(),
  checkout_km: z.string(),
});

const LineItemSchema = z.object({
  description: z.string(),
  summary1: z.string().nullable(),
  summary2: z.string().nullable(),
  quantity: z.number(),
  unitPrice: z.string(),
  totalWithoutVat: z.string(),
  vatRate: z.string(),
  vatAmount: z.string(),
  totalWithVat: z.string(),
});

// Define the CreateInvoiceResponseSchema
export const CreateInvoiceResponseSchema = z.object({
  externalId: z.string(),
  sequenceNumber: z.string(),
  metadata: MetadataSchema,
  vatRegistrationNumber: z.string().nullable(),
  companyName: z.string().nullable(),
  companyNameAr: z.string().nullable(),
  companyAddress: z.string().nullable(),
  companyAddressAr: z.string().nullable(),
  contactEmail: z.string().nullable(),
  lineItems: z.array(LineItemSchema),
  totalInvoiceBeforeVat: z.string(),
  totalVatAmount: z.string(),
  totalInvoiceAfterVat: z.string(),
  totalAmountPaid: z.string(),
});

export const invoiceStatusContract = c.router({
  fetchInvoiceStatus: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice/:agreement_number/:external_id/status`,
    pathParams: z.object({
      agreement_number: z.string(),
      external_id: z.string(),
    }),
    responses: {
      200: InvoiceStatusResponseSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  fetchBookingInvoices: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice`,
    query: z.object({
      page: z.number().optional(),
      size: z.number().optional(),
      externalIds: z.string().optional(),
      bookingNumbers: z.string().optional(),
    }),
    responses: {
      200: z.object({ data: z.array(InvoiceSearchResponseSchema) }), // Updated to return an array of invoices
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  createInvoice: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/invoice`,
    body: z.object({
      bookingNo: z.string(),
      configIdentifier: z.string(),
    }),
    responses: {
      200: CreateInvoiceResponseSchema, // Updated to return an array of invoices
      201: CreateInvoiceResponseSchema, // Updated to return an array of invoices
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  retryInvoice: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/invoice/:invoiceNumber/retry`,
    pathParams: z.object({
      invoiceNumber: z.string(),
    }),
    body: z.object({
      configIdentifier: z.string(),
    }),
    responses: {
      200: CreateInvoiceResponseSchema, // Updated to return an array of invoices
      201: CreateInvoiceResponseSchema, // Updated to return an array of invoices
      400: ErrorSchema,
      404: ErrorSchema,
      409: ErrorSchema,
      500: ServerFailureError,
    },
  },
});

export type InvoiceStatus = z.infer<typeof InvoiceStatusSchema>;
export type InvoiceStatusResponse = z.infer<typeof InvoiceStatusResponseSchema>;
export type InvoiceSearchResponse = z.infer<typeof InvoiceSearchResponseSchema>;
export type CreateInvoiceResponse = z.infer<typeof CreateInvoiceResponseSchema>;
