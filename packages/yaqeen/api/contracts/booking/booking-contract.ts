import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "../../constant";
import { AgreementInvoiceSchema } from "../schema";
import { SuggestedVehicleSchema } from "./suggested-vehicles-contract";
import { calculatedPriceSchema } from "./schema";
import { ErrorSchema, ServerFailureError } from "../common";

const c = initContract();

const vehicle = z.object({
  plateNo: z.string(),
  make: z.object({
    name: z.object({
      en: z.string(),
      ar: z.string(),
    }),
  }),
  model: z.object({
    id: z.number(),
    name: z.object({
      en: z.string(),
      ar: z.string(),
    }),
    make: z.object({
      id: z.number(),
      name: z.object({
        en: z.string(),
        ar: z.string(),
      }),
    }),
  }),
  year: z.number(),
  carGroup: z.string(),
  vehicleGroupCode: z.string(),
});

export const BookingSchema = z.object({
  id: z.number(),
  agreementNo: z.string(),
  flightNo: z.string(),
  source: z.string(),
  totalPrice: z.string(),
  paymentStatus: z.string(),
  bookingNo: z.string(),
  pickupBranchId: z.number().optional(),
  dropOffBranchId: z.number().optional(),
  pickupDateTime: z.number(),
  bookingDateTime: z.number(),
  dropOffDateTime: z.number(),
  status: z.string(),
  assignedVehicle: vehicle,
  preferredVehicleGroup: vehicle,
  checkinDate: z.string(),
  notes: z.string(),
  dueAmount: z.string(),
  pendingAction: z.string(),
  driver: z.object({
    id: z.number(),
    bookingNo: z.string(),
    pickupDateTime: z.number(),
    bookingDateTime: z.number(),
    dropOffDateTime: z.number(),
    status: z.string(),
    assignedVehicle: vehicle,
    preferredVehicleGroup: vehicle,
    checkinDate: z.string(),
    driverCode: z.number(),
    firstName: z.string(),
    lastName: z.string(),
    vehicle: vehicle,
    group: z.string(),
    source: z.string(),
    totalPrice: z.string(),
    paymentStatus: z.string(),
    flightNo: z.string(),
    notes: z.string(),
    dueAmount: z.string(),
    pendingAction: z.string(),
    agreementNo: z.number().optional(),
  }),
});

export const BookingListSchema = z.array(BookingSchema);
const DriverSchema = z.object({
  id: z.number(),
  driverCode: z.string(),
  driverUId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  mobileNumber: z.string(),
  countryCode: z.number(),
  email: z.string(),
  licenseNumber: z.string(),
  dob: z.string(),
  hijrahDob: z.string(),
  title: z.string(),
  address: z.object({
    street: z.string(),
  }),
  idType: z.string(),
  fullName: z.string(),
  fullMobileNumber: z.string(),
});
export const AgreementSchema = z.object({
  agreementNo: z.string(),
  bookingNo: z.string(),
  bookingId: z.number(),
  pickupDateTime: z.number(),
  dropOffDateTime: z.number(),
  checkinBranch: z.number(),

  driver: DriverSchema,
  driverResponse: DriverSchema,
  assignedVehicle: z.object({
    plateNo: z.string(),
    vehicleGroupId: z.number(),
    vehicleModelId: z.number(),
  }),
  source: z.string(),
  totalPrice: z.string(),
  status: z.string(),
  pendingAction: z.string(),
  pickupBranchId: z.number(),
  dropOffBranchId: z.number(),
  pickupBranch: z.object({
    id: z.number(),
    name: z.object({
      en: z.string(),
      ar: z.string().optional(),
    }),
  }),
  dropOffBranch: z.object({
    id: z.number(),
    name: z.object({
      en: z.string(),
      ar: z.string().optional(),
    }),
  }),
});

export const AgreementListSchema = z.array(AgreementSchema);

const bookingParams = z.object({
  pickupBranchId: z.number().optional(),
  "pickupDateRange.start": z.number().optional(),
  "pickupDateRange.end": z.number().optional(),
  "dropOffDateRange.start": z.number().optional(),
  "dropOffDateRange.end": z.number().optional(),
  sort: z.string().optional(),
  order: z.string().optional(),
  page: z.number(),
  size: z.number(),
  bookingNo: z.string().optional(),
  mobileNumber: z.string().optional(),
  driverName: z.string().optional(),
  paymentStatusFilter: z.string().optional(),
  source: z.string().optional(),
  status: z.string().optional(),
  agreementNo: z.string().optional(),
  pickUpBranchIds: z.string().optional(),
  dropOffBranchIds: z.string().optional(),
});

const inspectionSchema = z.object({
  odometerReading: z.number(),
  fuelLevel: z.number(),
  remark: z.string(),
  vehicleStatus: z.string().nullable(),
  newDamage: z.boolean().optional(),
  replacement: z.boolean(),
  images: z.array(
    z.object({
      typeId: z.number(),
      imageUrl: z.string(),
    })
  ),
});

export const vehicleInspectionDetailsSchema = z.object({
  agreementVehicleId: z.number(),
  plateNo: z.string(),
  checkoutInspection: inspectionSchema,
  checkinInspection: inspectionSchema,
  vehicleInspectionDetails: z.object({
    checkoutKm: z.number(),
    checkinKm: z.number().optional(),
    checkoutFuel: z.number(),
    checkinFuel: z.number().optional(),
    checkoutInspectionRefId: z.number(),
    checkinInspectionRefId: z.number().optional(),
  }),
});

export const ClosingPriceSchema = z.object({
  quoteId: z.string(),
  currency: z.string(),
  includedComprehensiveInsurance: z.boolean(),
  addOns: z.array(
    z.object({
      id: z.number(),
      quantity: z.number(),
      totalCost: z.string(),
    })
  ),
  priceDetail: z.object({
    rentalPerDay: z.string(),
    rentalAmount: z.string(),
    insuranceAmount: z.string(),
    totalAddOnAmount: z.string(),
    dropOffAmount: z.string(),
    vatPercentage: z.string(),
    vatAmount: z.string(),
    totalSum: z.string(),
    extraKmsChargeSum: z.string(),
    extraFuelChargeSum: z.string(),
  }),
  discountDetail: z.object({
    discountPercentage: z.string(),
    totalDiscount: z.string(),
  }),
  vehicleDetail: z.object({
    plateNumber: z.string(),
    vehicleGroupId: z.number(),
    vehicleMakeId: z.number(),
    vehicleModelId: z.number(),
  }),
  tariffDetail: z.object({
    tariffRateId: z.string(),
    validFrom: z.string(),
    validTill: z.string(),
    insurancePerDay: z.string(),
    insuranceDeductible: z.string(),
    authorizationAmount: z.string(),
    dailyKmsAllowance: z.number(),
    extraKmsCharge: z.string(),
  }),
  totalRentalDurationSeconds: z.number(),
  soldDays: z.string(),
  remainingAmount: z.string(),
  driverPaidAmount: z.string(),
});

const TajeerAuthorizationSchema = z.object({
  id: z.number(),
  bookingNo: z.string(),
  agreementNo: z.number(),
  agreementVehicleId: z.number(),
  externalAuthorizationNumber: z.string(),
  type: z.literal("TAJEER"),
  status: z.string(),
  skip: z.boolean(),
  metadata: z.object({
    type: z.string(),
    failureReason: z.object({
      code: z.string(),
      desc: z.string(),
      reqId: z.string(),
      retryable: z.boolean(),
    }),
    validationStatus: z.string(),
    validationDateTime: z.number(),
    saveStatus: z.string(),
    saveDateTime: z.number(),
    contractNumber: z.string(),
    otpStatus: z.string(),
    otpDateTime: z.number(),
    sendCount: z.number(),
    createStatus: z.string(),
    createdAt: z.number(),
    closeStatus: z.string(),
    closedAt: z.number(),
  }),
  createdOn: z.number(),
  updatedOn: z.number(),
  createdBy: z.string(),
  updatedBy: z.string(),
});

const TammAuthorizationSchema = z.object({
  id: z.number(),
  bookingNo: z.string(),
  agreementNo: z.string().nullable(),
  agreementVehicleId: z.string().nullable(),
  externalAuthorizationNumber: z.string(),
  type: z.literal("TAMM"),
  failureReason: z.object({
    code: z.string(),
    desc: z.string(),
    reqId: z.string(),
    retryable: z.boolean(),
  }),
  status: z.string(),
  skip: z.boolean(),
  metadata: z.object({
    type: z.literal("TAMM"),
    authorizationNumber: z.number(),
    verifyDriverAuthIssueStatus: z.string(),
    verifyDriverAuthIssueDateTime: z.number(),
    conversationId: z.string(),
    confirmDriverAuthIssueStatus: z.string(),
    confirmDriverAuthIssueDateTime: z.number(),
    verifyCancelDriverAuthStatus: z.string(),
    confirmCancelDriverAuthStatus: z.string(),
    confirmCancelDriverAuthDateTime: z.number(),
  }),
  createdOn: z.number(),
  updatedOn: z.number(),
  createdBy: z.string(),
  updatedBy: z.string(),
});

// Use discriminated union to handle the `type` field
const AuthorizationSchema = z.discriminatedUnion("type", [TajeerAuthorizationSchema, TammAuthorizationSchema]);
export type Authorization = z.infer<typeof AuthorizationSchema>;

// Define the InvoiceSchema
const InvoiceSchema = z.object({
  id: z.number(),
  externalId: z.string(),
  agreementNumber: z.number(),
  bookingNumber: z.string(),
  invoiceNumber: z.string(),
  invoiceConfigType: z.string(),
  issueDate: z.number(),
  invoiceType: z.string(),
  invoiceCategory: z.string(),
  totalInvoiceBeforeVat: z.number(),
  totalVatAmount: z.number(),
  totalInvoiceAfterVat: z.number(),
  totalAmountPaid: z.number(),
  invoiceStatus: z.enum(["SUCCESS", "FAILED", "PENDING"]),
});

// Use the InvoiceSchema in CloseAgreementResponseSchema
export const CloseAgreementResponseSchema = z.object({
  agreement: z.object({
    agreementNo: z.number(),
    bookingNo: z.string(),
    bookingId: z.number(),
    quotationReference: z.string(),
    checkoutBranch: z.number(),
    checkoutDate: z.number(),
    checkinBranch: z.number(),
    checkinDate: z.number(),
    status: z.string(),
    vehicle: z.object({
      id: z.number(),
      plateNo: z.string(),
      vehicleMakeId: z.number(),
      vehicleModelId: z.number(),
      vehicleGroupId: z.number(),
      checkoutDate: z.number(),
      checkinDate: z.number(),
      checkoutKm: z.number(),
      checkinKm: z.number(),
      checkoutFuel: z.number(),
      checkoutInspectionRefId: z.number(),
      checkinInspectionRefId: z.number(),
      checkoutBranchId: z.number(),
      checkinBranchId: z.number(),
      nextVehicleStatus: z.string(),
      status: z.string(),
    }),
    driverResponse: z.object({
      id: z.number(),
      driverCode: z.string(),
      driverUId: z.string(),
      firstName: z.string(),
      lastName: z.string(),
      mobileNumber: z.string(),
      countryCode: z.number(),
      email: z.string(),
      dob: z.string(),
      hijrahDob: z.string(),
      title: z.string(),
      address: z.object({
        street: z.string(),
      }),
      idType: z.string(),
    }),
    source: z.string(),
    extended: z.boolean(),
    pendingAction: z.string(),
    actualCheckoutDate: z.number(),
    actualCheckinDate: z.number(),
    metadata: z.object({
      bookingDateTime: z.number(),
    }),
  }),
  invoices: z.array(InvoiceSchema), // Updated invoices field
  authorization: AuthorizationSchema, // Use the discriminated union schema here
  error: z.object({
    code: z.string(),
    desc: z.string(),
  }),
});

export const bookingContract = c.router(
  {
    /**  USED FOR ALL BOOKINGS, AND UPCOMING BOOKINGS */
    getUpcomingBookings: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/search`,
      query: bookingParams,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          total: z.number(),
          data: BookingListSchema,
        }),
      },
    },
    getUpcomingBookingsCount: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/upcoming/count`,
      query: z.object({
        pickupBranchId: z.number(),
        "pickupDateRange.start": z.number().optional(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          nextTwoHourCount: z.number(),
          todayUpcomingCount: z.number(),
          paidCount: z.number(),
        }),
      },
    },
    /**  USED FOR ONGOING, ACTIONS NEEDED, AND COMPLETED BOOKINGS */
    getOngoingBookings: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/booking/search`,
      query: bookingParams,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          total: z.number(),
          data: BookingListSchema,
        }),
      },
    },
    getOngoingBookingsCount: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/ongoing/booking/count`,
      query: z.object({
        dropOffBranchId: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          nextTwoHourDropOffCount: z.number(),
          todayDropOffCount: z.number(),
        }),
      },
    },
    getBookingSearch: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/search`,
      query: z.object({
        driverUid: z.string(),
        size: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          total: z.number(),
          data: BookingListSchema,
        }),
      },
    },
    getSuggestedAuthorization: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/suggestion`,
      pathParams: z.object({
        bookingNo: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          suggestedAuths: z.array(
            z.object({
              name: z.string(),
            })
          ),
        }),
      },
    },
    getAgreements: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/booking/search`,
      query: z.object({
        sort: z.string().optional(),
        order: z.string().optional(),
        pickupBranchId: z.number().optional(),
        status: z.string().optional(),
        page: z.number().optional(),
        size: z.number().optional(),
        bookingNo: z.string().optional(),
        bookingId: z.string().optional(),
        driverName: z.string().optional(),
        agreementNo: z.string().optional(),
        "pickupDateRange.start": z.number().optional(),
        "pickupDateRange.end": z.number().optional(),
        "dropOffDateRange.start": z.number().optional(),
        "dropOffDateRange.end": z.number().optional(),
        pickupBranchIds: z.string().optional(),
        dropOffBranchIds: z.string().optional(),
        vehiclePlateNos: z.string().optional(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          total: z.number(),
          data: AgreementListSchema,
        }),
      },
    },
    getAgreement: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/details`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: AgreementInvoiceSchema,
      },
    },
    closingPrice: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/closing-price`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: ClosingPriceSchema,
      },
    },
    updateAgreementStatus: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/status/update`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      body: z.object({
        status: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          success: z.boolean(),
        }),
      },
    },
    getVehicleInspectionDetails: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/vehicle/:agreementVehicleId/inspection/details`,
      pathParams: z.object({
        agreementVehicleId: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: vehicleInspectionDetailsSchema,
      },
    },
    submitVehicleInspection: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/vehicle/:agreementVehicleId/inspection`,
      pathParams: z.object({
        agreementNo: z.string(),
        agreementVehicleId: z.number(),
      }),
      body: z.object({
        inspectionType: z.number(),
        metadata: z.object({
          odometerReading: z.number().optional(),
          fuelLevel: z.number().optional(),
          remark: z.string().optional(),
          images: z
            .array(
              z.object({
                typeId: z.number(),
                imageUrl: z.string(),
              })
            )
            .optional(),
        }),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          success: z.boolean(),
        }),
      },
    },
    updateVehicleInspection: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/vehicle/:agreementVehicleId/inspection/:inspectionId`,
      pathParams: z.object({
        agreementNo: z.string(),
        agreementVehicleId: z.number(),
        inspectionId: z.number(),
      }),
      body: z.object({
        inspectionType: z.number(),
        metadata: z.object({
          odometerReading: z.number().optional(),
          fuelLevel: z.number().optional(),
          remark: z.string().optional(),
          replacement: z.boolean().optional(),
          vehicleStatus: z.number().optional(),
        }),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          success: z.boolean(),
        }),
      },
    },
    updateBooking: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/bookings/:bookingId`,
      pathParams: z.object({
        bookingId: z.number(),
      }),
      body: z.object({
        newQuoteReference: z.string(),
        vehicleUpgradeReason: z
          .object({
            identifier: z.string(),
            reasonText: z.string().optional(),
          })
          .optional(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          success: z.boolean(),
          total: z.number(),
          data: BookingListSchema,
        }),
      },
    },
    getBranchAvailableVehicles: {
      method: "GET",
      path: `/pricing-service/api/vehicles/availability/live`,
      query: z.object({
        branchId: z.number(),
      }),
      responses: {
        200: z.array(SuggestedVehicleSchema),
        400: ErrorSchema,
        500: ErrorSchema,
        404: ErrorSchema,
      },
    },
    closeAgreement: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/close`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      body: z.object({
        quoteId: z.string(),
        nextVehicleStatus: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: CloseAgreementResponseSchema,
      },
    },
    getCalculatePriceForQuote: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/calculate-price/:quoteId`,
      pathParams: z.object({
        quoteId: z.string(),
      }),
      responses: {
        200: calculatedPriceSchema,
        400: ErrorSchema,
        500: ErrorSchema,
        404: ErrorSchema,
      },
    },
    waiveExtraCharge: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/financials/extra-charge/waive-off`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      body: z.object({
        extraChargeType: z.string(),
        waiveOff: z.boolean(),
        waiveOffReason: z.string().optional(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.object({
          success: z.boolean(),
        }),
      },
    },
    retryTajeerAuth: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:agreementNo/vehicle/:vehicleId/close/tajeer?entityType=AGREEMENT`,
      pathParams: z.object({
        agreementNo: z.string(),
        vehicleId: z.string(),
      }),
      body: z.object({}),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: AuthorizationSchema,
        201: AuthorizationSchema,
      },
    },
    retryTammAuth: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:agreementNo/vehicle/:vehicleId/close/tamm?entityType=AGREEMENT`,
      pathParams: z.object({
        agreementNo: z.string(),
        vehicleId: z.string(),
      }),
      body: z.object({}),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: AuthorizationSchema,
        201: AuthorizationSchema,
      },
    },
    downloadAgreement: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/print`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.string(),
      },
    },
    getPaymentReciept: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/:type/:id/print/receipt`,
      pathParams: z.object({
        id: z.string(),
        type: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.string(),
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);

export type CloseAgreementResponse = z.infer<typeof CloseAgreementResponseSchema>;
export type Booking = z.infer<typeof BookingListSchema>[number];
export type IAgreement = z.infer<typeof AgreementSchema>;
