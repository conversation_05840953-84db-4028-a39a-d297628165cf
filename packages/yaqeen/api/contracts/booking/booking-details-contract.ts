import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "../../constant";
import { ErrorSchema, ServerFailureError } from "../common";
import {
  BookingDetailsSchema,
  LoyaltySchema,
  VehicleGroupSchema,
  InsuranceSchema,
  AddonsSchema,
  PaymentsSchema,
  UpdateBookingRequestSchema,
  CalculatePriceSchema,
  AddonSchema,
  calculatePriceBody,
  CancelBookingRequestSchema,
} from "./schema";

const PAYMENT_TYPE = z.string().default("PAY_AT_BRANCH");

// Define schema for penalty details
const PenaltyDetailsSchema = z.object({
  type: z.enum(["DAMAGE", "TRAFFIC_FINE"]),
  policeReportUrl: z.string().optional(),
  severity: z.string().optional(),
});

// Define schema for penalty request body
const AddPenaltyRequestSchema = z.object({
  penaltyDescription: z.string(),
  penaltyAmount: z.string(),
  details: PenaltyDetailsSchema,
});

// Define schema for update penalty request body
const UpdatePenaltyRequestSchema = z.object({
  penaltyAmount: z.string(),
  penaltyDescription: z.string(),
  details: PenaltyDetailsSchema,
});

// Define schema for penalty response
const PenaltyResponseSchema = z.object({
  id: z.number(),
  penaltyAmount: z.string(),
  penaltyDescription: z.string(),
  penaltyDate: z.number(),
  type: z.string(),
  details: PenaltyDetailsSchema,
  createdBy: z.string(),
  updatedBy: z.string(),
});

const BookingCancelSchema = z.object({
  bookingId: z.number(),
  status: z.string(),
  message: z.string(),
});

// Define schema for initiate agreement extension request
const InitiateExtensionRequestSchema = z.object({
  quoteId: z.string(),
});

// Define schema for initiate agreement extension response
const InitiateExtensionResponseSchema = z.object({
  quoteId: z.string(),
  agreementNo: z.string(),
  bookingNo: z.string(),
  status: z.string(),
  totalPrice: z.string(),
  oldDropOffDateTime: z.number(),
  newDropOffDateTime: z.number(),
  extensionDuration: z.number(),
  paymentLink: z.string(),
});

const c = initContract();

/**
 *
 *
 * API calls
 *
 *
 * */
export const bookingDetailsContract = c.router(
  {
    getBookingById: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/:id`,
      pathParams: z.object({
        id: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: BookingDetailsSchema,
      },
    },
    getLoyaltyByReference: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/loyalty/v1/reward/:referenceNo/details`,
      pathParams: z.object({
        referenceNo: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: LoyaltySchema,
      },
    },
    getVehicleGroupById: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/fleet/v1/vehicle-group/find`,
      query: z.object({
        id: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: VehicleGroupSchema,
      },
    },
    getInsurance: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/insurance`,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: InsuranceSchema,
      },
    },
    getInsuranceByGroupId: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/insurance`,
      query: z.object({
        groupId: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: InsuranceSchema,
      },
    },
    getAddOns: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/fleet/v1/addon`,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: AddonsSchema,
      },
    },
    getAddOnsByQuoteId: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/pricing/quote/:quoteId/addons`,
      pathParams: z.object({
        quoteId: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: z.array(AddonSchema),
      },
    },
    calculateAgreementPrice: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/closing-price`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: CalculatePriceSchema,
      },
    },
    calculateExtendAgreementPrice: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/extensions/calculate-price`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      query: z.object({
        newCheckInDate: z.number(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: CalculatePriceSchema,
      },
    },
    initiateAgreementExtension: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/:agreementNo/extensions`,
      pathParams: z.object({
        agreementNo: z.string(),
      }),
      body: InitiateExtensionRequestSchema,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: InitiateExtensionResponseSchema,
      },
    },
    calculatePrice: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/bookings/:bookingId/v2/calculate-price`,
      body: calculatePriceBody,
      responses: {
        200: CalculatePriceSchema,
        400: ErrorSchema,
        500: ErrorSchema,
      },
    },
    getPaymentsById: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/:id/payments`,
      pathParams: z.object({
        id: z.number(),
      }),
      query: z.object({
        page: z.number().optional(),
        notInitiatedFor: z.string().optional(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: PaymentsSchema,
      },
    },
    updateBooking: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/bookings/:bookingId`,
      pathParams: z.object({
        bookingId: z.number(),
      }),
      body: UpdateBookingRequestSchema,
      responses: {
        200: BookingDetailsSchema,
        400: ErrorSchema,
        500: ServerFailureError,
      },
    },
    addVehiclePenalty: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/vehicle/:agreementVehicleId/penalties`,
      pathParams: z.object({
        agreementVehicleId: z.number(),
      }),
      body: AddPenaltyRequestSchema,
      responses: {
        200: PenaltyResponseSchema,
        400: ErrorSchema,
        500: ServerFailureError,
      },
    },
    getVehiclePenalties: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/vehicle/:agreementVehicleId/penalties`,
      pathParams: z.object({
        agreementVehicleId: z.number(),
      }),
      responses: {
        200: z.array(PenaltyResponseSchema),
        400: ErrorSchema,
        404: ErrorSchema,
        500: ServerFailureError,
      },
    },
    updateVehiclePenalty: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/agreements/vehicle/:agreementVehicleId/penalties/:penaltyId`,
      pathParams: z.object({
        agreementVehicleId: z.number(),
        penaltyId: z.number(),
      }),
      body: UpdatePenaltyRequestSchema,
      responses: {
        200: PenaltyResponseSchema,
        400: ErrorSchema,
        404: ErrorSchema,
        500: ServerFailureError,
      },
    },
    deleteVehiclePenalty: {
      method: "DELETE",
      path: `${YAQEEN_SERVICE}/agreements/vehicle/:agreementVehicleId/penalties/:penaltyId`,
      pathParams: z.object({
        agreementVehicleId: z.number(),
        penaltyId: z.number(),
      }),
      responses: {
        200: z.object({ success: z.boolean() }),
        204: z.object({ success: z.boolean() }),
        400: ErrorSchema,
        404: ErrorSchema,
        500: ServerFailureError,
      },
    },
    cancelBooking: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/bookings/cancel/:bookingRef`,
      pathParams: z.object({
        bookingRef: z.string(),
      }),
      body: CancelBookingRequestSchema,
      responses: {
        200: BookingCancelSchema,
        400: ErrorSchema,
        404: ErrorSchema,
        500: ServerFailureError,
      },
    },
    /**
     *
     *
     *  walk-in flow - B2C
     *
     *
     */

    // start agreeement / create booking
    createBookingByPayAtBranch: {
      method: "POST",
      path: `/booking-service/yaqeen/checkout/payment/:quoteId`,
      pathParams: z.object({
        quoteId: z.string(),
      }),
      body: z.object({
        paymentType: PAYMENT_TYPE,
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: BookingDetailsSchema,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);

// Export penalty schemas for use elsewhere in the application
export type PenaltyDetails = z.infer<typeof PenaltyDetailsSchema>;
export type AddPenaltyRequest = z.infer<typeof AddPenaltyRequestSchema>;
export type UpdatePenaltyRequest = z.infer<typeof UpdatePenaltyRequestSchema>;
export type PenaltyResponse = z.infer<typeof PenaltyResponseSchema>;
export type InitiateExtensionResponseSchema = z.infer<typeof InitiateExtensionResponseSchema>;
export { PenaltyDetailsSchema, AddPenaltyRequestSchema, UpdatePenaltyRequestSchema, PenaltyResponseSchema };
