import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "@/api/constant";
import { ErrorSchema, ServerFailureError } from "../common";

const DRIVER_SERVICE = "/driver-service";

const c = initContract();

const baseError = z.object({
  code: z.string(),
  desc: z.string(),
});

const errorResponse = baseError.extend({
  reqId: z.string(),
});

const name = z.object({
  en: z.string(),
  ar: z.string(),
});

const serverFailureError = baseError;

export const nationality = z.object({
  code: z.number(),
  position: z.number(),
  name: name,
  id: z.number(),
});

const document = z.object({
  id: z.number(),
  type: z.string(),
  documentNo: z.string(),
  expiry: z.string(),
  hijrahExpiry: z.string(),
  url: z.string(),
  version: z.number().optional(),
  issuedPlace: z
    .object({
      code: z.number(),
      name: name,
    })
    .optional(),
});

export const DriverSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  mobileNumber: z.string(),
  countryCode: z.number(),
  email: z.string(),
  dob: z.string(),
  hijrahDob: z.string(),
  title: z.string(),
  address: z.object({
    street: z.string(),
  }),
  id: z.number(),
  customer: z.object({
    id: z.number(),
  }),
  driverCode: z.string(),
  createdOn: z.number(),
  updatedOn: z.number(),
  documents: z.array(document),
  nationality: nationality,
  driverUId: z.string(),
  idType: z.string(),
  metadata: z
    .object({
      borderNumber: z.string(),
    })
    .optional(),
});

export const driverBody = z.object({
  ...DriverSchema.shape,
  driverUid: z.string().optional(),
  id: z.number().optional(),
  customer: z.object({}).optional(),
  driverCode: z.string().optional(),
  createdOn: z.number().optional(),
  updatedOn: z.number().optional(),
  driverUId: z.string().optional(),
  documents: z.array(
    z.object({
      ...document.shape,
      id: z.number().optional(),
      url: z.string().optional(),
      issuedPlace: z
        .object({
          id: z.number(),
          name: name.optional(),
        })
        .optional(),
    })
  ),
  nationality: nationality.partial().extend({
    id: z.number(),
  }),
});

export const DriverSearchResponseSchema = z.object({
  data: z.array(DriverSchema),
  total: z.number(),
  page: z.number(),
  size: z.number(),
});

export const driverDetailsContract = c.router(
  {
    getDriverById: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/drivers/find`,
      query: z.object({
        driverUid: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: DriverSchema,
      },
    },
    updateDriver: {
      method: "PUT",
      path: `${YAQEEN_SERVICE}/drivers/:driverUid`,
      pathParams: z.object({
        driverUid: z.string(),
      }),
      body: driverBody,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: DriverSchema,
      },
    },
    getBookingCountByDriverUid: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/bookings/driver/:driverUid/count`,
      pathParams: z.object({
        driverUid: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          totalAgreementCount: z.number(),
          totalNoShowCount: z.number(),
        }),
      },
    },

    /**
     *
     *
     * B2C - Walk-in flow
     *
     */

    searchDrivers: {
      method: "GET",
      path: `${DRIVER_SERVICE}/yaqeen/drivers/search`,
      query: z.object({
        sort: z.string().default("id"),
        order: z.string().default("desc"),
        query: z.string(),
        page: z.number().default(0),
        size: z.number().default(10),
      }),
      responses: {
        200: DriverSearchResponseSchema,
        400: ErrorSchema,
        404: ErrorSchema,
        500: ServerFailureError,
      },
    },
    driverAttachOrCreate: {
      method: "POST",
      path: `/booking-service/yaqeen/checkout/register/:quoteId`,
      pathParams: z.object({
        quoteId: z.string(),
      }),
      body: driverBody,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: DriverSchema,
      },
    },
  },

  {
    strictStatusCodes: true,
  }
);

export type Driver = z.infer<typeof DriverSchema>;
export type DriverSearchResponse = z.infer<typeof DriverSearchResponseSchema>;
