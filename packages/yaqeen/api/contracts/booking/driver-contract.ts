import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { DRIVER_SERVICE, YAQEEN_SERVICE } from "@/api/constant";
import { ErrorSchema, ServerFailureError } from "../common";

const c = initContract();

// Driver Schema
export const DriverSchema = z.object({
  id: z.string(),
  name: z.string(),
  idType: z.enum(["License number", "Passport"]),
  status: z.enum(["active", "warning", "blacklisted"]),
  // Add more fields as needed based on the actual API response
  email: z.string().optional(),
  mobile: z.string().optional(),
  nationality: z.string().optional(),
});

export const DriverSearchResponseSchema = z.object({
  data: z.array(DriverSchema),
  total: z.number(),
  page: z.number(),
  size: z.number(),
});

export const driverContract = c.router({
  searchDrivers: {
    method: "GET",
    path: `${DRIVER_SERVICE}/yaqeen/drivers/search`,
    query: z.object({
      sort: z.string().default("id"),
      order: z.enum(["asc", "desc"]).default("asc"),
      query: z.string(),
      page: z.number().default(0),
      size: z.number().default(10),
    }),
    responses: {
      200: DriverSearchResponseSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
});

export type Driver = z.infer<typeof DriverSchema>;
export type DriverSearchResponse = z.infer<typeof DriverSearchResponseSchema>;
