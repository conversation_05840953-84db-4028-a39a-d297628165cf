import { type AgreementSchema, type ClosingPriceSchema, type vehicleInspectionDetailsSchema } from "./booking-contract";
import { z } from "zod";
import { SuggestedVehicleSchema } from "./suggested-vehicles-contract";

export const NameSchema = z.object({
  en: z.string().optional(),
  ar: z.string().optional(),
});

export const BranchDetailSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
  city: z.object({
    id: z.number(),
    name: NameSchema,
  }),
});

export const AddOnsSchema = z.object({
  id: z.number(),
  name: NameSchema,
  imageUrl: z.string().optional(),
});

export const DiscountSchema = z.object({
  promoCode: z.string().optional(),
  discountPercentage: z.string().optional(),
  totalDiscount: z.string().optional(),
  isCorporate: z.boolean().optional(),
});

const BookingDetailsSchema = z.object({
  id: z.number(),
  bookingNo: z.string().optional(),
  referenceNo: z.string().optional(),
  bookingDateTime: z.number().optional(),
  pickupBranchId: z.number().optional(),
  agreementNo: z.string().optional(),
  pickupDateTime: z.number().optional(),
  dropOffBranchId: z.number().optional(),
  dropOffDateTime: z.number().optional(),
  remainingAmount: z.string().optional(),
  bookingType: z.string().optional(),
  pickupBranch: BranchDetailSchema.optional(),
  dropOffBranch: BranchDetailSchema.optional(),
  driver: z
    .object({
      customerId: z.number(),
      driverUId: z.string(),
      driverCode: z.string(),
      name: z.string().optional(),
      title: z.string().optional(),
    })
    .optional(),
  assignedVehicle: z.object({
    plateNo: z.string().optional(),
    vehiclePlateInfo: SuggestedVehicleSchema.optional(),
    vehicleGroupId: z.number().optional(),
  }),
  preferredVehicle: z.object({
    vehicleGroupId: z.number().optional(),
    vehicleGroup: z
      .object({
        id: z.number(),
        code: z.string(),
        displayName: z.string(),
        faceModelResponse: z.object({
          id: z.number(),
          make: z.object({
            id: z.number(),
            name: NameSchema,
          }),
          name: NameSchema,
        }),
        description: NameSchema,
        faceModelId: z.number(),
        enabled: z.boolean(),
        vehicleTypeResponse: z.string().optional(),
        vehicleClassResponse: z.string().optional(),
        models: z.array(
          z.object({
            id: z.number(),
            name: NameSchema,
            make: z.object({
              id: z.number(),
              name: NameSchema,
            }),
            modelYear: z.number(),
          })
        ),
      })
      .optional(),
  }),
  priceDetail: z.object({
    priceBreakdown: z.object({
      rentalAmount: z.string().optional(),
      insuranceAmount: z.string().optional(),
      totalAddOnAmount: z.string().optional(),
      dropOffAmount: z.number().optional(),
      vatPercentage: z.string().optional(),
      vatAmount: z.string().optional(),
      totalSum: z.string().optional(),
    }),
    discountDetail: z.object({
      name: NameSchema,
      promoCode: z.string().optional(),
      discountPercentage: z.string().optional(),
      totalDiscount: z.string().optional(),
      isCorporate: z.boolean().optional(),
    }),
    tariffDetail: z.object({
      rateCode: z.string().optional(),
      rateName: z.string().optional(),
      tariff: z.string().optional(),
      dailyKmsAllowance: z.number(),
      extraKmsCharge: z.string(),
    }),
    addOnIds: z.array(z.number()),
    addOns: z.array(AddOnsSchema).optional(),
    insurances: z
      .array(
        z.object({
          id: z.number(),
          name: NameSchema,
        })
      )
      .optional(),
    insuranceIds: z.array(z.number()).optional(),
    totalRentalDurationSeconds: z.number(),
    soldDays: z.string(),
    includedComprehensiveInsurance: z.boolean(),
    soldDaysInSeconds: z.number(),
    totalAllowedKms: z.number(),
  }),
  driverPaidAmount: z.string().optional(),
  status: z.string().optional(),
  source: z.string().optional(),
  aggregatorName: z.string().optional(),
  debitorCode: z.null(),
  debtorName: z.string().optional(),
  debtorPO: z.string().optional(),
  cancellationDetails: z
    .object({
      cancelledBy: z.string().optional(),
      employeeName: z.string().optional(),
      cancelledOn: z.number().optional(),
      reason: z.string().optional(),
      refundStatus: z.string().optional(),
    })
    .optional(),
  bookingPaymentMethod: z.string().optional(),
  refundApprovedAmount: z.string().optional(),
  refundRequestedAmount: z.string().optional(),
});

const LoyaltySchema = z.object({
  provider: z.string().optional(),
  customerIdentifier: z.string().optional(),
  value: z.string().optional(),
  valueType: z.string().optional(),
  status: z.string().optional(),
  estimatedAmount: z.string().optional(),
  trackId: z.string().optional(),
  id: z.number().optional(),
});

const VehicleGroupSchema = z.object({
  id: z.number().optional(),
  code: z.string().optional(),
  displayName: z.string().optional(),
  faceModelId: z.number().optional(),
  models: z.array(
    z.object({
      id: z.number().optional(),
      name: NameSchema,
      make: z.object({
        id: z.number().optional(),
        name: NameSchema,
      }),
      modelYear: z.number().optional(),
    })
  ),
});

const SingleInsuranceSchema = z.object({
  name: NameSchema,
  description: NameSchema,
  recommended: z.boolean().optional(),
  isEnabled: z.boolean().optional(),
  id: z.number(),
  perday: z.number().optional(),
  code: z.string().optional(),
  deductible: z.number().optional(),
});

const InsuranceSchema = z.object({
  total: z.number().optional(),
  data: z.array(SingleInsuranceSchema),
});

const AddonSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
  description: NameSchema,
  price: z.string(),
  currency: z.string(),
  enabled: z.boolean(),
  imageUrl: z.string().optional(),
  checked: z.boolean().optional(),
});

const AddonsSchema = z.object({
  content: z.array(
    z.object({
      id: z.number().optional(),
      code: z.string().optional(),
      name: NameSchema,
      description: NameSchema,
      imageUrl: z.string().optional(),
      enabled: z.boolean().optional(),
    })
  ),
});

const PaymentsSchema = z.object({
  total: z.number().optional(),
  data: z.array(
    z.object({
      createdOn: z.number().optional(),
      amount: z.string(),
      status: z.string().optional(),
      paymentChannel: z.string().optional(),
      receiptNo: z.string().optional(),
      paymentProvider: z.string().optional(),
      paymentProviderReference: z.string().optional(),
      trackId: z.string().optional(),
      is3DS: z.string().optional(),
      authCode: z.string().optional(),
      paidThrough: z.string().optional(),
      paymentLocation: z.string().optional(),
      platform: z.string().optional(),
      cardDetails: z.object({
        last4Digits: z.string().optional(),
        cardScheme: z.string().optional(),
      }),
      employee: z.string().optional(),
      refundDetails: z.string().optional(),
    })
  ),
});

const VehicleUpgradeReasonSchema = z.object({
  reason: z.string(),
  reasonText: z.string().optional(),
});

const RewardSchema = z.object({
  provider: z.enum(["NONE", "ALFURSAN", "MOKAFA", "QITAF"]),
  customerIdentifier: z.string(),
});

const UpdateBookingRequestSchema = z.object({
  newQuoteReference: z.string(),
  vehicleUpgradeReason: VehicleUpgradeReasonSchema.optional(),
  reward: RewardSchema.optional(),
});

const CancelBookingRequestSchema = z.object({
  message: z.string(),
});

const CalculatePriceSchema = z.object({
  quoteId: z.string().optional(),
  expiry: z.number().optional(),
  currency: z.string().optional(),
  includedComprehensiveInsurance: z.boolean().optional(),
  addOns: z.array(
    z.object({
      id: z.number().optional(),
      quantity: z.number().optional(),
      name: NameSchema.optional(),
      totalCost: z.number().optional(),
      unitPrice: z.number().optional(),
      unitDuration: z.number().optional(),
    })
  ),
  priceDetail: z.object({
    rentalPerDay: z.string().optional(),
    rentalPerDayInclVat: z.string().optional(),
    rentalAmount: z.string().optional(),
    insuranceAmount: z.string().optional(),
    totalAddOnAmount: z.string().optional(),
    dropOffAmount: z.number().optional(),
    vatPercentage: z.string().optional(),
    vatAmount: z.string().optional(),
    totalSum: z.string().optional(),
    trafficFineSum: z.string().optional(),
    penaltyChargeSum: z.string().optional(),
  }),
  discountDetail: z
    .object({
      promoCode: z.string().optional(),
      discountPercentage: z.string().optional(),
      totalDiscount: z.string().optional(),
      discountedPriceDetail: z.object({
        rentalPerDay: z.string().optional(),
        rentalPerDayInclVat: z.string().optional(),
        rentalAmount: z.string().optional(),
        insuranceAmount: z.string().optional(),
        totalAddOnAmount: z.string().optional(),
        vatPercentage: z.string().optional(),
        vatAmount: z.string().optional(),
        totalSum: z.string().optional(),
      }),
      isCorporate: z.boolean().optional(),
    })
    .optional(),
  vehicleDetail: z.object({
    plateNumber: z.string().optional(),
    vehicleGroupId: z.number().optional(),
    vehicleGroupCode: z.string().optional(),
    vehicleMakeId: z.string().optional(),
    vehicleModelId: z.string().optional(),
    isFreeUpgrade: z.boolean().optional(),
  }),
  tariffDetail: z.object({
    tariffRateId: z.string().optional(),
    validFrom: z.number().optional(),
    validTill: z.number().optional(),
    insurancePerDay: z.string().optional(),
    totalInsuranceAmount: z.string().optional(),
    insuranceDeductible: z.string().optional(),
    authorizationAmount: z.string().optional(),
    dailyKmsAllowance: z.number().optional(),
    extraKmsCharge: z.string().optional(),
  }),
  totalRentalDurationSeconds: z.number().optional(),
  soldDays: z.string().optional(),
  allowedLateHours: z.number().optional(),
  remainingAmount: z.string().optional(),
  driverPaidAmount: z.string().optional(),
  refundApprovedAmount: z.string().optional(),
  refundRequestedAmount: z.string().optional(),
  driverExpenses: z
    .array(
      z.object({
        expenseType: z.string(),
        allowedUnits: z.number(),
        payableUnits: z.number(),
        unitType: z.string(),
        totalSum: z.number(),
        totalSumInclVat: z.number(),
        currency: z.string(),
        waiveOff: z.boolean(),
        waiveOffReason: z.string().optional(),
      })
    )
    .optional(),
  request: z.object({
    pickupBranchId: z.number().optional(),
    pickupBranch: BranchDetailSchema.optional(),
    dropOffBranchId: z.number().optional(),
    dropOffBranch: BranchDetailSchema.optional(),
    pickupDateTime: z.number(),
    dropOffDateTime: z.number().optional(),
    insuranceIds: z.array(z.number()).optional(),
    addOnIds: z.array(z.number()).optional(),
    promoCode: z.string().optional(),
    vehicleGroupId: z.number().optional(),
    driverUid: z.string().optional(),
  }),
});

export const calculatePriceBody = z.object({
  pickupBranchId: z.number().optional(),
  dropOffBranchId: z.number().optional(),
  quoteId: z.string().optional(),
  dropOffTime: z.number().optional().describe("Unix timestamp in milliseconds (Riyadh timezone)"),
  driverId: z.number().optional(),
  vehicleGroupCode: z.string().optional(),
  vehiclePlateNo: z.string().optional(),
  vehicleGroupId: z.number().optional(),
  offerFreeVehicleUpgrade: z.boolean().optional(),
  vehicleLockRef: z.string().optional(),
  insuranceIds: z.array(z.number()).optional(),
  addOns: z
    .array(
      z.object({
        id: z.number(),
        quantity: z.number().optional(),
      })
    )
    .optional(),
  discountCode: z.string().nullable().optional(),
});

export const PosSchema = z.object({
  total: z.number(),
  data: z.array(
    z.object({
      posId: z.string(),
    })
  ),
});

/**
 *
 *
 *  WALK-IN BOOKING FLOW
 *
 */

/**
 * Reusable schemas to reduce redundancy
 */
const TimeRangeSchema = z.object({
  start: z.array(z.number()),
  end: z.array(z.number()),
});

const TimingSchema = z.object({
  day: z.string(),
  open: z.boolean(),
  start: z.string(),
  end: z.string(),
  timeRanges: z.array(TimeRangeSchema),
});

const TimingSummarySchema = z.object({
  startDay: z.string(),
  endDay: z.string(),
  start: z.string(),
  end: z.string(),
});

const RegionSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
});

const CitySchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
  position: z.number(),
  latitude: z.number(),
  longitude: z.number(),
  region: RegionSchema,
});

const MakeSchema = z.object({
  code: z.string(),
  name: NameSchema,
  id: z.number(),
});

const ImageSchema = z.object({
  id: z.number(),
  url: z.string(),
});

const FeatureSchema = z.object({
  id: z.number(),
  name: NameSchema,
  icon: z.string(),
  valueId: z.number(),
  value: NameSchema,
  primary: z.boolean(),
  filter: z.boolean(),
});

const ClassSchema = z.object({
  id: z.number(),
  name: NameSchema,
  code: z.string(),
});

const BranchSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
  email: z.string(),
  phoneNumber: z.string(),
  latitude: z.number(),
  longitude: z.number(),
  city: CitySchema,
  distance: z.number(),
  type: z.string(),
  timings: z.array(TimingSchema),
  timingSummary: z.array(TimingSummarySchema),
  timezone: z.string(),
  directions: z.string(),
  leadTimeMinutes: z.number(),
  tags: z.array(z.string()),
  carDelivery: z.boolean(),
});

const ModelSchema = z.object({
  id: z.number(),
  name: NameSchema,
  make: MakeSchema,
});

const VehicleGroupDetailsSchema = z.object({
  id: z.number(),
  code: z.string(),
  description: NameSchema,
  vthCode: z.string(),
  make: MakeSchema,
  model: ModelSchema,
  classId: z.number(),
  thumbnail: z.string(),
  images: z.array(ImageSchema),
  features: z.array(FeatureSchema),
  isEnabled: z.boolean(),
  displayName: z.string(),
  class: ClassSchema,
});

const InsuranceItemSchema = z.object({
  name: NameSchema,
  description: NameSchema,
  recommended: z.boolean(),
  id: z.number(),
  code: z.string(),
  deductible: z.number(),
  enabled: z.boolean().optional(),
});

const TariffRateSchema = z.object({
  id: z.number(),
  dailyPrice: z.number(),
  weeklyPrice: z.number(),
  monthlyPrice: z.number(),
  maxAllowedDiscount: z.number(),
  dailyCdw: z.number(),
  weeklyCdw: z.number(),
  monthlyCdw: z.number(),
  dailyKmAllowance: z.number(),
  extraKmCharges: z.number(),
  cdwDeductibles: z.number(),
  dailyAuthAmount: z.number(),
  weeklyAuthAmount: z.number(),
  monthlyAuthAmount: z.number(),
  carGroupId: z.number(),
  carGroupCode: z.string(),
  validFrom: z.string(),
  validTo: z.string(),
});

export const AddOnRateSchema = z.object({
  id: z.number().optional(),
  addOnId: z.number(),
  code: z.string(),
  dailyPrice: z.number(),
  weeklyPrice: z.number().optional(),
  monthlyPrice: z.number().optional(),
  pricingStrategy: z.string(),
});

/**
 * QuotePriceSchema based on the API response structure
 */
const QuotePriceSchema = z.object({
  quoteId: z.string(),
  offerId: z.string(),
  expiry: z.number(),
  currency: z.string(),
  extCarGroup: z.string(),
  groupId: z.number(),
  available: z.boolean(),
  vatPercentage: z.string(),
  pricePerDay: z.number(),
  pricePerDayInclVat: z.number(),
  cdwPerDay: z.number(),
  soldDays: z.number(),
  cdwOn: z.boolean(),
  finalPrice: z.number(),
  authAmount: z.number(),
  discountPercentage: z.number(),
  dailyKmsAllowance: z.number(),
  extraKmsCharge: z.number(),
  cdwDeductible: z.number(),
  priceDetails: z.object({
    rentalSum: z.number(),
    vat: z.number(),
    cdw: z.number(),
    extraSum: z.number(),
    dropOffSum: z.number(),
    dropOffCharge: z.number(),
    yaqeenDropOffCharge: z.number(),
    discount: z.number(),
    discountPercentage: z.number(),
    addOnBreakdown: z.object({
      addOnSum: z.number(),
      addOnSumInclVat: z.number(),
      addOns: z.array(
        z.object({
          addOnId: z.number(),
          code: z.string(),
          quantity: z.number(),
          price: z.number(),
          priceInclVat: z.number(),
        })
      ),
    }),
  }),
  b2BPaymentBreakUp: z.object({
    payByDriver: z.number(),
    payByCompany: z.number(),
  }),
  group: VehicleGroupDetailsSchema,
  vehiclePlateNo: z.string(),
  promoted: z.boolean(),
  pickupBranchId: z.number(),
  dropOffBranchId: z.number(),
  pickupDateTime: z.number(),
  dropOffDateTime: z.number(),
  pickupBranch: BranchSchema,
  dropOffBranch: BranchSchema,
  vehicleGroup: z.object({
    ...VehicleGroupDetailsSchema.shape,
    vehiclePlateNumber: z.string(),
  }),
  addons: z.array(z.any()).optional(),
  insurances: z.array(InsuranceItemSchema),
  pickUpDateInDestZone: z.number(),
  dropOffDateInDestZone: z.number(),
  debtorAccountNumber: z.string(),
  tariffPlan: z.object({
    tariffRate: TariffRateSchema,
    addOnRates: z.array(AddOnRateSchema),
    bookingTariffDetail: z.object({
      rateType: z.string(),
      rentalPrice: z.number(),
      cdwPrice: z.number(),
      cdwDeductible: z.number(),
      dailyKmsAllowance: z.number(),
      extraKmsCharge: z.number(),
      authAmount: z.number(),
      cdwPricePerDay: z.number(),
      pricePerDay: z.number(),
    }),
  }),
  bookingType: z.string(),
});

const calculatedPriceSchema = z.object({
  quoteId: z.string(),
  offerId: z.string(),
  expiry: z.number(),
  currency: z.string(),
  includedComprehensiveInsurance: z.boolean(),
  addOns: z.array(z.any()),
  priceDetail: z.object({
    rentalPerDay: z.string(),
    rentalPerDayInclVat: z.string(),
    rentalAmount: z.string(),
    insuranceAmount: z.string(),
    totalAddOnAmount: z.string(),
    vatPercentage: z.string(),
    vatAmount: z.string(),
    totalSum: z.string(),
  }),
  vehicleDetail: z.object({
    plateNumber: z.string(),
    vehicleGroupId: z.number(),
    actualVehicleGroupId: z.number(),
  }),
  tariffDetail: z.object({
    insurancePerDay: z.string(),
    totalInsuranceAmount: z.string(),
    insuranceDeductible: z.string(),
    authorizationAmount: z.string(),
    dailyKmsAllowance: z.number(),
    extraKmsCharge: z.string(),
  }),
  totalRentalDurationSeconds: z.number(),
  soldDays: z.string(),
  soldDaysInSeconds: z.number(),
  allowedLateHours: z.number(),
  totalAllowedKms: z.number(),
  remainingAmount: z.string(),
  driverPaidAmount: z.string(),
  request: z.object({
    pickupBranchId: z.number(),
    dropOffBranchId: z.number(),
    pickupDateTime: z.number(),
    dropOffDateTime: z.number(),
    insuranceId: z.number(),
    insuranceIds: z.array(z.number()),
    addOnIds: z.array(z.any()),
    vehicleGroupId: z.number(),
    vehicleGroupCode: z.string(),
    vehiclePlateNo: z.string(),
    actualVehicleGroupId: z.number(),
    driverUid: z.string(),
  }),
});

export type QuotePrice = z.infer<typeof QuotePriceSchema>;
export type Pos = z.infer<typeof PosSchema>;
export type Booking = z.infer<typeof BookingDetailsSchema>;
export type Payments = z.infer<typeof PaymentsSchema>;
export type Agreement = z.infer<typeof AgreementSchema>;
export type PaymentData = Payments["data"];
export type CalculatePrice = z.infer<typeof CalculatePriceSchema>;
export type VehicleInspection = z.infer<typeof vehicleInspectionDetailsSchema>;
export type ClosingPrice = z.infer<typeof ClosingPriceSchema>;
export type CalculatedPrice = z.infer<typeof calculatedPriceSchema>;
export type BranchDetail = z.infer<typeof BranchDetailSchema>;
export type VehicleDetail = z.infer<typeof SuggestedVehicleSchema>;
export type AddonRate = z.infer<typeof AddOnRateSchema>;
export type CancelBookingRequest = z.infer<typeof CancelBookingRequestSchema>;
export type BookingAddOns = z.infer<typeof AddOnsSchema>;
export type BookingDiscount = z.infer<typeof DiscountSchema>;
export {
  BookingDetailsSchema,
  LoyaltySchema,
  VehicleGroupSchema,
  SingleInsuranceSchema,
  InsuranceSchema,
  AddonSchema,
  AddonsSchema,
  PaymentsSchema,
  UpdateBookingRequestSchema,
  CalculatePriceSchema,
  QuotePriceSchema,
  calculatedPriceSchema,
  CancelBookingRequestSchema,
};
