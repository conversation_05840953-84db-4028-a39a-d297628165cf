import { initContract } from "@ts-rest/core";
import { z } from "zod";

const c = initContract();

const SERVICE = "/core-yaqeen-service/yaqeen";

const pricingStrategy = z.enum(["DURATION", "FIXED"]);

const errorResponse = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

const serverFailureError = z.object({
  code: z.string(),
  desc: z.string(),
});

const paginationQuery = z.object({
  query: z.string().optional(),
  page: z.number().optional(),
  size: z.number().optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
});

const tariffRates = z.array(
  z.object({
    id: z.string().optional(),
    type: z.enum(["dynamic", "fixed"]),
    tariffRateCardId: z.string().optional(),
    maxAllowedDiscount: z.number().optional(),
    carGroupId: z.string(),
    carMaterialId: z.string().optional(),
    carGroupCode: z.string(),
    currency: z.enum(["SAR"]),
    dailyPrice: z.number(),
    weeklyPrice: z.number(),
    monthlyPrice: z.number(),
    quarterlyPrice: z.number().optional(),
    halfYearlyPrice: z.number().optional(),
    annualPrice: z.number().optional(),
    dailyCdw: z.number(),
    weeklyCdw: z.number(),
    monthlyCdw: z.number(),
    quarterlyCdw: z.number().optional(),
    halfYearlyCdw: z.number().optional(),
    annualCdw: z.number().optional(),
    pricingStrategy,
    description: z.string(),
    rentalDiscountPercentage: z.number(),
    cdwDiscountPercentage: z.number(),
    dailyKmDiscount: z.number(),
    extraKmDiscount: z.number(),
    extraKmCharges: z.number().optional(),
    dailyKmAllowance: z.number(),
    cdwDeductibles: z.number().optional(),
    dailyAuthAmount: z.number(),
    weeklyAuthAmount: z.number(),
    monthlyAuthAmount: z.number(),
    quarterlyAuthAmount: z.number().optional(),
    halfYearlyAuthAmount: z.number().optional(),
    annualAuthAmount: z.number().optional(),
    /** Date */
    createdOn: z.string().optional(),
    /** Date */
    updatedOn: z.string().optional(),
    createdBy: z.string().optional(),
    updatedBy: z.string().optional(),
  })
);

const tariffCardDetails = z.object({
  id: z.string(),
  tariffIdentifierKey: z.enum(["B2C", "B2B"]),
  baseTariffRateCardId: z.number().optional(),
  type: z.enum(["fixed", "dynamic"]),
  tariffIdentifierValue: z.enum(["DEFAULT"]),
  tariffRateCode: z.number().optional(),
  tariffRateName: z.string(),
  tariffRates,
  status: z.enum(["ACTIVE", "DRAFT", "APPROVED", "IN_APPROVAL"]),
  currency: z.string(),
  /** Date */
  validFrom: z.date(),
  /** Date */
  validTill: z.date(),
  /** Date */
  createdOn: z.string(),
  /** Date */
  updatedOn: z.string(),
  createdBy: z.string(),
  updatedBy: z.string(),
});

const tariffCardDetailsReq = z.object({
  id: z.string(),
  tariffIdentifierKey: z.enum(["B2C", "B2B"]),
  baseTariffRateCardId: z.number().optional(),
  type: z.enum(["fixed", "dynamic"]),
  tariffIdentifierValue: z.enum(["DEFAULT"]),
  tariffRateCode: z.number().optional(),
  tariffRateName: z.string(),
  tariffRates,
  currency: z.string(),
  /** Date */
  validFrom: z.string(),
  /** Date */
  validTill: z.string(),
});

const tariffCards = z.object({
  content: z.array(tariffCardDetails),
  pageable: z.object({
    pageNumber: z.number(),
    pageSize: z.number(),
    sort: z.object({ unsorted: z.boolean(), sorted: z.boolean(), empty: z.boolean() }),
    offset: z.number(),
    unpaged: z.boolean(),
    paged: z.boolean(),
  }),
  totalPages: z.number(),
  totalElements: z.number(),
  numberOfElements: z.number(),
  first: z.boolean(),
  last: z.boolean(),
  empty: z.boolean(),
  size: z.number(),
  sort: z.string(),
});

const addOnsRatesCardsSchema = z.object({
  addOnResponses: z.array(
    z.object({
      id: z.number(),
      addOnId: z.number(),
      carGroupId: z.number(),
      carGroupCode: z.string(),
      name: z.string(),
      description: z.string().optional(),
      dailyPrice: z.number(),
      weeklyPrice: z.number(),
      monthlyPrice: z.number(),
      quarterlyPrice: z.number(),
      halfYearlyPrice: z.number(),
      annualPrice: z.number(),
      pricingStrategy,
      /** Date */
      validFrom: z.date(),
      /** Date */
      validTill: z.date(),
      status: z.enum(["ACTIVE", "DRAFT"]),
      currency: z.string(),
      isValid: z.boolean(),
      /** Date */
      createdOn: z.string(),
      /** Date */
      updatedOn: z.string(),
      createdBy: z.string(),
      updatedBy: z.string(),
    })
  ),
});

export const addOnsRatesCardsReqSchema = z.object({
  name: z.string(),
  pricingStrategy,
  description: z.string().optional(),
  addOnId: z.string(),
  carGroupCode: z.string().optional(),
  currency: z.string(),
  dailyPrice: z.number(),
  weeklyPrice: z.number(),
  monthlyPrice: z.number(),
  quarterlyPrice: z.number(),
  halfYearlyPrice: z.number(),
  annualPrice: z.number(),
  isValid: z.boolean().optional(),
  validFrom: z.string(),
  validTill: z.string(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
});

export type ErrorResponse = z.infer<typeof errorResponse | typeof serverFailureError>;
export type TariffCards = z.infer<typeof tariffCards>;
export type TariffCardDetails = z.infer<typeof tariffCardDetails>;
export type TariffCardDetailsReq = z.infer<typeof tariffCardDetailsReq>;
export type AddOnsRatesCards = z.infer<typeof addOnsRatesCardsSchema>;
export type AddOnsRatesCardsReq = z.infer<typeof addOnsRatesCardsReqSchema>;

export const tariffContract = c.router(
  {
    getTariffRateCards: {
      method: "GET",
      path: `${SERVICE}/v1/tariff-cards`,
      query: z.intersection(
        z.object({
          type: z.enum(["B2B", "B2C"]),
        }),
        paginationQuery
      ),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: tariffCards,
      },
    },
    getActiveTariffRateCards: {
      method: "GET",
      path: "/core-tariff-service/internal/api/v1/tariff-cards/active",
      query: z.intersection(
        z.object({
          accountNo: z.string().optional(),
          pickUpDateTime: z.string().optional(),
        }),
        paginationQuery
      ),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.array(tariffCardDetails),
      },
    },
    getTariffCardById: {
      method: "GET",
      path: `${SERVICE}/v1/tariff-cards/:id`,
      pathParams: z.object({
        id: z.number(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: tariffCardDetails,
      },
    },
    createTariffRate: {
      method: "POST",
      path: `${SERVICE}/v1/tariff-cards`,
      body: z.object({
        tariffIdentifierKey: z.string(),
        currency: z.string(),
        tariffRateName: z.string(),
        validFrom: z.string(),
        validTill: z.string(),
        tariffRates,
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        201: tariffCardDetailsReq,
      },
    },
    updateTariffRate: {
      method: "PUT",
      path: `${SERVICE}/v1/tariff-cards/:id`,
      pathParams: z.object({
        id: z.string(),
      }),
      body: z.object({
        tariffIdentifierKey: z.string(),
        currency: z.string(),
        tariffRateName: z.string(),
        validFrom: z.string(),
        validTill: z.string(),
        tariffRates,
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        200: tariffCardDetailsReq,
      },
    },
    deleteTariffRate: {
      method: "PUT",
      path: `${SERVICE}/v1/tariff-cards/:id/status?newStatus=DELETED`,
      pathParams: z.object({
        id: z.string(),
      }),
      body: null,
      responses: {
        404: errorResponse,
        400: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        200: tariffCardDetailsReq,
      },
    },
    updateTariffCardStatus: {
      method: "PUT",
      path: `${SERVICE}/v1/tariff-cards/:id/status`,
      pathParams: z.object({
        id: z.string(),
      }),
      query: z.object({
        newStatus: z.enum(["DRAFT", "IN_APPROVAL", "APPROVED", "ACTIVE"]),
      }),
      body: null,
      responses: {
        404: errorResponse,
        400: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        200: tariffCardDetailsReq,
      },
    },
    getAddOnsRates: {
      method: "GET",
      path: `${SERVICE}/v1/add-on-rate-cards`,
      query: z.object({
        carGroupCode: z.string().optional(),
        addOnIds: z.string().optional(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: addOnsRatesCardsSchema,
      },
    },
    createAddOnsRates: {
      method: "POST",
      path: `${SERVICE}/v1/add-on-rate-cards`,
      body: z.object({
        addOnRequests: z.array(addOnsRatesCardsReqSchema),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        401: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        201: addOnsRatesCardsSchema,
      },
    },
    updateAddOnsRates: {
      method: "PUT",
      path: `${SERVICE}/v1/add-on-rate-cards`,
      body: z.object({
        addOnRequests: z.array(z.intersection(addOnsRatesCardsReqSchema, z.object({ id: z.string() }))),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        401: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        200: addOnsRatesCardsSchema,
      },
    },
    deleteAddOnsRates: {
      method: "DELETE",
      path: `${SERVICE}/v1/add-on-rate-cards/:id`,
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        401: errorResponse,
        500: serverFailureError,
        204: z.object({
          type: z.string(),
        }),
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);
