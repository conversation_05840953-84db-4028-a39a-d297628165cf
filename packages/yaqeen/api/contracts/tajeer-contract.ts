import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE as YAQEEN_SERVICE_CONSTANT } from "../constant";
import { TajeerAgreement } from "./schema";

export type ITajeerAgreement = z.infer<typeof TajeerAgreement>;
const c = initContract();

const baseError = z.object({
  code: z.string(),
  desc: z.string(),
});

const errorResponse = baseError.extend({
  reqId: z.string(),
  desc: z.string(),
  code: z.string(),
  errorKey: z.string(),
});

const serverFailureError = baseError;

const YAQEEN_SERVICE = process.env.MOCK === "true" ? "/api/mock" : YAQEEN_SERVICE_CONSTANT;
export const tajeerContract = c.router(
  {
    initiateTajeer: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/initiate/tajeer`,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: TajeerAgreement,
      },
      body: z.object({}),
    },
    initiateTamm: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/initiate/tamm`,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: TajeerAgreement,
      },
      body: z.object({}),
    },
    getAllTajeerAgreements: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization`,
      query: z.object({
        referenceNumber: z.string(),
        bookingNo: z.string().optional(),
        type: z.string().optional(),
        page: z.number().optional(),
        size: z.number().optional(),
        status: z.string().optional(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          data: z.array(TajeerAgreement),
        }),
      },
    },
    confirmTajeer: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/confirm/tajeer`,
      body: z.object({
        otp: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: TajeerAgreement,
      },
    },
    confirmTamm: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/confirm/tamm`,
      body: z.object({
        otp: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: TajeerAgreement,
      },
    },
    convertToAgreement: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements`,
      body: z.object({
        bookingNo: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        201: TajeerAgreement,
      },
    },
    resendOTP: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/tajeer/resend-otp`,
      body: z.null(),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: TajeerAgreement,
      },
    },
    addSecurityDeposit: {
      method: "POST",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization/:bookingNo/add/security-deposit`,
      body: z.object({
        depositAmount: z.string(),
        cardType: z.enum(["CREDIT_CARD", "DEBIT_CARD"]),
        entryType: z.enum(["MANUAL", "POS"]),
        approvalCode: z.string(),
        cardLast4Digit: z.string(),
        posMachine: z.string(),
        branchId: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: TajeerAgreement,
      },
    },
    getDriverAuthStatus: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/agreements/driver/authorization`,
      query: z.object({
        referenceNumber: z.string(),
        page: z.number().optional(),
        size: z.number().optional(),
        type: z.string().optional(),
        status: z.string().optional(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
        }),
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);
