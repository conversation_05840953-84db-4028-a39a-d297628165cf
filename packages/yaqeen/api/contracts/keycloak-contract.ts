import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema } from "./common";
import { KEYCLOAK_SERVICE } from "@/api/constant";

const clientId = process.env.KEYCLOAK_CLIENT_ID;

const c = initContract();

const KeycloakUserSchema = z.object({
  id: z.string(),
  username: z.string(),
  enabled: z.boolean().optional(),
  firstName: z.string().optional().nullable(),
  lastName: z.string().optional().nullable(),
  email: z.string().optional().nullable(),
  emailVerified: z.boolean().optional(),
  createdTimestamp: z.number().optional(),
  attributes: z.record(z.string(), z.array(z.string())).optional(),
  access: z.record(z.string(), z.boolean()).optional(),
  disableableCredentialTypes: z.array(z.string()).optional(),
  requiredActions: z.array(z.string()).optional(),
  notBefore: z.number().optional(),
  totp: z.boolean().optional(),
});

const KeycloakRoleSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional().nullable(),
  composite: z.boolean().optional(),
  clientRole: z.boolean().optional(),
  containerId: z.string().optional(),
});

interface KeycloakGroupType {
  id: string;
  name: string;
  path: string;
  subGroups?: KeycloakGroupType[];
  attributes?: Record<string, string[]>;
}

const KeycloakGroupSchema: z.ZodType<KeycloakGroupType> = z.lazy(() =>
  z.object({
    id: z.string(),
    name: z.string(),
    path: z.string(),
    subGroups: z.array(KeycloakGroupSchema).optional(),
    attributes: z.record(z.string(), z.array(z.string())).optional(),
  })
);

const KeycloakUserCountSchema = z.number();

export const KeycloakPermissionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: z.string(),
  logic: z.string().optional(),
  decisionStrategy: z.string().optional(),
  resources: z.array(z.string()).optional(),
  resourceType: z.string().optional(),
  scopes: z.array(z.string()).optional(),
  policies: z.array(z.string()).optional(),
});

export const KeycloakPolicySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: z.string(),
  logic: z.string().optional(),
  decisionStrategy: z.string().optional(),
  config: z.record(z.string(), z.string()).optional(),
});

export const AppPermissionSchema = z.object({
  service: z.string(),
  feature: z.string(),
  permissions: z.array(
    z.object({
      scope: z.string(),
      uri: z.string(),
      method: z.string(),
      description: z.string(),
      roles: z.array(z.string()).optional(),
    })
  ),
});

export const MappedPermissionSchema = z.object({
  keycloakPermission: KeycloakPermissionSchema,
  appPermissions: z.array(
    z.object({
      service: z.string(),
      feature: z.string(),
      scope: z.string(),
      uri: z.string(),
      method: z.string(),
      description: z.string(),
    })
  ),
});

// Schema for user credentials
export const KeycloakUserCredentialSchema = z.object({
  type: z.string(),
  value: z.string(),
  temporary: z.boolean().optional(),
});

// Schema for creating a new user
export const CreateKeycloakUserSchema = z.object({
  username: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  enabled: z.boolean().optional(),
  credentials: z.array(KeycloakUserCredentialSchema).optional(),
  requiredActions: z.array(z.string()).optional(),
});

// Type for user credentials
export type KeycloakUserCredential = z.infer<typeof KeycloakUserCredentialSchema>;

// Type for creating a new user
export type CreateKeycloakUser = z.infer<typeof CreateKeycloakUserSchema>;

export type KeycloakPermission = z.infer<typeof KeycloakPermissionSchema>;
export type KeycloakPolicy = z.infer<typeof KeycloakPolicySchema>;
export type AppPermission = z.infer<typeof AppPermissionSchema>;
export type MappedPermission = z.infer<typeof MappedPermissionSchema>;
export type KeycloakUser = z.infer<typeof KeycloakUserSchema>;
export type KeycloakRole = z.infer<typeof KeycloakRoleSchema>;
export type KeycloakGroup = KeycloakGroupType;

export const keycloakContract = c.router(
  {
    getUsers: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users`,
      query: z.object({
        first: z.string().optional(),
        max: z.string().optional(),
        search: z.string().optional(),
      }),
      responses: {
        200: z.array(KeycloakUserSchema),
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    getUserCount: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/count`,
      query: z.object({
        search: z.string().optional(),
      }),
      responses: {
        200: KeycloakUserCountSchema,
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    getUserById: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:id`,
      pathParams: z.object({ id: z.string() }),
      responses: {
        200: KeycloakUserSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getUserRoles: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/role-mappings/realm`,
      pathParams: z.object({ userId: z.string() }),
      responses: {
        200: z.array(KeycloakRoleSchema),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getUserGroups: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/groups`,
      pathParams: z.object({ userId: z.string() }),
      responses: {
        200: z.array(KeycloakGroupSchema),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getAllRealmRoles: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles`,
      responses: {
        200: z.array(KeycloakRoleSchema),
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    getAllGroups: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/groups`,
      responses: {
        200: z.array(KeycloakGroupSchema),
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    addRealmRolesToUser: {
      method: "POST",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/role-mappings/realm`,
      pathParams: z.object({ userId: z.string() }),
      body: z.array(KeycloakRoleSchema),
      responses: {
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    removeRealmRolesFromUser: {
      method: "DELETE",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/role-mappings/realm`,
      pathParams: z.object({ userId: z.string() }),
      body: z.array(KeycloakRoleSchema),
      responses: {
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    addUserToGroup: {
      method: "PUT",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/groups/:groupId`,
      pathParams: z.object({
        userId: z.string(),
        groupId: z.string(),
      }),
      body: z.object({}),
      responses: {
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    removeUserFromGroup: {
      method: "DELETE",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/groups/:groupId`,
      pathParams: z.object({
        userId: z.string(),
        groupId: z.string(),
      }),
      body: z.object({}),
      responses: {
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getClientPermissions: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/clients/${clientId}/authz/resource-server/permission`,
      responses: {
        200: z.array(KeycloakPermissionSchema),
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    getClientPolicies: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/clients/${clientId}/authz/resource-server/policy`,
      query: z.object({ max: z.string().optional() }),
      responses: {
        200: z.array(KeycloakPolicySchema),
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    getRolePermissions: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles/:roleName/permissions`,
      pathParams: z.object({ roleName: z.string() }),
      responses: {
        200: z.array(KeycloakPermissionSchema),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getRoleById: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles/:roleName`,
      pathParams: z.object({ roleName: z.string() }),
      responses: {
        200: KeycloakRoleSchema,
        404: ErrorSchema,
      },
    },

    createRealmRole: {
      method: "POST",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles`,
      body: z.object({
        name: z.string(),
        description: z.string().optional(),
        composite: z.boolean().optional(),
        clientRole: z.boolean().optional(),
        containerId: z.string().optional(),
      }),
      responses: {
        201: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        403: ErrorSchema,
        409: ErrorSchema,
      },
    },

    getUsersInRole: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles/:roleName/users`,
      pathParams: z.object({ roleName: z.string() }),
      responses: {
        200: z.array(KeycloakUserSchema),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getGroups: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/groups`,
      responses: {
        200: z.array(KeycloakGroupSchema),
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    getGroupById: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/groups/:id`,
      pathParams: z.object({ id: z.string() }),
      responses: {
        200: KeycloakGroupSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getGroupMembers: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/groups/:id/members`,
      pathParams: z.object({ id: z.string() }),
      responses: {
        200: z.array(KeycloakUserSchema),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getPolicyById: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/clients/${clientId}/authz/resource-server/policy/:policyId`,
      pathParams: z.object({ policyId: z.string() }),
      responses: {
        200: KeycloakPolicySchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    updateRolePolicy: {
      method: "PUT",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/clients/${clientId}/authz/resource-server/policy/role/:policyId`,
      pathParams: z.object({ policyId: z.string() }),
      body: z.object({
        id: z.string(),
        name: z.string(),
        type: z.string(),
        logic: z.string().optional(),
        decisionStrategy: z.string().optional(),
        roles: z.array(
          z.object({
            id: z.string(),
            required: z.boolean(),
          })
        ),
      }),
      responses: {
        201: z.null(),
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    assignPermissionToRole: {
      method: "PUT",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/clients/${clientId}/authz/resource-server/permission/:permissionId/policy`,
      pathParams: z.object({ permissionId: z.string() }),
      body: z.object({ roleId: z.string() }),
      responses: {
        200: z.object({ success: z.boolean() }),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    removePermissionFromRole: {
      method: "DELETE",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/clients/${clientId}/authz/resource-server/permission/:permissionId/policy/:roleId`,
      pathParams: z.object({
        permissionId: z.string(),
        roleId: z.string(),
      }),
      responses: {
        201: z.null(),
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    createUser: {
      method: "POST",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users`,
      body: CreateKeycloakUserSchema,
      responses: {
        201: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        403: ErrorSchema,
      },
    },

    updateUser: {
      method: "PUT",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId`,
      pathParams: z.object({ userId: z.string() }),
      body: CreateKeycloakUserSchema,
      responses: {
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        403: ErrorSchema,
        404: ErrorSchema,
      },
    },

    resetPassword: {
      method: "PUT",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/reset-password`,
      pathParams: z.object({ userId: z.string() }),
      body: z.object({
        type: z.string(),
        value: z.string(),
        temporary: z.boolean().optional(),
      }),
      responses: {
        204: z.null(),
        400: ErrorSchema,
        401: ErrorSchema,
        403: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getRoleByName: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles/:roleName`,
      pathParams: z.object({ roleName: z.string() }),
      responses: {
        200: KeycloakRoleSchema,
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    addUsersToRole: {
      method: "POST",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles/:roleName/users`,
      pathParams: z.object({ roleName: z.string() }),
      body: z.array(z.string()),
      responses: {
        204: z.null(),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    removeUserFromRole: {
      method: "DELETE",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/roles/:roleName/users/:userId`,
      pathParams: z.object({
        roleName: z.string(),
        userId: z.string(),
      }),
      responses: {
        204: z.null(),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getUserCredentials: {
      method: "GET",
      path: `${KEYCLOAK_SERVICE}/admin/realms/lumi/users/:userId/credentials`,
      pathParams: z.object({
        userId: z.string(),
      }),
      responses: {
        200: z.array(z.object({
          id: z.string(),
          type: z.string(),
          userLabel: z.string().optional(),
          createdDate: z.number().optional(),
          temporary: z.boolean().optional(),
        })),
        401: ErrorSchema,
        404: ErrorSchema,
      },
      summary: "Get user credentials to check if password is set",
    },
  },
  {
    pathPrefix: "",
    strictStatusCodes: true,
  }
);
