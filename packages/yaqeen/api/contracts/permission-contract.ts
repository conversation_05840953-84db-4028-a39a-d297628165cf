import { initContract } from "@ts-rest/core";
import { YAQEEN_SERVICE } from "../constant";
import { z } from "zod";

const c = initContract();

// Define the schema for permission scope
const PermissionScopeSchema = z.enum(["read", "write", "admin"]);

// Define the schema for a single permission
const PermissionSchema = z.object({
  scope: z.string(),
  uri: z.string(),
  method: z.string(),
  roles: z.array(z.string()).nullable(),
});

// Define the schema for a feature's permissions
const FeaturePermissionsSchema = z.object({
  fileName: z.string(),
  service: z.string(),
  feature: z.string(),
  permissions: z.array(PermissionSchema),
});

// Define the schema for the error response
const ErrorSchema = z.object({
  message: z.string(),
  status: z.number(),
});

// Define the permission contract
export const permissionContract = c.router({
  /**
   * Get All Permissions
   * This endpoint fetches all permissions from the system
   */
  getAllPermissions: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/permissions`,
    responses: {
      200: z.array(FeaturePermissionsSchema),
      401: ErrorSchema,
      403: ErrorSchema,
    },
    summary: "Get all permissions",
    description: "Fetches all permissions from the system",
  },
});

// Export the contract and schemas
export type PermissionContract = typeof permissionContract;
export type Permission = z.infer<typeof PermissionSchema>;
export type FeaturePermissions = z.infer<typeof FeaturePermissionsSchema>;