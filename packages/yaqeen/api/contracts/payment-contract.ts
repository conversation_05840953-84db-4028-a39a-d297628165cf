import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "../constant";
import { PromotionResponseSchema } from "./schema";
import { ErrorSchema, ServerFailureError } from "./common";

const c = initContract();

export const PaymentDone = z.object({
  receiptNo: z.string(),
  amount: z.number(),
  status: z.enum(["SUCCESS", "FAILED"]),
});

const PaymentDetailSchema = z.object({
  id: z.number(),
  binNumber: z.string(),
  customerId: z.string(),
  expiryMonth: z.string(),
  expiryYear: z.string(),
  last4Digit: z.string(),
  nameOnCard: z.string(),
  paymentId: z.string(),
  pyScheme: z.string(),
});

const PaymentInfoSchema = z.object({
  id: z.number(),
  createdOn: z.number(),
  updatedOn: z.number(),
  amount: z.number(),
  billingCountryCode: z.string(),
  currency: z.string(),
  customerEmail: z.string(),
  customerName: z.string(),
  referenceId: z.string(),
  paymentId: z.string(),
  trackId: z.string(),
  responseSummary: z.string(),
  authCode: z.string(),
  responseCode: z.string(),
  paymentStatus: z.string(),
  approved: z.boolean(),
  is3ds: z.boolean(),
  initiatedFor: z.string(),
  paymentDetail: PaymentDetailSchema,
  paidThrough: z.string(),
});

const RefundSchema = z.object({
  id: z.number(),
  createdOn: z.number(),
  updatedOn: z.number(),
  amount: z.number(),
  currency: z.string(),
  customerEmail: z.string(),
  customerName: z.string(),
  referenceId: z.string(),
  paymentId: z.string(),
  trackId: z.string(),
  responseSummary: z.string(),
  refundStatus: z.string(),
  initiatedFor: z.string(),
  bookingId: z.string(),
});

const RewardsSchema = z.object({
  provider: z.string(),
  customerIdentifier: z.string(),
  value: z.string(),
  valueType: z.string(),
  status: z.string(),
  trackId: z.string(),
  bookingDate: z.number(),
  displayId: z.string(),
  rewardedOn: z.number(),
  netAmount: z.string(),
});

const QuickPaySchema = z.object({
  id: z.number(),
  trackId: z.string(),
  payType: z.string(),
  amount: z.string(),
  currency: z.string(),
  referenceType: z.string(),
  referenceNo: z.string(),
  paymentLink: z.string(),
  paymentDetails: z
    .object({
      status: z.boolean(),
    })
    .optional(),
  customerName: z.string(),
  customerIdentifierType: z.string(),
  customerIdentifier: z.string(),
  expireAt: z.number(),
  remarks: z.string().optional(),
  createdOn: z.number(),
  updatedOn: z.number(),
});

const PaymentRefundSchema = z.object({
  createdOn: z.number(),
  id: z.number(),
  referenceId: z.string(),
  paymentId: z.string(),
  amount: z.string(),
  actionId: z.string(),
  status: z.string(),
});

const RefundDetailsSchema = z.object({
  amount: z.string(),
  refundMode: z.string(),
  refundStatus: z.string(),
  recordedBy: z.string().nullable(),
  approvedTime: z.number().nullable(),
  recipientBankDetail: z
    .object({
      recipientAccountName: z.string().optional(),
      recipientIBAN: z.string().optional(),
      recipientBankName: z.string().optional(),
      ibanLetterImageUrl: z.string().optional(),
    })
    .optional(),
});

const BookingTransactionSchema = z.object({
  receiptNo: z.string(),
  trackId: z.string().optional(),
  paymentId: z.string().optional(),
  id: z.string().optional(),
  amount: z.string(),
  createdTime: z.number(),
  type: z.enum(["RefundRequest", "Refund", "Payment"]),
  mode: z.enum(["BANK_TRANSFER", "CASH", "POS", "ONLINE"]),
  status: z.enum(["REQUESTED", "SUCCESS"]),
  refundDetails: RefundDetailsSchema.optional(),
});

export const paymentContract = c.router({
  getPosIds: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/branch/pos/:id`,
    pathParams: z.object({
      id: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(
          z.object({
            posId: z.string(),
          })
        ),
      }),
    },
  },
  acceptPayment: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/bookings/:bookingId/payments`,
    pathParams: z.object({
      bookingId: z.string(),
    }),
    body: z.object({
      amount: z.number(),
      branchId: z.number(),
      paidThrough: z.enum(["CASH", "POS"]),
      initiatedFor: z.enum(["SECURITY_DEPOSIT_AUTHORIZATION", "BOOKING"]),
      posDetail: z
        .object({
          posId: z.string(),
          approvalCode: z.string(),
          last4Digit: z.string(),
        })
        .optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: PaymentDone,
    },
  },
  getPromotions: {
    method: "GET",
    path: `/pricing-service//api/promotions/active`,
    query: z.object({
      quoteId: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: PromotionResponseSchema,
    },
  },
  getCorporatePromotions: {
    method: "GET",
    path: `/pricing-service/api/promotions/corporate/active`,
    query: z.object({
      quoteId: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: PromotionResponseSchema,
    },
  },
  getPayments: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/payment`,
    query: z.object({
      size: z.number().optional(),
      page: z.number().optional(),
      query: z.string().optional(),
    }),

    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(PaymentInfoSchema),
      }),
    },
  },
  getPaymentDetail: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/payment/find/:paymentId`,
    pathParams: z.object({
      paymentId: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: PaymentInfoSchema,
    },
  },
  getRefunds: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/refund`,
    query: z.object({
      page: z.number().optional(),
      size: z.number().optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(RefundSchema),
      }),
    },
  },
  getRewards: {
    method: "GET",
    path: "/loyalty-service/v1/reward/list",
    query: z.object({
      startDateTime: z.string().optional(),
      endDateTime: z.string().optional(),
      rewardProviders: z.string().optional(),
      status: z.string().optional(),
      page: z.number().optional(),
      size: z.number().optional(),
      sort: z.string(),
      order: z.string(),
      query: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(RewardsSchema),
      }),
    },
  },
  getQuickPayLinks: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/quickPay/search`,
    query: z.object({
      page: z.number().optional(),
      size: z.number().optional(),
      sort: z.string(),
      order: z.string(),
      query: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(QuickPaySchema),
      }),
    },
  },

  extractReconciliationReport: {
    method: "GET",
    path: "/loyalty-service/v1/reward/report",
    query: z.object({
      startDateTime: z.string(),
      endDateTime: z.string(),
      rewardProviders: z.string(),
      status: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.custom<Blob>(),
    },
  },
  getPaymentRefunds: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/payment/refunds/:paymentId`,
    pathParams: z.object({
      paymentId: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.array(PaymentRefundSchema),
    },
  },
  getBookingTransactions: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/bookings/:bookingId/transactions`,
    pathParams: z.object({
      bookingId: z.string(),
    }),
    query: z.object({
      notInitiatedFor: z.string().optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(BookingTransactionSchema),
      }),
    },
  },
  quickPaySearch: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/quickPay/search`,
    query: z.object({
      payTypes: z.string().optional(),
      referenceNo: z.string().optional(),
      order: z.string().optional(),
      sort: z.string().optional(),
      page: z.number().optional(),
      size: z.number().optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(QuickPaySchema),
      }),
    },
  },
});

export type BookingTransaction = z.infer<typeof BookingTransactionSchema>;
export type QuickPay = z.infer<typeof QuickPaySchema>;
