import { z } from "zod";

export const PaginationResponse = z.object({
  pageable: z.object({
    pageNumber: z.number(),
    pageSize: z.number(),
    sort: z.object({ unsorted: z.boolean(), sorted: z.boolean(), empty: z.boolean() }),
    offset: z.number(),
    unpaged: z.boolean(),
    paged: z.boolean(),
  }),
  totalPages: z.number(),
  totalElements: z.number(),
  numberOfElements: z.number(),
  first: z.boolean(),
  last: z.boolean(),
  empty: z.boolean(),
  size: z.number(),
  sort: z.string(),
});

export const oldPaginationQuery = z.object({
  query: z.string().optional(),
  page: z.number().optional(),
  pageSize: z.number().optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
});

export const CORE_FLEET_SERVICE = "/core-yaqeen-service/yaqeen/v1/fleet/v1";
export const CORE_FLEET_SERVICE3 = "/core-yaqeen-service/yaqeen/v1/fleet/v3";
