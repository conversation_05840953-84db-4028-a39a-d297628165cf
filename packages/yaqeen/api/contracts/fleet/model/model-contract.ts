import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { PaginationResponse } from "../common";
import { MakeSchema } from "../make/make-contract";
import { YAQEEN_SERVICE } from "@/api/constant";

const c = initContract();

const vehicleClassSchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  enabled: z.boolean(),
});

const specificationSchema = z.object({
  seatingCapacity: z.number(),
  doors: z.number(),
  bootSpace: z.number(),
  luggageCountBig: z.number(),
  luggageCountMedium: z.number(),
  luggageCountSmall: z.number(),
  transmission: z.string(),
  transmissionType: z.string(),
  engineSize: z.number(),
  horsepower: z.number(),
  fuelType: z.string(),
  fuelCapacity: z.number(),
  interiorFeatures: z.record(z.string(), z.boolean()).optional(),
  exteriorFeatures: z.record(z.string(), z.boolean()).optional(),
  safetyFeatures: z.record(z.string(), z.boolean()).optional(),
});

const groupResponseSchema = z.object({
  id: z.number(),
  code: z.string(),
  displayName: z.string().nullable(),
  description: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  faceModelId: z.number(),
  faceModelResponse: z.any().nullable(),
  enabled: z.boolean(),
});

const imageSchema = z.object({
  id: z.number(),
  url: z.string(),
  primary: z.boolean(),
});

const modelSchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  make: MakeSchema,
  vehicleClass: vehicleClassSchema,
  faceModelId: z.number(),
  fleetCount: z.number(),
  vehicleGroup: z.string(),
  specification: specificationSchema,
  version: z.string(),
  series: z.string(),
  materialId: z.string(),
});

const ModelDetailBaseSchema = modelSchema.extend({
  groupResponse: groupResponseSchema,
  primaryImageUrl: z.string(),
  images: z.array(imageSchema),
  fleetCount: z.number(),
});

const ModelDetailSchema = ModelDetailBaseSchema.extend({
  variants: z.array(ModelDetailBaseSchema),
});

const ModelListResponseSchema = z.intersection(
  z.object({
    content: z.array(modelSchema),
  }),
  PaginationResponse
);

const modelListParamsSchema = z.object({
  pageNumber: z.number().optional(),
  pageSize: z.number().optional(),
});

export const modelContract = c.router({
  list: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model`,
    query: modelListParamsSchema,
    responses: {
      200: ModelListResponseSchema,
    },
  },
  details: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model/:id`,
    pathParams: z.object({
      id: z.number(),
    }),
    responses: {
      200: ModelDetailSchema,
    },
  },
});

export type ModelContract = typeof modelContract;
export type Model = z.infer<typeof modelSchema>;
export type ModelDetail = z.infer<typeof ModelDetailSchema>;
export type ModelListResponse = z.infer<typeof ModelListResponseSchema>;
export type ModelListParams = z.infer<typeof modelListParamsSchema>;
