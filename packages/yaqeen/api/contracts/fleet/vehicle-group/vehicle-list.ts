import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { PaginationResponse, oldPaginationQuery, CORE_FLEET_SERVICE } from "../common";
import { ErrorSchema, ServerFailureError } from "../../common";

const c = initContract();

const vehicleGroups = z.intersection(
  z.object({
    content: z.array(
      z.object({
        id: z.string(),
        code: z.string(),
        displayName: z.string(),
        faceModelId: z.string(),
        description: z.object({ en: z.string().optional(), ar: z.string().optional() }),
        enabled: z.boolean(),
        models: z.array(
          z.object({
            id: z.string(),
            name: z.object({ en: z.string().optional(), ar: z.string().optional() }),
          })
        ),
      })
    ),
  }),
  PaginationResponse
);

export type VehicleGroups = z.infer<typeof vehicleGroups>;

export const vehicleGroupsContract = c.router(
  {
    getCarGroups: {
      method: "GET",
      path: `${CORE_FLEET_SERVICE}/vehicle-group`,
      query: oldPaginationQuery,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        503: ServerFailureError,
        200: vehicleGroups,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);
