import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { PaginationResponse, CORE_FLEET_SERVICE } from "../common";
import { ErrorSchema, ServerFailureError } from "../../common";

const c = initContract();

const addonSchema = z.object({
  id: z.string().or(z.number()),
  code: z.string(),
  name: z.object({ en: z.string(), ar: z.string() }),
  description: z.object({ en: z.string().optional(), ar: z.string().optional() }),
  imageUrl: z.string().optional(),
  enabled: z.boolean(),
});

const addonRequestSchema = z.object({
  code: z.string(),
  name: z.object({ en: z.string(), ar: z.string() }),
  description: z.object({ en: z.string().optional(), ar: z.string().optional() }).optional(),
  imageUrl: z.string().optional(),
  enabled: z.boolean().optional(),
});

const conflictError = z.object({
  code: z.string(),
  desc: z.string(),
});

const addons = z.intersection(
  z.object({
    content: z.array(addonSchema),
  }),
  PaginationResponse
);

export const addonContract = c.router(
  {
    getAddons: {
      method: "GET",
      path: `${CORE_FLEET_SERVICE}/addon`,
      query: z.object({
        pageNumber: z.number().optional(),
        pageSize: z.number().optional(),
      }),
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: addons,
      },
    },
    getAddonById: {
      method: "GET",
      path: `${CORE_FLEET_SERVICE}/addon/:id`,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        200: addonSchema,
      },
    },
    createAddon: {
      method: "POST",
      path: `${CORE_FLEET_SERVICE}/addon`,
      body: addonRequestSchema,
      responses: {
        400: ErrorSchema,
        409: conflictError,
        500: ServerFailureError,
        200: addonSchema,
        201: addonSchema,
      },
    },
    updateAddon: {
      method: "PUT",
      path: `${CORE_FLEET_SERVICE}/addon/:id`,
      body: addonRequestSchema,
      responses: {
        400: ErrorSchema,
        404: ErrorSchema,
        409: conflictError,
        500: ServerFailureError,
        200: addonSchema,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);

export type Addon = z.infer<(typeof addonContract.getAddonById.responses)[200]>;
export type ConflictError = z.infer<(typeof addonContract.createAddon.responses)[409]>;
