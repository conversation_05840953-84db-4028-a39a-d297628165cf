import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema } from "./common";

const c = initContract();

const PermissionSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
});

export type Permission = z.infer<typeof PermissionSchema>;

export const authContract = c.router(
  {
    initiate: {
      method: "GET",
      path: "/auth-service/user/auth/login/v2/initiate",
      query: z.object({
        username: z.string().optional(),
        callbackUrl: z.string().optional(),
      }),
      responses: {
        200: z.object({
          requiresRedirect: z.boolean(),
          redirectUrl: z.string(),
        }),
      },
    },
    logout: {
      method: "POST",
      path: "/auth-service/user/auth/logout",
      body: z.object({}),
      responses: {
        200: z.object({}),
      },
    },
    sso: {
      method: "GET",
      path: "/auth-service/user/auth/login/sso/authenticate",
      query: z.object({
        code: z.string(),
        state: z.string(),
      }),
      responses: {
        500: ErrorSchema,
        400: ErrorSchema,
        404: ErrorSchema,
        200: z.object({
          token: z.string(),
          expiresIn: z.number(),
          refreshExpiresIn: z.number(),
          refreshToken: z.string(),
          tokenType: z.string(),
          idToken: z.string(),
          notBeforePolicy: z.number(),
          sessionState: z.string(),
          otherClaims: z.object({}).passthrough(),
          scope: z.string(),
        }),
      },
    },
    permissions: {
      method: "GET",
      path: "/core-user-service/v2/permissions/token",
      query: z.object({
        realm: z.string(),
        clientId: z.string(),
      }),
      responses: {
        200: z.array(PermissionSchema),
        400: ErrorSchema,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);
