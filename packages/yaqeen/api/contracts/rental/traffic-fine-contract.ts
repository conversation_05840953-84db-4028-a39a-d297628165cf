import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "@/api/constant";
import { AgreementInvoiceSchema } from "../schema";
import { ErrorSchema, ServerFailureError } from "../common";
import { AgreementSchema } from "../booking/booking-contract";

const c = initContract();

// Schema for the metadata object
const MetadataSchema = z.object({
  municipality: z.string(),
  violationCode: z.number(),
  violationCodeAr: z.string(),
});

// Schema for each traffic fine item
const TrafficFineItemSchema = z.object({
  id: z.number(),
  ticketNumber: z.string(),
  plateNo: z.string(),
  fineDate: z.number(),
  issuedBranchId: z.number(),
  chargeStatus: z.enum(["DRIVER", "DEBTOR"]),
  paymentStatus: z.enum(["UNPAID", "PAID"]),
  amount: z.number(),
  paymentLink: z.string().url(),
  paymentLinkExpiryDate: z.number(),
  metadata: MetadataSchema,
  agreementNo: z.string(),
  bookingNo: z.string(),
  bookingId: z.number(),
  invoiceNumber: z.string().optional(),
});

// Schema for the sort object
const SortSchema = z.object({
  unsorted: z.boolean(),
  sorted: z.boolean(),
  empty: z.boolean(),
});

// Schema for the pageable object
const PageableSchema = z.object({
  pageNumber: z.number(),
  pageSize: z.number(),
  sort: SortSchema,
  offset: z.number(),
  paged: z.boolean(),
  unpaged: z.boolean(),
});

// Main schema for the traffic fine list response
const TrafficFineListSchema = z.object({
  content: z.array(TrafficFineItemSchema),
  pageable: PageableSchema,
  totalPages: z.number(),
  totalElements: z.number(),
  last: z.boolean(),
  numberOfElements: z.number(),
  first: z.boolean(),
  size: z.number(),
  number: z.number(),
  sort: SortSchema,
  empty: z.boolean(),
});

const trafficFineQueryParams = z.object({
  ticketNumbers: z.string().optional(),
  agreementNumbers: z.string().optional(),
  trafficFineIds: z.string().optional(),
  bookingNos: z.string().optional(),
  plateNos: z.string().optional(),
  chargeStatuses: z.string().optional(),
  paymentStatuses: z.string().optional(),
  "fineDateRange.start": z.number().optional(),
  "fineDateRange.end": z.number().optional(),
  branchIds: z.string().optional(),
  size: z.number().optional(),
  page: z.number().optional(),
  sort: z.string().optional(),
  order: z.string().optional(),
});

export const WithdrawFromDepositBodySchema = z.object({
  posMachine: z.string().min(1, "posMachineRequired"),
  approvalCode: z.string().min(1, "approvalCodeRequired"),
  cardLast4Digit: z.string().refine((val) => /^\d{4}$/.test(val), {
    message: "cardLast4DigitRequired",
  }),
  customerConsent: z.boolean().default(false),
});

const WithdrawFromDepositResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

export const InvoiceHtmlResponseSchema = z.object({
  invoiceStatus: z.string(),
  htmlContent: z.object({
    html: z.string(),
  }),
});

export const SearchAgreementSchema = z.object({
  ticketNumber: z.string().min(1, "ticketNumberRequired"),
  plateNo: z.string().min(1, "plateNoRequired"),
  amount: z.coerce.number().min(1, "amountRequired"),
  violationDate: z.coerce.number().min(1, "violationDateTimeRequired"),
});

export const CreateTrafficFineBodySchema = z.object({
  ticketNumber: z.string().min(1, "ticketNumberRequired"),
  plateNo: z.string().min(1, "plateNoRequired"),
  agreementNo: z.string().min(1, "agreementNoRequired"),
  amount: z.coerce.number().min(1, "amountRequired"),
  fineDate: z.coerce.number().min(1, "fineDateTimeRequired"),
  issuedBranchId: z.coerce.number().min(1, "issuedBranchIdRequired"),
  chargeStatus: z.enum(["DRIVER", "DEBTOR"]),
  violationCode: z.coerce.number().min(1, "violationCodeRequired"),
  municipality: z.string().min(1, "municipalityRequired"),
  generateSeparateInvoice: z.boolean().default(true),
});

const ViolationCodeSchema = z.object({
  id: z.number(),
  nameEn: z.string(),
  nameAr: z.string(),
});

const MunicipalitySchema = z.object({
  id: z.number(),
  name: z.string(),
});

export const trafficFineContract = c.router({
  getTrafficFineList: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine`,
    query: trafficFineQueryParams,
    responses: {
      200: TrafficFineListSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  checkSecurityDeposit: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine/:trafficFineId/security-deposit/check`,
    pathParams: z.object({
      trafficFineId: z.string(),
    }),
    responses: {
      200: z.object({
        depositAmount: z.number(),
        fineAmount: z.number(),
      }),
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  withdrawFromDeposit: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine/:trafficFineId/payment/security-deposit`,
    pathParams: z.object({
      trafficFineId: z.string(),
    }),
    body: WithdrawFromDepositBodySchema,
    responses: {
      200: WithdrawFromDepositResponseSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  downloadInvoice: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice/:agreementNo/:invoiceNo/status`,
    pathParams: z.object({
      agreementNo: z.string(),
      invoiceNo: z.string(),
    }),
    responses: {
      200: InvoiceHtmlResponseSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  generatePaymentLink: {
    method: "POST",
    path: `${YAQEEN_SERVICE}//agreements/traffic-fine/:trafficFineId/regenerate/payment-link`,
    pathParams: z.object({
      trafficFineId: z.string(),
    }),
    body: z.undefined(),
    responses: {
      200: TrafficFineItemSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  searchAgreementForFine: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine/find/agreement`,
    query: z.object({
      plateNo: z.string(),
      violationDate: z.number(),
      ticketNumber: z.string(),
    }),
    responses: {
      200: AgreementSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  getAgreementDetails: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/:agreementNo/details`,
    pathParams: z.object({
      agreementNo: z.string(),
    }),
    responses: {
      200: AgreementInvoiceSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  createTrafficFine: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine`,
    body: CreateTrafficFineBodySchema,
    responses: {
      200: TrafficFineItemSchema,
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  getMunicipalityList: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine/municipalities`,
    responses: {
      200: z.array(MunicipalitySchema),
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  getViolationCodeList: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/traffic-fine/violation-codes`,
    responses: {
      200: z.array(ViolationCodeSchema),
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
  getTrafficFinesForAgreement: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/:agreementNo/traffic-fine`,
    responses: {
      200: z.array(TrafficFineItemSchema),
      400: ErrorSchema,
      404: ErrorSchema,
      500: ServerFailureError,
    },
  },
});

export type TrafficFine = z.infer<typeof TrafficFineItemSchema>;
export type AgreementDetails = z.infer<typeof AgreementInvoiceSchema>;
export type ViolationCode = z.infer<typeof ViolationCodeSchema>;
