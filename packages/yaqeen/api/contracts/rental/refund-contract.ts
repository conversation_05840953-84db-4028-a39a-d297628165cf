import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema } from "../common";
import { YAQEEN_SERVICE } from "@/api/constant";

const c = initContract();

const RefundMethodEnum = z.enum(["CASH", "CARD", "BANK_TRANSFER"]);
const RefundStatusEnum = z.enum(["REQUESTED", "COMPLETED", "REJECTED"]);

const RefundRequestSchema = z.object({
  bookingId: z.string(),
  amount: z.number(),
  branchId: z.number(),
  refundThrough: RefundMethodEnum,
  remarks: z.string().optional(),
});

const BookingDetailsSchema = z.object({
  bookingNo: z.string(),
  pickUpBranchId: z.number(),
  pickUpBranchName: z.string(),
  pickUpBranchNameAr: z.string().optional(),
  pickUpDate: z.number(),
  agreementNo: z.string().nullable(),
  bookingType: z.string().nullable(),
  dropOffBranchName: z.string(),
  dropOffBranchNameAr: z.string().optional(),
  dropOffDate: z.number(),
  dropOffBranchId: z.number(),
  bookingId: z.number().nullable(),
});
const RecipientDetailsSchema = z.object({
  phoneNumber: z.string(),
  email: z.string(),
  ibanLetterImageUrl: z.string().nullable(),
  recipientIBAN: z.string().nullable(),
  recipientBankName: z.string().nullable(),
  recipientAccountName: z.string().nullable(),
});

const RefundSearchResponseItemSchema = z.object({
  id: z.number(),
  status: RefundStatusEnum,
  amount: z.string(),
  bookingDetails: BookingDetailsSchema,
  recipientDetails: RecipientDetailsSchema,
  link: z.string().nullable(),
});

const RefundSearchResponseSchema = z.object({
  total: z.number(),
  data: z.array(RefundSearchResponseItemSchema),
});

const RefundResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  refundId: z.string().optional(),
  bookingId: z.string().optional(),
  amount: z.number().optional(),
  timestamp: z.number().optional(),
});

const UpdateRefundRecipientBankDetailSchema = z.object({
  recipientIBAN: z.string(),
  recipientBankName: z.string(),
  ibanLetterImageUrl: z.string(),
});

const UpdateRefundRequestSchema = z.object({
  userToken: z.string(),
  requestRefundId: z.number(),
  recipientBankDetail: UpdateRefundRecipientBankDetailSchema,
});

const UpdateRefundResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});

const ProcessRefundRequestSchema = z.object({
  refundRequestId: z.number(),
  recipientBankDetail: z.object({
    recipientAccountName: z.string(),
    recipientIBAN: z.string(),
    recipientBankName: z.string(),
    ibanLetterImageUrl: z.string(),
  }),
});

const ProcessRefundResponseSchema = z.object({
  refundRequestId: z.number(),
  recipientBankDetail: z.object({
    recipientAccountName: z.string(),
    recipientIBAN: z.string(),
    recipientBankName: z.string(),
    ibanLetterImageUrl: z.string(),
  }),
});

const SendPaymentLinkResponseSchema = z.object({
  referenceId: z.string(),
});

export const refundContract = c.router({
  createRefund: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/bookings/refund`,
    responses: {
      200: RefundResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    body: RefundRequestSchema,
    summary: "Create a refund for a booking",
    description: "Creates a refund transaction for a booking with the specified amount and method",
  },
  searchRefunds: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/refund/search`,
    responses: {
      200: RefundSearchResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    query: z.object({
      refundRequestStatus: RefundStatusEnum.optional(),
      bookingNo: z.string().optional(),
      page: z.number().optional(),
      size: z.number().optional(),
      customerDetailAvailable: z.string().optional(),
      pickUpBranchIds: z.string().optional(),
      dropOffBranchIds: z.string().optional(),
      agreementNo: z.string().optional(),
    }),
    summary: "Search refund requests",
    description: "Retrieves a list of refund requests",
  },
  updateRefund: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/payment/refund/update`,
    responses: {
      200: UpdateRefundResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    body: UpdateRefundRequestSchema,
    summary: "Update refund request details",
    description: "Updates specific details of a refund request, such as bank information.",
  },
  processRefund: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/payment/refund/process`,
    responses: {
      200: ProcessRefundResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    body: ProcessRefundRequestSchema,
    summary: "Process a refund request",
    description: "Process a refund request with recipient bank details",
  },
  sendPaymentLink: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/payment/refund/send-payment-link`,
    responses: {
      200: SendPaymentLinkResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    query: z.object({
      bookingNo: z.string(),
    }),
    summary: "Send refund payment link",
    description: "Sends a payment link for refund based on the booking number",
  },
});

export type RefundContract = typeof refundContract;
export type RefundMethod = z.infer<typeof RefundMethodEnum>;
export type RefundStatus = z.infer<typeof RefundStatusEnum>;
export type RefundRequest = z.infer<typeof RefundRequestSchema>;
export type RefundResponse = z.infer<typeof RefundResponseSchema>;
export type RefundSearchResponse = z.infer<typeof RefundSearchResponseSchema>;
export type UpdateRefundRequest = z.infer<typeof UpdateRefundRequestSchema>;
export type ProcessRefundRequest = z.infer<typeof ProcessRefundRequestSchema>;
export type ProcessRefundResponse = z.infer<typeof ProcessRefundResponseSchema>;
export type SendPaymentLinkResponse = z.infer<typeof SendPaymentLinkResponseSchema>;
