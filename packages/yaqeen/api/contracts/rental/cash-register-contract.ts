import { initContract } from "@ts-rest/core";
import { z } from "zod";

const c = initContract();

const StatusSchema = z.enum(["OPEN", "CLOSED", "PRE_CLOSED"]);

const BalanceSchema = z.object({
  cashAmount: z.string(),
  posAmount: z.string(),
  bankTransferAmount: z.string(),
});

const BankDetailsSchema = z.array(
  z.object({
    id: z.number(),
    ibanNo: z.string(),
    accountNo: z.string(),
    bankName: z.string(),
    branchName: z.string(),
    branchAddress: z.string(),
  })
);

const CashRegisterResponseSchema = z.array(
  z.object({
    registerNo: z.number(),
    displayRegisterNo: z.number(),
    branchId: z.number(),
    status: StatusSchema,
    openingTime: z.number(),
    closingTime: z.number().nullable(),
    closedBy: z.string().nullable(),
    approvedBy: z.string().nullable(),
    openingBalance: BalanceSchema,
    received: BalanceSchema,
    withdraw: BalanceSchema,
    bankDeposit: BalanceSchema,
    closingBalance: BalanceSchema,
  })
);

const QueryParamsSchema = z.object({
  branchIds: z.string(),
  updateDate: z.string(),
});

const ApproveRequestSchema = z.object({
  registerId: z.number(),
  bankId: z.number(),
});

const ApproveResponseSchema = z.object({
  registerNo: z.number(),
  branchId: z.number(),
  status: StatusSchema,
  closedBy: z.string(),
  approvedBy: z.string(),
});

const CloseRequestSchema = z.object({
  registerId: z.number(),
});

const CloseResponseSchema = z.object({
  registerNo: z.number(),
  branchId: z.number(),
  status: StatusSchema,
  remark: z.string(),
});

const DepositRequestSchema = z.object({
  registerId: z.number(),
});

const DepositResponseSchema = z.object({
  registerNo: z.number(),
  branchId: z.number(),
  status: StatusSchema,
  remark: z.string(),
});

export const cashRegisterContract = c.router({
  getFinanceRegister: {
    method: "GET",
    path: `/core-yaqeen-service/yaqeen/v1/payment/register/finance`,
    responses: {
      200: CashRegisterResponseSchema,
    },
    query: QueryParamsSchema,
    summary: "Get finance register details",
    description: "Retrieves the cash register financial details for specified branches and date",
  },
  getBankDetails: {
    method: "GET",
    path: `/core-yaqeen-service/yaqeen/v1/payment/bank`,
    responses: {
      200: BankDetailsSchema,
    },
    summary: "Get bank details",
    description: "Retrieves the list of bank details including IBAN, account numbers and branch information",
  },
  approve: {
    method: "POST",
    path: `/core-yaqeen-service/yaqeen/v1/payment/register/approve`,
    responses: {
      200: ApproveResponseSchema,
    },
    body: ApproveRequestSchema,
    summary: "Approve cash register",
    description: "Approves a cash register with specified register ID and bank ID",
  },
  close: {
    method: "POST",
    path: `/core-yaqeen-service/yaqeen/v1/payment/register/close`,
    responses: {
      200: CloseResponseSchema,
    },
    body: CloseRequestSchema,
    summary: "Close cash register",
    description: "Closes a cash register with the specified register ID",
  },
  deposit: {
    method: "POST",
    path: `/core-yaqeen-service/yaqeen/v1/payment/register/deposit`,
    responses: {
      200: DepositResponseSchema,
    },
    body: DepositRequestSchema,
    summary: "Deposit cash register",
    description:
      "Changes register status from OPEN to PRE_CLOSE and moves the remaining balance to the next cash register",
  },
});

export type CashRegisterContract = typeof cashRegisterContract;

export type CashRegisterResponse = z.infer<typeof CashRegisterResponseSchema>;

export type QueryParams = z.infer<typeof QueryParamsSchema>;

export type BankDetailsResponse = z.infer<typeof BankDetailsSchema>;

export type ApproveRequest = z.infer<typeof ApproveRequestSchema>;
export type ApproveResponse = z.infer<typeof ApproveResponseSchema>;

export type CloseRequest = z.infer<typeof CloseRequestSchema>;
export type CloseResponse = z.infer<typeof CloseResponseSchema>;

export type DepositRequest = z.infer<typeof DepositRequestSchema>;
export type DepositResponse = z.infer<typeof DepositResponseSchema>;
