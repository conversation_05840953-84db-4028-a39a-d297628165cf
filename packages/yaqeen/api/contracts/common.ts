import { z } from "zod";

export const PaginationResponseSchema = z.object({
  pageable: z.object({
    pageNumber: z.number(),
    pageSize: z.number(),
    sort: z.object({ unsorted: z.boolean(), sorted: z.boolean(), empty: z.boolean() }),
    offset: z.number(),
    unpaged: z.boolean(),
    paged: z.boolean(),
  }),
  totalPages: z.number(),
  totalElements: z.number(),
  numberOfElements: z.number(),
  first: z.boolean(),
  last: z.boolean(),
  empty: z.boolean(),
  size: z.number(),
  sort: z.string(),
});

export const ErrorSchema = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

export const ServerFailureError = ErrorSchema.omit({
  reqId: true,
});
