import { z } from "zod";

const promotionsType = z.enum(["PROMOTIONAL", "CORPORATE", "ONE_TIME"]);
const paymentOption = z.enum(["PAY_ONLINE", "PAY_AT_BRANCH"]);

export const promotionBodyReq = z.object({
  id: z.number().optional(),
  voucherType: promotionsType,
  nameEn: z.string(),
  nameAr: z.string(),
  descriptionEn: z.string(),
  descriptionAr: z.string(),
  percentageDiscount: z.number(),
  code: z.string(),
  validFrom: z.string(),
  validTo: z.string(),
  showInOffers: z.boolean(),
  eligibleDays: z
    .array(z.enum(["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"]))
    .optional(),
  validCount: z.number().optional(),
  branchIds: z.array(z.number()),
  carGroupCodes: z.array(z.string()),
  paymentOption,
  termsAndConditions: z.string().optional(),
  eligibleEmailDomains: z.array(z.string().optional()).optional(),
  noOfVouchers: z.coerce.number().optional(),
  isEnabled: z.boolean(),
  // isAutoApplied: z.boolean(),
});

export const promotion = z.object({
  id: z.number(),
  code: z.string(),
  name: z.object({
    en: z.string(),
    ar: z.string(),
  }),
  description: z.object({
    en: z.string(),
    ar: z.string(),
  }),
  paymentOption,
  onlineDiscount: z.number(),
  percentageDiscount: z.number(),
  branches: z.array(z.number()),
  branchIds: z.array(z.number()),
  carGroups: z.array(z.number()),
  carGroupCodes: z.array(z.string()),
  eligibleDays: z.array(z.enum(["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"])),
  position: z.number(),
  validCount: z.number().optional(),
  promotionRuleExpression: z.string(),
  showInOffers: z.boolean(),
  termsAndConditions: z.string(),
  updatedBy: z.string(),
  updatedOn: z.number(),
  createdOn: z.number(),
  createdBy: z.string(),
  voucherType: promotionsType,
  isCorporate: z.boolean(),
  isEnabled: z.boolean(),
  isAutoApplied: z.boolean(),
  valid: z.boolean(),
  validFrom: z.string(),
  validTo: z.string(),
  eligibleEmailDomains: z.array(z.string().optional()).optional(),
  noOfVouchers: z.coerce.number().optional(),
  batchPromotionCodes: z.array(z.object({ code: z.string(), validCount: z.number() })).optional(),
});

export const promotions = z.object({
  content: z.array(promotion),
  pageable: z.object({
    pageNumber: z.number(),
    pageSize: z.number(),
    sort: z.object({ unsorted: z.boolean(), sorted: z.boolean(), empty: z.boolean() }),
    offset: z.number(),
    unpaged: z.boolean(),
    paged: z.boolean(),
  }),
  totalPages: z.number(),
  totalElements: z.number(),
  numberOfElements: z.number(),
  first: z.boolean(),
  last: z.boolean(),
  empty: z.boolean(),
  size: z.number(),
  sort: z.string(),
});

export type PromotionApiResSchema = z.infer<typeof promotion>;
export type PromotionsReq = z.infer<typeof promotionBodyReq>;
export type PromotionsRes = z.infer<typeof promotions>;
export type PromotionsType = z.infer<typeof promotionsType>;
