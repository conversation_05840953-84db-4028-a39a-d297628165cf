import { initContract } from "@ts-rest/core";
import { z } from "zod";

import { errorResponse, paginationQuery, serverFailureError } from "@/api/types";

import { promotion, promotionBodyReq, promotions } from "./schema";

const c = initContract();

const SERVICE = "/pricing-service/api";

export const promotionsContract = c.router(
  {
    getNonBatchPromotions: {
      method: "GET",
      path: `${SERVICE}/promotions/v2`,
      query: z.intersection(
        z.object({
          status: z.string().optional(),
          voucherType: z.string().optional(),
        }),
        paginationQuery
      ),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: promotions,
      },
    },
    getBatchPromotions: {
      method: "GET",
      path: `${SERVICE}/promotions/v2/batch`,
      query: z.intersection(
        z.object({
          status: z.string().optional(),
          voucherType: z.string().optional(),
        }),
        paginationQuery
      ),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: promotions,
      },
    },
    getPromotionByCode: {
      method: "GET",
      path: `${SERVICE}/promotions/code/:code`,
      pathParams: z.object({
        code: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: promotion,
      },
    },
    createPromotion: {
      method: "POST",
      path: `${SERVICE}/promotions`,
      body: promotionBodyReq,
      responses: {
        404: errorResponse,
        400: errorResponse,
        409: errorResponse,
        500: serverFailureError,
        200: promotion,
      },
    },
    updatePromotion: {
      method: "PUT",
      path: `${SERVICE}/promotions/:code`,
      pathParams: z.object({
        code: z.string(),
      }),
      body: promotionBodyReq,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: promotion,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);
