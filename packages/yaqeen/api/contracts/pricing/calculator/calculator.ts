import { z } from "zod";
import { initContract } from "@ts-rest/core";

import { ErrorSchema } from "../../common";
import { calculatePriceBody, QuotePriceSchema } from "../../booking/schema";

const c = initContract();

export const calculatorContract = c.router({
  calculatePrice: {
    method: "POST",
    path: `/pricing-service/api/pricing/quote`,
    body: calculatePriceBody,
    responses: {
      200: QuotePriceSchema,
      400: ErrorSchema,
      500: ErrorSchema,
      404: ErrorSchema,
    },
  },
  getQuoteDetail: {
    method: "GET",
    path: `/pricing-service/api/pricing/quote/:quoteId`,
    pathParams: z.object({
      quoteId: z.string(),
    }),
    responses: {
      200: QuotePriceSchema,
      400: ErrorSchema,
      500: ErrorSchema,
      404: ErrorSchema,
    },
  },
  getQuoteWithAddon: {
    method: "GET",
    path: `/pricing-service/api/pricing/offers/:offerId/quote/:vehicleGroupId`,
    pathParams: z.object({
      offerId: z.string(),
      vehicleGroupId: z.string(),
    }),
    query: z.object({
      insuranceId: z.string().optional(),
      addOnIds: z.string().optional(),
    }),
    responses: {
      200: z.array(QuotePriceSchema),
      400: ErrorSchema,
      500: ErrorSchema,
      404: ErrorSchema,
    },
  },
});
