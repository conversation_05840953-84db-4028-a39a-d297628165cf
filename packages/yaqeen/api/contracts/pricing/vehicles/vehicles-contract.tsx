import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema } from "../../common";

const c = initContract();

export const VehicleLockResponseSchema = z.object({
  reference: z.string(),
  plateNumber: z.string(),
  locked: z.boolean(),
});

export const vehiclesContract = c.router({
  lockVehicle: {
    method: "PUT",
    path: `/pricing-service/api/vehicles/assign/:plateNo`,
    pathParams: z.object({
      plateNo: z.string(),
    }),
    body: z.object({}),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ErrorSchema.omit({ reqId: true }),
      200: VehicleLockResponseSchema,
    },
  },
});
