import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema, ServerFailureError } from "./common";
import { CONTENT_SERVICE } from "../constant";
const c = initContract();

const ContentUploadResponseSchema = z.object({
  data: z.string(),
});

export const contentContract = c.router(
  {
    uploadInspectionImage: {
      method: "POST",
      path: `${CONTENT_SERVICE}/fleet-service`,
      contentType: "multipart/form-data",
      body: z.object({
        file: z.any(),
      }),
      responses: {
        200: ContentUploadResponseSchema,
        400: ErrorSchema,
        500: ServerFailureError,
      },
      summary: "Upload inspection image",
      description: "Uploads an inspection image and returns the CDN URL",
    },
  },
  {
    strictStatusCodes: true,
  }
);

export type ContentContract = typeof contentContract;
