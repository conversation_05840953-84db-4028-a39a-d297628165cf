import { initContract } from "@ts-rest/core";
import { z } from "zod";

const c = initContract();

const SERVICE = "/core-customer-service";

const errorResponse = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

const serverFailureError = z.object({
  code: z.string(),
  desc: z.string(),
});

const oldPaginationQuery = z.object({
  query: z.string().optional(),
  page: z.number().optional(),
  pageSize: z.number().optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
});

const customerAccountsData = z.object({
  id: z.string(),
  name: z.object({
    id: z.string(),
    en: z.string(),
    ar: z.string(),
  }),
  address: z
    .object({
      houseNo: z.string(),
      fullAddress: z.object({
        id: z.string(),
        en: z.string(),
      }),
      zip: z.string(),
      city: z.string(),
      country: z.string(),
    })
    .optional(),
  carProId: z.string(),
  sapId: z.string(),
  isActive: z.boolean(),
});

const customerAccounts = z.object({
  data: z.array(customerAccountsData),
  total: z.number(),
});

export type ErrorResponse = z.infer<typeof errorResponse>;
export type CustomerAccountsRes = z.infer<typeof customerAccounts>;

export const customerContract = c.router(
  {
    getCustomerAccounts: {
      method: "GET",
      path: `${SERVICE}/v1/customer/accounts`,
      query: oldPaginationQuery,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: customerAccounts,
      },
    },
    getCustomerAccountsById: {
      method: "GET",
      path: `${SERVICE}/v1/customer/accounts`,
      query: z.intersection(
        z.object({
          id: z.string().optional(),
        }),
        oldPaginationQuery,
      ),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: customerAccounts,
      },
    },
  },
  {
    strictStatusCodes: true,
  },
);
