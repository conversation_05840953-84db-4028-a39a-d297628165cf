name: SonarQube Scan

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  sonar:
    name: SonarQube Scan
    runs-on: Lumi-selfhosted
    steps:
      - uses: actions/checkout@v1
      - name: Set up JDK 17
        uses: actions/setup-java@v1
        with:
          java-version: 17
      
      - name: SonarQube Scan
        run: sonar-scanner -Dsonar.projectKey=${{ github.event.repository.name }} -Dsonar.host.url=http://sonarqube-sonarqube.sonarqube.svc.cluster.local:9000 -Dsonar.token=$SONAR_GITHUB_BOT_TOKEN 

      - name: Post-SonarQube Result
        if: success()
        run: |
          echo "Sonar Analysis completed successfully"

          export token="$GITHUB_TOKEN" 
          export temp="Bearer $token"
  
          curl --location 'https://api.github.com/repos/${{ github.repository }}/statuses/${{ github.sha }}' \
            --header 'Content-Type: application/json' \
            --header "Authorization: $temp" \
            --data '{ "state": "success", "context":"Sonar Scan Report", "description" : "Sonar Scan Report", "target_url": "https://sonar.lumirental.io/dashboard?id=${{ github.event.repository.name }}"}'

      
