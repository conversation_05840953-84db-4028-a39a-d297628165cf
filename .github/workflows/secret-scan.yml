name: Secret Scan
on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  gitleaks:
    runs-on: lumi-devops
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run secret scan
        id: gitleaks
        run: |
          gitleaks dir --report-format json --report-path gitleaks.json || true
          if [ "$(jq length gitleaks.json)" -gt 0 ]; then
            echo "LEAKS_FOUND=true" >> $GITHUB_OUTPUT
          else
            echo "LEAKS_FOUND=false" >> $GITHUB_OUTPUT
          fi

      - name: Format secret scan results
        if: ${{ steps.gitleaks.outputs.LEAKS_FOUND == 'true' }}
        env:
          REPO: ${{ github.repository }}
          SHA: ${{ github.event.pull_request.head.sha }}
        run: |
          echo "## ❌ Secret scan – Potential Secrets Detected" > gitleaks.md
          echo "" >> gitleaks.md
          echo "| 📄 File | 🕵️ Secret Type | 🔢 Line | 🔗 Link |" >> gitleaks.md
          echo "|--------|----------------|--------|--------|" >> gitleaks.md
          jq -r --arg repo "$REPO" --arg sha "$SHA" '.[] |
            "| \(.File // "unknown") | \(.RuleID // "unknown") | \(.StartLine // "unknown") | [View](https://github.com/\($repo)/blob/\($sha)/\(.File // "")#L\((.StartLine // 1))) |"' gitleaks.json >> gitleaks.md
        

      - name: Publish secret scan results
        if: ${{ steps.gitleaks.outputs.LEAKS_FOUND == 'true' }}
        uses: peter-evans/create-or-update-comment@v4
        with:
          issue-number: ${{ github.event.pull_request.number }}
          body-file: gitleaks.md

      - name: Fail if secrets found
        if: ${{ steps.gitleaks.outputs.LEAKS_FOUND == 'true' }}
        run: |
          echo "❌ Secret scan detected potential secrets. Blocking the PR."
          exit 1