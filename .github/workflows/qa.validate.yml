name: Trigger Test Repo
on:
  workflow_dispatch:
    inputs:
      ONDEMAND_URL:
        required: true
        description: 'URL for ONDEMAND deployment.'

jobs:
  run-after-deployment:
    runs-on: Lumi-selfhosted

    steps:
    - name: Trigger Tests
      shell: bash
      run:  |
        export token="$GITHUB_TOKEN" 
        export temp="Bearer $token"

        response=$(curl --location --header "Authorization: $temp" \
        --url https://api.github.com/repos/${{ github.repository }}/commits/${{ github.sha }}/pulls)
        number=$(echo "$response" | jq -r '.[0].number')
        user_login=$(echo "$response" | jq -r '.[0].user.login')

        curl --location 'https://api.github.com/repos/${{ github.repository }}/statuses/${{ github.sha }}' \
        --header 'Content-Type: application/json' \
        --header "Authorization: $temp" \
        --data '{ "state": "pending", "context":"AUTOMATION_CHECK", "description" : "Test in Queue"}'

        curl --location 'https://api.github.com/repos/${{ github.repository }}/statuses/${{ github.sha }}' \
        --header 'Content-Type: application/json' \
        --header "Authorization: $temp" \
        --data '{ "state": "success", "context":"Ondemand url", "description" : "Ondemand Url", "target_url": "https://${{ github.event.inputs.ONDEMAND_URL }}"}'

      